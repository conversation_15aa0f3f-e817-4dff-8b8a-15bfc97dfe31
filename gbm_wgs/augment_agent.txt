I want to generate snakemake rules in /Users/<USER>/Library/CloudStorage/Dropbox/phd/reimand_lab_ab/gbm_wgs/bin/2023_09_05/snakemake/501-coverage_bed.smk and scripts (in /Users/<USER>/Library/CloudStorage/Dropbox/phd/reimand_lab_ab/gbm_wgs/bin/2023_09_05/coverage_to_bed) to do the following:

I need to create 1 kb-level covergae maps for DNA-seq, RNA-seq, and methylation for the entire genome. I want the coverage to be in bed format for each tumor. 

1) I want to convert the bed file from short-read data to 1kb-level coverage for the tumor vs. normal. I want the value to be log2(tumor/normal) for each 1kb resolution. Normal coverage levels are in paths like RES_DIR + "sarek_wgs/RLGS2-primary/reports/mosdepth/RLGS2-primary-blood/RLGS2-primary-blood.md.regions.bed.gz" tumor levels are in like RES_DIR + "sarek_wgs/RLGS2-primary/reports/mosdepth/RLGS2-primary-primary/RLGS2-primary-primary.md.regions.bed.gz"

2) For methylation, you will have to combine and infer regions from the bed files. Bed files for primary tumors are in like RES_DIR + "/nanopore_evolution/RLGS1/RLGS1-primary/mod/5mC/bedMethyl/5mC.RLGS1-primary_normal.bed.gz" and recurrent tumors in like RES_DIR + "/nanopore_evolution/RLGS1/RLGS1-primary/mod/5mC/bedMethyl/5mC.RLGS1-primary_tumor.bed.gz". Note that the primary tumor mathylation bed is actually labeled "normal" while recurrent tumor is "tumor". Correct these names for subsequent rules and the results files. 

3) For RNA-seq coverage, we want to convert the coverage from .bigWig to bed files and get 1kb resolution. All bigwig files are in the dir RES_DIR + "/rnaseq/star_salmon/bigwig".


Files for testing:
1. DNA coverage files: /Users/<USER>/Desktop/RLGS1-primary-blood.md.regions.bed.gz and /Users/<USER>/Desktop/RLGS1-primary-primary.md.regions.bed.gz

2. Methylation: 

3. RNA-seq coverage: /Users/<USER>/Desktop/RLGS9_recurrent.forward.bigWig and /Users/<USER>/Desktop/RLGS9_recurrent.reverse.bigWig


For all steps of this thread, make sure you follow the following rules:

1. Use python to perform data analysis and table generation. When a command line tool would work better, then use it. But, use command line tools in their own job and do not use a python wrapper for these tools. For figure generation, use R scripts. 

2. Use /Users/<USER>/opt/miniconda3/envs/py_dev/bin/python within the py_dev environment for testing python locally. 

3. Use /Users/<USER>/opt/miniconda3/envs/r4_env/bin/Rscript for the Rscript when testing locally for figure generation and /Users/<USER>/opt/miniconda3/envs/testing/bin/Rscript as "AP_SCRIPT" but keep the original Snakemake Rscripts paths. 

4. For all R scripts, use "=" and not "<-" for all cases. 

5. All python scripts should follow the same syntax as /Users/<USER>/Library/CloudStorage/Dropbox/phd/reimand_lab_ab/gbm_wgs/bin/2023_09_05/circ_dna_scripts/002a-combine_decoil_results.py. All R script should follow the same syntax as /Users/<USER>/Library/CloudStorage/Dropbox/phd/reimand_lab_ab/gbm_wgs/bin/2023_09_05/circ_dna_scripts/002b-visualize_decoil_results.R. All snakemake rules should follow the same syntax as /Users/<USER>/Library/CloudStorage/Dropbox/phd/reimand_lab_ab/gbm_wgs/bin/2023_09_05/snakemake/145-LR_SR_ecdna_comparison.smk. 

6. Do not print progress updated in the Python or R scripts. The syntax is very important.

7. Put all results in the testing directory: /Users/<USER>/Desktop/tmp

8. All R scripts should be for figure generation only, not table generation or printing commands (use python scripts for this).




continue testing but refer to /Users/<USER>/Library/CloudStorage/Dropbox/phd/reimand_lab_ab/gbm_wgs/bin/2023_09_05/nanopore_diff_methylation/002b-prepare_methylation_data.py to determine the bed file format for the methylation data

for converting bigwig to bed, use bedtools with a command similar to bedtools makewindows -g genome.chrom.sizes -w 1000 > genome.1kb.bed


