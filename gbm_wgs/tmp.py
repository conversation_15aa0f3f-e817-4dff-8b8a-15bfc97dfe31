# <PERSON>cheli
import argparse
import pandas as pd
import os
import subprocess
import tempfile

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--results_dir", required=True)  # Amplicon BED file
    parser.add_argument("--gene_bed", required=True)  # Gene BED file
    parser.add_argument("--enhancer_bed", required=True)  # Enhancer BED file
    parser.add_argument("--cgc_file", required=True)  # Cancer Gene Census file
    parser.add_argument("--bedtools_path", required=True)  # Path to bedtools
    parser.add_argument("--sample_name", required=True)  # Sample name
    parser.add_argument("--output_file", required=True)  # Output file
    return parser.parse_args()

def load_cgc_genes(cgc_file):
    """Load Cancer Gene Census genes"""
    cgc_df = pd.read_csv(cgc_file, sep='\t')
    canonical_genes = set(cgc_df['GENE_SYMBOL'].dropna().unique())
    return canonical_genes

def run_bedtools_intersect(bed_a, bed_b, bedtools_path):
    """Run bedtools intersect and return results"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.bed', delete=False) as temp_output:
        temp_output_path = temp_output.name
    
    try:
        cmd = [bedtools_path, 'intersect', '-a', bed_a, '-b', bed_b, '-wa', '-wb']
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        with open(temp_output_path, 'w') as f:
            f.write(result.stdout)
        
        return temp_output_path
    except subprocess.CalledProcessError as e:
        print(f"Error running bedtools: {e}")
        return None

def analyze_amplicon_annotations(amplicon_bed, gene_bed, enhancer_bed, cgc_genes, bedtools_path, sample_name):
    """Analyze amplicon annotations"""
    
    # Check if amplicon file exists and is not empty
    if not os.path.exists(amplicon_bed) or os.path.getsize(amplicon_bed) == 0:
        return pd.DataFrame([{
            'sample_name': sample_name,
            'amplicon_id': 'No amplicons',
            'total_genes': 0,
            'canonical_oncogenes_count': 0,
            'canonical_oncogenes': 'None',
            'enhancer_count': 0,
            'has_oncogenes': False,
            'has_enhancers': False,
            'has_other_genes': False,
            'classification': 'None'
        }])
    
    results = []
    
    # Read amplicon BED file
    amplicon_df = pd.read_csv(amplicon_bed, sep='\t', header=None)
    if amplicon_df.shape[1] == 3:
        amplicon_df.columns = ['chr', 'start', 'end']
        amplicon_df['amplicon_id'] = f"{sample_name}_amplicon"
    else:
        print(f"Warning: Unexpected amplicon BED format for {sample_name}")
        return pd.DataFrame()
    
    # For each amplicon region
    for idx, amplicon_row in amplicon_df.iterrows():
        amplicon_id = f"{sample_name}_amplicon_{idx+1}"
        
        # Create temporary amplicon file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.bed', delete=False) as temp_amplicon:
            temp_amplicon.write(f"{amplicon_row['chr']}\t{amplicon_row['start']}\t{amplicon_row['end']}\t{amplicon_id}\n")
            temp_amplicon_path = temp_amplicon.name
        
        try:
            # Intersect with genes
            gene_intersect = run_bedtools_intersect(temp_amplicon_path, gene_bed, bedtools_path)
            genes_in_amplicon = set()
            if gene_intersect and os.path.getsize(gene_intersect) > 0:
                gene_df = pd.read_csv(gene_intersect, sep='\t', header=None)
                if len(gene_df.columns) >= 7:  # amplicon + gene columns
                    genes_in_amplicon = set(gene_df.iloc[:, 6].unique())  # gene name column
            
            # Intersect with enhancers
            enhancer_intersect = run_bedtools_intersect(temp_amplicon_path, enhancer_bed, bedtools_path)
            enhancer_count = 0
            if enhancer_intersect and os.path.getsize(enhancer_intersect) > 0:
                enhancer_df = pd.read_csv(enhancer_intersect, sep='\t', header=None)
                enhancer_count = len(enhancer_df)
            
            # Find canonical oncogenes
            oncogenes_in_amplicon = genes_in_amplicon.intersection(cgc_genes)
            
            # Classify amplicon
            has_oncogenes = len(oncogenes_in_amplicon) > 0
            has_enhancers = enhancer_count > 0
            has_other_genes = len(genes_in_amplicon) > len(oncogenes_in_amplicon)
            
            # Priority classification
            if has_oncogenes:
                classification = 'Oncogenes'
            elif has_enhancers:
                classification = 'Enhancers'
            elif has_other_genes:
                classification = 'Other genes'
            else:
                classification = 'None'
            
            result = {
                'sample_name': sample_name,
                'amplicon_id': amplicon_id,
                'total_genes': len(genes_in_amplicon),
                'canonical_oncogenes_count': len(oncogenes_in_amplicon),
                'canonical_oncogenes': ','.join(sorted(oncogenes_in_amplicon)) if oncogenes_in_amplicon else 'None',
                'enhancer_count': enhancer_count,
                'has_oncogenes': has_oncogenes,
                'has_enhancers': has_enhancers,
                'has_other_genes': has_other_genes,
                'classification': classification
            }
            
            results.append(result)
            
        finally:
            # Clean up temporary files
            if os.path.exists(temp_amplicon_path):
                os.unlink(temp_amplicon_path)
            if gene_intersect and os.path.exists(gene_intersect):
                os.unlink(gene_intersect)
            if enhancer_intersect and os.path.exists(enhancer_intersect):
                os.unlink(enhancer_intersect)
    
    return pd.DataFrame(results)

def main():
    """Main function to analyze amplicon annotations"""
    args = parse_arguments()
    
    # Load canonical genes
    cgc_genes = load_cgc_genes(args.cgc_file)

    bed_file_path = os.path.join(args.results_dir, args.sample_name, "ampliconsuite/ampliconclassifier/amplicon_information/", f"{args.sample_name}_amplicon1_ecDNA_1_intervals.bed")
    
    # Analyze amplicon annotations
    results_df = analyze_amplicon_annotations(
        bed_file_path, args.gene_bed, args.enhancer_bed, 
        cgc_genes, args.bedtools_path, args.sample_name
    )
    
    # Save results
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    results_df.to_csv(args.output_file, sep='\t', index=False)
    
    print(f"Analyzed {len(results_df)} amplicon regions for sample {args.sample_name}")

if __name__ == "__main__":
    main()
