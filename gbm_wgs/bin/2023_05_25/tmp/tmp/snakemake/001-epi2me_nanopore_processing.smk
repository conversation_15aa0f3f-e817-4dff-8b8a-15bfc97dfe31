#####################
# nanopore basecalling and alignment
#####################

# basecalling and aligning with epi2me
rule guppy_basecaller:
    input:
        human_genome = RAW_DATA_DIR + "/genome_reference/hg38.fa"
        
    output:
        output_dir = dir(RAW_DATA_DIR + "/{sample}")
        
    resources:
        threads = 1,
        runtime = '0:8:0:0',
        individual_core_memory = '20G'
        
    shell:
        "guppy_basecaller \
    --config dna_r9.4.1_450bps_modbases_5mc_hac.cfg \
    --device cuda:0 \
    --bam_out --recursive --compress \
    --align_ref <reference fasta> \
    -i <fast5 input directory> -s <output directory>"



# basecalling and aligning with epi2me
rule _epi2me:
    input:
        human_genome = RAW_DATA_DIR + "/genome_reference/hg38.fa"
        
    output:
        output_dir = dir(RAW_DATA_DIR + "/{sample}")
        
    resources:
        threads = 1,
        runtime = '0:8:0:0',
        individual_core_memory = '20G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/nextflow run epi2me-labs/wf-human-snp --out_dir {output.output_dir} -w clair3_workflow/work/ -c {input.uge_config} -profile singularity --bam {input.bam} --ref {config[reference]} --model {params.clair_model}"








