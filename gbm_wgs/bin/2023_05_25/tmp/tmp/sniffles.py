# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os, subprocess

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import pybedtools

from common_utils.pre_processing import read_vcf


help_message = '''
Failed
'''


def load_vcf_regions(infile, patient_id):
    # list of chromosomes of interest
    chr_list = ['chr' + str(i) for i in range(23)][1:]
    chr_list.extend(['chrX', 'chrY'])

    # load vcf as df
    df = read_vcf(infile)

    # subset to chromosomes of interest
    mask = np.isin(df['CHROM'], chr_list)
    df = df.loc[mask,:]

    # categorize in which samples the variants exist
    df.loc[:,'gl'] = list(map(lambda x: "1" in str(x.split(":")[0]), df[f'{patient_id}_gl'].to_numpy()))
    df.loc[:,'t1'] = list(map(lambda x: "1" in str(x.split(":")[0]), df[f'{patient_id}_t1'].to_numpy()))
    df.loc[:,'t2'] = list(map(lambda x: "1" in str(x.split(":")[0]), df[f'{patient_id}_t2'].to_numpy()))

    return df


def summarize_variants(sv_df):
    res_df = []

    # iteratively classify variants by row
    for index in sv_df.index:
        info = sv_df.loc[index,'INFO'].split(";")

        # do we have information on the variant length (translocations may not have length info)
        try:
            length = np.abs(int(info[2].split("=")[1]))
        except:
            length = 0

        # type and support for the variant
        svtype = sv_df.loc[index,'ID'].split(".")[1]
        support = info[4].split("=")[1]

        # is there coverage information
        try:
            coverage = np.sum(np.array(info[5].split("=")[1].split(',')).astype('int'))
        except:
            coverage = 'NA'

        # add locus
        chrom = sv_df.loc[index,'CHROM']
        start = sv_df.loc[index,'POS']
        stop = info[3].split("=")[1]

        # add tumor information
        t1 = sv_df.loc[index,'t1']
        t2 = sv_df.loc[index,'t2']

        # append information to new df
        res_df.append([chrom, start, stop, svtype, length, support, coverage, t1, t2])

    # return df
    return pd.DataFrame(res_df, columns = ['chr', 'start', 'stop', 'type', 'length', 'support', 'coverage', 't1', 't2'])


def intersect_bed(sv_file, gene_file):
    # load bedfile objects
    bed_file1 = pybedtools.BedTool(sv_file)
    bed_file2 = pybedtools.BedTool(gene_file)

    # return intersect
    return bed_file1.intersect(bed_file2)

# def classify_clonality(common_genes_df, ):
#     # load depth dfs

#     return



def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    patient_id = sniffles_file.split("/")[-1].split("-")[0]
    print(patient_id)
    patient_id = 'RLGS_0010'



    # load and classify variants
    sv_df = load_vcf_regions(sniffles_file, patient_id)

    # remove variants identified in the germline
    mask = np.invert(sv_df['gl'])
    sv_df = sv_df.loc[mask,:]



    # summarize variants by type
    summary_df = summarize_variants(sv_df)

    # save to file
    summary_df.to_csv(variant_summary_file, sep='\t', index=False)



    # intersect with genes
    common_genes_df = intersect_bed(variant_summary_file, gene_bed_file)

    # save to file and reload as df
    common_genes_df.saveas(figure_data_file)

    common_genes_df = pd.read_csv(figure_data_file, sep='\t', header=None)
    common_genes_df.columns = ['chr', 'start', 'start', 'type', 'length', 'support', 'coverage', 'ens_chromosome', 'ens_start', 'ens_stop', 'ensemble_id', 'score', 'strand', 'source', 'gene_type', 'type_score', 'details']



    # specify gene names
    mask = np.invert(common_genes_df['details'] == ".")
    common_genes_df.loc[mask,'gene'] = list(map(lambda x: x.split(";")[3].split("=")[1], common_genes_df.loc[mask,'details']))

    # classify the mutation types and support
    common_genes_df.to_csv(figure_data_file, sep='\t')



    # # create figure
    # cline = [rscript, figure_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]
    
    # print(" ".join(cline))
    # subprocess.run(cline)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # r environment
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r_env/bin/Rscript"
    
    sniffles_file = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/sniffles/sniffles_v2.vcf"

    gene_bed_file = "/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/ensembl_hg38_27_10_2022.bed"

    variant_summary_file = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/sniffles/combined_variants.bed"

    figure_data_file = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/sniffles/combined_results.tsv"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["sniffles_file=", "variant_summary_file=", "gene_bed_file=", "figure_script=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--sniffles_file"):
            sniffles_file = str(arg).split(",")
        if opt in ("--variant_summary_file"):
            variant_summary_file = str(arg)
        if opt in ("--gene_bed_file"):
            gene_bed_file = str(arg)

        if opt in ("--figure_script"):
            figure_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()




