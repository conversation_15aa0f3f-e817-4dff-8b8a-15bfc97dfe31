# <PERSON>cheli

import sys, getopt, time, os, glob

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''

def combine_dfs(files):
    # combine all dfs
    combined_df = []

    for file in files:
        # load and subset to TPM
        df = pd.read_csv(file, sep='\t')
        df.index = df.iloc[:,0].to_numpy()
        df = df.loc[:,['TPM']]

        # append
        file_name = file.split("/")[-1].split(".")[0]
        df.columns = [file_name]
        combined_df.append(df)

    return pd.concat(combined_df, axis=1)

def ensembl_to_genes(combined_df, ensembl_translation_file):
    # load translation df
    trans_dict = pd.read_csv(ensembl_translation_file, sep='\t')
    trans_dict = dict(zip(trans_dict['Gene stable ID'], trans_dict['<PERSON> name']))

    combined_df.index = list(map(lambda x: x.split(".")[0], combined_df.index))

    return combined_df.rename(index = trans_dict)


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # combine dfs for all files
    files = glob.glob(os.path.join(rsubread_directory, file_regex))
    combined_df = combine_dfs(files)

    # translate from ensembl to gene IDs
    combined_df = ensembl_to_genes(combined_df, ensembl_translation_file)

    # save to output
    combined_df.to_csv(tpm_outfile, sep='\t')


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # regex for files of interest
    file_regex = "*.ent"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["rsubread_directory=", "ensembl_translation_file=", "gene_bed_file=", "tpm_outfile=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--rsubread_directory"):
            rsubread_directory = str(arg)
        if opt in ("--ensembl_translation_file"):
            ensembl_translation_file = str(arg)

        if opt in ("--tpm_outfile"):
            tpm_outfile = str(arg)
            
    main()




