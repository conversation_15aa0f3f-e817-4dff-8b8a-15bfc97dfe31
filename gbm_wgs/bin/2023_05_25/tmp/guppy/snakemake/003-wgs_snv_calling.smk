# Alec Ba<PERSON>cheli - <EMAIL>

#####################
# linking germline control files for easy use
#####################


rule link_bam_primary:
    input:
        merged_alignment_bam_germline = RES_DIR + "/wgs/{sample_code}-blood_wgs_seq-merged_alignment.bam",
        merged_alignment_bam_index = RES_DIR + "/wgs/{sample_code}-blood_wgs_seq-merged_alignment.bai"
        
    output:
        bam_germline_link = RES_DIR + "/wgs/{sample_code}-primary_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bam",
        bam_index_link = RES_DIR + "/wgs/{sample_code}-primary_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bai"

    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        individual_core_memory = '10G'
    
    run:
        shell("ln -s {input.merged_alignment_bam_germline} {output.bam_germline_link}")
        shell("ln -s {input.merged_alignment_bam_index} {output.bam_index_link}")

rule link_bam_recurrent:
    input:
        merged_alignment_bam_germline = RES_DIR + "/wgs/{sample_code}-blood_wgs_seq-merged_alignment.bam",
        merged_alignment_bam_index = RES_DIR + "/wgs/{sample_code}-blood_wgs_seq-merged_alignment.bai"
        
    output:
        bam_germline_link = RES_DIR + "/wgs/{sample_code}-recurrent_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bam",
        bam_index_link = RES_DIR + "/wgs/{sample_code}-recurrent_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bai"
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        individual_core_memory = '10G'
    
    run:
        shell("ln -s {input.merged_alignment_bam_germline} {output.bam_germline_link}")
        shell("ln -s {input.merged_alignment_bam_index} {output.bam_index_link}")

#####################
# Mutect2
#####################

# rule Mutect2:
#     input:
#         MAIN_DIR + "/" + GENOME_VERSION + ".fa.fai",
#         genome_fasta = MAIN_DIR + "/" + GENOME_VERSION + ".fa",

#         tumor_bam = MAIN_DIR + "/tmp/{sample}-merged_alignment.bam",
#         normal_bam = MAIN_DIR + "/tmp/46775_HR_dgApobec3_Liver_S6-merged_alignment.bam",
                
#     output:
#         output_vcf_gz = MAIN_DIR + "/vcfs/{sample}.vcf.gz"
        
#     params:
#         java_memory = '230g',
#         sample_name = "{sample}",
#         sample_tmp_dir = MAIN_DIR + "/vcfs/{sample}/"
        
#     resources:
#         threads = 1,
        # queue = "all.q",
#         runtime = '5d',
#         individual_core_memory = '235G'
        
#     shell:
#         "conda run -n gatk /.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-4.3.0.0/gatk --java-options '-Xmx{params.java_memory}' Mutect2 -R {input.genome_fasta} --input {input.tumor_bam} --input {input.normal_bam} --output {output.output_vcf_gz} -normal 46775_HR_dgApobec3_Liver_S6 --native-pair-hmm-threads {resources.threads} --tmp-dir {params.sample_tmp_dir} --intervals {input.intervals_bed_file}"


rule Mutect2:
    input:
        RES_DIR + "/" + GENOME_VERSION + ".fa.fai",
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        
        tumor_bam = RES_DIR + "/wgs/{sample_code}-recurrent_wgs_seq-merged_alignment.bam",
        normal_bam = RES_DIR + "/wgs/{sample_code}-recurrent_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bam"
        
    output:
        output_vcf_gz = RES_DIR + "/wgs/{sample_code}-recurrent_wgs_seq/{sample_code}-recurrent_wgs_seq-mutect2_{chromosome}.vcf.gz"
        
    params:
        java_memory = '230g',
        normal_sample = "{sample_code}-blood_wgs_seq",
        chromosome = "{chromosome}",
        sample_tmp_dir = RES_DIR + "/wgs/{sample_code}-recurrent_wgs_seq/"
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        individual_core_memory = '235G'
        
    shell:
        "conda run -n gatk /.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-4.3.0.0/gatk --java-options '-Xmx{params.java_memory}' Mutect2 -R {input.genome_fasta} --input {input.tumor_bam} --input {input.normal_bam} --output {output.output_vcf_gz} -normal {params.normal_sample} --tmp-dir {params.sample_tmp_dir} --intervals {params.chromosome}"



# gatk  Mutect2 -R /path/to/hg38fasta -I tumor.bam -O tumour_id -I normal.bam -O normal_id -L chrN -L intervals.bed  -O sample.vcf



#####################
# Strelka
#####################


rule init_Strelka_primary:
    input:
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        merged_alignment_bam_tumor = RES_DIR + "/wgs/{sample_code}-primary_wgs_seq-merged_alignment.bam",
        merged_alignment_bam_germline = RES_DIR + "/wgs/{sample_code}-primary_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bam"
        
    output:
        RES_DIR + "/wgs/{sample_code}-primary_wgs_seq/runWorkflow.py"
        
    params:
        working_directory = RES_DIR + "/wgs/{sample_code}-primary_wgs_seq"
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        individual_core_memory = '10G'

    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/snv_callers/bin/configureStrelkaSomaticWorkflow.py --normalBam {input.merged_alignment_bam_germline} --tumorBam {input.merged_alignment_bam_tumor} --referenceFasta {input.genome_fasta} --runDir {params.working_directory}"

rule init_Strelka_recurrent:
    input:
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        merged_alignment_bam_tumor = RES_DIR + "/wgs/{sample_code}-recurrent_wgs_seq-merged_alignment.bam",
        merged_alignment_bam_germline = RES_DIR + "/wgs/{sample_code}-recurrent_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bam"
        
    output:
        RES_DIR + "/wgs/{sample_code}-recurrent_wgs_seq/runWorkflow.py"
        
    params:
        working_directory = RES_DIR + "/wgs/{sample_code}-recurrent_wgs_seq"
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        individual_core_memory = '10G'

    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/snv_callers/bin/configureStrelkaSomaticWorkflow.py --normalBam {input.merged_alignment_bam_germline} --tumorBam {input.merged_alignment_bam_tumor} --referenceFasta {input.genome_fasta} --runDir {params.working_directory}"



rule Strelka:
    input:
        run_file = RES_DIR + "/wgs/{sample}/runWorkflow.py"
        
    output:
        directory(RES_DIR + "/wgs/{sample}/results")
        
    resources:
        threads = 30,
        queue = "all.q",
        runtime = '5d',
        individual_core_memory = '5G'

    shell:
        "{input.run_file} -m local -j {resources.threads}"



#####################
# VarScan2
#####################






#####################
# MuSe
#####################




