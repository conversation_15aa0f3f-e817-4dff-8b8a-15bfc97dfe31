# Alec Ba<PERSON>cheli - <EMAIL>
# this workflow is based on the epi2me best practices


###################################
# Create project directories
###################################
# create directory if it does not already exist
rule fastq_directories:
    output:
        working_file = RAW_DATA_DIR + "/{sample}/null.txt"
        
    params:
        working_directory = RAW_DATA_DIR + "/{sample}"
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        individual_core_memory = '10G'
        
    run:
        shell("mkdir -p {RAW_DATA_DIR} {params.working_directory}")
        shell("touch {output.working_file}")


# create directory if it does not already exist
rule nanopore_results_dir:
    input:
        res = RES_DIR + "/null.txt"
    output:
        working_file = RES_DIR + "/nanopore/{sample}/null.txt"
        
    params:
        working_directory = RES_DIR + "/nanopore/{sample}"
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = "5d",
        individual_core_memory = '10G'
        
    run:
        shell("mkdir -p {RES_DIR}/nanopore {params.working_directory}")
        shell("touch {output.working_file}")


#####################
# nanopore basecalling
#####################

# # guppy basecalling parameters
# rule guppy_gpu_basecaller:
#     input:
#         input_fast5_dir = FAST5_DIRECTORY + "/{sample}/{sub_dir}"
        
#     output:
#         BASECALLED_DIRECTORY + "/{sample}/{sub_dir}/sequencing_summary.txt"
        
#     singularity:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/software/guppy/guppy_gpu_v3.sif"
    
#     params:
#         output_fastq_dir = BASECALLED_DIRECTORY + "/{sample}/{sub_dir}/"
    
#     resources:
#         binds = "/.mounts,/usr",
#         threads = 1,
#         queue = "gpu.q -l gpu=1",
#         runtime = '5d',
#         individual_core_memory = '60G'
        
#     shell:
#         '/ont-guppy/guppy_basecaller --recursive --device "cuda:all" -q 0 --compress_fastq --config dna_r9.4.1_450bps_sup.cfg \
# -i {input.input_fast5_dir} \
# -s {params.output_fastq_dir}'


# combine fastqs after basecalling
rule concat_fastqs:
    input:
        # expand("{fastq_dir}/{sample}/{sub_dir}/sequencing_summary.txt", fastq_dir = BASECALLED_DIRECTORY, sample = nanopore_sample_list, sub_dir = list(range(n_basecalling_directories)))
        expand("{fastq_dir}/{{sample}}/{sub_dir}/sequencing_summary.txt", fastq_dir = BASECALLED_DIRECTORY, sub_dir = list(range(n_basecalling_directories)))

    output:
        fastq_file = FASTQ_DIR + "/{sample}.fastq.gz"
        
    params:
        fastq_infile_bash_regex = BASECALLED_DIRECTORY + "/{sample}/*/pass/*.fastq.gz"
    
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        individual_core_memory = '20G'
        
    shell:
        'cat {params.fastq_infile_bash_regex} > {output.fastq_file}'





#####################
# nanopore data alignment
#####################

# align with minimap2
rule minimap2_index:
    input:
        human_genome = RES_DIR + "/{genome_version}.fa"
        
    output:
        genome_index = RES_DIR + "/{genome_version}.mmi"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        individual_core_memory = '45G'
        
    shell:
        "minimap2 -d {output.genome_index} {input.human_genome}"


# align with minimap2
rule minimap2_alignment:
    input:
        RES_DIR + "/nanopore/{sample}/null.txt",
        human_genome_index = RES_DIR + "/" + GENOME_VERSION + ".mmi",
        
        fastq_file = FASTQ_DIR + "/{sample}.fastq.gz"
        
    output:
        output_sam = RES_DIR + "/nanopore/{sample}-nanopore.sam"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    params:
        tmp_file_prefix = RES_DIR + "/{sample}.tmp"
        
    resources:
        threads = NANOPORE_THREADS,
        queue = "all.q",
        runtime = '5d',
        individual_core_memory = '20G'
        
    shell:
        "minimap2 -ax map-ont -t {resources.threads} {input.human_genome_index} {input.fastq_file} > {output.output_sam}"


# sort the bam file
rule sort_bam:
    input:
        output_sam = RES_DIR + "/nanopore/{sample}-nanopore.sam"
        
    output:
        output_bam = RES_DIR + "/nanopore/{sample}-nanopore_sorted.bam"
        
    params:
        tmp_file_prefix = RES_DIR + "/{sample}.tmp"
        
    resources:
        threads = NANOPORE_THREADS,
        queue = "all.q",
        runtime = '5d',
        individual_core_memory = '5G'
        
    shell:
        "sed '$d' {input.output_sam} | /.mounts/labs/reimandlab/private/users/abahcheli/software/bin/samtools sort -@ {resources.threads} -T {params.tmp_file_prefix} -o {output.output_bam}"


# index the bam file
rule index_bam:
    input:
        output_bam = RES_DIR + "/nanopore/{sample}-nanopore_sorted.bam"
        
    output:
        RES_DIR + "/nanopore/{sample}-nanopore_sorted.bam.bai"
        
    resources:
        threads = NANOPORE_THREADS,
        queue = "all.q",
        runtime = '5d',
        individual_core_memory = '5G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/bin/samtools index -@ {resources.threads} {input.output_bam}"





#####################
# nanopore variant calling and VCF generation
#####################
rule index_fasta:
    input:
        human_genome = RES_DIR + "/" + GENOME_VERSION + ".fa"
        
    output:
        RES_DIR + "/" + GENOME_VERSION + ".fa.fai"
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        individual_core_memory = '40G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/bin/samtools faidx {input.human_genome}"




# SNVs
rule mosdepth:
    input:
        RES_DIR + "/nanopore/{sample}-nanopore_sorted.bam.bai",
        RES_DIR + "/" + GENOME_VERSION + ".fa.fai",
        
        reference_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        output_bam = RES_DIR + "/nanopore/{sample}-nanopore_sorted.bam"
        
    output:
         RES_DIR + "/nanopore/{sample}.per-base.bed.gz"
            
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    params:
        output_prefix = RES_DIR + "/nanopore/{sample}",
        
    resources:
        threads = NANOPORE_THREADS,
        queue = "all.q",
        runtime = '5d',
        individual_core_memory = '3G'
        
    shell:
        'mosdepth {params.output_prefix} {input.output_bam} --fasta {input.reference_fasta} --threads {resources.threads}'




# SNVs
rule clair3:
    input:
        RES_DIR + "/nanopore/{sample}-nanopore_sorted.bam.bai",
        RES_DIR + "/" + GENOME_VERSION + ".fa.fai",
        reference_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        
        output_bam = RES_DIR + "/nanopore/{sample}-nanopore_sorted.bam"
        
    output:
         RES_DIR + "/nanopore/{sample}/pileup.vcf.gz"
            
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/clair3"
        
    params:
        output_path = RES_DIR + "/nanopore/{sample}",
        # dna_r9.4.1_450bps_sup.cfg
        basecalling_model = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/clair3/bin/models/r941_prom_sup_g5014"
        
    resources:
        threads = NANOPORE_THREADS,
        queue = "all.q",
        runtime = '5d',
        individual_core_memory = '10G'
        
    shell:
        'run_clair3.sh --bam_fn={input.output_bam} --ref_fn={input.reference_fasta} --threads={resources.threads} --platform="ont" --model_path={params.basecalling_model} --output={params.output_path}'




# SVs
rule sniffles2:
    input:
        RES_DIR + "/nanopore/{sample}-nanopore_sorted.bam.bai",
        reference_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        
        output_bam = RES_DIR + "/nanopore/{sample}-nanopore_sorted.bam"
        
    output:
        output_snf = RES_DIR + "/nanopore/{sample}-sniffles2.snf"
        
    resources:
        threads = NANOPORE_THREADS,
        queue = "all.q",
        runtime = '5d',
        individual_core_memory = '10G'
        
    shell:
        '/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/clair3/bin/sniffles --input {input.output_bam} --snf {output.output_snf} --reference {input.reference_fasta} --threads {resources.threads} --output-rnames --non-germline'


rule sniffles2_combine:
    input:
        output_snf_blood = RES_DIR + "/nanopore/{sample_code}-blood_nanopore-sniffles2.snf",
        output_snf_primary = RES_DIR + "/nanopore/{sample_code}-primary_nanopore-sniffles2.snf",
        output_snf_recurrent = RES_DIR + "/nanopore/{sample_code}-recurrent_nanopore-sniffles2.snf"
        
    output:
        output_vcf = RES_DIR + "/nanopore/{sample_code}-sniffles2.vcf.gz"
        
    resources:
        threads = NANOPORE_THREADS,
        queue = "all.q",
        runtime = '5d',
        individual_core_memory = '10G'
        
    shell:
        '/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/clair3/bin/sniffles --input {input.output_snf_blood} {input.output_snf_primary} {input.output_snf_recurrent} --vcf {output.output_vcf} --threads {resources.threads} --output-rnames --non-germline'


# combine sniffles output to a single file
# rule sniffles2_combine:
#     input:
#         output_snf_blood = RES_DIR + "/nanopore/{sample_code}_blood_nanopore-sniffles2.snf",
#         output_snf_primary = RES_DIR + "/nanopore/{sample_code}_primary_nanopore-sniffles2.snf",
#         output_snf_recurrent = RES_DIR + "/nanopore/{sample_code}_recurrent_nanopore-sniffles2.snf"
        
#     output:
#         output_vcf = RES_DIR + "/nanopore/{sample_code}-sniffles2.vcf.gz"
        
#     resources:
#         threads = NANOPORE_THREADS,
#         queue = "all.q",
#         runtime = '5d',
#         individual_core_memory = '10G'
        
#     shell:
#         'conda run -n clair3 sniffles --input {input.output_snf_blood} {input.output_snf_primary} {input.output_snf_recurrent} --vcf {output.output_vcf} --reference {input.reference_fasta} --threads {resources.threads} --output-rnames --non-germline'










