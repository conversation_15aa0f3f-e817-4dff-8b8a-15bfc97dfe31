# Alec Ba<PERSON>cheli - <EMAIL>
# this workflow is based on GATK's best practices: https://gatk.broadinstitute.org/hc/en-us/articles/360035535912-Data-pre-processing-for-variant-discovery


###################################
# Create project directories
###################################
# create directory if it does not already exist
rule wgs_results_dir:
    input:
        RES_DIR + "/null.txt"
        
    output:
        working_file = RES_DIR + "/wgs/{sample}/null.txt"
        
    params:
        working_directory = RES_DIR + "/wgs/{sample}"
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '10G'
        
    run:
        shell("mkdir -p {RES_DIR}/wgs {params.working_directory}")
        shell("touch {output.working_file}")



# #####################
# # WGS data pre-processing
# ## NOTE follows the GATK best-practices
# #####################

# # convert fastq files to bam files before processing
# rule FastqToSam:
#     input:
#         RES_DIR + "/wgs/{sample}/null.txt",

#         fastq_forward = FASTQ_DIR + "/{sample}_R1.fastq.gz",
#         fastq_reverse = FASTQ_DIR + "/{sample}_R2.fastq.gz"
        
#     output:
#         # output_bam = temp(RES_DIR + "/wgs/{sample}_initial.bam")
#         output_bam = RES_DIR + "/wgs/{sample}_initial.bam"
        
#     singularity:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-*******/gatk.sif"
        
#     params:
#         binds = "/.mounts",
#         working_directory = RES_DIR + "/wgs/{sample}",
#         sample_name = "{sample}",
#         java_memory = '65g'
        
#     resources:
#         threads = 1,
#         queue = "all.q",
#         runtime = '5d',
#         jobtime = '0:112:0:0',
#         individual_core_memory = '70G'
        
#     shell:
#         "gatk --java-options '-Xmx{params.java_memory}' FastqToSam --FASTQ {input.fastq_forward} --FASTQ2 {input.fastq_reverse} --SAMPLE_NAME {params.sample_name} --READ_GROUP_NAME rg{params.sample_name} --PLATFORM illumina --OUTPUT {output.output_bam} --TMP_DIR {params.working_directory}"
#         # "conda run -n gatk /.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-4.3.0.0/gatk --java-options '-Xmx{params.java_memory}' FastqToSam --FASTQ {input.fastq_forward} --FASTQ2 {input.fastq_reverse} --SAMPLE_NAME {params.sample_name} --READ_GROUP_NAME rg{params.sample_name} --PLATFORM illumina --OUTPUT {output.output_bam} --TMP_DIR {params.working_directory}"



# # mark adapters
# rule MarkIlluminaAdapters:
#     input:
#         BAM_READS = RES_DIR + "/wgs/{sample}_initial.bam"
        
#     output:
#         output_bam = temp(RES_DIR + "/wgs/{sample}-markilluminaadapters.bam"),
#         output_adapters_file = temp(RES_DIR + "/wgs/{sample}-markilluminaadapters.txt")
        
#     singularity:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-*******/gatk.sif"
        
#     params:
#         binds = "/.mounts",
#         working_directory = RES_DIR + "/wgs/{sample}",
#         java_memory = '65g'
        
#     resources:
#         threads = 1,
#         queue = "all.q",
#         runtime = '5d',
#         jobtime = '0:112:0:0',
#         individual_core_memory = '70G'
        
#     shell:
#         "gatk --java-options '-Xmx{params.java_memory}' MarkIlluminaAdapters --INPUT {input.BAM_READS} --OUTPUT {output.output_bam} --METRICS {output.output_adapters_file} --TMP_DIR {params.working_directory}"


# # revert samfile with marked adapters back to fastq for alignment
# rule SamToFastq:
#     input:
#         marked_adapters_bam = RES_DIR + "/wgs/{sample}-markilluminaadapters.bam"
        
#     output:
#         fastq_file = temp(RES_DIR + "/wgs/{sample}-trimmed_interleaved.fastq")
        
#     singularity:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-*******/gatk.sif"
        
#     params:
#         binds = "/.mounts",
#         working_directory = RES_DIR + "/wgs/{sample}",
#         java_memory = '65g'
        
#     resources:
#         threads = 1,
#         queue = "all.q",
#         runtime = '5d',
#         jobtime = '0:112:0:0',
#         individual_core_memory = '70G'
        
#     shell:
#         "gatk --java-options '-Xmx{params.java_memory}' SamToFastq --INPUT {input.marked_adapters_bam} --FASTQ {output.fastq_file} --CLIPPING_ATTRIBUTE XT --CLIPPING_ACTION 2 --INTERLEAVE true --INCLUDE_NON_PF_READS true --TMP_DIR {params.working_directory}"



# # compress fastqs
# rule compress_fastq:
#     input:
#         fastq_file = RES_DIR + "/wgs/{sample}-trimmed_interleaved.fastq"
        
#     output:
#         temp(RES_DIR + "/wgs/{sample}-trimmed_interleaved.fastq.gz")
        
#     resources:
#         threads = 20,
#         queue = "all.q",
#         runtime = '5d',
#         jobtime = '0:112:0:0',
#         individual_core_memory = '10G'
        
#     shell:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/pigz -6 -k -p{resources.threads} {input.fastq_file}"


#####################
# genomic alignment of fastqs
#####################

# create genomic index
rule BWA_index:
    input:
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa"
        
    output:
        RES_DIR + "/" + GENOME_VERSION + ".fa.amb"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '120G'

    shell:
        "bwa-mem2 index {input.genome_fasta}"


# create genome dictionary for post-alignment analysis
rule CreateSequenceDictionary:
    input:
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa"
        
    output:
        genome_dict = RES_DIR + "/" + GENOME_VERSION + ".dict"
        
    singularity:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-*******/gatk.sif"
        
    params:
        binds = "/.mounts",
        java_memory = '65g'
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '70G'
        
    shell:
        "gatk --java-options '-Xmx{params.java_memory}' CreateSequenceDictionary --REFERENCE {input.genome_fasta} --OUTPUT {output.genome_dict}"


# align fastq's
rule BWA_alignment:
    input:
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        genome_index = RES_DIR + "/" + GENOME_VERSION + ".fa.amb",
        fastq_file = RES_DIR + "/wgs/{sample}-trimmed_interleaved.fastq.gz"
        
    output:
        aligned_bam_file = temp(RES_DIR + "/wgs/{sample}-bwa_mem.bam")

    resources:
        threads = WGS_THREADS,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '20G'

    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bwa-mem2 mem -M -t {resources.threads} -p {input.genome_fasta} {input.fastq_file} | /.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools view -b - > {output.aligned_bam_file}"

# # this step if very important because, for whatever reason, an extra empty line is added at the end of the sam file that needs to be deleted before further analysis
# rule sam_to_bam:
#     input:
#         aligned_sam_file = RES_DIR + "/wgs/{sample}-bwa_mem.sam"
        
#     output:
#         aligned_bam_file = temp(RES_DIR + "/wgs/{sample}-bwa_mem.bam")
        
#     resources:
#         threads = WGS_THREADS,
#         queue = "all.q",
#         runtime = '5d',
#         jobtime = '0:112:0:0',
#         individual_core_memory = '10G'

#     shell:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools view -@ {resources.threads} -b {input.aligned_sam_file} > {output.aligned_bam_file}"
#         # "sed '$d' {input.aligned_sam_file} | /.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools view -@ {resources.threads} -b - > {output.aligned_bam_file}"




#####################
# post-alignment merging and quality control
#####################

# apply original base scores to alignment
rule MergeBamAlignment:
    input:
        RES_DIR + "/" + GENOME_VERSION + ".dict",
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        
        original_bam_file = RES_DIR + "/wgs/{sample}_initial.bam",
        aligned_bam_file = RES_DIR + "/wgs/{sample}-bwa_mem.bam"
        
    output:
        # merged_alignment_bam = temp(RES_DIR + "/wgs/{sample}-merged_alignment.bam")
        merged_alignment_bam = RES_DIR + "/wgs/{sample}-merged_alignment.bam"
        
    singularity:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-*******/gatk.sif"
        
    params:
        binds = "/.mounts",
        working_directory = RES_DIR + "/wgs/{sample}",
        java_memory = '65g'
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '70G'

    shell:
        "gatk --java-options '-Xmx{params.java_memory}' MergeBamAlignment --REFERENCE_SEQUENCE {input.genome_fasta} --UNMAPPED_BAM {input.original_bam_file} --ALIGNED_BAM {input.aligned_bam_file} --OUTPUT {output.merged_alignment_bam} --CREATE_INDEX true --ADD_MATE_CIGAR true --CLIP_ADAPTERS false --CLIP_OVERLAPPING_READS true --INCLUDE_SECONDARY_ALIGNMENTS true --MAX_INSERTIONS_OR_DELETIONS -1 --PRIMARY_ALIGNMENT_STRATEGY MostDistant --ATTRIBUTES_TO_RETAIN XS --TMP_DIR {params.working_directory}"


# identify duplicate reads
rule MarkDuplicatesSpark:
    input:
        merged_alignment_bam = RES_DIR + "/wgs/{sample}-merged_alignment.bam"
        
    output:
        # marked_duplicates_bam = temp(RES_DIR + "/wgs/{sample}-marked_duplicates.bam")
        marked_duplicates_bam = RES_DIR + "/wgs/{sample}-marked_duplicates.bam"
        
    singularity:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-*******/gatk.sif"
        
    params:
        binds = "/.mounts",
        working_directory = RES_DIR + "/wgs/{sample}",
        java_memory = '190g'
        
    resources:
        threads = 20,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '10G'
        
    shell:
        "gatk --java-options '-Xmx{params.java_memory}' MarkDuplicatesSpark -I {input.merged_alignment_bam} -O {output.marked_duplicates_bam} --conf 'spark.executor.cores={resources.threads}' --tmp-dir {params.working_directory} --remove-sequencing-duplicates"


# identify sources of error for score calibration
rule BaseRecalibrator:
    input:
        marked_duplicates_bam = RES_DIR + "/wgs/{sample}-marked_duplicates.bam",
        
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        known_variance_sites = REF_DATA_DIR + "/1000G.phase3.integrated.sites_only.no_MATCHED_REV.hg38.vcf"
        
    output:
        base_recalibrated_file = RES_DIR + "/wgs/{sample}-recal_data.table"
        
    singularity:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-*******/gatk.sif"
        
    params:
        binds = "/.mounts",
        java_memory = '75g'
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '80G'
        
    shell:
        "gatk --java-options '-Xmx{params.java_memory}' BaseRecalibrator -I {input.marked_duplicates_bam} -O {output.base_recalibrated_file} --known-sites {input.known_variance_sites} -R {input.genome_fasta}"


# apply new base scores
rule ApplyRecalibration:
    input:
        marked_duplicates_bam = RES_DIR + "/wgs/{sample}-marked_duplicates.bam",
        
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        base_recalibrated_file = RES_DIR + "/wgs/{sample}-recal_data.table"
        
    output:
        recalibrated_bam = RES_DIR + "/wgs/{sample}-recalibrated.bam"

    singularity:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-*******/gatk.sif"    
        
    params:
        binds = "/.mounts",
        java_memory = '75g'
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '80G'
        
    shell:
        "gatk --java-options '-Xmx{params.java_memory}' ApplyBQSR -I {input.marked_duplicates_bam} -O {output.recalibrated_bam} --bqsr-recal-file {input.base_recalibrated_file} -R {input.genome_fasta}"



#####################
# sorting and indexing
#####################

# sort alignment
rule sort_bam_wgs:
    input:
        recalibrated_bam = RES_DIR + "/wgs/{sample}-recalibrated.bam"
        
    output:
        sorted_bam = RES_DIR + "/wgs/{sample}-recalibrated_sorted.bam"
    
    resources:
        threads = WGS_THREADS,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '10G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools sort -o {output.sorted_bam} -@ {resources.threads} {input.recalibrated_bam}"



# index alignment
rule index_bam_wgs:
    input:
        sorted_bam = RES_DIR + "/wgs/{sample}-recalibrated_sorted.bam"
        
    output:
        sorted_index = RES_DIR + "/wgs/{sample}-recalibrated_sorted.bai"
    
    resources:
        threads = WGS_THREADS,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '10G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools sort -o {output.sorted_index} -@ {resources.threads} {input.sorted_bam}"




