# Alec <PERSON>i - <EMAIL>
# this workflow is based on the Reimand Lab best practices (University of Toronto, OICR)

# create directory if it does not already exist
rule create_rna_results_dir:
    input:
        RES_DIR + "/null.txt"
        
    output:
        working_file = RES_DIR + "/r_subread/null.txt"
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '10G'
        
    run:
        shell("mkdir -p {RES_DIR}/r_subread")
        shell("touch {output.working_file}")



# create mapping index
rule r_subread_create_index:
    input:
        RES_DIR + "/r_subread/null.txt",
        genome_fasta = genome_fasta,
        
        script = BIN_DIR + "/Rsubread/001-Rsubread_index.R"
        
    output:
        RES_DIR + "/r_subread/{genome_version}.00.b.tab",
        RES_DIR + "/r_subread/{genome_version}.00.b.array",
        RES_DIR + "/r_subread/{genome_version}.log"
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '120G'
        
    shell:
        "{RSCRIPT} {input.script} --genome_version {GENOME_VERSION} --genome_fasta {input.genome_fasta} --working_directory {RES_DIR}/r_subread"



# map reads using subread
rule r_subread_alignment:
    input:
        # DEFINE genome version
        RES_DIR + "/r_subread/" + GENOME_VERSION + ".log",
        
        fastq_forward = FASTQ_DIR + "/{sample}_R1.fastq.gz",
        fastq_reverse = FASTQ_DIR + "/{sample}_R2.fastq.gz",
        
        script = BIN_DIR + "/Rsubread/002-Rsubread_alignment.R"
        
    output:
        output_bam_file = RES_DIR + "/r_subread/{sample}.subread.BAM"
        
    resources:
        threads = RNA_THREADS,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '3G'
    shell:
        "{RSCRIPT} {input.script} --fastq_forward {input.fastq_forward} --fastq_reverse {input.fastq_reverse} --genome_version {GENOME_VERSION} --output_bam_file {output.output_bam_file} --working_directory {RES_DIR}/r_subread --threads {resources.threads}"



# obtain counts for each gene
rule r_subread_counts:
    input:        
        expand("{res_dir}/r_subread/{sample}.subread.BAM", res_dir = RES_DIR, sample = rna_sample_list),
        GTF_FILE,
        
        script = BIN_DIR + "/Rsubread/003-Rsubread_counts.R"
        
    output:
        output_counts_file = RES_DIR + "/r_subread/counts.tsv"
    
    resources:
        threads = RNA_THREADS,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '3G'

    shell:
        "{RSCRIPT} {input.script} --gtf_file {GTF_FILE} --output_counts_file {output.output_counts_file} --working_directory {RES_DIR}/r_subread --threads {resources.threads}"



# rename the bam files by convention
# https://github.com/ncbi/TPMCalculator
rule rename_bam_files:
    input:
        old_bam_name = "{complete_path}.BAM"

    output:
        new_bam_name = "{complete_path}.bam"
    
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '20G'

    shell:
        "mv {input.old_bam_name} {output.new_bam_name}"
        
rule rename_bam_index:
    input:
        old_bam_name = "{complete_path}.BAM.bai"

    output:
        new_bam_name = "{complete_path}.bam.bai"
    
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '20G'

    shell:
        "mv {input.old_bam_name} {output.new_bam_name}"



# calculate tpm
# https://github.com/ncbi/TPMCalculator
rule TPMcalculator:
    input:
        expand("{res_dir}/r_subread/{sample}.subread.bam", res_dir = RES_DIR, sample = rna_sample_list),
        expand("{res_dir}/r_subread/{sample}.subread.bam.bai", res_dir = RES_DIR, sample = rna_sample_list),
        GTF_FILE
                
    output:
        expand("{res_dir}/r_subread/{sample}.subread.out", res_dir = RES_DIR, sample = rna_sample_list)
    
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '80G'

    run:
        shell("cd {res_dir}/r_subread")
        shell("/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/TPMCalculator -p -g {GTF_FILE} -d {RES_DIR}/r_subread")


# combine tpm counts
rule combine_tpm_counts:
    input:
        expand("{res_dir}/r_subread/{sample}.subread.out", res_dir = RES_DIR, sample = rna_sample_list),
        ensembl_translation_file = REF_DATA_DIR + "/ensembl_canonical_hg38.tsv",
        
        script = BIN_DIR + "/000a-tpm_processing.py"
        
    output:
        tpm_outfile = RES_DIR + "/r_subread/tpm.tsv"
        
    params:
        subread_dir = RES_DIR + "/r_subread"
        
    resources:
        threads = 1,
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTHON} {input.script} --subread_dir {params.subread_dir} --ensembl_translation_file {input.ensembl_translation_file} --tpm_outfile {output.tpm_outfile}"








