{"cells": [{"cell_type": "code", "execution_count": 2, "id": "d3419a12-ba6e-4c45-9014-8f3d07b85215", "metadata": {}, "outputs": [], "source": ["# load packages\n", "import sys, getopt, time, subprocess, os, pathlib, glob, re\n", "import gzip, io\n", "\n", "try:\n", "    sys.path.insert(1, \"/\".join(os.path.realpath(__file__).split(\"/\")[:-2]))\n", "except:\n", "    sys.path.insert(1, \"/\".join(os.getcwd().split(\"/\")[:-1]))\n", "\n", "\n", "import pandas as pd \n", "import numpy as np\n", "from lifelines import CoxPHFitter, utils\n", "\n", "# from common_utils.a_data_preprocessing import generate_cancer_abbreviation, generate_cancer_tissue_source_dict\n", "import scipy.stats as stats\n", "import statsmodels.stats.multitest as multitest\n", "\n", "from common_utils.pre_processing import read_vcf\n"]}, {"cell_type": "code", "execution_count": null, "id": "e2584561-1eb7-4d88-8706-c3139d3c5011", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8f6e7357-7403-413c-8420-b51c8653cc24", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "eab2c719-e287-48dc-b3f2-192b9a1b5d62", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7505300b-ab62-4ff3-8eff-3efe1cd2585d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "id": "9ee28faa-a6a7-4431-a41f-1b8d4dadb64a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CHROM</th>\n", "      <th>POS</th>\n", "      <th>ID</th>\n", "      <th>REF</th>\n", "      <th>ALT</th>\n", "      <th>QUAL</th>\n", "      <th>FILTER</th>\n", "      <th>INFO</th>\n", "      <th>FORMAT</th>\n", "      <th>RLGS_0010_gl</th>\n", "      <th>RLGS_0010_t2</th>\n", "      <th>gl</th>\n", "      <th>t2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>chr1</td>\n", "      <td>140033</td>\n", "      <td>Sniffles2.INS.7M0</td>\n", "      <td>N</td>\n", "      <td>ACAGAGGAAGCCTCTCACCCTCACTCGGACACCGGCCACCTCCTGC...</td>\n", "      <td>31</td>\n", "      <td>PASS</td>\n", "      <td>PRECISE;SVTYPE=INS;SVLEN=3397;END=140033;SUPPO...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>./.:0:2:0:NULL</td>\n", "      <td>0/1:6:5:2:Sniffles2.INS.DS0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>chr1</td>\n", "      <td>492199</td>\n", "      <td>Sniffles2.INS.DM0</td>\n", "      <td>N</td>\n", "      <td>GCCTGACCTCTGTCCGTATGTGGGAGGGGCCGGTGAGGCAAGGGCT...</td>\n", "      <td>31</td>\n", "      <td>PASS</td>\n", "      <td>IMPRECISE;SVTYPE=INS;SVLEN=1121;END=492199;SUP...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>./.:0:0:0:NULL</td>\n", "      <td>1/1:5:0:2:Sniffles2.INS.15S0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>chr1</td>\n", "      <td>727291</td>\n", "      <td>Sniffles2.INS.15M0</td>\n", "      <td>N</td>\n", "      <td>CACGCTGACCTCTGTCCACGTGGGAGGGGCCGGGGTGAGGCAAGGG...</td>\n", "      <td>54</td>\n", "      <td>PASS</td>\n", "      <td>IMPRECISE;SVTYPE=INS;SVLEN=94;END=727291;SUPPO...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>0/0:16:13:2:Sniffles2.INS.31S0</td>\n", "      <td>0/1:26:12:6:Sniffles2.INS.2BS0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>chr1</td>\n", "      <td>740741</td>\n", "      <td>Sniffles2.INS.17M0</td>\n", "      <td>N</td>\n", "      <td>GTCCAATCAGGAAGCATAAACCACTCAAAAGTTTAAGTGGTAAAAT...</td>\n", "      <td>59</td>\n", "      <td>PASS</td>\n", "      <td>PRECISE;SVTYPE=INS;SVLEN=5668;END=740741;SUPPO...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>./.:24:16:2:Sniffles2.INS.33S0</td>\n", "      <td>0/1:10:14:5:Sniffles2.INS.2CS0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>chr1</td>\n", "      <td>744868</td>\n", "      <td>Sniffles2.INS.14M0</td>\n", "      <td>N</td>\n", "      <td>ATATATATATATATATATATATATATATATATATATAT</td>\n", "      <td>57</td>\n", "      <td>PASS</td>\n", "      <td>PRECISE;SVTYPE=INS;SVLEN=43;END=744868;SUPPORT...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>0/0:16:13:2:Sniffles2.INS.34S0</td>\n", "      <td>0/1:39:11:7:Sniffles2.INS.2DS0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31038</th>\n", "      <td>chrY</td>\n", "      <td>56721191</td>\n", "      <td>Sniffles2.DEL.E9M1C5</td>\n", "      <td>N</td>\n", "      <td>&lt;DEL&gt;</td>\n", "      <td>51</td>\n", "      <td>PASS</td>\n", "      <td>PRECISE;SVTYPE=DEL;SVLEN=-35;END=56721226;SUPP...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>0/0:0:8:0:NULL</td>\n", "      <td>0/1:13:6:3:Sniffles2.DEL.226S1C5</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31043</th>\n", "      <td>chrY</td>\n", "      <td>56743083</td>\n", "      <td>Sniffles2.DEL.EAM1C5</td>\n", "      <td>N</td>\n", "      <td>&lt;DEL&gt;</td>\n", "      <td>52</td>\n", "      <td>PASS</td>\n", "      <td>PRECISE;SVTYPE=DEL;SVLEN=-41;END=56743124;SUPP...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>0/0:17:10:1:Sniffles2.DEL.92DS1C5,Sniffles2.DE...</td>\n", "      <td>0/1:2:10:3:Sniffles2.DEL.22DS1C5</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31044</th>\n", "      <td>chrY</td>\n", "      <td>56746241</td>\n", "      <td>Sniffles2.DEL.EDM1C5</td>\n", "      <td>N</td>\n", "      <td>&lt;DEL&gt;</td>\n", "      <td>52</td>\n", "      <td>PASS</td>\n", "      <td>PRECISE;SVTYPE=DEL;SVLEN=-37;END=56746278;SUPP...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>0/0:12:8:1:Sniffles2.DEL.931S1C5</td>\n", "      <td>0/1:30:7:5:Sniffles2.DEL.22ES1C5</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31048</th>\n", "      <td>chrY</td>\n", "      <td>56760427</td>\n", "      <td>Sniffles2.DEL.EEM1C5</td>\n", "      <td>N</td>\n", "      <td>&lt;DEL&gt;</td>\n", "      <td>42</td>\n", "      <td>PASS</td>\n", "      <td>PRECISE;SVTYPE=DEL;SVLEN=-39;END=56760466;SUPP...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>0/0:16:13:2:Sniffles2.DEL.933S1C5</td>\n", "      <td>0/1:3:13:4:Sniffles2.DEL.230S1C5</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31063</th>\n", "      <td>chrY</td>\n", "      <td>56871632</td>\n", "      <td>Sniffles2.INS.F8M1C5</td>\n", "      <td>N</td>\n", "      <td>AAAATATATATATATATATATATATATATATATATAT</td>\n", "      <td>58</td>\n", "      <td>PASS</td>\n", "      <td>PRECISE;SVTYPE=INS;SVLEN=37;END=56871632;SUPPO...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>0/0:48:46:8:Sniffles2.INS.30CS1C5</td>\n", "      <td>0/1:14:45:14:Sniffles2.INS.DES1C5</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1560 rows × 13 columns</p>\n", "</div>"], "text/plain": ["      CHROM       POS                    ID REF  \\\n", "6      chr1    140033     Sniffles2.INS.7M0   N   \n", "13     chr1    492199     Sniffles2.INS.DM0   N   \n", "21     chr1    727291    Sniffles2.INS.15M0   N   \n", "22     chr1    740741    Sniffles2.INS.17M0   N   \n", "23     chr1    744868    Sniffles2.INS.14M0   N   \n", "...     ...       ...                   ...  ..   \n", "31038  chrY  56721191  Sniffles2.DEL.E9M1C5   N   \n", "31043  chrY  56743083  Sniffles2.DEL.EAM1C5   N   \n", "31044  chrY  56746241  Sniffles2.DEL.EDM1C5   N   \n", "31048  chrY  56760427  Sniffles2.DEL.EEM1C5   N   \n", "31063  chrY  56871632  Sniffles2.INS.F8M1C5   N   \n", "\n", "                                                     ALT QUAL FILTER  \\\n", "6      ACAGAGGAAGCCTCTCACCCTCACTCGGACACCGGCCACCTCCTGC...   31   PASS   \n", "13     GCCTGACCTCTGTCCGTATGTGGGAGGGGCCGGTGAGGCAAGGGCT...   31   PASS   \n", "21     CACGCTGACCTCTGTCCACGTGGGAGGGGCCGGGGTGAGGCAAGGG...   54   PASS   \n", "22     GTCCAATCAGGAAGCATAAACCACTCAAAAGTTTAAGTGGTAAAAT...   59   PASS   \n", "23                ATATATATATATATATATATATATATATATATATATAT   57   PASS   \n", "...                                                  ...  ...    ...   \n", "31038                                              <DEL>   51   PASS   \n", "31043                                              <DEL>   52   PASS   \n", "31044                                              <DEL>   52   PASS   \n", "31048                                              <DEL>   42   PASS   \n", "31063              AAAATATATATATATATATATATATATATATATATAT   58   PASS   \n", "\n", "                                                    INFO          FORMAT  \\\n", "6      PRECISE;SVTYPE=INS;SVLEN=3397;END=140033;SUPPO...  GT:GQ:DR:DV:ID   \n", "13     IMPRECISE;SVTYPE=INS;SVLEN=1121;END=492199;SUP...  GT:GQ:DR:DV:ID   \n", "21     IMPRECISE;SVTYPE=INS;SVLEN=94;END=727291;SUPPO...  GT:GQ:DR:DV:ID   \n", "22     PRECISE;SVTYPE=INS;SVLEN=5668;END=740741;SUPPO...  GT:GQ:DR:DV:ID   \n", "23     PRECISE;SVTYPE=INS;SVLEN=43;END=744868;SUPPORT...  GT:GQ:DR:DV:ID   \n", "...                                                  ...             ...   \n", "31038  PRECISE;SVTYPE=DEL;SVLEN=-35;END=56721226;SUPP...  GT:GQ:DR:DV:ID   \n", "31043  PRECISE;SVTYPE=DEL;SVLEN=-41;END=56743124;SUPP...  GT:GQ:DR:DV:ID   \n", "31044  PRECISE;SVTYPE=DEL;SVLEN=-37;END=56746278;SUPP...  GT:GQ:DR:DV:ID   \n", "31048  PRECISE;SVTYPE=DEL;SVLEN=-39;END=56760466;SUPP...  GT:GQ:DR:DV:ID   \n", "31063  PRECISE;SVTYPE=INS;SVLEN=37;END=56871632;SUPPO...  GT:GQ:DR:DV:ID   \n", "\n", "                                            RLGS_0010_gl  \\\n", "6                                         ./.:0:2:0:NULL   \n", "13                                        ./.:0:0:0:NULL   \n", "21                        0/0:16:13:2:Sniffles2.INS.31S0   \n", "22                        ./.:24:16:2:Sniffles2.INS.33S0   \n", "23                        0/0:16:13:2:Sniffles2.INS.34S0   \n", "...                                                  ...   \n", "31038                                     0/0:0:8:0:NULL   \n", "31043  0/0:17:10:1:Sniffles2.DEL.92DS1C5,Sniffles2.DE...   \n", "31044                   0/0:12:8:1:Sniffles2.DEL.931S1C5   \n", "31048                  0/0:16:13:2:Sniffles2.DEL.933S1C5   \n", "31063                  0/0:48:46:8:Sniffles2.INS.30CS1C5   \n", "\n", "                            RLGS_0010_t2     gl    t2  \n", "6            0/1:6:5:2:Sniffles2.INS.DS0  False  True  \n", "13          1/1:5:0:2:Sniffles2.INS.15S0  False  True  \n", "21        0/1:26:12:6:Sniffles2.INS.2BS0  False  True  \n", "22        0/1:10:14:5:Sniffles2.INS.2CS0  False  True  \n", "23        0/1:39:11:7:Sniffles2.INS.2DS0  False  True  \n", "...                                  ...    ...   ...  \n", "31038   0/1:13:6:3:Sniffles2.DEL.226S1C5  False  True  \n", "31043   0/1:2:10:3:Sniffles2.DEL.22DS1C5  False  True  \n", "31044   0/1:30:7:5:Sniffles2.DEL.22ES1C5  False  True  \n", "31048   0/1:3:13:4:Sniffles2.DEL.230S1C5  False  True  \n", "31063  0/1:14:45:14:Sniffles2.INS.DES1C5  False  True  \n", "\n", "[1560 rows x 13 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# gl_df = read_vcf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/RLGS_0010_gl/sniffles2.vcf.gz\")\n", "# t2_df = read_vcf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/RLGS_0010_t2/sniffles2.vcf.gz\")\n", "\n", "chr_list = ['chr' + str(i) for i in range(23)][1:]\n", "chr_list.extend(['chrX', 'chrY'])\n", "\n", "# mask = np.isin(t2_df['CHROM'], chr_list)\n", "# t2_df = t2_df.loc[mask,:]\n", "# t2_df\n", "\n", "\n", "c_df = read_vcf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/RLGS_010_sniffles.vcf.gz\")\n", "\n", "\n", "mask = np.isin(c_df['CHROM'], chr_list)\n", "c_df = c_df.loc[mask,:]\n", "\n", "c_df.loc[:,'gl'] = list(map(lambda x: \"1\" in str(x.split(\":\")[0]), c_df['RLGS_0010_gl'].to_numpy()))\n", "c_df.loc[:,'t2'] = list(map(lambda x: \"1\" in str(x.split(\":\")[0]), c_df['RLGS_0010_t2'].to_numpy()))\n", "\n", "df = c_df.loc[np.logical_and(c_df['t2'], np.invert(c_df['gl'])),:]\n", "\n", "df"]}, {"cell_type": "code", "execution_count": 21, "id": "c46c8ae5-0a78-4a76-b0b0-0121b8b99078", "metadata": {}, "outputs": [{"data": {"text/plain": ["'IMPRECISE;SVTYPE=INS;SVLEN=1121;END=492199;SUPPORT=2;COVERAGE=2,3,2,2,4;STRAND=+-;AC=2;STDEV_LEN=0;STDEV_POS=0;SUPP_VEC=01'"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["df.loc[13,'INFO']"]}, {"cell_type": "code", "execution_count": null, "id": "27540c09-a54a-4607-ab9e-12c7061dc16b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 24, "id": "8cdef4fb-e1c1-4252-a008-43b0589afdd8", "metadata": {}, "outputs": [], "source": ["res_df = []\n", "\n", "# chrtypes = list(map(lambda x: x.split(\".\")[1], df['ID']))\n", "\n", "# for chr_type in np.unique(chrtypes):\n", "# for index in df['ID'].str.contains(chr_type, regex=True):\n", "for index in df.index:\n", "    info = df.loc[index,'INFO'].split(\";\")\n", "\n", "    try:\n", "        length = np.abs(int(info[2].split(\"=\")[1]))\n", "    except:\n", "        length = 0\n", "\n", "    svtype = df.loc[index,'ID'].split(\".\")[1]\n", "    support = info[4].split(\"=\")[1]\n", "    try:\n", "        coverage = np.sum(np.array(info[5].split(\"=\")[1].split(',')).astype('int'))\n", "    except:\n", "        coverage = 'NA'\n", "\n", "    chrom = df.loc[index,'CHROM']\n", "    start = df.loc[index,'POS']\n", "    stop = info[3].split(\"=\")[1]\n", "    \n", "    if len(stop.split(\",\")) > 1:\n", "        stop = start\n", "\n", "    res_df.append([chrom, start, stop, svtype, length, support, coverage])\n", "\n", "res_df = pd.DataFrame(res_df)\n", "res_df.columns = ['chr', 'start', 'stop', 'type', 'length', 'support', 'coverage']\n", "\n", "chr_list = ['chr' + str(i) for i in range(23)][1:]\n", "\n", "mask = np.isin(res_df['chr'], chr_list)\n", "res_df = res_df.loc[mask,:]\n", "\n", "res_df.to_csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/tmp/sniffles.tsv\", sep='\\t', index=False)"]}, {"cell_type": "code", "execution_count": 16, "id": "32564a8b-ae67-441e-9ad9-c39ef2ca0583", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>chr</th>\n", "      <th>start</th>\n", "      <th>stop</th>\n", "      <th>type</th>\n", "      <th>length</th>\n", "      <th>support</th>\n", "      <th>coverage</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>chr1</td>\n", "      <td>140033</td>\n", "      <td>140033</td>\n", "      <td>INS</td>\n", "      <td>3397</td>\n", "      <td>1</td>\n", "      <td>38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>chr1</td>\n", "      <td>492199</td>\n", "      <td>492199</td>\n", "      <td>INS</td>\n", "      <td>1121</td>\n", "      <td>2</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>chr1</td>\n", "      <td>727291</td>\n", "      <td>727291</td>\n", "      <td>INS</td>\n", "      <td>94</td>\n", "      <td>4</td>\n", "      <td>82</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>chr1</td>\n", "      <td>740741</td>\n", "      <td>740741</td>\n", "      <td>INS</td>\n", "      <td>5668</td>\n", "      <td>2</td>\n", "      <td>91</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>chr1</td>\n", "      <td>744868</td>\n", "      <td>744868</td>\n", "      <td>INS</td>\n", "      <td>43</td>\n", "      <td>4</td>\n", "      <td>80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1496</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>DEL</td>\n", "      <td>6044</td>\n", "      <td>4</td>\n", "      <td>71</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1497</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>DEL</td>\n", "      <td>4001</td>\n", "      <td>4</td>\n", "      <td>58</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1498</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>INS</td>\n", "      <td>159</td>\n", "      <td>3</td>\n", "      <td>63</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1499</th>\n", "      <td>chr9</td>\n", "      <td>138272917</td>\n", "      <td>138272917</td>\n", "      <td>INS</td>\n", "      <td>40</td>\n", "      <td>2</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1500</th>\n", "      <td>chr9</td>\n", "      <td>138277511</td>\n", "      <td>138277511</td>\n", "      <td>INS</td>\n", "      <td>3389</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1501 rows × 7 columns</p>\n", "</div>"], "text/plain": ["       chr      start       stop type  length support  coverage\n", "0     chr1     140033     140033  INS    3397       1        38\n", "1     chr1     492199     492199  INS    1121       2        13\n", "2     chr1     727291     727291  INS      94       4        82\n", "3     chr1     740741     740741  INS    5668       2        91\n", "4     chr1     744868     744868  INS      43       4        80\n", "...    ...        ...        ...  ...     ...     ...       ...\n", "1496  chr9  *********  *********  DEL    6044       4        71\n", "1497  chr9  *********  *********  DEL    4001       4        58\n", "1498  chr9  *********  *********  INS     159       3        63\n", "1499  chr9  138272917  138272917  INS      40       2        10\n", "1500  chr9  138277511  138277511  INS    3389       1         5\n", "\n", "[1501 rows x 7 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["res_df"]}, {"cell_type": "code", "execution_count": 26, "id": "7dfcef8b-3c92-45db-8891-f67ef4df11c2", "metadata": {}, "outputs": [], "source": ["res_df.to_csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/tmp/sniffles.tsv\", sep='\\t', index=False, header=False)\n", "# tmp = res_df.iloc[:,:3]"]}, {"cell_type": "code", "execution_count": null, "id": "ed45d7ab-a47d-42da-8434-9fb08351b75e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b3d6bc70-6c45-4654-8730-581af86e2055", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 58, "id": "129c1e6a-08a0-46b8-8809-441d56f6d377", "metadata": {}, "outputs": [], "source": ["import pybedtools\n", "\n", "\n", "sv_file = \"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/tmp/sniffles.tsv\"\n", "gene_file = \"/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/ensembl_hg38_27_10_2022.bed\"\n", "\n", "\n", "def intersect_bed(sv_file, gene_file):\n", "    # load bedfile objects\n", "    bed_file1 = pybedtools.BedTool(sv_file)\n", "    bed_file2 = pybedtools.BedTool(gene_file)\n", "\n", "    genes_df = bed_file1.intersect(bed_file2, loj=True)\n", "    \n", "    # return intersect\n", "    return genes_df\n", "\n", "genes_df = intersect_bed(sv_file, gene_file)\n", "genes_df.saveas(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/tmp/t2_genes.bed\")\n", "\n", "genes_df = pd.read_csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/tmp/t2_genes.bed\", sep='\\t', header=None)\n"]}, {"cell_type": "code", "execution_count": 80, "id": "e47cab3e-68d4-4134-92d9-26b22e175ae0", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/89419079.1.all.q/ipykernel_32712/2875275191.py:2: DtypeWarning: Columns (4) have mixed types. Specify dtype option on import or set low_memory=False.\n", "  trans_df = pd.read_csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/ensembl_hg38.tsv\", sep='\\t')\n"]}], "source": ["# load translation df\n", "trans_df = pd.read_csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/ensembl_hg38.tsv\", sep='\\t')\n", "trans_df = dict(zip(trans_df['Gene stable ID'], trans_df['Gene name']))\n", "\n", "# genes_df.index = list(map(lambda x: (x.split(\".\")[0]), genes_df[10]))\n", "mask = np.invert(genes_df[16] == \".\")\n", "genes_df.loc[mask,17] = list(map(lambda x: x.split(\";\")[3].split(\"=\")[1], genes_df.loc[mask,16]))\n", "\n", "genes_df.to_csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/tmp/t2_genes.tsv\", sep='\\t')"]}, {"cell_type": "code", "execution_count": 2, "id": "f602d14f-1f77-49de-9311-ef9424d6bdaa", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "      <th>6</th>\n", "      <th>7</th>\n", "      <th>8</th>\n", "      <th>9</th>\n", "      <th>10</th>\n", "      <th>11</th>\n", "      <th>12</th>\n", "      <th>13</th>\n", "      <th>14</th>\n", "      <th>15</th>\n", "      <th>16</th>\n", "      <th>17</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>chr1</td>\n", "      <td>140033</td>\n", "      <td>140033</td>\n", "      <td>INS</td>\n", "      <td>3397</td>\n", "      <td>1</td>\n", "      <td>38</td>\n", "      <td>chr1</td>\n", "      <td>139789</td>\n", "      <td>140339</td>\n", "      <td>ENSG00000239906.1</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000239906.1;gene_id=ENSG00000239906.1...</td>\n", "      <td>RP11-34P13.14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>chr1</td>\n", "      <td>492199</td>\n", "      <td>492199</td>\n", "      <td>INS</td>\n", "      <td>1121</td>\n", "      <td>2</td>\n", "      <td>13</td>\n", "      <td>chr1</td>\n", "      <td>491224</td>\n", "      <td>493241</td>\n", "      <td>ENSG00000250575.1</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000250575.1;gene_id=ENSG00000250575.1...</td>\n", "      <td>RP4-669L17.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>chr1</td>\n", "      <td>492199</td>\n", "      <td>492199</td>\n", "      <td>INS</td>\n", "      <td>1121</td>\n", "      <td>2</td>\n", "      <td>13</td>\n", "      <td>chr1</td>\n", "      <td>365388</td>\n", "      <td>522928</td>\n", "      <td>ENSG00000237094.12</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000237094.12;gene_id=ENSG00000237094....</td>\n", "      <td>RP4-669L17.4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>chr1</td>\n", "      <td>727291</td>\n", "      <td>727291</td>\n", "      <td>INS</td>\n", "      <td>94</td>\n", "      <td>4</td>\n", "      <td>82</td>\n", "      <td>chr1</td>\n", "      <td>725884</td>\n", "      <td>778626</td>\n", "      <td>ENSG00000228327.3</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000228327.3;gene_id=ENSG00000228327.3...</td>\n", "      <td>RP11-206L10.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>chr1</td>\n", "      <td>727291</td>\n", "      <td>727291</td>\n", "      <td>INS</td>\n", "      <td>94</td>\n", "      <td>4</td>\n", "      <td>82</td>\n", "      <td>chr1</td>\n", "      <td>586070</td>\n", "      <td>827796</td>\n", "      <td>ENSG00000230021.10</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000230021.10;gene_id=ENSG00000230021....</td>\n", "      <td>RP11-206L10.17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6979</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>DEL</td>\n", "      <td>6044</td>\n", "      <td>4</td>\n", "      <td>71</td>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>ENSG00000148408.13</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000148408.13;gene_id=ENSG00000148408....</td>\n", "      <td>CACNA1B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6980</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>DEL</td>\n", "      <td>4001</td>\n", "      <td>4</td>\n", "      <td>58</td>\n", "      <td>.</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6981</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>INS</td>\n", "      <td>159</td>\n", "      <td>3</td>\n", "      <td>63</td>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>ENSG00000233013.10</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000233013.10;gene_id=ENSG00000233013....</td>\n", "      <td>FAM157B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6982</th>\n", "      <td>chr9</td>\n", "      <td>138272917</td>\n", "      <td>138272917</td>\n", "      <td>INS</td>\n", "      <td>40</td>\n", "      <td>2</td>\n", "      <td>10</td>\n", "      <td>.</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6983</th>\n", "      <td>chr9</td>\n", "      <td>138277511</td>\n", "      <td>138277511</td>\n", "      <td>INS</td>\n", "      <td>3389</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>.</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6984 rows × 18 columns</p>\n", "</div>"], "text/plain": ["        0          1          2    3     4  5   6     7          8   \\\n", "0     chr1     140033     140033  INS  3397  1  38  chr1     139789   \n", "1     chr1     492199     492199  INS  1121  2  13  chr1     491224   \n", "2     chr1     492199     492199  INS  1121  2  13  chr1     365388   \n", "3     chr1     727291     727291  INS    94  4  82  chr1     725884   \n", "4     chr1     727291     727291  INS    94  4  82  chr1     586070   \n", "...    ...        ...        ...  ...   ... ..  ..   ...        ...   \n", "6979  chr9  *********  *********  DEL  6044  4  71  chr9  *********   \n", "6980  chr9  *********  *********  DEL  4001  4  58     .         -1   \n", "6981  chr9  *********  *********  INS   159  3  63  chr9  *********   \n", "6982  chr9  138272917  138272917  INS    40  2  10     .         -1   \n", "6983  chr9  138277511  138277511  INS  3389  1   5     .         -1   \n", "\n", "             9                   10 11 12      13    14 15  \\\n", "0        140339   ENSG00000239906.1  .  -  HAVANA  gene  .   \n", "1        493241   ENSG00000250575.1  .  -  HAVANA  gene  .   \n", "2        522928  ENSG00000237094.12  .  -  HAVANA  gene  .   \n", "3        778626   ENSG00000228327.3  .  -  HAVANA  gene  .   \n", "4        827796  ENSG00000230021.10  .  -  HAVANA  gene  .   \n", "...         ...                 ... .. ..     ...   ... ..   \n", "6979  *********  ENSG00000148408.13  .  +  HAVANA  gene  .   \n", "6980         -1                   .  .  .       .     .  .   \n", "6981  *********  ENSG00000233013.10  .  +  HAVANA  gene  .   \n", "6982         -1                   .  .  .       .     .  .   \n", "6983         -1                   .  .  .       .     .  .   \n", "\n", "                                                     16              17  \n", "0     ID=ENSG00000239906.1;gene_id=ENSG00000239906.1...   RP11-34P13.14  \n", "1     ID=ENSG00000250575.1;gene_id=ENSG00000250575.1...    RP4-669L17.8  \n", "2     ID=ENSG00000237094.12;gene_id=ENSG00000237094....    RP4-669L17.4  \n", "3     ID=ENSG00000228327.3;gene_id=ENSG00000228327.3...   RP11-206L10.2  \n", "4     ID=ENSG00000230021.10;gene_id=ENSG00000230021....  RP11-206L10.17  \n", "...                                                 ...             ...  \n", "6979  ID=ENSG00000148408.13;gene_id=ENSG00000148408....         CACNA1B  \n", "6980                                                  .             NaN  \n", "6981  ID=ENSG00000233013.10;gene_id=ENSG00000233013....         FAM157B  \n", "6982                                                  .             NaN  \n", "6983                                                  .             NaN  \n", "\n", "[6984 rows x 18 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["genes_df = pd.read_csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/tmp/t2_genes.bed\", sep='\\t', header=None)\n", "\n", "mask = np.invert(genes_df[16] == \".\")\n", "genes_df.loc[mask,17] = list(map(lambda x: x.split(\";\")[3].split(\"=\")[1], genes_df.loc[mask,16]))\n", "\n", "\n", "genes_df"]}, {"cell_type": "code", "execution_count": 7, "id": "ecc54c9a-7d7d-49b4-9300-1c24a49c9779", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>chr</th>\n", "      <th>start</th>\n", "      <th>start</th>\n", "      <th>type</th>\n", "      <th>length</th>\n", "      <th>support</th>\n", "      <th>coverage</th>\n", "      <th>ens_chromosome</th>\n", "      <th>ens_start</th>\n", "      <th>ens_stop</th>\n", "      <th>ensemble_id</th>\n", "      <th>score</th>\n", "      <th>strand</th>\n", "      <th>source</th>\n", "      <th>gene_type</th>\n", "      <th>type_score</th>\n", "      <th>details</th>\n", "      <th>gene</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>chr1</td>\n", "      <td>140033</td>\n", "      <td>140033</td>\n", "      <td>INS</td>\n", "      <td>3397</td>\n", "      <td>1</td>\n", "      <td>38</td>\n", "      <td>chr1</td>\n", "      <td>139789</td>\n", "      <td>140339</td>\n", "      <td>ENSG00000239906.1</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000239906.1;gene_id=ENSG00000239906.1...</td>\n", "      <td>RP11-34P13.14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>chr1</td>\n", "      <td>492199</td>\n", "      <td>492199</td>\n", "      <td>INS</td>\n", "      <td>1121</td>\n", "      <td>2</td>\n", "      <td>13</td>\n", "      <td>chr1</td>\n", "      <td>491224</td>\n", "      <td>493241</td>\n", "      <td>ENSG00000250575.1</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000250575.1;gene_id=ENSG00000250575.1...</td>\n", "      <td>RP4-669L17.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>chr1</td>\n", "      <td>492199</td>\n", "      <td>492199</td>\n", "      <td>INS</td>\n", "      <td>1121</td>\n", "      <td>2</td>\n", "      <td>13</td>\n", "      <td>chr1</td>\n", "      <td>365388</td>\n", "      <td>522928</td>\n", "      <td>ENSG00000237094.12</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000237094.12;gene_id=ENSG00000237094....</td>\n", "      <td>RP4-669L17.4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>chr1</td>\n", "      <td>727291</td>\n", "      <td>727291</td>\n", "      <td>INS</td>\n", "      <td>94</td>\n", "      <td>4</td>\n", "      <td>82</td>\n", "      <td>chr1</td>\n", "      <td>725884</td>\n", "      <td>778626</td>\n", "      <td>ENSG00000228327.3</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000228327.3;gene_id=ENSG00000228327.3...</td>\n", "      <td>RP11-206L10.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>chr1</td>\n", "      <td>727291</td>\n", "      <td>727291</td>\n", "      <td>INS</td>\n", "      <td>94</td>\n", "      <td>4</td>\n", "      <td>82</td>\n", "      <td>chr1</td>\n", "      <td>586070</td>\n", "      <td>827796</td>\n", "      <td>ENSG00000230021.10</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000230021.10;gene_id=ENSG00000230021....</td>\n", "      <td>RP11-206L10.17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6979</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>DEL</td>\n", "      <td>6044</td>\n", "      <td>4</td>\n", "      <td>71</td>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>ENSG00000148408.13</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000148408.13;gene_id=ENSG00000148408....</td>\n", "      <td>CACNA1B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6980</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>DEL</td>\n", "      <td>4001</td>\n", "      <td>4</td>\n", "      <td>58</td>\n", "      <td>.</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6981</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>INS</td>\n", "      <td>159</td>\n", "      <td>3</td>\n", "      <td>63</td>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>ENSG00000233013.10</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000233013.10;gene_id=ENSG00000233013....</td>\n", "      <td>FAM157B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6982</th>\n", "      <td>chr9</td>\n", "      <td>138272917</td>\n", "      <td>138272917</td>\n", "      <td>INS</td>\n", "      <td>40</td>\n", "      <td>2</td>\n", "      <td>10</td>\n", "      <td>.</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6983</th>\n", "      <td>chr9</td>\n", "      <td>138277511</td>\n", "      <td>138277511</td>\n", "      <td>INS</td>\n", "      <td>3389</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>.</td>\n", "      <td>-1</td>\n", "      <td>-1</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>.</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6984 rows × 18 columns</p>\n", "</div>"], "text/plain": ["       chr      start      start type  length support  coverage  \\\n", "0     chr1     140033     140033  INS    3397       1        38   \n", "1     chr1     492199     492199  INS    1121       2        13   \n", "2     chr1     492199     492199  INS    1121       2        13   \n", "3     chr1     727291     727291  INS      94       4        82   \n", "4     chr1     727291     727291  INS      94       4        82   \n", "...    ...        ...        ...  ...     ...     ...       ...   \n", "6979  chr9  *********  *********  DEL    6044       4        71   \n", "6980  chr9  *********  *********  DEL    4001       4        58   \n", "6981  chr9  *********  *********  INS     159       3        63   \n", "6982  chr9  138272917  138272917  INS      40       2        10   \n", "6983  chr9  138277511  138277511  INS    3389       1         5   \n", "\n", "     ens_chromosome  ens_start   ens_stop         ensemble_id score strand  \\\n", "0              chr1     139789     140339   ENSG00000239906.1     .      -   \n", "1              chr1     491224     493241   ENSG00000250575.1     .      -   \n", "2              chr1     365388     522928  ENSG00000237094.12     .      -   \n", "3              chr1     725884     778626   ENSG00000228327.3     .      -   \n", "4              chr1     586070     827796  ENSG00000230021.10     .      -   \n", "...             ...        ...        ...                 ...   ...    ...   \n", "6979           chr9  *********  *********  ENSG00000148408.13     .      +   \n", "6980              .         -1         -1                   .     .      .   \n", "6981           chr9  *********  *********  ENSG00000233013.10     .      +   \n", "6982              .         -1         -1                   .     .      .   \n", "6983              .         -1         -1                   .     .      .   \n", "\n", "      source gene_type type_score  \\\n", "0     HAVANA      gene          .   \n", "1     HAVANA      gene          .   \n", "2     HAVANA      gene          .   \n", "3     HAVANA      gene          .   \n", "4     HAVANA      gene          .   \n", "...      ...       ...        ...   \n", "6979  HAVANA      gene          .   \n", "6980       .         .          .   \n", "6981  HAVANA      gene          .   \n", "6982       .         .          .   \n", "6983       .         .          .   \n", "\n", "                                                details            gene  \n", "0     ID=ENSG00000239906.1;gene_id=ENSG00000239906.1...   RP11-34P13.14  \n", "1     ID=ENSG00000250575.1;gene_id=ENSG00000250575.1...    RP4-669L17.8  \n", "2     ID=ENSG00000237094.12;gene_id=ENSG00000237094....    RP4-669L17.4  \n", "3     ID=ENSG00000228327.3;gene_id=ENSG00000228327.3...   RP11-206L10.2  \n", "4     ID=ENSG00000230021.10;gene_id=ENSG00000230021....  RP11-206L10.17  \n", "...                                                 ...             ...  \n", "6979  ID=ENSG00000148408.13;gene_id=ENSG00000148408....         CACNA1B  \n", "6980                                                  .             NaN  \n", "6981  ID=ENSG00000233013.10;gene_id=ENSG00000233013....         FAM157B  \n", "6982                                                  .             NaN  \n", "6983                                                  .             NaN  \n", "\n", "[6984 rows x 18 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["figure_data_file = \"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/tmp/t2_genes.bed\"\n", "\n", "common_genes_df = pd.read_csv(figure_data_file, sep='\\t', header=None)\n", "common_genes_df.columns = ['chr', 'start', 'start', 'type', 'length', 'support', 'coverage', 'ens_chromosome', 'ens_start', 'ens_stop', 'ensemble_id', 'score', 'strand', 'source', 'gene_type', 'type_score', 'details']\n", "\n", "mask = np.invert(common_genes_df['details'] == \".\")\n", "common_genes_df.loc[mask,'gene'] = list(map(lambda x: x.split(\";\")[3].split(\"=\")[1], common_genes_df.loc[mask,'details']))\n", "\n", "\n", "common_genes_df"]}, {"cell_type": "code", "execution_count": 13, "id": "c2935c1d-1aa1-4627-9c50-e7ccf19cf4b9", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['+', '+-', '-', '1', '10', '11', '12', '2', '3', '30', '4', '5',\n", "       '6', '65', '7', '8', '9', '90'], dtype=object)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["np.unique(common_genes_df['support'])"]}, {"cell_type": "code", "execution_count": 12, "id": "e59691ac-b1e2-45fa-ba4b-2ef89a3012b3", "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "invalid literal for int() with base 10: '-'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[12], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43mcommon_genes_df\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43msupport\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mastype\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mint\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mdescribe()\n", "File \u001b[0;32m/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev/lib/python3.10/site-packages/pandas/core/generic.py:5912\u001b[0m, in \u001b[0;36mNDFrame.astype\u001b[0;34m(self, dtype, copy, errors)\u001b[0m\n\u001b[1;32m   5905\u001b[0m     results \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m   5906\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39miloc[:, i]\u001b[38;5;241m.\u001b[39mastype(dtype, copy\u001b[38;5;241m=\u001b[39mcopy)\n\u001b[1;32m   5907\u001b[0m         \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(\u001b[38;5;28mlen\u001b[39m(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns))\n\u001b[1;32m   5908\u001b[0m     ]\n\u001b[1;32m   5910\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m:\n\u001b[1;32m   5911\u001b[0m     \u001b[38;5;66;03m# else, only a single dtype is given\u001b[39;00m\n\u001b[0;32m-> 5912\u001b[0m     new_data \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_mgr\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mastype\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdtype\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcopy\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcopy\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merrors\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   5913\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_constructor(new_data)\u001b[38;5;241m.\u001b[39m__finalize__(\u001b[38;5;28mself\u001b[39m, method\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mastype\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m   5915\u001b[0m \u001b[38;5;66;03m# GH 33113: handle empty frame or series\u001b[39;00m\n", "File \u001b[0;32m/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev/lib/python3.10/site-packages/pandas/core/internals/managers.py:419\u001b[0m, in \u001b[0;36mBaseBlockManager.astype\u001b[0;34m(self, dtype, copy, errors)\u001b[0m\n\u001b[1;32m    418\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mastype\u001b[39m(\u001b[38;5;28mself\u001b[39m: T, dtype, copy: \u001b[38;5;28mbool\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mFalse\u001b[39;00m, errors: \u001b[38;5;28mstr\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mraise\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m T:\n\u001b[0;32m--> 419\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mapply\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mastype\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdtype\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcopy\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcopy\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merrors\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev/lib/python3.10/site-packages/pandas/core/internals/managers.py:304\u001b[0m, in \u001b[0;36mBaseBlockManager.apply\u001b[0;34m(self, f, align_keys, ignore_failures, **kwargs)\u001b[0m\n\u001b[1;32m    302\u001b[0m         applied \u001b[38;5;241m=\u001b[39m b\u001b[38;5;241m.\u001b[39mapply(f, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m    303\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 304\u001b[0m         applied \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mgetattr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mb\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mf\u001b[49m\u001b[43m)\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    305\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (\u001b[38;5;167;01mTypeError\u001b[39;00m, \u001b[38;5;167;01mNotImplementedError\u001b[39;00m):\n\u001b[1;32m    306\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m ignore_failures:\n", "File \u001b[0;32m/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev/lib/python3.10/site-packages/pandas/core/internals/blocks.py:580\u001b[0m, in \u001b[0;36mBlock.astype\u001b[0;34m(self, dtype, copy, errors)\u001b[0m\n\u001b[1;32m    562\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    563\u001b[0m \u001b[38;5;124;03m<PERSON><PERSON><PERSON>e to the new dtype.\u001b[39;00m\n\u001b[1;32m    564\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    576\u001b[0m \u001b[38;5;124;03mBlock\u001b[39;00m\n\u001b[1;32m    577\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    578\u001b[0m values \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mvalues\n\u001b[0;32m--> 580\u001b[0m new_values \u001b[38;5;241m=\u001b[39m \u001b[43mastype_array_safe\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcopy\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcopy\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merrors\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    582\u001b[0m new_values \u001b[38;5;241m=\u001b[39m maybe_coerce_values(new_values)\n\u001b[1;32m    583\u001b[0m newb \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmake_block(new_values)\n", "File \u001b[0;32m/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev/lib/python3.10/site-packages/pandas/core/dtypes/cast.py:1292\u001b[0m, in \u001b[0;36mastype_array_safe\u001b[0;34m(values, dtype, copy, errors)\u001b[0m\n\u001b[1;32m   1289\u001b[0m     dtype \u001b[38;5;241m=\u001b[39m dtype\u001b[38;5;241m.\u001b[39mnumpy_dtype\n\u001b[1;32m   1291\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1292\u001b[0m     new_values \u001b[38;5;241m=\u001b[39m \u001b[43mastype_array\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcopy\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcopy\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1293\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (\u001b[38;5;167;01mValueError\u001b[39;00m, \u001b[38;5;167;01mTypeError\u001b[39;00m):\n\u001b[1;32m   1294\u001b[0m     \u001b[38;5;66;03m# e.g. astype_nansafe can fail on object-dtype of strings\u001b[39;00m\n\u001b[1;32m   1295\u001b[0m     \u001b[38;5;66;03m#  trying to convert to float\u001b[39;00m\n\u001b[1;32m   1296\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m errors \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mignore\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n", "File \u001b[0;32m/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev/lib/python3.10/site-packages/pandas/core/dtypes/cast.py:1237\u001b[0m, in \u001b[0;36mastype_array\u001b[0;34m(values, dtype, copy)\u001b[0m\n\u001b[1;32m   1234\u001b[0m     values \u001b[38;5;241m=\u001b[39m values\u001b[38;5;241m.\u001b[39mastype(dtype, copy\u001b[38;5;241m=\u001b[39mcopy)\n\u001b[1;32m   1236\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1237\u001b[0m     values \u001b[38;5;241m=\u001b[39m \u001b[43mastype_nansafe\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvalues\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcopy\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcopy\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1239\u001b[0m \u001b[38;5;66;03m# in pandas we don't store numpy str dtypes, so convert to object\u001b[39;00m\n\u001b[1;32m   1240\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(dtype, np\u001b[38;5;241m.\u001b[39mdtype) \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28missubclass\u001b[39m(values\u001b[38;5;241m.\u001b[39mdtype\u001b[38;5;241m.\u001b[39mtype, \u001b[38;5;28mstr\u001b[39m):\n", "File \u001b[0;32m/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev/lib/python3.10/site-packages/pandas/core/dtypes/cast.py:1154\u001b[0m, in \u001b[0;36mastype_nansafe\u001b[0;34m(arr, dtype, copy, skipna)\u001b[0m\n\u001b[1;32m   1150\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m is_object_dtype(arr\u001b[38;5;241m.\u001b[39mdtype):\n\u001b[1;32m   1151\u001b[0m \n\u001b[1;32m   1152\u001b[0m     \u001b[38;5;66;03m# work around NumPy brokenness, #1987\u001b[39;00m\n\u001b[1;32m   1153\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m np\u001b[38;5;241m.\u001b[39missubdtype(dtype\u001b[38;5;241m.\u001b[39mtype, np\u001b[38;5;241m.\u001b[39minteger):\n\u001b[0;32m-> 1154\u001b[0m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[43mlib\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mastype_intsafe\u001b[49m\u001b[43m(\u001b[49m\u001b[43marr\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1156\u001b[0m     \u001b[38;5;66;03m# if we have a datetime/timedelta array of objects\u001b[39;00m\n\u001b[1;32m   1157\u001b[0m     \u001b[38;5;66;03m# then coerce to a proper dtype and recall astype_nansafe\u001b[39;00m\n\u001b[1;32m   1159\u001b[0m     \u001b[38;5;28;01melif\u001b[39;00m is_datetime64_dtype(dtype):\n", "File \u001b[0;32m/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev/lib/python3.10/site-packages/pandas/_libs/lib.pyx:668\u001b[0m, in \u001b[0;36mpandas._libs.lib.astype_intsafe\u001b[0;34m()\u001b[0m\n", "\u001b[0;31mValueError\u001b[0m: invalid literal for int() with base 10: '-'"]}], "source": ["common_genes_df['support'].astype('int').describe()"]}, {"cell_type": "code", "execution_count": null, "id": "466379a1-56de-4b50-845f-3262ca3c62a3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 52, "id": "b5b00d0d-344e-43b3-a333-221174392c0d", "metadata": {}, "outputs": [], "source": ["def read_vcf(path):\n", "    if path.endswith(\".gz\"):\n", "        with gzip.open(path, 'rt') as f:\n", "            lines = [l for l in f if not l.startswith('##')]\n", "    else:\n", "        with open(path, 'r') as f:\n", "            lines = [l for l in f if not l.startswith('##')]\n", "    df = pd.read_csv(\n", "        io.StringIO(''.join(lines[1:])), sep='\\t', header=None)\n", "    df.columns = ['CHROM', 'POS', 'ID', 'REF', 'ALT', 'QUAL', 'FILTER', 'INFO', 'FORMAT', 'SAMPLE', 'UNKNOWN1', 'UNKNOWN2', 'UNKNOWN3']\n", "    return df\n", "\n", "tmp = read_vcf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/sniffles/sniffles_combined.vcf\")"]}, {"cell_type": "code", "execution_count": 53, "id": "3235ef2a-cb0b-40b2-a285-ae7e78f6f82d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CHROM</th>\n", "      <th>POS</th>\n", "      <th>ID</th>\n", "      <th>REF</th>\n", "      <th>ALT</th>\n", "      <th>QUAL</th>\n", "      <th>FILTER</th>\n", "      <th>INFO</th>\n", "      <th>FORMAT</th>\n", "      <th>SAMPLE</th>\n", "      <th>UNKNOWN1</th>\n", "      <th>UNKNOWN2</th>\n", "      <th>UNKNOWN3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>chr1</td>\n", "      <td>140033</td>\n", "      <td>Sniffles2.INS.7M0</td>\n", "      <td>N</td>\n", "      <td>ACAGAGGAAGCCTCTCACCCTCACTCGGACACCGGCCACCTCCTGC...</td>\n", "      <td>31</td>\n", "      <td>PASS</td>\n", "      <td>PRECISE;SVTYPE=INS;SVLEN=3397;END=140033;SUPPO...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>./.:0:2:0:NULL</td>\n", "      <td>0/1:6:5:2:Sniffles2.INS.DS0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>chr1</td>\n", "      <td>492199</td>\n", "      <td>Sniffles2.INS.DM0</td>\n", "      <td>N</td>\n", "      <td>GCCTGACCTCTGTCCGTATGTGGGAGGGGCCGGTGAGGCAAGGGCT...</td>\n", "      <td>31</td>\n", "      <td>PASS</td>\n", "      <td>IMPRECISE;SVTYPE=INS;SVLEN=1121;END=492199;SUP...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>./.:0:0:0:NULL</td>\n", "      <td>1/1:5:0:2:Sniffles2.INS.15S0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>chr1</td>\n", "      <td>727291</td>\n", "      <td>Sniffles2.INS.15M0</td>\n", "      <td>N</td>\n", "      <td>CACGCTGACCTCTGTCCACGTGGGAGGGGCCGGGGTGAGGCAAGGG...</td>\n", "      <td>54</td>\n", "      <td>PASS</td>\n", "      <td>IMPRECISE;SVTYPE=INS;SVLEN=94;END=727291;SUPPO...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>0/0:16:13:2:Sniffles2.INS.31S0</td>\n", "      <td>0/1:26:12:6:Sniffles2.INS.2BS0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>chr1</td>\n", "      <td>740741</td>\n", "      <td>Sniffles2.INS.17M0</td>\n", "      <td>N</td>\n", "      <td>GTCCAATCAGGAAGCATAAACCACTCAAAAGTTTAAGTGGTAAAAT...</td>\n", "      <td>59</td>\n", "      <td>PASS</td>\n", "      <td>PRECISE;SVTYPE=INS;SVLEN=5668;END=740741;SUPPO...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>./.:24:16:2:Sniffles2.INS.33S0</td>\n", "      <td>0/1:10:14:5:Sniffles2.INS.2CS0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>chr1</td>\n", "      <td>744868</td>\n", "      <td>Sniffles2.INS.14M0</td>\n", "      <td>N</td>\n", "      <td>ATATATATATATATATATATATATATATATATATATAT</td>\n", "      <td>57</td>\n", "      <td>PASS</td>\n", "      <td>PRECISE;SVTYPE=INS;SVLEN=43;END=744868;SUPPORT...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>0/0:16:13:2:Sniffles2.INS.34S0</td>\n", "      <td>0/1:39:11:7:Sniffles2.INS.2DS0</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1684</th>\n", "      <td>chrY</td>\n", "      <td>56721191</td>\n", "      <td>Sniffles2.DEL.E9M1C5</td>\n", "      <td>N</td>\n", "      <td>&lt;DEL&gt;</td>\n", "      <td>51</td>\n", "      <td>PASS</td>\n", "      <td>PRECISE;SVTYPE=DEL;SVLEN=-35;END=56721226;SUPP...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>0/0:0:8:0:NULL</td>\n", "      <td>0/1:13:6:3:Sniffles2.DEL.226S1C5</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1685</th>\n", "      <td>chrY</td>\n", "      <td>56743083</td>\n", "      <td>Sniffles2.DEL.EAM1C5</td>\n", "      <td>N</td>\n", "      <td>&lt;DEL&gt;</td>\n", "      <td>52</td>\n", "      <td>PASS</td>\n", "      <td>PRECISE;SVTYPE=DEL;SVLEN=-41;END=56743124;SUPP...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>0/0:17:10:1:Sniffles2.DEL.92DS1C5,Sniffles2.DE...</td>\n", "      <td>0/1:2:10:3:Sniffles2.DEL.22DS1C5</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1686</th>\n", "      <td>chrY</td>\n", "      <td>56746241</td>\n", "      <td>Sniffles2.DEL.EDM1C5</td>\n", "      <td>N</td>\n", "      <td>&lt;DEL&gt;</td>\n", "      <td>52</td>\n", "      <td>PASS</td>\n", "      <td>PRECISE;SVTYPE=DEL;SVLEN=-37;END=56746278;SUPP...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>0/0:12:8:1:Sniffles2.DEL.931S1C5</td>\n", "      <td>0/1:30:7:5:Sniffles2.DEL.22ES1C5</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1687</th>\n", "      <td>chrY</td>\n", "      <td>56760427</td>\n", "      <td>Sniffles2.DEL.EEM1C5</td>\n", "      <td>N</td>\n", "      <td>&lt;DEL&gt;</td>\n", "      <td>42</td>\n", "      <td>PASS</td>\n", "      <td>PRECISE;SVTYPE=DEL;SVLEN=-39;END=56760466;SUPP...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>0/0:16:13:2:Sniffles2.DEL.933S1C5</td>\n", "      <td>0/1:3:13:4:Sniffles2.DEL.230S1C5</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1688</th>\n", "      <td>chrY</td>\n", "      <td>56871632</td>\n", "      <td>Sniffles2.INS.F8M1C5</td>\n", "      <td>N</td>\n", "      <td>AAAATATATATATATATATATATATATATATATATAT</td>\n", "      <td>58</td>\n", "      <td>PASS</td>\n", "      <td>PRECISE;SVTYPE=INS;SVLEN=37;END=56871632;SUPPO...</td>\n", "      <td>GT:GQ:DR:DV:ID</td>\n", "      <td>0/0:48:46:8:Sniffles2.INS.30CS1C5</td>\n", "      <td>0/1:14:45:14:Sniffles2.INS.DES1C5</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1689 rows × 13 columns</p>\n", "</div>"], "text/plain": ["     CHROM       POS                    ID REF  \\\n", "0     chr1    140033     Sniffles2.INS.7M0   N   \n", "1     chr1    492199     Sniffles2.INS.DM0   N   \n", "2     chr1    727291    Sniffles2.INS.15M0   N   \n", "3     chr1    740741    Sniffles2.INS.17M0   N   \n", "4     chr1    744868    Sniffles2.INS.14M0   N   \n", "...    ...       ...                   ...  ..   \n", "1684  chrY  56721191  Sniffles2.DEL.E9M1C5   N   \n", "1685  chrY  56743083  Sniffles2.DEL.EAM1C5   N   \n", "1686  chrY  56746241  Sniffles2.DEL.EDM1C5   N   \n", "1687  chrY  56760427  Sniffles2.DEL.EEM1C5   N   \n", "1688  chrY  56871632  Sniffles2.INS.F8M1C5   N   \n", "\n", "                                                    ALT  QUAL FILTER  \\\n", "0     ACAGAGGAAGCCTCTCACCCTCACTCGGACACCGGCCACCTCCTGC...    31   PASS   \n", "1     GCCTGACCTCTGTCCGTATGTGGGAGGGGCCGGTGAGGCAAGGGCT...    31   PASS   \n", "2     CACGCTGACCTCTGTCCACGTGGGAGGGGCCGGGGTGAGGCAAGGG...    54   PASS   \n", "3     GTCCAATCAGGAAGCATAAACCACTCAAAAGTTTAAGTGGTAAAAT...    59   PASS   \n", "4                ATATATATATATATATATATATATATATATATATATAT    57   PASS   \n", "...                                                 ...   ...    ...   \n", "1684                                              <DEL>    51   PASS   \n", "1685                                              <DEL>    52   PASS   \n", "1686                                              <DEL>    52   PASS   \n", "1687                                              <DEL>    42   PASS   \n", "1688              AAAATATATATATATATATATATATATATATATATAT    58   PASS   \n", "\n", "                                                   INFO          FORMAT  \\\n", "0     PRECISE;SVTYPE=INS;SVLEN=3397;END=140033;SUPPO...  GT:GQ:DR:DV:ID   \n", "1     IMPRECISE;SVTYPE=INS;SVLEN=1121;END=492199;SUP...  GT:GQ:DR:DV:ID   \n", "2     IMPRECISE;SVTYPE=INS;SVLEN=94;END=727291;SUPPO...  GT:GQ:DR:DV:ID   \n", "3     PRECISE;SVTYPE=INS;SVLEN=5668;END=740741;SUPPO...  GT:GQ:DR:DV:ID   \n", "4     PRECISE;SVTYPE=INS;SVLEN=43;END=744868;SUPPORT...  GT:GQ:DR:DV:ID   \n", "...                                                 ...             ...   \n", "1684  PRECISE;SVTYPE=DEL;SVLEN=-35;END=56721226;SUPP...  GT:GQ:DR:DV:ID   \n", "1685  PRECISE;SVTYPE=DEL;SVLEN=-41;END=56743124;SUPP...  GT:GQ:DR:DV:ID   \n", "1686  PRECISE;SVTYPE=DEL;SVLEN=-37;END=56746278;SUPP...  GT:GQ:DR:DV:ID   \n", "1687  PRECISE;SVTYPE=DEL;SVLEN=-39;END=56760466;SUPP...  GT:GQ:DR:DV:ID   \n", "1688  PRECISE;SVTYPE=INS;SVLEN=37;END=56871632;SUPPO...  GT:GQ:DR:DV:ID   \n", "\n", "                                                 SAMPLE  \\\n", "0                                        ./.:0:2:0:NULL   \n", "1                                        ./.:0:0:0:NULL   \n", "2                        0/0:16:13:2:Sniffles2.INS.31S0   \n", "3                        ./.:24:16:2:Sniffles2.INS.33S0   \n", "4                        0/0:16:13:2:Sniffles2.INS.34S0   \n", "...                                                 ...   \n", "1684                                     0/0:0:8:0:NULL   \n", "1685  0/0:17:10:1:Sniffles2.DEL.92DS1C5,Sniffles2.DE...   \n", "1686                   0/0:12:8:1:Sniffles2.DEL.931S1C5   \n", "1687                  0/0:16:13:2:Sniffles2.DEL.933S1C5   \n", "1688                  0/0:48:46:8:Sniffles2.INS.30CS1C5   \n", "\n", "                               UNKNOWN1  UNKNOWN2  UNKNOWN3  \n", "0           0/1:6:5:2:Sniffles2.INS.DS0     False      True  \n", "1          1/1:5:0:2:Sniffles2.INS.15S0     False      True  \n", "2        0/1:26:12:6:Sniffles2.INS.2BS0     False      True  \n", "3        0/1:10:14:5:Sniffles2.INS.2CS0     False      True  \n", "4        0/1:39:11:7:Sniffles2.INS.2DS0     False      True  \n", "...                                 ...       ...       ...  \n", "1684   0/1:13:6:3:Sniffles2.DEL.226S1C5     False      True  \n", "1685   0/1:2:10:3:Sniffles2.DEL.22DS1C5     False      True  \n", "1686   0/1:30:7:5:Sniffles2.DEL.22ES1C5     False      True  \n", "1687   0/1:3:13:4:Sniffles2.DEL.230S1C5     False      True  \n", "1688  0/1:14:45:14:Sniffles2.INS.DES1C5     False      True  \n", "\n", "[1689 rows x 13 columns]"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["tmp"]}, {"cell_type": "code", "execution_count": 90, "id": "7411d627-3fe1-4598-af72-50095e31ce2d", "metadata": {}, "outputs": [], "source": ["import pybedtools\n", "\n", "from common_utils.pre_processing import read_vcf\n", "\n", "\n", "help_message = '''\n", "Failed\n", "'''\n", "\n", "\n", "def load_vcf_regions(infile, patient_id):\n", "    # list of chromosomes of interest\n", "    chr_list = ['chr' + str(i) for i in range(23)][1:]\n", "    chr_list.extend(['chrX', 'chrY'])\n", "\n", "    # load vcf as df\n", "    df = read_vcf(infile)\n", "\n", "    # subset to chromosomes of interest\n", "    mask = np.isin(df['CHROM'], chr_list)\n", "    df = df.loc[mask,:]\n", "\n", "    # categorize in which samples the variants exist\n", "    df.loc[:,'gl'] = list(map(lambda x: \"1\" in str(x.split(\":\")[0]), df[f'{patient_id}_gl'].to_numpy()))\n", "    df.loc[:,'t1'] = list(map(lambda x: \"1\" in str(x.split(\":\")[0]), df[f'{patient_id}_t1'].to_numpy()))\n", "    df.loc[:,'t2'] = list(map(lambda x: \"1\" in str(x.split(\":\")[0]), df[f'{patient_id}_t2'].to_numpy()))\n", "\n", "    return df\n", "\n", "\n", "def summarize_variants(sv_df):\n", "    res_df = []\n", "\n", "    # iteratively classify variants by row\n", "    for index in sv_df.index:\n", "        info = sv_df.loc[index,'INFO'].split(\";\")\n", "        \n", "        # type and support for the variant\n", "        svtype = sv_df.loc[index,'ID'].split(\".\")[1]\n", "        support = info[4].split(\"=\")[1].split(\",\")[0]\n", "\n", "        # do we have information on the variant length (translocations may not have length info)\n", "        if svtype != \"BND\":\n", "            try:\n", "                length = np.abs(int(info[2].split(\"=\")[1]))\n", "            except:\n", "                length = 0\n", "        else:\n", "            length = 0\n", "\n", "\n", "        # is there coverage information\n", "        try:\n", "            coverage = np.sum(np.array(info[4].split(\"=\")[1].split(',')).astype('int'))\n", "        except:\n", "            coverage = 'NA'\n", "\n", "        # add locus\n", "        chrom = sv_df.loc[index,'CHROM']\n", "        start = sv_df.loc[index,'POS']\n", "        \n", "        if svtype != \"BND\":\n", "            stop = info[3].split(\"=\")[1]\n", "        else:\n", "            stop = start\n", "\n", "        # add tumor information\n", "        t1 = sv_df.loc[index,'t1']\n", "        t2 = sv_df.loc[index,'t2']\n", "\n", "        # append information to new df\n", "        res_df.append([chrom, start, stop, svtype, length, support, coverage, t1, t2])\n", "\n", "    # return df\n", "    return pd.DataFrame(res_df, columns = ['chr', 'start', 'stop', 'type', 'length', 'support', 'coverage', 't1', 't2'])\n", "\n", "\n", "def intersect_bed(sv_file, gene_file):\n", "    # load bedfile objects\n", "    bed_file1 = pybedtools.BedTool(sv_file)\n", "    bed_file2 = pybedtools.BedTool(gene_file)\n", "\n", "    # return intersect\n", "    return bed_file1.intersect(bed_file2, wa=True, wb=True)\n"]}, {"cell_type": "code", "execution_count": 83, "id": "cfba2f06-676c-44e4-b364-80ac4d90eb9b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>chr</th>\n", "      <th>start</th>\n", "      <th>stop</th>\n", "      <th>type</th>\n", "      <th>length</th>\n", "      <th>support</th>\n", "      <th>coverage</th>\n", "      <th>t1</th>\n", "      <th>t2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>chr1</td>\n", "      <td>10693</td>\n", "      <td>10928</td>\n", "      <td>DEL</td>\n", "      <td>235</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>chr1</td>\n", "      <td>64078</td>\n", "      <td>NA</td>\n", "      <td>BND</td>\n", "      <td>0</td>\n", "      <td>15</td>\n", "      <td>73</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>chr1</td>\n", "      <td>140033</td>\n", "      <td>140033</td>\n", "      <td>INS</td>\n", "      <td>3397</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>chr1</td>\n", "      <td>492291</td>\n", "      <td>492291</td>\n", "      <td>INS</td>\n", "      <td>1073</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>chr1</td>\n", "      <td>610401</td>\n", "      <td>610533</td>\n", "      <td>DEL</td>\n", "      <td>132</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7419</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>DEL</td>\n", "      <td>122</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7420</th>\n", "      <td>chr9</td>\n", "      <td>138237207</td>\n", "      <td>NA</td>\n", "      <td>BND</td>\n", "      <td>0</td>\n", "      <td>16</td>\n", "      <td>50</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7421</th>\n", "      <td>chr9</td>\n", "      <td>138272910</td>\n", "      <td>138272910</td>\n", "      <td>INS</td>\n", "      <td>39</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7422</th>\n", "      <td>chr9</td>\n", "      <td>138277511</td>\n", "      <td>138277511</td>\n", "      <td>INS</td>\n", "      <td>3398</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7423</th>\n", "      <td>chr9</td>\n", "      <td>138279081</td>\n", "      <td>138279081</td>\n", "      <td>INS</td>\n", "      <td>1453</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7424 rows × 9 columns</p>\n", "</div>"], "text/plain": ["       chr      start       stop type  length support  coverage     t1     t2\n", "0     chr1      10693      10928  DEL     235       1         1  False  False\n", "1     chr1      64078         NA  BND       0      15        73  False  False\n", "2     chr1     140033     140033  INS    3397       1         1  False   True\n", "3     chr1     492291     492291  INS    1073       2         2   True   True\n", "4     chr1     610401     610533  DEL     132       1         1  False  False\n", "...    ...        ...        ...  ...     ...     ...       ...    ...    ...\n", "7419  chr9  *********  *********  DEL     122       2         2   True  False\n", "7420  chr9  138237207         NA  BND       0      16        50   True  False\n", "7421  chr9  138272910  138272910  INS      39       4         4   True   True\n", "7422  chr9  138277511  138277511  INS    3398       1         1   True   True\n", "7423  chr9  138279081  138279081  INS    1453       2         2   True   True\n", "\n", "[7424 rows x 9 columns]"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["summary_df"]}, {"cell_type": "code", "execution_count": 78, "id": "a19172f5-a963-4bc8-8c72-6d466262f58b", "metadata": {}, "outputs": [], "source": ["\n", "sniffles_file = \"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/sniffles/sniffles_v2.vcf\"\n", "\n", "gene_bed_file = \"/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/ensembl_hg38_27_10_2022.bed\"\n", "\n", "variant_summary_file = \"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/sniffles/combined_variants.bed\"\n", "\n", "figure_data_file = \"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/sniffles/combined_results.tsv\"\n", "\n"]}, {"cell_type": "code", "execution_count": 87, "id": "50621b7f-ef43-4ee4-a042-7abe5cd9c286", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sniffles_v2.vcf\n"]}], "source": ["patient_id = sniffles_file.split(\"/\")[-1].split(\"-\")[0]\n", "print(patient_id)\n", "patient_id = 'RLGS_0010'\n", "\n", "\n", "\n", "# load and classify variants\n", "sv_df = load_vcf_regions(sniffles_file, patient_id)\n", "\n", "# remove variants identified in the germline\n", "mask = np.invert(sv_df['gl'])\n", "sv_df = sv_df.loc[mask,:]\n", "\n", "\n", "# summarize variants by type\n", "summary_df = summarize_variants(sv_df)\n", "\n", "# save to file\n", "summary_df.to_csv(variant_summary_file, sep='\\t', index=False, header=False)\n", "\n"]}, {"cell_type": "code", "execution_count": 95, "id": "2fdb26a6-18ef-405f-9618-a68298987db8", "metadata": {}, "outputs": [], "source": ["\n", "\n", "\n", "\n", "# intersect with genes\n", "common_genes_df = intersect_bed(variant_summary_file, gene_bed_file)\n", "\n", "# save to file and reload as df\n", "common_genes_df.saveas(figure_data_file)\n", "\n", "common_genes_df = pd.read_csv(figure_data_file, sep='\\t', header=None)\n", "common_genes_df.columns = ['chr', 'start', 'stop', 'type', 'length', 'support', 'coverage', 't1', 't2', 'ens_chromosome', 'ens_start', 'ens_stop', 'ensemble_id', 'score', 'strand', 'source', 'gene_type', 'type_score', 'details']\n", "\n", "\n", "\n", "# specify gene names\n", "mask = np.invert(common_genes_df['details'] == \".\")\n", "common_genes_df.loc[mask,'gene'] = list(map(lambda x: x.split(\";\")[3].split(\"=\")[1], common_genes_df.loc[mask,'details']))\n", "\n", "# classify the mutation types and support\n", "common_genes_df.to_csv(figure_data_file, sep='\\t')\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "a352ceec-a9dc-4c3b-8e1e-4e5d82fc4014", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 100, "id": "c98761cc-61b8-4cbc-adfa-5a597c057298", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>chr</th>\n", "      <th>start</th>\n", "      <th>stop</th>\n", "      <th>type</th>\n", "      <th>length</th>\n", "      <th>support</th>\n", "      <th>coverage</th>\n", "      <th>t1</th>\n", "      <th>t2</th>\n", "      <th>ens_chromosome</th>\n", "      <th>ens_start</th>\n", "      <th>ens_stop</th>\n", "      <th>ensemble_id</th>\n", "      <th>score</th>\n", "      <th>strand</th>\n", "      <th>source</th>\n", "      <th>gene_type</th>\n", "      <th>type_score</th>\n", "      <th>details</th>\n", "      <th>gene</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>chr1</td>\n", "      <td>64078</td>\n", "      <td>64078</td>\n", "      <td>BND</td>\n", "      <td>0</td>\n", "      <td>15</td>\n", "      <td>73</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>chr1</td>\n", "      <td>57597</td>\n", "      <td>64116</td>\n", "      <td>ENSG00000240361.2</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000240361.2;gene_id=ENSG00000240361.2...</td>\n", "      <td>OR4G11P</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>chr1</td>\n", "      <td>140033</td>\n", "      <td>140033</td>\n", "      <td>INS</td>\n", "      <td>3397</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>chr1</td>\n", "      <td>139789</td>\n", "      <td>140339</td>\n", "      <td>ENSG00000239906.1</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000239906.1;gene_id=ENSG00000239906.1...</td>\n", "      <td>RP11-34P13.14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>chr1</td>\n", "      <td>492291</td>\n", "      <td>492291</td>\n", "      <td>INS</td>\n", "      <td>1073</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>chr1</td>\n", "      <td>491224</td>\n", "      <td>493241</td>\n", "      <td>ENSG00000250575.1</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000250575.1;gene_id=ENSG00000250575.1...</td>\n", "      <td>RP4-669L17.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>chr1</td>\n", "      <td>492291</td>\n", "      <td>492291</td>\n", "      <td>INS</td>\n", "      <td>1073</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>chr1</td>\n", "      <td>365388</td>\n", "      <td>522928</td>\n", "      <td>ENSG00000237094.12</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000237094.12;gene_id=ENSG00000237094....</td>\n", "      <td>RP4-669L17.4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>chr1</td>\n", "      <td>610401</td>\n", "      <td>610533</td>\n", "      <td>DEL</td>\n", "      <td>132</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>chr1</td>\n", "      <td>586070</td>\n", "      <td>827796</td>\n", "      <td>ENSG00000230021.10</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000230021.10;gene_id=ENSG00000230021....</td>\n", "      <td>RP11-206L10.17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81194</th>\n", "      <td>chr9</td>\n", "      <td>138019113</td>\n", "      <td>*********</td>\n", "      <td>DEL</td>\n", "      <td>142</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>ENSG00000148408.13</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000148408.13;gene_id=ENSG00000148408....</td>\n", "      <td>CACNA1B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81195</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>DEL</td>\n", "      <td>6044</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>ENSG00000148408.13</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000148408.13;gene_id=ENSG00000148408....</td>\n", "      <td>CACNA1B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81196</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>INS</td>\n", "      <td>151</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>ENSG00000233013.10</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000233013.10;gene_id=ENSG00000233013....</td>\n", "      <td>FAM157B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81197</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>DEL</td>\n", "      <td>122</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>ENSG00000233013.10</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000233013.10;gene_id=ENSG00000233013....</td>\n", "      <td>FAM157B</td>\n", "    </tr>\n", "    <tr>\n", "      <th>81198</th>\n", "      <td>chr9</td>\n", "      <td>138237207</td>\n", "      <td>138237207</td>\n", "      <td>BND</td>\n", "      <td>0</td>\n", "      <td>16</td>\n", "      <td>50</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>ENSG00000233013.10</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000233013.10;gene_id=ENSG00000233013....</td>\n", "      <td>FAM157B</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>81199 rows × 20 columns</p>\n", "</div>"], "text/plain": ["        chr      start       stop type  length  support  coverage     t1  \\\n", "0      chr1      64078      64078  BND       0       15        73  False   \n", "1      chr1     140033     140033  INS    3397        1         1  False   \n", "2      chr1     492291     492291  INS    1073        2         2   True   \n", "3      chr1     492291     492291  INS    1073        2         2   True   \n", "4      chr1     610401     610533  DEL     132        1         1  False   \n", "...     ...        ...        ...  ...     ...      ...       ...    ...   \n", "81194  chr9  138019113  *********  DEL     142        1         1  False   \n", "81195  chr9  *********  *********  DEL    6044        4         4  False   \n", "81196  chr9  *********  *********  INS     151        2         2  False   \n", "81197  chr9  *********  *********  DEL     122        2         2   True   \n", "81198  chr9  138237207  138237207  BND       0       16        50   True   \n", "\n", "          t2 ens_chromosome  ens_start   ens_stop         ensemble_id score  \\\n", "0      False           chr1      57597      64116   ENSG00000240361.2     .   \n", "1       True           chr1     139789     140339   ENSG00000239906.1     .   \n", "2       True           chr1     491224     493241   ENSG00000250575.1     .   \n", "3       True           chr1     365388     522928  ENSG00000237094.12     .   \n", "4      False           chr1     586070     827796  ENSG00000230021.10     .   \n", "...      ...            ...        ...        ...                 ...   ...   \n", "81194  False           chr9  *********  *********  ENSG00000148408.13     .   \n", "81195   True           chr9  *********  *********  ENSG00000148408.13     .   \n", "81196  False           chr9  *********  *********  ENSG00000233013.10     .   \n", "81197  False           chr9  *********  *********  ENSG00000233013.10     .   \n", "81198  False           chr9  *********  *********  ENSG00000233013.10     .   \n", "\n", "      strand  source gene_type type_score  \\\n", "0          +  HAVANA      gene          .   \n", "1          -  HAVANA      gene          .   \n", "2          -  HAVANA      gene          .   \n", "3          -  HAVANA      gene          .   \n", "4          -  HAVANA      gene          .   \n", "...      ...     ...       ...        ...   \n", "81194      +  HAVANA      gene          .   \n", "81195      +  HAVANA      gene          .   \n", "81196      +  HAVANA      gene          .   \n", "81197      +  HAVANA      gene          .   \n", "81198      +  HAVANA      gene          .   \n", "\n", "                                                 details            gene  \n", "0      ID=ENSG00000240361.2;gene_id=ENSG00000240361.2...         OR4G11P  \n", "1      ID=ENSG00000239906.1;gene_id=ENSG00000239906.1...   RP11-34P13.14  \n", "2      ID=ENSG00000250575.1;gene_id=ENSG00000250575.1...    RP4-669L17.8  \n", "3      ID=ENSG00000237094.12;gene_id=ENSG00000237094....    RP4-669L17.4  \n", "4      ID=ENSG00000230021.10;gene_id=ENSG00000230021....  RP11-206L10.17  \n", "...                                                  ...             ...  \n", "81194  ID=ENSG00000148408.13;gene_id=ENSG00000148408....         CACNA1B  \n", "81195  ID=ENSG00000148408.13;gene_id=ENSG00000148408....         CACNA1B  \n", "81196  ID=ENSG00000233013.10;gene_id=ENSG00000233013....         FAM157B  \n", "81197  ID=ENSG00000233013.10;gene_id=ENSG00000233013....         FAM157B  \n", "81198  ID=ENSG00000233013.10;gene_id=ENSG00000233013....         FAM157B  \n", "\n", "[81199 rows x 20 columns]"]}, "execution_count": 100, "metadata": {}, "output_type": "execute_result"}], "source": ["mask = np.invert(common_genes_df['gene'].isnull())\n", "common_genes_df = common_genes_df.loc[mask,:]\n", "\n", "common_genes_df"]}, {"cell_type": "code", "execution_count": 104, "id": "d8dec102-4dc6-4643-8d13-c2612955c372", "metadata": {}, "outputs": [], "source": ["mask = np.logical_and(common_genes_df['t1'], np.invert(common_genes_df['t2']))\n", "mask = np.logical_and(mask, common_genes_df['type'] != 'BND')\n", "common_genes_df.loc[mask,:].to_csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t1_only.tsv\", sep='\\t', index=False)\n", "\n", "mask = np.logical_and(common_genes_df['t2'], np.invert(common_genes_df['t1']))\n", "mask = np.logical_and(mask, common_genes_df['type'] != 'BND')\n", "common_genes_df.loc[mask,:].to_csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t2_only.tsv\", sep='\\t', index=False)\n", "\n", "mask = np.logical_and(common_genes_df['t1'], common_genes_df['t2'])\n", "mask = np.logical_and(mask, common_genes_df['type'] != 'BND')\n", "common_genes_df.loc[mask,:].to_csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t1_t2.tsv\", sep='\\t', index=False)\n"]}, {"cell_type": "code", "execution_count": null, "id": "deab3a1b-eecd-4a91-af72-b09f9a985adc", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5ae5f34f-b369-4d4c-8bb8-71fef7a1c20c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 111, "id": "5a1978db-876a-4895-bd92-cb3b34e88d0f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CHROM</th>\n", "      <th>POS</th>\n", "      <th>ID</th>\n", "      <th>REF</th>\n", "      <th>ALT</th>\n", "      <th>QUAL</th>\n", "      <th>FILTER</th>\n", "      <th>INFO</th>\n", "      <th>FORMAT</th>\n", "      <th>NORMAL</th>\n", "      <th>TUMOR</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>463</th>\n", "      <td>chr1</td>\n", "      <td>634814</td>\n", "      <td>.</td>\n", "      <td>G</td>\n", "      <td>A</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=670;TQSS=1;NT=ref;QSS_NT=3070;TQSS...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>2223:36:0:0:2,5:1,3:2179,2755:5,9</td>\n", "      <td>108:0:0:0:39,70:0,1:69,123:0,0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1032</th>\n", "      <td>chr1</td>\n", "      <td>963232</td>\n", "      <td>.</td>\n", "      <td>A</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=66;TQSS=1;NT=ref;QSS_NT=66;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>132:1:0:0:129,129:2,3:0,0:0,0</td>\n", "      <td>377:3:0:0:359,361:14,15:1,1:0,0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1041</th>\n", "      <td>chr1</td>\n", "      <td>965476</td>\n", "      <td>.</td>\n", "      <td>G</td>\n", "      <td>A</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=41;TQSS=2;NT=ref;QSS_NT=41;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>128:2:0:0:0,0:0,0:126,128:0,0</td>\n", "      <td>304:3:0:0:6,6:0,0:295,298:0,0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1127</th>\n", "      <td>chr1</td>\n", "      <td>1000262</td>\n", "      <td>.</td>\n", "      <td>A</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=60;TQSS=1;NT=ref;QSS_NT=60;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>141:1:0:0:136,137:4,4:0,0:0,0</td>\n", "      <td>305:1:0:0:283,283:20,21:1,1:0,0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1147</th>\n", "      <td>chr1</td>\n", "      <td>1014249</td>\n", "      <td>.</td>\n", "      <td>A</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=34;TQSS=2;NT=ref;QSS_NT=34;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>103:0:0:0:103,103:0,0:0,0:0,0</td>\n", "      <td>272:2:0:0:259,261:10,10:0,0:1,1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2436515</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>.</td>\n", "      <td>T</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=30;TQSS=2;NT=ref;QSS_NT=30;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>93:0:0:0:0,0:0,0:0,0:93,93</td>\n", "      <td>206:3:0:0:2,2:4,4:1,1:196,200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2436711</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>.</td>\n", "      <td>T</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=56;TQSS=1;NT=ref;QSS_NT=56;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>142:0:0:0:1,1:0,0:0,0:141,141</td>\n", "      <td>336:3:0:0:0,0:6,6:2,3:325,328</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2437127</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>.</td>\n", "      <td>C</td>\n", "      <td>A</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=68;TQSS=1;NT=ref;QSS_NT=68;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>66:0:0:0:0,0:66,66:0,0:0,0</td>\n", "      <td>163:0:0:0:7,7:156,156:0,0:0,0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2437205</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>.</td>\n", "      <td>C</td>\n", "      <td>T</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=37;TQSS=2;NT=ref;QSS_NT=37;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>112:2:0:0:0,0:110,113:0,0:0,0</td>\n", "      <td>242:2:0:0:1,1:235,241:0,0:4,4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2437435</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>.</td>\n", "      <td>C</td>\n", "      <td>A</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=69;TQSS=1;NT=ref;QSS_NT=69;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>83:1:0:0:0,0:82,83:0,0:0,0</td>\n", "      <td>187:2:0:0:7,7:178,180:0,0:0,0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>39727 rows × 11 columns</p>\n", "</div>"], "text/plain": ["        CHROM        POS ID REF ALT QUAL FILTER  \\\n", "463      chr1     634814  .   G   A    .   <PERSON>   \n", "1032     chr1     963232  .   A   C    .   PASS   \n", "1041     chr1     965476  .   G   A    .   <PERSON>   \n", "1127     chr1    1000262  .   A   C    .   PASS   \n", "1147     chr1    1014249  .   A   C    .   PASS   \n", "...       ...        ... ..  ..  ..  ...    ...   \n", "2436515  chr9  *********  .   T   C    .   PASS   \n", "2436711  chr9  *********  .   T   C    .   PASS   \n", "2437127  chr9  *********  .   C   A    .   <PERSON>   \n", "2437205  chr9  *********  .   C   T    .   PASS   \n", "2437435  chr9  *********  .   C   A    .   <PERSON>   \n", "\n", "                                                      INFO  \\\n", "463      SOMATIC;QSS=670;TQSS=1;NT=ref;QSS_NT=3070;TQSS...   \n", "1032     SOMATIC;QSS=66;TQSS=1;NT=ref;QSS_NT=66;TQSS_NT...   \n", "1041     SOMATIC;QSS=41;TQSS=2;NT=ref;QSS_NT=41;TQSS_NT...   \n", "1127     SOMATIC;QSS=60;TQSS=1;NT=ref;QSS_NT=60;TQSS_NT...   \n", "1147     SOMATIC;QSS=34;TQSS=2;NT=ref;QSS_NT=34;TQSS_NT...   \n", "...                                                    ...   \n", "2436515  SOMATIC;QSS=30;TQSS=2;NT=ref;QSS_NT=30;TQSS_NT...   \n", "2436711  SOMATIC;QSS=56;TQSS=1;NT=ref;QSS_NT=56;TQSS_NT...   \n", "2437127  SOMATIC;QSS=68;TQSS=1;NT=ref;QSS_NT=68;TQSS_NT...   \n", "2437205  SOMATIC;QSS=37;TQSS=2;NT=ref;QSS_NT=37;TQSS_NT...   \n", "2437435  SOMATIC;QSS=69;TQSS=1;NT=ref;QSS_NT=69;TQSS_NT...   \n", "\n", "                               FORMAT                             NORMAL  \\\n", "463      DP:FDP:SDP:SUBDP:AU:CU:GU:TU  2223:36:0:0:2,5:1,3:2179,2755:5,9   \n", "1032     DP:FDP:SDP:SUBDP:AU:CU:GU:TU      132:1:0:0:129,129:2,3:0,0:0,0   \n", "1041     DP:FDP:SDP:SUBDP:AU:CU:GU:TU      128:2:0:0:0,0:0,0:126,128:0,0   \n", "1127     DP:FDP:SDP:SUBDP:AU:CU:GU:TU      141:1:0:0:136,137:4,4:0,0:0,0   \n", "1147     DP:FDP:SDP:SUBDP:AU:CU:GU:TU      103:0:0:0:103,103:0,0:0,0:0,0   \n", "...                               ...                                ...   \n", "2436515  DP:FDP:SDP:SUBDP:AU:CU:GU:TU         93:0:0:0:0,0:0,0:0,0:93,93   \n", "2436711  DP:FDP:SDP:SUBDP:AU:CU:GU:TU      142:0:0:0:1,1:0,0:0,0:141,141   \n", "2437127  DP:FDP:SDP:SUBDP:AU:CU:GU:TU         66:0:0:0:0,0:66,66:0,0:0,0   \n", "2437205  DP:FDP:SDP:SUBDP:AU:CU:GU:TU      112:2:0:0:0,0:110,113:0,0:0,0   \n", "2437435  DP:FDP:SDP:SUBDP:AU:CU:GU:TU         83:1:0:0:0,0:82,83:0,0:0,0   \n", "\n", "                                   TUMOR  \n", "463       108:0:0:0:39,70:0,1:69,123:0,0  \n", "1032     377:3:0:0:359,361:14,15:1,1:0,0  \n", "1041       304:3:0:0:6,6:0,0:295,298:0,0  \n", "1127     305:1:0:0:283,283:20,21:1,1:0,0  \n", "1147     272:2:0:0:259,261:10,10:0,0:1,1  \n", "...                                  ...  \n", "2436515    206:3:0:0:2,2:4,4:1,1:196,200  \n", "2436711    336:3:0:0:0,0:6,6:2,3:325,328  \n", "2437127    163:0:0:0:7,7:156,156:0,0:0,0  \n", "2437205    242:2:0:0:1,1:235,241:0,0:4,4  \n", "2437435    187:2:0:0:7,7:178,180:0,0:0,0  \n", "\n", "[39727 rows x 11 columns]"]}, "execution_count": 111, "metadata": {}, "output_type": "execute_result"}], "source": ["df = read_vcf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/wgs/RLGS10-recurrent_wgs_seq/somatic.snvs.vcf\")\n", "df = df.loc[df['FILTER'] == 'PASS',:]\n", "df"]}, {"cell_type": "code", "execution_count": 117, "id": "9bd15e2a-17d7-45a4-983a-2ed67d65c0d5", "metadata": {}, "outputs": [], "source": ["variant_file = \"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/wgs/RLGS10-recurrent_wgs_seq/pass.bed\"\n", "new_variant_file = \"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/wgs/RLGS10-recurrent_wgs_seq/pass_intersect.bed\"\n", "\n", "df.iloc[:,2] = df['POS']\n", "df.to_csv(variant_file, sep='\\t', index=False, header=False)\n"]}, {"cell_type": "code", "execution_count": 121, "id": "7132289a-bd46-42c0-9df1-604a1920539b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>chr</th>\n", "      <th>start</th>\n", "      <th>stop</th>\n", "      <th>ref</th>\n", "      <th>alt</th>\n", "      <th>qual</th>\n", "      <th>filter</th>\n", "      <th>info</th>\n", "      <th>format</th>\n", "      <th>normal</th>\n", "      <th>...</th>\n", "      <th>ens_start</th>\n", "      <th>ens_stop</th>\n", "      <th>ensemble_id</th>\n", "      <th>score</th>\n", "      <th>strand</th>\n", "      <th>source</th>\n", "      <th>gene_type</th>\n", "      <th>type_score</th>\n", "      <th>details</th>\n", "      <th>gene</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>chr1</td>\n", "      <td>634814</td>\n", "      <td>634814</td>\n", "      <td>G</td>\n", "      <td>A</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=670;TQSS=1;NT=ref;QSS_NT=3070;TQSS...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>2223:36:0:0:2,5:1,3:2179,2755:5,9</td>\n", "      <td>...</td>\n", "      <td>634375</td>\n", "      <td>634922</td>\n", "      <td>ENSG00000198744.5</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000198744.5;gene_id=ENSG00000198744.5...</td>\n", "      <td>MTCO3P12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>chr1</td>\n", "      <td>634814</td>\n", "      <td>634814</td>\n", "      <td>G</td>\n", "      <td>A</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=670;TQSS=1;NT=ref;QSS_NT=3070;TQSS...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>2223:36:0:0:2,5:1,3:2179,2755:5,9</td>\n", "      <td>...</td>\n", "      <td>586070</td>\n", "      <td>827796</td>\n", "      <td>ENSG00000230021.10</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000230021.10;gene_id=ENSG00000230021....</td>\n", "      <td>RP11-206L10.17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>chr1</td>\n", "      <td>963232</td>\n", "      <td>963232</td>\n", "      <td>A</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=66;TQSS=1;NT=ref;QSS_NT=66;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>132:1:0:0:129,129:2,3:0,0:0,0</td>\n", "      <td>...</td>\n", "      <td>960583</td>\n", "      <td>965719</td>\n", "      <td>ENSG00000187961.14</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000187961.14;gene_id=ENSG00000187961....</td>\n", "      <td>KLHL17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>chr1</td>\n", "      <td>965476</td>\n", "      <td>965476</td>\n", "      <td>G</td>\n", "      <td>A</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=41;TQSS=2;NT=ref;QSS_NT=41;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>128:2:0:0:0,0:0,0:126,128:0,0</td>\n", "      <td>...</td>\n", "      <td>960583</td>\n", "      <td>965719</td>\n", "      <td>ENSG00000187961.14</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000187961.14;gene_id=ENSG00000187961....</td>\n", "      <td>KLHL17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>chr1</td>\n", "      <td>1014249</td>\n", "      <td>1014249</td>\n", "      <td>A</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=34;TQSS=2;NT=ref;QSS_NT=34;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>103:0:0:0:103,103:0,0:0,0:0,0</td>\n", "      <td>...</td>\n", "      <td>1001137</td>\n", "      <td>1014540</td>\n", "      <td>ENSG00000187608.10</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000187608.10;gene_id=ENSG00000187608....</td>\n", "      <td>ISG15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26921</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>T</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=30;TQSS=2;NT=ref;QSS_NT=30;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>93:0:0:0:0,0:0,0:0,0:93,93</td>\n", "      <td>...</td>\n", "      <td>137306895</td>\n", "      <td>137423211</td>\n", "      <td>ENSG00000187609.16</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000187609.16;gene_id=ENSG00000187609....</td>\n", "      <td>EXD3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26922</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>T</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=56;TQSS=1;NT=ref;QSS_NT=56;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>142:0:0:0:1,1:0,0:0,0:141,141</td>\n", "      <td>...</td>\n", "      <td>137459951</td>\n", "      <td>137550402</td>\n", "      <td>ENSG00000130653.16</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000130653.16;gene_id=ENSG00000130653....</td>\n", "      <td>PNPLA7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26923</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>C</td>\n", "      <td>A</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=68;TQSS=1;NT=ref;QSS_NT=68;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>66:0:0:0:0,0:66,66:0,0:0,0</td>\n", "      <td>...</td>\n", "      <td>137618991</td>\n", "      <td>137870016</td>\n", "      <td>ENSG00000181090.21</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000181090.21;gene_id=ENSG00000181090....</td>\n", "      <td>EHMT1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26924</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>C</td>\n", "      <td>T</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=37;TQSS=2;NT=ref;QSS_NT=37;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>112:2:0:0:0,0:110,113:0,0:0,0</td>\n", "      <td>...</td>\n", "      <td>137618991</td>\n", "      <td>137870016</td>\n", "      <td>ENSG00000181090.21</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000181090.21;gene_id=ENSG00000181090....</td>\n", "      <td>EHMT1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26925</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>C</td>\n", "      <td>A</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=69;TQSS=1;NT=ref;QSS_NT=69;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>83:1:0:0:0,0:82,83:0,0:0,0</td>\n", "      <td>...</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>ENSG00000148408.13</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000148408.13;gene_id=ENSG00000148408....</td>\n", "      <td>CACNA1B</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>26926 rows × 22 columns</p>\n", "</div>"], "text/plain": ["        chr      start       stop ref alt qual filter  \\\n", "0      chr1     634814     634814   G   A    .   PASS   \n", "1      chr1     634814     634814   G   A    .   <PERSON>   \n", "2      chr1     963232     963232   A   C    .   PASS   \n", "3      chr1     965476     965476   G   A    .   <PERSON>   \n", "4      chr1    1014249    1014249   A   C    .   PASS   \n", "...     ...        ...        ...  ..  ..  ...    ...   \n", "26921  chr9  *********  *********   T   C    .   PASS   \n", "26922  chr9  *********  *********   T   C    .   PASS   \n", "26923  chr9  *********  *********   C   A    .   PASS   \n", "26924  chr9  *********  *********   C   T    .   PASS   \n", "26925  chr9  *********  *********   C   A    .   PASS   \n", "\n", "                                                    info  \\\n", "0      SOMATIC;QSS=670;TQSS=1;NT=ref;QSS_NT=3070;TQSS...   \n", "1      SOMATIC;QSS=670;TQSS=1;NT=ref;QSS_NT=3070;TQSS...   \n", "2      SOMATIC;QSS=66;TQSS=1;NT=ref;QSS_NT=66;TQSS_NT...   \n", "3      SOMATIC;QSS=41;TQSS=2;NT=ref;QSS_NT=41;TQSS_NT...   \n", "4      SOMATIC;QSS=34;TQSS=2;NT=ref;QSS_NT=34;TQSS_NT...   \n", "...                                                  ...   \n", "26921  SOMATIC;QSS=30;TQSS=2;NT=ref;QSS_NT=30;TQSS_NT...   \n", "26922  SOMATIC;QSS=56;TQSS=1;NT=ref;QSS_NT=56;TQSS_NT...   \n", "26923  SOMATIC;QSS=68;TQSS=1;NT=ref;QSS_NT=68;TQSS_NT...   \n", "26924  SOMATIC;QSS=37;TQSS=2;NT=ref;QSS_NT=37;TQSS_NT...   \n", "26925  SOMATIC;QSS=69;TQSS=1;NT=ref;QSS_NT=69;TQSS_NT...   \n", "\n", "                             format                             normal  ...  \\\n", "0      DP:FDP:SDP:SUBDP:AU:CU:GU:TU  2223:36:0:0:2,5:1,3:2179,2755:5,9  ...   \n", "1      DP:FDP:SDP:SUBDP:AU:CU:GU:TU  2223:36:0:0:2,5:1,3:2179,2755:5,9  ...   \n", "2      DP:FDP:SDP:SUBDP:AU:CU:GU:TU      132:1:0:0:129,129:2,3:0,0:0,0  ...   \n", "3      DP:FDP:SDP:SUBDP:AU:CU:GU:TU      128:2:0:0:0,0:0,0:126,128:0,0  ...   \n", "4      DP:FDP:SDP:SUBDP:AU:CU:GU:TU      103:0:0:0:103,103:0,0:0,0:0,0  ...   \n", "...                             ...                                ...  ...   \n", "26921  DP:FDP:SDP:SUBDP:AU:CU:GU:TU         93:0:0:0:0,0:0,0:0,0:93,93  ...   \n", "26922  DP:FDP:SDP:SUBDP:AU:CU:GU:TU      142:0:0:0:1,1:0,0:0,0:141,141  ...   \n", "26923  DP:FDP:SDP:SUBDP:AU:CU:GU:TU         66:0:0:0:0,0:66,66:0,0:0,0  ...   \n", "26924  DP:FDP:SDP:SUBDP:AU:CU:GU:TU      112:2:0:0:0,0:110,113:0,0:0,0  ...   \n", "26925  DP:FDP:SDP:SUBDP:AU:CU:GU:TU         83:1:0:0:0,0:82,83:0,0:0,0  ...   \n", "\n", "       ens_start   ens_stop         ensemble_id  score strand  source  \\\n", "0         634375     634922   ENSG00000198744.5      .      +  HAVANA   \n", "1         586070     827796  ENSG00000230021.10      .      -  HAVANA   \n", "2         960583     965719  ENSG00000187961.14      .      +  HAVANA   \n", "3         960583     965719  ENSG00000187961.14      .      +  HAVANA   \n", "4        1001137    1014540  ENSG00000187608.10      .      +  HAVANA   \n", "...          ...        ...                 ...    ...    ...     ...   \n", "26921  137306895  137423211  ENSG00000187609.16      .      -  HAVANA   \n", "26922  137459951  137550402  ENSG00000130653.16      .      -  HAVANA   \n", "26923  137618991  137870016  ENSG00000181090.21      .      +  HAVANA   \n", "26924  137618991  137870016  ENSG00000181090.21      .      +  HAVANA   \n", "26925  *********  *********  ENSG00000148408.13      .      +  HAVANA   \n", "\n", "      gene_type type_score                                            details  \\\n", "0          gene          .  ID=ENSG00000198744.5;gene_id=ENSG00000198744.5...   \n", "1          gene          .  ID=ENSG00000230021.10;gene_id=ENSG00000230021....   \n", "2          gene          .  ID=ENSG00000187961.14;gene_id=ENSG00000187961....   \n", "3          gene          .  ID=ENSG00000187961.14;gene_id=ENSG00000187961....   \n", "4          gene          .  ID=ENSG00000187608.10;gene_id=ENSG00000187608....   \n", "...         ...        ...                                                ...   \n", "26921      gene          .  ID=ENSG00000187609.16;gene_id=ENSG00000187609....   \n", "26922      gene          .  ID=ENSG00000130653.16;gene_id=ENSG00000130653....   \n", "26923      gene          .  ID=ENSG00000181090.21;gene_id=ENSG00000181090....   \n", "26924      gene          .  ID=ENSG00000181090.21;gene_id=ENSG00000181090....   \n", "26925      gene          .  ID=ENSG00000148408.13;gene_id=ENSG00000148408....   \n", "\n", "                 gene  \n", "0            MTCO3P12  \n", "1      RP11-206L10.17  \n", "2              KLHL17  \n", "3              KLHL17  \n", "4               ISG15  \n", "...               ...  \n", "26921            EXD3  \n", "26922          PNPLA7  \n", "26923           EHMT1  \n", "26924           EHMT1  \n", "26925         CACNA1B  \n", "\n", "[26926 rows x 22 columns]"]}, "execution_count": 121, "metadata": {}, "output_type": "execute_result"}], "source": ["# intersect with genes\n", "common_genes_df = intersect_bed(variant_file, gene_bed_file)\n", "\n", "# save to file and reload as df\n", "common_genes_df.saveas(new_variant_file)\n", "\n", "common_genes_df = pd.read_csv(new_variant_file, sep='\\t', header=None)\n", "common_genes_df.columns = ['chr', 'start', 'stop', 'ref', 'alt', 'qual', 'filter', 'info', 'format', 'normal', 'tumor', 'ens_chromosome', 'ens_start', 'ens_stop', 'ensemble_id', 'score', 'strand', 'source', 'gene_type', 'type_score', 'details']\n", "\n", "# specify gene names\n", "mask = np.invert(common_genes_df['details'] == \".\")\n", "common_genes_df.loc[mask,'gene'] = list(map(lambda x: x.split(\";\")[3].split(\"=\")[1], common_genes_df.loc[mask,'details']))\n", "\n", "common_genes_df"]}, {"cell_type": "code", "execution_count": 124, "id": "0951cc5b-0dff-41f3-a5c3-1b124de588ff", "metadata": {}, "outputs": [], "source": ["normal = np.array(list(map(lambda x: int(x.split(\":\")[0]), common_genes_df['normal'])))\n", "tumor = np.array(list(map(lambda x: int(x.split(\":\")[0]), common_genes_df['tumor'])))\n", "\n", "common_genes_df.loc[:,'count'] = tumor / normal\n", "common_genes_df.to_csv(new_variant_file, sep='\\t', index=False)"]}, {"cell_type": "code", "execution_count": 125, "id": "75996545-c04b-454d-92ce-6e7c6ca83db9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>chr</th>\n", "      <th>start</th>\n", "      <th>stop</th>\n", "      <th>ref</th>\n", "      <th>alt</th>\n", "      <th>qual</th>\n", "      <th>filter</th>\n", "      <th>info</th>\n", "      <th>format</th>\n", "      <th>normal</th>\n", "      <th>...</th>\n", "      <th>ens_stop</th>\n", "      <th>ensemble_id</th>\n", "      <th>score</th>\n", "      <th>strand</th>\n", "      <th>source</th>\n", "      <th>gene_type</th>\n", "      <th>type_score</th>\n", "      <th>details</th>\n", "      <th>gene</th>\n", "      <th>count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>chr1</td>\n", "      <td>634814</td>\n", "      <td>634814</td>\n", "      <td>G</td>\n", "      <td>A</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=670;TQSS=1;NT=ref;QSS_NT=3070;TQSS...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>2223:36:0:0:2,5:1,3:2179,2755:5,9</td>\n", "      <td>...</td>\n", "      <td>634922</td>\n", "      <td>ENSG00000198744.5</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000198744.5;gene_id=ENSG00000198744.5...</td>\n", "      <td>MTCO3P12</td>\n", "      <td>0.048583</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>chr1</td>\n", "      <td>634814</td>\n", "      <td>634814</td>\n", "      <td>G</td>\n", "      <td>A</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=670;TQSS=1;NT=ref;QSS_NT=3070;TQSS...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>2223:36:0:0:2,5:1,3:2179,2755:5,9</td>\n", "      <td>...</td>\n", "      <td>827796</td>\n", "      <td>ENSG00000230021.10</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000230021.10;gene_id=ENSG00000230021....</td>\n", "      <td>RP11-206L10.17</td>\n", "      <td>0.048583</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>chr1</td>\n", "      <td>963232</td>\n", "      <td>963232</td>\n", "      <td>A</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=66;TQSS=1;NT=ref;QSS_NT=66;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>132:1:0:0:129,129:2,3:0,0:0,0</td>\n", "      <td>...</td>\n", "      <td>965719</td>\n", "      <td>ENSG00000187961.14</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000187961.14;gene_id=ENSG00000187961....</td>\n", "      <td>KLHL17</td>\n", "      <td>2.856061</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>chr1</td>\n", "      <td>965476</td>\n", "      <td>965476</td>\n", "      <td>G</td>\n", "      <td>A</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=41;TQSS=2;NT=ref;QSS_NT=41;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>128:2:0:0:0,0:0,0:126,128:0,0</td>\n", "      <td>...</td>\n", "      <td>965719</td>\n", "      <td>ENSG00000187961.14</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000187961.14;gene_id=ENSG00000187961....</td>\n", "      <td>KLHL17</td>\n", "      <td>2.375000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>chr1</td>\n", "      <td>1014249</td>\n", "      <td>1014249</td>\n", "      <td>A</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=34;TQSS=2;NT=ref;QSS_NT=34;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>103:0:0:0:103,103:0,0:0,0:0,0</td>\n", "      <td>...</td>\n", "      <td>1014540</td>\n", "      <td>ENSG00000187608.10</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000187608.10;gene_id=ENSG00000187608....</td>\n", "      <td>ISG15</td>\n", "      <td>2.640777</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26921</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>T</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=30;TQSS=2;NT=ref;QSS_NT=30;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>93:0:0:0:0,0:0,0:0,0:93,93</td>\n", "      <td>...</td>\n", "      <td>137423211</td>\n", "      <td>ENSG00000187609.16</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000187609.16;gene_id=ENSG00000187609....</td>\n", "      <td>EXD3</td>\n", "      <td>2.215054</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26922</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>T</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=56;TQSS=1;NT=ref;QSS_NT=56;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>142:0:0:0:1,1:0,0:0,0:141,141</td>\n", "      <td>...</td>\n", "      <td>137550402</td>\n", "      <td>ENSG00000130653.16</td>\n", "      <td>.</td>\n", "      <td>-</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000130653.16;gene_id=ENSG00000130653....</td>\n", "      <td>PNPLA7</td>\n", "      <td>2.366197</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26923</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>C</td>\n", "      <td>A</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=68;TQSS=1;NT=ref;QSS_NT=68;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>66:0:0:0:0,0:66,66:0,0:0,0</td>\n", "      <td>...</td>\n", "      <td>137870016</td>\n", "      <td>ENSG00000181090.21</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000181090.21;gene_id=ENSG00000181090....</td>\n", "      <td>EHMT1</td>\n", "      <td>2.469697</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26924</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>C</td>\n", "      <td>T</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=37;TQSS=2;NT=ref;QSS_NT=37;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>112:2:0:0:0,0:110,113:0,0:0,0</td>\n", "      <td>...</td>\n", "      <td>137870016</td>\n", "      <td>ENSG00000181090.21</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000181090.21;gene_id=ENSG00000181090....</td>\n", "      <td>EHMT1</td>\n", "      <td>2.160714</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26925</th>\n", "      <td>chr9</td>\n", "      <td>*********</td>\n", "      <td>*********</td>\n", "      <td>C</td>\n", "      <td>A</td>\n", "      <td>.</td>\n", "      <td>PASS</td>\n", "      <td>SOMATIC;QSS=69;TQSS=1;NT=ref;QSS_NT=69;TQSS_NT...</td>\n", "      <td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td>\n", "      <td>83:1:0:0:0,0:82,83:0,0:0,0</td>\n", "      <td>...</td>\n", "      <td>*********</td>\n", "      <td>ENSG00000148408.13</td>\n", "      <td>.</td>\n", "      <td>+</td>\n", "      <td>HAVANA</td>\n", "      <td>gene</td>\n", "      <td>.</td>\n", "      <td>ID=ENSG00000148408.13;gene_id=ENSG00000148408....</td>\n", "      <td>CACNA1B</td>\n", "      <td>2.253012</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>26926 rows × 23 columns</p>\n", "</div>"], "text/plain": ["        chr      start       stop ref alt qual filter  \\\n", "0      chr1     634814     634814   G   A    .   PASS   \n", "1      chr1     634814     634814   G   A    .   <PERSON>   \n", "2      chr1     963232     963232   A   C    .   PASS   \n", "3      chr1     965476     965476   G   A    .   <PERSON>   \n", "4      chr1    1014249    1014249   A   C    .   PASS   \n", "...     ...        ...        ...  ..  ..  ...    ...   \n", "26921  chr9  *********  *********   T   C    .   PASS   \n", "26922  chr9  *********  *********   T   C    .   PASS   \n", "26923  chr9  *********  *********   C   A    .   PASS   \n", "26924  chr9  *********  *********   C   T    .   PASS   \n", "26925  chr9  *********  *********   C   A    .   PASS   \n", "\n", "                                                    info  \\\n", "0      SOMATIC;QSS=670;TQSS=1;NT=ref;QSS_NT=3070;TQSS...   \n", "1      SOMATIC;QSS=670;TQSS=1;NT=ref;QSS_NT=3070;TQSS...   \n", "2      SOMATIC;QSS=66;TQSS=1;NT=ref;QSS_NT=66;TQSS_NT...   \n", "3      SOMATIC;QSS=41;TQSS=2;NT=ref;QSS_NT=41;TQSS_NT...   \n", "4      SOMATIC;QSS=34;TQSS=2;NT=ref;QSS_NT=34;TQSS_NT...   \n", "...                                                  ...   \n", "26921  SOMATIC;QSS=30;TQSS=2;NT=ref;QSS_NT=30;TQSS_NT...   \n", "26922  SOMATIC;QSS=56;TQSS=1;NT=ref;QSS_NT=56;TQSS_NT...   \n", "26923  SOMATIC;QSS=68;TQSS=1;NT=ref;QSS_NT=68;TQSS_NT...   \n", "26924  SOMATIC;QSS=37;TQSS=2;NT=ref;QSS_NT=37;TQSS_NT...   \n", "26925  SOMATIC;QSS=69;TQSS=1;NT=ref;QSS_NT=69;TQSS_NT...   \n", "\n", "                             format                             normal  ...  \\\n", "0      DP:FDP:SDP:SUBDP:AU:CU:GU:TU  2223:36:0:0:2,5:1,3:2179,2755:5,9  ...   \n", "1      DP:FDP:SDP:SUBDP:AU:CU:GU:TU  2223:36:0:0:2,5:1,3:2179,2755:5,9  ...   \n", "2      DP:FDP:SDP:SUBDP:AU:CU:GU:TU      132:1:0:0:129,129:2,3:0,0:0,0  ...   \n", "3      DP:FDP:SDP:SUBDP:AU:CU:GU:TU      128:2:0:0:0,0:0,0:126,128:0,0  ...   \n", "4      DP:FDP:SDP:SUBDP:AU:CU:GU:TU      103:0:0:0:103,103:0,0:0,0:0,0  ...   \n", "...                             ...                                ...  ...   \n", "26921  DP:FDP:SDP:SUBDP:AU:CU:GU:TU         93:0:0:0:0,0:0,0:0,0:93,93  ...   \n", "26922  DP:FDP:SDP:SUBDP:AU:CU:GU:TU      142:0:0:0:1,1:0,0:0,0:141,141  ...   \n", "26923  DP:FDP:SDP:SUBDP:AU:CU:GU:TU         66:0:0:0:0,0:66,66:0,0:0,0  ...   \n", "26924  DP:FDP:SDP:SUBDP:AU:CU:GU:TU      112:2:0:0:0,0:110,113:0,0:0,0  ...   \n", "26925  DP:FDP:SDP:SUBDP:AU:CU:GU:TU         83:1:0:0:0,0:82,83:0,0:0,0  ...   \n", "\n", "        ens_stop         ensemble_id  score  strand  source gene_type  \\\n", "0         634922   ENSG00000198744.5      .       +  HAVANA      gene   \n", "1         827796  ENSG00000230021.10      .       -  HAVANA      gene   \n", "2         965719  ENSG00000187961.14      .       +  HAVANA      gene   \n", "3         965719  ENSG00000187961.14      .       +  HAVANA      gene   \n", "4        1014540  ENSG00000187608.10      .       +  HAVANA      gene   \n", "...          ...                 ...    ...     ...     ...       ...   \n", "26921  137423211  ENSG00000187609.16      .       -  HAVANA      gene   \n", "26922  137550402  ENSG00000130653.16      .       -  HAVANA      gene   \n", "26923  137870016  ENSG00000181090.21      .       +  HAVANA      gene   \n", "26924  137870016  ENSG00000181090.21      .       +  HAVANA      gene   \n", "26925  *********  ENSG00000148408.13      .       +  HAVANA      gene   \n", "\n", "      type_score                                            details  \\\n", "0              .  ID=ENSG00000198744.5;gene_id=ENSG00000198744.5...   \n", "1              .  ID=ENSG00000230021.10;gene_id=ENSG00000230021....   \n", "2              .  ID=ENSG00000187961.14;gene_id=ENSG00000187961....   \n", "3              .  ID=ENSG00000187961.14;gene_id=ENSG00000187961....   \n", "4              .  ID=ENSG00000187608.10;gene_id=ENSG00000187608....   \n", "...          ...                                                ...   \n", "26921          .  ID=ENSG00000187609.16;gene_id=ENSG00000187609....   \n", "26922          .  ID=ENSG00000130653.16;gene_id=ENSG00000130653....   \n", "26923          .  ID=ENSG00000181090.21;gene_id=ENSG00000181090....   \n", "26924          .  ID=ENSG00000181090.21;gene_id=ENSG00000181090....   \n", "26925          .  ID=ENSG00000148408.13;gene_id=ENSG00000148408....   \n", "\n", "                 gene     count  \n", "0            MTCO3P12  0.048583  \n", "1      RP11-206L10.17  0.048583  \n", "2              KLHL17  2.856061  \n", "3              KLHL17  2.375000  \n", "4               ISG15  2.640777  \n", "...               ...       ...  \n", "26921            EXD3  2.215054  \n", "26922          PNPLA7  2.366197  \n", "26923           EHMT1  2.469697  \n", "26924           EHMT1  2.160714  \n", "26925         CACNA1B  2.253012  \n", "\n", "[26926 rows x 23 columns]"]}, "execution_count": 125, "metadata": {}, "output_type": "execute_result"}], "source": ["common_genes_df"]}, {"cell_type": "code", "execution_count": null, "id": "78844f23-659b-4fc5-b75a-053554923dba", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 5}