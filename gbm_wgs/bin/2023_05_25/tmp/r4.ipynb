{"cells": [{"cell_type": "code", "execution_count": 2, "id": "50ef7f26-53d2-49ec-89b6-55582702eb13", "metadata": {}, "outputs": [], "source": ["library(ggplot2)\n", "library(ggrepel)\n", "library(ggrastr)\n", "library(gplots)\n", "library(ggupset)\n", "\n", "\n", "library(dplyr)\n", "library(forcats)\n", "library(data.table)\n", "\n", "library(tidytext)\n", "\n", "library(grid)\n", "library(gridExtra)\n", "library(patchwork)\n", "\n", "\n", "library(survival)\n", "library(survminer)\n", "\n", "\n", "plot_theme = function(...) {\n", "     \n", "    theme_bw() +\n", "     \n", "    theme(    \n", "     \n", "        plot.title = element_text(size = 22),\n", "         \n", "        plot.caption = element_text(size = 12),\n", "         \n", "        plot.subtitle = element_text(size = 16),\n", "         \n", "        axis.title = element_text(size = 18),\n", "         \n", "        axis.text.x = element_text(size = 12,\n", "                angle = 90, hjust = 1, vjust=0.5, color = \"black\"),\n", "        axis.text.y = element_text(size = 12, color = \"black\"),\n", "        legend.title = element_text(size = 16),\n", "        legend.text = element_text(size = 14),\n", "        ...\n", "    )\n", "}\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "d879d609-8c2e-4a7a-a12e-767b9914876a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 32, "id": "8fb9d116-2bad-4249-9b66-d75adc8a4995", "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A data.frame: 6 × 22</caption>\n", "<thead>\n", "\t<tr><th></th><th scope=col>chr</th><th scope=col>start</th><th scope=col>stop</th><th scope=col>ref</th><th scope=col>alt</th><th scope=col>qual</th><th scope=col>filter</th><th scope=col>info</th><th scope=col>format</th><th scope=col>normal</th><th scope=col>...</th><th scope=col>ens_start</th><th scope=col>ens_stop</th><th scope=col>ensemble_id</th><th scope=col>score</th><th scope=col>strand</th><th scope=col>source</th><th scope=col>gene_type</th><th scope=col>type_score</th><th scope=col>details</th><th scope=col>gene</th></tr>\n", "\t<tr><th></th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>...</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><th scope=row>1</th><td>chr1</td><td> 634814</td><td> 634814</td><td>G</td><td>A</td><td>.</td><td>PASS</td><td>SOMATIC;QSS=670;TQSS=1;NT=ref;QSS_NT=3070;TQSS_NT=1;SGT=GG-&gt;AG;DP=2966;MQ=59.12;MQ0=0;ReadPosRankSum=-1.76;SNVSB=0.00;SomaticEVS=15.74</td><td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td><td>2223:36:0:0:2,5:1,3:2179,2755:5,9</td><td>...</td><td> 634375</td><td> 634922</td><td>ENSG00000198744.5 </td><td>.</td><td>+</td><td>HAVANA</td><td>gene</td><td>.</td><td><span style=white-space:pre-wrap>ID=ENSG00000198744.5;gene_id=ENSG00000198744.5;gene_type=unprocessed_pseudogene;gene_name=MTCO3P12;level=2;hgnc_id=HGNC:52042;havana_gene=OTTHUMG00000002337.2          </span></td><td><span style=white-space:pre-wrap>MTCO3P12      </span></td></tr>\n", "\t<tr><th scope=row>2</th><td>chr1</td><td> 634814</td><td> 634814</td><td>G</td><td>A</td><td>.</td><td>PASS</td><td>SOMATIC;QSS=670;TQSS=1;NT=ref;QSS_NT=3070;TQSS_NT=1;SGT=GG-&gt;AG;DP=2966;MQ=59.12;MQ0=0;ReadPosRankSum=-1.76;SNVSB=0.00;SomaticEVS=15.74</td><td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td><td>2223:36:0:0:2,5:1,3:2179,2755:5,9</td><td>...</td><td> 586070</td><td> 827796</td><td>ENSG00000230021.10</td><td>.</td><td>-</td><td>HAVANA</td><td>gene</td><td>.</td><td><span style=white-space:pre-wrap>ID=ENSG00000230021.10;gene_id=ENSG00000230021.10;gene_type=transcribed_processed_pseudogene;gene_name=RP11-206L10.17;level=2;havana_gene=OTTHUMG00000191652.4           </span></td><td>RP11-206L10.17</td></tr>\n", "\t<tr><th scope=row>3</th><td>chr1</td><td> 963232</td><td> 963232</td><td>A</td><td>C</td><td>.</td><td>PASS</td><td><span style=white-space:pre-wrap>SOMATIC;QSS=66;TQSS=1;NT=ref;QSS_NT=66;TQSS_NT=1;SGT=AA-&gt;AC;DP=509;MQ=60.00;MQ0=0;ReadPosRankSum=3.11;SNVSB=0.00;SomaticEVS=7.14      </span></td><td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td><td><span style=white-space:pre-wrap>132:1:0:0:129,129:2,3:0,0:0,0    </span></td><td>...</td><td> 960583</td><td> 965719</td><td>ENSG00000187961.14</td><td>.</td><td>+</td><td>HAVANA</td><td>gene</td><td>.</td><td><span style=white-space:pre-wrap>ID=ENSG00000187961.14;gene_id=ENSG00000187961.14;gene_type=protein_coding;gene_name=KLHL17;level=2;hgnc_id=HGNC:24023;havana_gene=OTTHUMG00000040721.8                  </span></td><td><span style=white-space:pre-wrap>KLHL17        </span></td></tr>\n", "\t<tr><th scope=row>4</th><td>chr1</td><td> 965476</td><td> 965476</td><td>G</td><td>A</td><td>.</td><td>PASS</td><td><span style=white-space:pre-wrap>SOMATIC;QSS=41;TQSS=2;NT=ref;QSS_NT=41;TQSS_NT=2;SGT=GG-&gt;AG;DP=433;MQ=60.00;MQ0=0;ReadPosRankSum=0.02;SNVSB=0.00;SomaticEVS=7.98      </span></td><td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td><td><span style=white-space:pre-wrap>128:2:0:0:0,0:0,0:126,128:0,0    </span></td><td>...</td><td> 960583</td><td> 965719</td><td>ENSG00000187961.14</td><td>.</td><td>+</td><td>HAVANA</td><td>gene</td><td>.</td><td><span style=white-space:pre-wrap>ID=ENSG00000187961.14;gene_id=ENSG00000187961.14;gene_type=protein_coding;gene_name=KLHL17;level=2;hgnc_id=HGNC:24023;havana_gene=OTTHUMG00000040721.8                  </span></td><td><span style=white-space:pre-wrap>KLHL17        </span></td></tr>\n", "\t<tr><th scope=row>5</th><td>chr1</td><td>1014249</td><td>1014249</td><td>A</td><td>C</td><td>.</td><td>PASS</td><td><span style=white-space:pre-wrap>SOMATIC;QSS=34;TQSS=2;NT=ref;QSS_NT=34;TQSS_NT=2;SGT=AA-&gt;AC;DP=375;MQ=60.00;MQ0=0;ReadPosRankSum=-0.17;SNVSB=0.00;SomaticEVS=8.35     </span></td><td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td><td><span style=white-space:pre-wrap>103:0:0:0:103,103:0,0:0,0:0,0    </span></td><td>...</td><td>1001137</td><td>1014540</td><td>ENSG00000187608.10</td><td>.</td><td>+</td><td>HAVANA</td><td>gene</td><td>.</td><td><span style=white-space:pre-wrap>ID=ENSG00000187608.10;gene_id=ENSG00000187608.10;gene_type=protein_coding;gene_name=ISG15;level=2;hgnc_id=HGNC:4053;havana_gene=OTTHUMG00000040777.4                    </span></td><td><span style=white-space:pre-wrap>ISG15         </span></td></tr>\n", "\t<tr><th scope=row>6</th><td>chr1</td><td>1041393</td><td>1041393</td><td>T</td><td>G</td><td>.</td><td>PASS</td><td><span style=white-space:pre-wrap>SOMATIC;QSS=38;TQSS=1;NT=ref;QSS_NT=38;TQSS_NT=1;SGT=TT-&gt;GT;DP=424;MQ=60.00;MQ0=0;ReadPosRankSum=0.03;SNVSB=0.00;SomaticEVS=7.84      </span></td><td>DP:FDP:SDP:SUBDP:AU:CU:GU:TU</td><td><span style=white-space:pre-wrap>139:2:0:0:0,0:0,0:2,2:135,140    </span></td><td>...</td><td>1020119</td><td>1056118</td><td>ENSG00000188157.15</td><td>.</td><td>+</td><td>HAVANA</td><td>gene</td><td>.</td><td>ID=ENSG00000188157.15;gene_id=ENSG00000188157.15;gene_type=protein_coding;gene_name=AGRN;level=2;hgnc_id=HGNC:329;tag=overlapping_locus;havana_gene=OTTHUMG00000040778.7</td><td><span style=white-space:pre-wrap>AGRN          </span></td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A data.frame: 6 × 22\n", "\\begin{tabular}{r|lllllllllllllllllllll}\n", "  & chr & start & stop & ref & alt & qual & filter & info & format & normal & ... & ens\\_start & ens\\_stop & ensemble\\_id & score & strand & source & gene\\_type & type\\_score & details & gene\\\\\n", "  & <chr> & <int> & <int> & <chr> & <chr> & <chr> & <chr> & <chr> & <chr> & <chr> & ... & <int> & <int> & <chr> & <chr> & <chr> & <chr> & <chr> & <chr> & <chr> & <chr>\\\\\n", "\\hline\n", "\t1 & chr1 &  634814 &  634814 & G & A & . & PASS & SOMATIC;QSS=670;TQSS=1;NT=ref;QSS\\_NT=3070;TQSS\\_NT=1;SGT=GG->AG;DP=2966;MQ=59.12;MQ0=0;ReadPosRankSum=-1.76;SNVSB=0.00;SomaticEVS=15.74 & DP:FDP:SDP:SUBDP:AU:CU:GU:TU & 2223:36:0:0:2,5:1,3:2179,2755:5,9 & ... &  634375 &  634922 & ENSG00000198744.5  & . & + & HAVANA & gene & . & ID=ENSG00000198744.5;gene\\_id=ENSG00000198744.5;gene\\_type=unprocessed\\_pseudogene;gene\\_name=MTCO3P12;level=2;hgnc\\_id=HGNC:52042;havana\\_gene=OTTHUMG00000002337.2           & MTCO3P12      \\\\\n", "\t2 & chr1 &  634814 &  634814 & G & A & . & PASS & SOMATIC;QSS=670;TQSS=1;NT=ref;QSS\\_NT=3070;TQSS\\_NT=1;SGT=GG->AG;DP=2966;MQ=59.12;MQ0=0;ReadPosRankSum=-1.76;SNVSB=0.00;SomaticEVS=15.74 & DP:FDP:SDP:SUBDP:AU:CU:GU:TU & 2223:36:0:0:2,5:1,3:2179,2755:5,9 & ... &  586070 &  827796 & ENSG00000230021.10 & . & - & HAVANA & gene & . & ID=ENSG00000230021.10;gene\\_id=ENSG00000230021.10;gene\\_type=transcribed\\_processed\\_pseudogene;gene\\_name=RP11-206L10.17;level=2;havana\\_gene=OTTHUMG00000191652.4            & RP11-206L10.17\\\\\n", "\t3 & chr1 &  963232 &  963232 & A & C & . & PASS & SOMATIC;QSS=66;TQSS=1;NT=ref;QSS\\_NT=66;TQSS\\_NT=1;SGT=AA->AC;DP=509;MQ=60.00;MQ0=0;ReadPosRankSum=3.11;SNVSB=0.00;SomaticEVS=7.14       & DP:FDP:SDP:SUBDP:AU:CU:GU:TU & 132:1:0:0:129,129:2,3:0,0:0,0     & ... &  960583 &  965719 & ENSG00000187961.14 & . & + & HAVANA & gene & . & ID=ENSG00000187961.14;gene\\_id=ENSG00000187961.14;gene\\_type=protein\\_coding;gene\\_name=KLHL17;level=2;hgnc\\_id=HGNC:24023;havana\\_gene=OTTHUMG00000040721.8                   & KLHL17        \\\\\n", "\t4 & chr1 &  965476 &  965476 & G & A & . & PASS & SOMATIC;QSS=41;TQSS=2;NT=ref;QSS\\_NT=41;TQSS\\_NT=2;SGT=GG->AG;DP=433;MQ=60.00;MQ0=0;ReadPosRankSum=0.02;SNVSB=0.00;SomaticEVS=7.98       & DP:FDP:SDP:SUBDP:AU:CU:GU:TU & 128:2:0:0:0,0:0,0:126,128:0,0     & ... &  960583 &  965719 & ENSG00000187961.14 & . & + & HAVANA & gene & . & ID=ENSG00000187961.14;gene\\_id=ENSG00000187961.14;gene\\_type=protein\\_coding;gene\\_name=KLHL17;level=2;hgnc\\_id=HGNC:24023;havana\\_gene=OTTHUMG00000040721.8                   & KLHL17        \\\\\n", "\t5 & chr1 & 1014249 & 1014249 & A & C & . & PASS & SOMATIC;QSS=34;TQSS=2;NT=ref;QSS\\_NT=34;TQSS\\_NT=2;SGT=AA->AC;DP=375;MQ=60.00;MQ0=0;ReadPosRankSum=-0.17;SNVSB=0.00;SomaticEVS=8.35      & DP:FDP:SDP:SUBDP:AU:CU:GU:TU & 103:0:0:0:103,103:0,0:0,0:0,0     & ... & 1001137 & 1014540 & ENSG00000187608.10 & . & + & HAVANA & gene & . & ID=ENSG00000187608.10;gene\\_id=ENSG00000187608.10;gene\\_type=protein\\_coding;gene\\_name=ISG15;level=2;hgnc\\_id=HGNC:4053;havana\\_gene=OTTHUMG00000040777.4                     & ISG15         \\\\\n", "\t6 & chr1 & 1041393 & 1041393 & T & G & . & PASS & SOMATIC;QSS=38;TQSS=1;NT=ref;QSS\\_NT=38;TQSS\\_NT=1;SGT=TT->GT;DP=424;MQ=60.00;MQ0=0;ReadPosRankSum=0.03;SNVSB=0.00;SomaticEVS=7.84       & DP:FDP:SDP:SUBDP:AU:CU:GU:TU & 139:2:0:0:0,0:0,0:2,2:135,140     & ... & 1020119 & 1056118 & ENSG00000188157.15 & . & + & HAVANA & gene & . & ID=ENSG00000188157.15;gene\\_id=ENSG00000188157.15;gene\\_type=protein\\_coding;gene\\_name=AGRN;level=2;hgnc\\_id=HGNC:329;tag=overlapping\\_locus;havana\\_gene=OTTHUMG00000040778.7 & AGRN          \\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A data.frame: 6 × 22\n", "\n", "| <!--/--> | chr &lt;chr&gt; | start &lt;int&gt; | stop &lt;int&gt; | ref &lt;chr&gt; | alt &lt;chr&gt; | qual &lt;chr&gt; | filter &lt;chr&gt; | info &lt;chr&gt; | format &lt;chr&gt; | normal &lt;chr&gt; | ... ... | ens_start &lt;int&gt; | ens_stop &lt;int&gt; | ensemble_id &lt;chr&gt; | score &lt;chr&gt; | strand &lt;chr&gt; | source &lt;chr&gt; | gene_type &lt;chr&gt; | type_score &lt;chr&gt; | details &lt;chr&gt; | gene &lt;chr&gt; |\n", "|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|\n", "| 1 | chr1 |  634814 |  634814 | G | A | . | PASS | SOMATIC;QSS=670;TQSS=1;NT=ref;QSS_NT=3070;TQSS_NT=1;SGT=GG-&gt;AG;DP=2966;MQ=59.12;MQ0=0;ReadPosRankSum=-1.76;SNVSB=0.00;SomaticEVS=15.74 | DP:FDP:SDP:SUBDP:AU:CU:GU:TU | 2223:36:0:0:2,5:1,3:2179,2755:5,9 | ... |  634375 |  634922 | ENSG00000198744.5  | . | + | HAVANA | gene | . | ID=ENSG00000198744.5;gene_id=ENSG00000198744.5;gene_type=unprocessed_pseudogene;gene_name=MTCO3P12;level=2;hgnc_id=HGNC:52042;havana_gene=OTTHUMG00000002337.2           | MTCO3P12       |\n", "| 2 | chr1 |  634814 |  634814 | G | A | . | PASS | SOMATIC;QSS=670;TQSS=1;NT=ref;QSS_NT=3070;TQSS_NT=1;SGT=GG-&gt;AG;DP=2966;MQ=59.12;MQ0=0;ReadPosRankSum=-1.76;SNVSB=0.00;SomaticEVS=15.74 | DP:FDP:SDP:SUBDP:AU:CU:GU:TU | 2223:36:0:0:2,5:1,3:2179,2755:5,9 | ... |  586070 |  827796 | ENSG00000230021.10 | . | - | HAVANA | gene | . | ID=ENSG00000230021.10;gene_id=ENSG00000230021.10;gene_type=transcribed_processed_pseudogene;gene_name=RP11-206L10.17;level=2;havana_gene=OTTHUMG00000191652.4            | RP11-206L10.17 |\n", "| 3 | chr1 |  963232 |  963232 | A | C | . | PASS | SOMATIC;QSS=66;TQSS=1;NT=ref;QSS_NT=66;TQSS_NT=1;SGT=AA-&gt;AC;DP=509;MQ=60.00;MQ0=0;ReadPosRankSum=3.11;SNVSB=0.00;SomaticEVS=7.14       | DP:FDP:SDP:SUBDP:AU:CU:GU:TU | 132:1:0:0:129,129:2,3:0,0:0,0     | ... |  960583 |  965719 | ENSG00000187961.14 | . | + | HAVANA | gene | . | ID=ENSG00000187961.14;gene_id=ENSG00000187961.14;gene_type=protein_coding;gene_name=KLHL17;level=2;hgnc_id=HGNC:24023;havana_gene=OTTHUMG00000040721.8                   | KLHL17         |\n", "| 4 | chr1 |  965476 |  965476 | G | A | . | PASS | SOMATIC;QSS=41;TQSS=2;NT=ref;QSS_NT=41;TQSS_NT=2;SGT=GG-&gt;AG;DP=433;MQ=60.00;MQ0=0;ReadPosRankSum=0.02;SNVSB=0.00;SomaticEVS=7.98       | DP:FDP:SDP:SUBDP:AU:CU:GU:TU | 128:2:0:0:0,0:0,0:126,128:0,0     | ... |  960583 |  965719 | ENSG00000187961.14 | . | + | HAVANA | gene | . | ID=ENSG00000187961.14;gene_id=ENSG00000187961.14;gene_type=protein_coding;gene_name=KLHL17;level=2;hgnc_id=HGNC:24023;havana_gene=OTTHUMG00000040721.8                   | KLHL17         |\n", "| 5 | chr1 | 1014249 | 1014249 | A | C | . | PASS | SOMATIC;QSS=34;TQSS=2;NT=ref;QSS_NT=34;TQSS_NT=2;SGT=AA-&gt;AC;DP=375;MQ=60.00;MQ0=0;ReadPosRankSum=-0.17;SNVSB=0.00;SomaticEVS=8.35      | DP:FDP:SDP:SUBDP:AU:CU:GU:TU | 103:0:0:0:103,103:0,0:0,0:0,0     | ... | 1001137 | 1014540 | ENSG00000187608.10 | . | + | HAVANA | gene | . | ID=ENSG00000187608.10;gene_id=ENSG00000187608.10;gene_type=protein_coding;gene_name=ISG15;level=2;hgnc_id=HGNC:4053;havana_gene=OTTHUMG00000040777.4                     | ISG15          |\n", "| 6 | chr1 | 1041393 | 1041393 | T | G | . | PASS | SOMATIC;QSS=38;TQSS=1;NT=ref;QSS_NT=38;TQSS_NT=1;SGT=TT-&gt;GT;DP=424;MQ=60.00;MQ0=0;ReadPosRankSum=0.03;SNVSB=0.00;SomaticEVS=7.84       | DP:FDP:SDP:SUBDP:AU:CU:GU:TU | 139:2:0:0:0,0:0,0:2,2:135,140     | ... | 1020119 | 1056118 | ENSG00000188157.15 | . | + | HAVANA | gene | . | ID=ENSG00000188157.15;gene_id=ENSG00000188157.15;gene_type=protein_coding;gene_name=AGRN;level=2;hgnc_id=HGNC:329;tag=overlapping_locus;havana_gene=OTTHUMG00000040778.7 | AGRN           |\n", "\n"], "text/plain": ["  chr  start   stop    ref alt qual filter\n", "1 chr1  634814  634814 G   A   .    <PERSON>  \n", "2 chr1  634814  634814 G   A   .    <PERSON>  \n", "3 chr1  963232  963232 A   C   .    <PERSON>SS  \n", "4 chr1  965476  965476 G   A   .    <PERSON>  \n", "5 chr1 1014249 1014249 A   C   .    PA<PERSON>  \n", "6 chr1 1041393 1041393 T   G   .    <PERSON>  \n", "  info                                                                                                                                  \n", "1 SOMATIC;QSS=670;TQSS=1;NT=ref;QSS_NT=3070;TQSS_NT=1;SGT=GG->AG;DP=2966;MQ=59.12;MQ0=0;ReadPosRankSum=-1.76;SNVSB=0.00;SomaticEVS=15.74\n", "2 SOMATIC;QSS=670;TQSS=1;NT=ref;QSS_NT=3070;TQSS_NT=1;SGT=GG->AG;DP=2966;MQ=59.12;MQ0=0;ReadPosRankSum=-1.76;SNVSB=0.00;SomaticEVS=15.74\n", "3 SOMATIC;QSS=66;TQSS=1;NT=ref;QSS_NT=66;TQSS_NT=1;SGT=AA->AC;DP=509;MQ=60.00;MQ0=0;ReadPosRankSum=3.11;SNVSB=0.00;SomaticEVS=7.14      \n", "4 SOMATIC;QSS=41;TQSS=2;NT=ref;QSS_NT=41;TQSS_NT=2;SGT=GG->AG;DP=433;MQ=60.00;MQ0=0;ReadPosRankSum=0.02;SNVSB=0.00;SomaticEVS=7.98      \n", "5 SOMATIC;QSS=34;TQSS=2;NT=ref;QSS_NT=34;TQSS_NT=2;SGT=AA->AC;DP=375;MQ=60.00;MQ0=0;ReadPosRankSum=-0.17;SNVSB=0.00;SomaticEVS=8.35     \n", "6 SOMATIC;QSS=38;TQSS=1;NT=ref;QSS_NT=38;TQSS_NT=1;SGT=TT->GT;DP=424;MQ=60.00;MQ0=0;ReadPosRankSum=0.03;SNVSB=0.00;SomaticEVS=7.84      \n", "  format                       normal                            ... ens_start\n", "1 DP:FDP:SDP:SUBDP:AU:CU:GU:TU 2223:36:0:0:2,5:1,3:2179,2755:5,9 ...  634375  \n", "2 DP:FDP:SDP:SUBDP:AU:CU:GU:TU 2223:36:0:0:2,5:1,3:2179,2755:5,9 ...  586070  \n", "3 DP:FDP:SDP:SUBDP:AU:CU:GU:TU 132:1:0:0:129,129:2,3:0,0:0,0     ...  960583  \n", "4 DP:FDP:SDP:SUBDP:AU:CU:GU:TU 128:2:0:0:0,0:0,0:126,128:0,0     ...  960583  \n", "5 DP:FDP:SDP:SUBDP:AU:CU:GU:TU 103:0:0:0:103,103:0,0:0,0:0,0     ... 1001137  \n", "6 DP:FDP:SDP:SUBDP:AU:CU:GU:TU 139:2:0:0:0,0:0,0:2,2:135,140     ... 1020119  \n", "  ens_stop ensemble_id        score strand source gene_type type_score\n", "1  634922  ENSG00000198744.5  .     +      HAVANA gene      .         \n", "2  827796  ENSG00000230021.10 .     -      HAVANA gene      .         \n", "3  965719  ENSG00000187961.14 .     +      HAVANA gene      .         \n", "4  965719  ENSG00000187961.14 .     +      HAVANA gene      .         \n", "5 1014540  ENSG00000187608.10 .     +      HAVANA gene      .         \n", "6 1056118  ENSG00000188157.15 .     +      HAVANA gene      .         \n", "  details                                                                                                                                                                 \n", "1 ID=ENSG00000198744.5;gene_id=ENSG00000198744.5;gene_type=unprocessed_pseudogene;gene_name=MTCO3P12;level=2;hgnc_id=HGNC:52042;havana_gene=OTTHUMG00000002337.2          \n", "2 ID=ENSG00000230021.10;gene_id=ENSG00000230021.10;gene_type=transcribed_processed_pseudogene;gene_name=RP11-206L10.17;level=2;havana_gene=OTTHUMG00000191652.4           \n", "3 ID=ENSG00000187961.14;gene_id=ENSG00000187961.14;gene_type=protein_coding;gene_name=KLHL17;level=2;hgnc_id=HGNC:24023;havana_gene=OTTHUMG00000040721.8                  \n", "4 ID=ENSG00000187961.14;gene_id=ENSG00000187961.14;gene_type=protein_coding;gene_name=KLHL17;level=2;hgnc_id=HGNC:24023;havana_gene=OTTHUMG00000040721.8                  \n", "5 ID=ENSG00000187608.10;gene_id=ENSG00000187608.10;gene_type=protein_coding;gene_name=ISG15;level=2;hgnc_id=HGNC:4053;havana_gene=OTTHUMG00000040777.4                    \n", "6 ID=ENSG00000188157.15;gene_id=ENSG00000188157.15;gene_type=protein_coding;gene_name=AGRN;level=2;hgnc_id=HGNC:329;tag=overlapping_locus;havana_gene=OTTHUMG00000040778.7\n", "  gene          \n", "1 MTCO3P12      \n", "2 RP11-206L10.17\n", "3 KLHL17        \n", "4 KLHL17        \n", "5 ISG15         \n", "6 AGRN          "]}, "metadata": {}, "output_type": "display_data"}], "source": ["input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/wgs/RLGS10-recurrent_wgs_seq/pass_intersect.bed\", sep='\\t')\n", "\n", "head(input_df)"]}, {"cell_type": "code", "execution_count": 47, "id": "c6e68c95-297e-4b68-8823-455e0221ebaa", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA0gAAANICAIAAAByhViMAAAABmJLR0QA/wD/AP+gvaeTAAAg\nAElEQVR4nOzdeXwTdf7H8U+SNm0pR0k5WkC3FaKC4KJ4dNWKuAjounJ6i7oKiv68V8RrPdb1\nwHPdFQUWdV1XQAWsqxyr6KJBbZFTKB4FUQkEoSmlV9okk/z+GKglbZNMrqbT1/MPH9PJZyaf\nJCV9O8f3a/D7/QIAAID2z9jWDQAAACA2CHYAAAA6QbADAADQCYIdAACAThDsAAAAdIJgBwAA\noBMEOwAAAJ0g2AEAAOgEwQ4AAEAnCHYAAAA6QbADAADQCYIdAACAThDsAAAAdIJgBwAAoBME\nOwAAAJ0g2AEAAOgEwQ4AAEAnCHYAAAA6QbADAADQCYIdAACAThDsAAAAdIJgBwAAoBMEOwAA\nAJ0g2AEAAOgEwQ4AAEAnCHYAAAA6QbADAADQCYIdAACAThDsAAAAdIJgBwAAoBMEOwAAAJ0g\n2AEAAOgEwQ4AAEAnCHYAAAA6QbADAADQCYIdAACAThDsAAAAdIJgBwAAoBMEOwAAAJ0g2AEA\nAOgEwQ4AAEAnCHYAAAA6QbADAADQCYIdAACAThDsAAAAdIJgBwAAoBMEOwAAAJ0g2AEAAOgE\nwQ4AAEAnCHYAAAA6kdLWDSSIx+NxuVzN1/v9fnXBYDCE3AnFFFNMMcUUh1NPMcVxLU5JSenU\nqVPLD4Wzdx344IMPZs2adfzxxwes93q9iqIYjcbU1NSQO4mg2GAwmM3m9lKsKIrX66WYYoop\n7gjFIpKWltZein0+n8fjoZjir776au/evSNGjHjqqada3LajBDsROe6445544omAlbW1tS6X\nKyUlJSsrK+QeIig2mUzdu3dvL8V1dXV1dXUUR1xsNBotFksbFrtcrtraWoojLjYYDNnZ2e2l\nuL6+vqamhuKIi0WkR48e7aW4oaGhurqa4miKs7OzQx4tS5Jit9tdVVXVYvH06dP37t0bZFuu\nsQMAANAJgh0AAIBOEOwAAAB0gmAHAACgEwQ7AAAAnSDYAQAA6ATBDgAAQCcIdgAAADpBsAMA\nANAJgh0AAIBOEOwAAAB0gmAHAACgEwQ7AAAAnSDYAQAA6ATBDgAAQCcIdgAAADpBsAMAANAJ\ngh0AAIBOEOwAAAB0gmAHAACgEwQ7AAAAnSDYAQAA6ERKWzcAIBIzZ84sLi4OWKkoiqIoImI2\nm5tvUlBQMGPGjEQ0BwBoIwQ7oF0qLi4uKipq6y4AAMmFYAe0YxkZGdnZ2SHLnE6ny+VKQD8A\ngLZFsAPasezs7MLCwpBlNpvNbrcnoB8AQNvi5gkAAACdINgBAADoBMEOAABAJwh2AAAAOkGw\nAwAA0ImOcles1+v1eDyVlZUB630+n4goitL8oeaSpFgdgTYexWobPp+P4uQv9ng8IbcNEPBP\nwO/3J7jnIMV+v59ircXqJ9i+iuP91omIXovV95niCIob3+cDBw60l+LGF9i8OOSXf0cJdkaj\n0Wg0pqenB6xvaGjw+XwtPtRc8hR7PJ54FLvdbrfbbTAYKE7+YqNR8+H2gF8Dj8ejKEqYbXg8\nnoaGhvgVi4hei9VPUFNx+G+dpk8wSYr5RYqs2Ov1qv+jTnEExfX19SKSlpZmMBjaRbGiKOrg\no82LQ375d6BgZzKZmv8GKIri8XjC/OLQfbHP5wv/jwrFbVtsMplCbhsg4J+A3+8P/68mxdEU\nh/9xq8US3p8rEQk/FlDc3osbGhrUWEBxNMUhE1WSFLvdbjXYNS8O+eXPNXYAAAA6QbADAADQ\nCYIdAACATnSUa+wAnSkrKxMRp9Nps9lCFjudzsZNAAA6RrAD2qWKigoRcblcdrtd0yYAAB0j\n2AHtksVicTgcJpPJbDaHLHa73YqiWCyWBDQGAGhDBDugXbJaraWlpbm5uYWFhSGLbTab3W63\nWq0JaAwA0Ia4eQIAAEAnCHYAAAA6QbADAADQCYIdAACAThDsAAAAdIJgBwAAoBMEOwAAAJ0g\n2AEAAOgEwQ4AAEAnCHYAAAA6QbADAADQCYIdAACATqS0dQMAIlFWViYiTqfTZrOFLHY6nY2b\nAAB0jGAHtEsVFRUi4nK57Ha7pk0AADpGsAPaJYvF4nA4TCaT2WwOWex2uxVFsVgsCWgMANCG\nCHZAu2S1WktLS3NzcwsLC0MW22w2u91utVoT0BgAoA1x8wQAAIBOEOwAAAB0gmAHAACgEwQ7\nAAAAnSDYAQAA6ATBDgAAQCcIdgAAADpBsAMAANAJBigG2iXmigUANEewA9ol5ooFADRHsAPa\nJeaKBQA0R7AD2iXmigUANMfNEwAAADpBsAMAANAJgh0AAIBOEOwAAAB0gmAHAACgEwQ7AAAA\nnSDYAQAA6ATBDgAAQCcIdgAAADrBzBNAu1RWViYiTqfTZrOFLHY6nY2bAAB0jGAHtEsVFRUi\n4nK57Ha7pk0AADpGsAPaJYvF4nA4TCaT2WwOWex2uxVFsVgsCWgMANCGCHZAu2S1WktLS3Nz\ncwsLC0MW22w2u91utVoT0BgAoA1x8wQAAIBOEOwAAAB0gmAHAACgEwQ7AAAAnSDYAQAA6ATB\nDgAAQCcIdgAAADpBsAMAANAJgh0AAIBOEOwAAAB0gmAHAACgEwQ7AAAAnSDYAQAA6ATBDgAA\nQCeSI9itmNLZ0Koj7/j8UF3l7JEt14yZV9mW/QMAACSB5Ah2wXQdPnxoW/cAAADQDiRHsBsz\nr8bfzPdPDTOI5Fw25XedDq8+7pGvA2tXTMlqm84BAACSRnIEuxb418+du84vR/9h6vCUtu4F\nAACgPUjWYOf53+xXy8RwwrXXntjWrQAAALQPSRrsqovmLPhZUkdMvap/8wd3/uuKwbld0syd\nuvc7rvDi6XM+cyiJ7xAAACDZJOdpzr1vzCmqkYwJUy7r3cKjVWXrSkVExL1r6+q3tq5++5/z\nZ65YPn1YwKV4Mm3atNraWnW5Z8+ePp+vsjLw5lmfzyciiqI0f6i5JClWFCVOxWobLb5RFCdb\nscfjCbltAI/H03Qnfr8/wT0HKfb7/RRrLVY/wfZVHO+3TkT0Wqy+zxRHUNz4Ph84cKC9FDe+\nwObFIb/8kzLYbXt1zsdusVw1dUK3wx8wZA44744J100cfsLAo3r4yn8sXfX6zIeeW/b9p3dN\nvKvgmxcK0w+r/u6776qqqtTl1NTULl26eL3eFp/Q7/e39lCUxSLS7orj927ovlgS+Ak2/rMP\nX2uvJRleYAf8BCmORzEfN8W6Lw755Z+Ewc6/Zu4/NvrlyMlTRqUFPNRt8uylkxt/OmLgmZMf\nO/PcYRcNmvT2j6/OWfFM4bjDNrjkkksaGhrU5Zqamn379mVkZATs0ePxeL1eo9GYlhb4ZM1F\nUGwwGNLT09tLsdfr9Xg8Oi7W08dtMplCbhvAZDI1/SeQJB9KUhWLSPNviaQtVhTF7XYnSXGS\nfIJ83BQHKU5PTzcYDO2i2OfzqQGm+QsM+eWffMHOvXLOP7eLHPuHKWeEdQFgjwnXXJD19suV\nW7bskHHHNn3kuuuua1xeunTpqlWrMjMzA7aura1V/3g3f6g53RfX1dV5PB59F6shSQfFkQW7\npjtxuVzqH8Jw2qA4CYvr6+vVRJUMxSISZrEaZSjWWtzQ0BD++0xxa8UhE1WSFLvdbjXYderU\nKaA45Jd/0t08cWDJnIX7xFgw5ZrBbd0KAABAu5JswW7Pv+f8p07Mo6ZceWSYW1S8+9p7lSKd\nBg3Ki2djAAAAyS7Jgt23r8xZ5ZEuY6de3LP5g7ULryu49N55y4pLd/xc3eDab//2s/kPjvvN\n5Qv3ihxx5XXnhr6uAgAAQMeS6ho73xdz/7FZpNclU37fuYWH/fW7Sxb+o2Th4wHrDdlnPr74\nqbNCX0AJAACgZ8kU7Bo+mPPaDyK/umrKyNSWHu982ctruy54fX7Rqk1lP+zcW2/O6tN/6Onn\nXXrTLVed2lvzheQAAAA6k0zBLm3MP8v9/wxSYO49bMJtwybclqiGAAAA2pMku8YOAAAAkSLY\nAQAA6ATBDgAAQCcIdgAAADpBsAMAANAJgh0AAIBOEOwAAAB0gmAHAACgEwQ7AAAAnSDYAQAA\n6ATBDgAAQCcIdgAAADqREt3mvto9O3Y4yvdXuc1dLT365OX3ziQqAgAAtIkIg12D/dPXnn9+\n3qKPNvxwwNtkb93yTxx54dTbb7ny9L7m2DQIAACA8ERwfK3+m39PPWng8OufXvLlYalORLwH\ndqxZ/OTUMwaeMm3Bdw0xahEAAADh0HrEzv/TgslnX7nI4T+0fdcjBx9vze1mdh9wbPtqy49V\nXhGR6k1zLj/7gKl4/kX9DLHtFwAAAK3QeMSu/I0brz+Y6kw5Z929YP0e548bbCuXvb9spW3D\nDxV7Niy4+6wck4iIf9fCqTe96Yx9xwAAAGiRtmC39cUnl1aLiEiv37/6xcrHLzkhu+khP1P2\n0EseX/n5K+f3EhGRqnefeOmbWDUKAACA4DQFu90rVmwWEZGUEQ/OnpxnarHIlH/lnAfOUh/b\ntGKFI8oGAQAAEB5Nwa6srExdOGns2D5B6vpccMGwQ1tsi7AxAAAAaKMp2FVWVqoLvXr1ClqY\nk5OjLuzfvz+itgAAAKCVpmCXlZWlLpSXlwct3LdvX8AWAAAAiDNNwa5///7qwpfLlu0LUrdv\n+fK1AVsAAAAgzjQFu36jRw8SERHPiodve/fnVqr2vHvrQys8IiIyeMzovlG1BwAAgHBpG6B4\nyA13jnr6mg/qRHbOv/g01yMvPn3j6KMyf3m8dvt/Z02/8YF37CIi0vnc6TcMjmWzAA5Rb2Vy\nOp02my1ksdPplCZ3PwEA9ErjzBM5V734tyUFU94vF2n4/p27xhQ91GfwSUMH5HRJ9VTt2bZx\n7RZH3aE5KXqNfWHWFcHvsQAQqYqKChFxuVx2u13TJgAAHdM6pZix/7ULP/Ree+Ftb26rFxF/\n3e7Nn+7eHFiVcfTlf1s096r8CGaiBRAOi8XicDhMJpPZbA5Z7Ha7FUWxWCwJaAwA0Ia0BjsR\nyRx6/cKNv7167nPPv7L4k9KfXf5fHjJk9B48YtK1t94xddRRnWLXJIBAVqu1tLQ0Nze3sLAw\nZLHNZrPb7VarNQGNAQDaUATBTkQkc8CY22eNuX2Wp2LHN9t3OyurPaldsrL7Djg2r3tqbBsE\nAABAeCIMdoekWvKHWPJj0woAAACiwVVwAAAAOkGwAwAA0AmCHQAAgE60do3dZ0+c//jqg8tn\n3PP+3acHrgxT47YAAACIr9aCnWPt0qVLDy6nX93CyjA1bgsAAID44lQsAACATrR2xO6CV/ft\nm31wOa1rCyvD1LgtAAAA4qu1YGfu0qNHl7BWAgAAIClwKhYAAEAnNM088f2Hsz/YLiLSf9S0\nc46KTSUAAABiQ1OwWz/nhhsWi4hMfDtEXAu/EgAAALHBqVgAAACdiE+w8/v9B3dvMsXlCQAA\nABAoPsGuoqJCXejcuXNcngAAAACB4hHslJ/eXlKiLh51FBfYAQAAJEbwmyeKn5n09Be//Lir\n+Jf1kxa2tIHf69r/0+aSjTtdIiKS99vfEuwAAAASI3iws3+xePHiFtbvKm5xdYAe5z9ye4Eh\nsr4AAACgUTxOxZoyc48bec1jy75cfEXfOOweAAAALQl+xO68l3bsePqXH5ffmH/jchGRc1/c\n8eK5LdQbjKlpmV27W7qkcaAOAAAgwYIHu0498/J6/vJjz05N1ufFrScAAABEQNPME6Oe27z5\nIRGRbkfGpRkAAABETlOw63rE4MFHxKsTAAAAREVTsAvkq6tw7HVWVtV5fP5WSrLyh+Z1i+Y5\nAAAAEJ6Igl3d9uVzn33hX+/ZvtpZrQQvnfi2f9GkSJ4DAAAA2mgOdr4fF08578pXt9bFoxsA\nAABETGOw8256ZNzlr25tEBFDt2NHjuld9uYnP4iI5J39h9O779z62eoNe+pFJHPQ7y76TY5R\n5KT8mLcMAACAlmgboHj/Ww8/tbFBRKTn2Ne+2vzBwpuGHXxk2A2v/HvRh+t/+v5/D43oIVL7\n7cY9J9z893nzpg0LsjsAAADEjqZgV7980dJaERE5efqzk49s4Whfau5ZD77/7i1Wg7Jr+S0T\n/vi/6pg0CQAAgNA0BbvN69a5RUTk6HPHHNVqVafT/nT3mFQR3/ez75/3U3TtAQAAIFyagt2+\nffvUhf79+x/c3Hhw7jC32/1LXY+RI4eKiPi/WLzEEX2PAAAACIOmmyf8/oPD1ZnNZnUhMzNT\npEZEqqqqRHocKszJyVE32L79e5Hc2LSqEzNnziwuLg5YqSiKoigGgyE1NbX5JgUFBTNmzEhI\ndwAAoB3TFOx69eol8pOIlJeXi+SISM+ePdVgZ7fbmwS7yspKdcHpdMawWV0oLi4uKipq6y4A\nAIAOaQp2OTk5arDbu3evGuyOPe44o+zwiexYs6Zchh5Mdg3r1pWqS927d49tv5FSFMXr9VZX\nB97N4fV6RcTn8zV/qLmYFKvrMzIysrOzQ+7E6XS6XK6AzuPXs6Io+i5O/Mcdq2K/39/iL5Im\nAb9I6lvXfM8tojgJi30+XzK0EUGxiFCstVj9uClWRfw+GwyGeBTX1NSE30Y4xY3vRvPikF/+\nmoJdv+OPt8iaCpGdZWX1cny6SKdRY840vr/KJ76P//5EyWVPn9pZpHbjzD8vqFD3fvLJQ7U8\nQVwZDAajMfCawsaPrflDLe4h+mJ1fXZ2dmFhYcid2Gw2u90e0Hn8em78TdJrsfputPibkOTF\n0sovkiYBz5gkHwrFERc3XhyTDMVh/j4nyVvXHouT6uNuv8UhvzkjKzYYDLEtbhQkt7RGU7Az\nnDFieOq8dzxS/+F/P/VNHGUUyb38lkkPrnrLKcqWZ84aaBt5cvbPaz/5cqdLRESyL582qYuW\nJ4gfk8lkMpkyMzMD1tfW1nq9XqPR2Pyh5mJSbDKZNHWubtJ0J/Hrua6uzuPx6LvY6/UaDAYd\nFEf/i+RyuTweT5htUJyExfX19W63O0mK5eA116GLPR4PxREUNzQ0hP8+U9xacchUlCTFbre7\noaFBRDp16hRQHPLLX9sAxZkjzhvdt2/fvn3Tt6w5eLI1a/xf512VbxIRqbevef+d5V/uVGcb\nM1uve+2Z8ztr2j8AAAAipi3YSe6U9+x2u91u//z+IY3rxr1S/OnfpowY0O1giDRlDRgx7cVP\nvpjzu9AXkQEAACBGNM4V2zJjr9Nu/sfHN8+ur9y3r8qX2bO3JUPzWSIAAABEJybB7iBTelbO\nEVkx3CEAAADCp/FUrCbuXZ/OXbIpjk8AAACAX8Qn2NVuX/7c9YX5Rw2/fn5ZXJ4AAAAAgWJ5\nKlZEfJWlS154/LG/LtzgVGK7ZwAAAAQXVrCr21m84r2VJd/ay+tSehx51MDfjL1oZP9Oh9co\nzg3zn/nzo7Pe/bbK37iy01FH5cS0XQAAALQmVLDz71l+76VTnlm129N07V13nXLjvHeev6CP\nQUTEu/vDp269+fHF31Y3RjpD1nHj/u/u+267dFiPwD0CAAAgLoIHO5dt+tljn/naE7he2bfm\n7xeebVq5/rnC2pV3/e7Cp76sbNxh71Muve2ee24cO7Cr5hmPAAAAELmgwe6b52567lCqS8ke\nMuK3w47IqPnxy49Xba1QxP3trAfmjRn5+rin1taLiIi575nX3POnGdeOzEuPe9sAAAAIFCzY\nrXvt1a/UGYq7j3j6o3f/eMLBeV8PfPnE78+5x3bAs+qusZ83NIiIKffse2bPve+C/kQ6AACA\nthJkuJO9q1dvExERQ8G9LzemOhHpdvLdr9xzskFEGhrcItLvkre+/PARUh0AAECbCnLE7tvv\nvlMXBv/+gvyAxwaMvWDQ3V+WioiYR97/zIS+8RzoGEAzZWVlIuJ0Om02W8hip9PZuAkAQMeC\nBLvKyoN3RPzqV79q9mBeXp5IqYjIkNGj+8SjMwBBVFRUiIjL5bLb7Zo2AQDoWJBgp55nFZG0\ntLRmD6anHzrx2q9fv9i3BSA4i8XicDhMJpPZbA5Z7Ha7FUWxWCwJaAwA0Iain3kiJSXGs1cA\nCM1qtZaWlubm5hYWFoYsttlsdrvdarUmoDEAQBvi2jgAAACdCOdoW0Plnj17AldWNgR7tFF6\n95ys5idyAQAAEHPhBLv3p+bmRvroxLf9iyZp7goAAACacSoWAABAJ4IcsUvJyMzMjHb/GdxZ\nAQAAkBhBcte412tqEtcIAAAAosOpWAAAAJ0g2AEAAOgEwQ4AAEAnCHYAAAA6QbADAADQCYId\nAACAThDsAAAAdIJgBwAAoBMEOwAAAJ0g2AEAAOgEwQ4AAEAnCHYAAAA6kdLWDQCIRFlZmYg4\nnU6bzRay2Ol0Nm4CANAxgh3QLlVUVIiIy+Wy2+2aNgEA6BjBDmiXLBaLw+EwmUxmszlksdvt\nVhTFYrEkoDEAQBtqLdh99sT5j6+Owf7PuOf9u0+PwX4AHMZqtZaWlubm5hYWFoYsttlsdrvd\narUmoDEAQBtqLdg51i5dujQG+0+/OgY7AQAAQGjcFQsAAKATrR2xu+DVfftmx2D/aV1jsBMA\nAACE1lqwM3fp0aNLQjsBAABAVDgVCwAAoBMEOwAAAJ2Iahw7X12FY6+zsqrO4/O3UpKVPzSv\nWzTPAQAAgPBEFOzqti+f++wL/3rP9tXOaiV46cS3/YsmRfIcAAAA0EZzsPP9uHjKeVe+urUu\nHt0AAAAgYhqDnXfTI+Muf3Vrg4gYuh07ckzvsjc/+UFEJO/sP5zefefWz1Zv2FMvIpmDfnfR\nb3KMIiflx7xlAAAAtETbzRP733r4qY0NIiI9x7721eYPFt407OAjw2545d+LPlz/0/f/e2hE\nD5HabzfuOeHmv8+bN21YkN0BAAAgdjQFu/rli5bWiojIydOfnXxkC0f7UnPPevD9d2+xGpRd\ny2+Z8Mf/VcekSQAAAISmKdhtXrfOLSIiR5875qhWqzqd9qe7x6SK+L6fff+8n6JrDwAAAOHS\nFOz27dunLvTv3//g5kaDuuB2u3+p6zFy5FAREf8Xi5c4ou8RAAAAYdAU7Pz+g8PVmc1mdSEz\nM1NdqKqqalKYk5OjbrB9+/fRdggAAICwaAp2vXr1UhfKy8vVhZ49e6oLdru9SWFlZaW64HQ6\no2wQAAAA4dEU7A4diJO9e/eqC8ced5y6hx1r1pQ31jWsW1eqLnXv3j36HgEAABAGTcGu3/HH\nW0REZGdZWb2IiHQaNeZMo4iI7+O/P1FSIyIitRtn/nlBhYiIpJx88tDY9QoAAIAgNAU7wxkj\nhqeKiNR/+N9PfSIiknv5LZOyRUSULc+cNfDU308475SBpz9Y7BIRkezLp03qEtt+AQAA0Apt\nAxRnjjhvdN++ffv2Td+y5uDJ1qzxf513Vb5JRKTevub9d5Z/uVOdbcxsve61Z87vHNt2AQAA\n0BptwU5yp7xnt9vtdvvn9w9pXDfuleJP/zZlxIBuJnWFKWvAiGkvfvLFnN9lx7JVAAAABKNx\nrtiWGXuddvM/Pr55dn3lvn1VvsyevS0ZpljsFwAAAOGLSbA7yJSelXNEVgx3CAAAgPBpPBUb\nL5WzRxpaNGZeZWBt7dcL7rv49KN7d01P79JrwGkX3v3GFqakBQAAiOkRu0So+uyu4ec8tdF1\n8Md9279YNPOL995fv9z2zAiGzAMAAB1ahMHOX/NDySefrSvdvqeiqqbe62+1cOgf/nr1r8Pd\n63GPfL3l/mODFDR8et8VT210GY84f+Yrz0454wixf/7y9GvuKip9dvKM35XNPTtDy2sA2rOy\nsjIRcTqdNpstZLE6BYy6CQBAx7QHu7pvFz5wy90vffhjXetx7hcTz9AQ7EKpeefZeT+I5N+6\n8O07T0sXERlw9h8XvrlrcMFz2/7517efPPtKrvBDR1FRUSEiLpfr8On8Qm8CANAxjdfYub96\nanTBpc98EF6qizHlf8s/qBcZcvU0NdWp0k694ZoTRTwfLFvpTXxPQBuxWCwiYjKZMsJgMpka\nNwEA6Ji2I3Zlz0+9d7V6N4Ox96mTp1015iRrTrf0FEOrW/QYqGH3O/91xeBZ35Y5lU698gef\nft4Vt9wx5fTcXwZO2b55s0uk2ymnHH34ZtZTT+0u6/dv2bJNJgU7kQvoiNVqLS0tzc3NLSws\nDFlss9nsdrvVak1AYwCANqQp2G1/84016lGxrDGzv1w29YjWA11EqsrWqfNZuHdtXf3W1tVv\n/3P+zBXLpw/rpD68d+9eEenbt2/gdn379hXZv3fvXpGmwe7OO++sq1OnwZCuXbt6PJ4DBw4E\nbKooivrf5g81F5Nij8cTctsAAZ2re/b5fOG3EWaxz+fTd3H83rrEF0f/ixTB++z3+ymOa7H6\ncbd5G0lVLCIUi4jf749TcZK8wMQUGwwhgktjcVVVVfh7jnlx4yfYvDjkl7+mYPfdoWuvf3Xd\nn2Ob6gyZA867Y8J1E4efMPCoHr7yH0tXvT7zoeeWff/pXRPvKvjmhcJ0ERGXyyUiaWlpgVun\np6eLSGOIO2T9+vWN78jxxx/fpUuX1t4Ov98f/p9JTcXS7DNo/LTC1+Izxq9niiMuFo15Kxl+\nkTS1kSTvsz4+QYrDrOTjpjhWxV6vhiu2kqTnFv/6B99EU7BLTU1VFwYOGqRlu9C6TZ69dHLj\nT0cMPHPyY2eeO+yiQZPe/vHVOSueKRyXJiIZGRki0tDQELh1fX29iHTq1Onw1cOHD1ezoNp6\nTU1N81Do9XoVRTEajY2vLYgIig0Gg9lsbrreaNQ8dqDRaGzaeWt71tRGixRF8Xq9Oi6O31un\nFkuL/+MRi+J4/CIlyYeSVMUS3oeSJMU+n0/90m9HPfNxR1ys6ePWfXFk77PZbA55xC6y4kR+\n3CG//DUFu/z8fJGNojH1RqrHhGsuyHr75cotW3bIuGNFpFevXiK7du3aJTLksMpdu3aJSM+e\nPQ/fwYMPPti4vHTp0lWrVnXp0iXgSWpra10ul9FobP5QczEpTknRfCdySk7oHloAACAASURB\nVEpK053Er+e6ujqv16vv4rq6uvZYbDAYYv6L5HK51D+E4bRBcRIW19fXezye8ItramqSpFhE\nKNZa3NDQoP6lp1gtrq6ujqA4ZFaLrLhz586xLXa73eq70bw45Je/pv/p7z9+gjp0yfqSEs3X\n90Sv/5AhGSIH1qz57vD1ZSUl+0XShgzhynAAANCRaTubM2jaAxf1FpGK+TPn/hjvAU8q3n3t\nvUqRToMG5akrTCPOHZUusvmfsz+v/6WsYc1Lr6wXSR113sj2No0GAABALGm8TKfnhFeKHj65\ni9R+/MfRV83bVOmLSRO1C68ruPTeecuKS3f8XN3g2m//9rP5D477zeUL94occeV15x4ata7z\n+Nun5InseP6Si55ZWVbZ0FC5/eNnL7no+W0ifa++bRKjEwMAgA5N80GuzIIHbBuH3DHpyhdf\nn3rCO4+cXFgwsG/3dFNr5SdeP/u6E0Lt01+/u2ThP0oWPh6w3pB95uOLnzrrl4nC0oY/+vp0\n26inNr135znv3fnL6kG3vz7z7IBbJwAAADoY7WcvXVsXvTB76dc1IuKv+WnN8p/WBKueODKM\nYNf5spfXdl3w+vyiVZvKfti5t96c1af/0NPPu/SmW646tffhmbHrGU+uXjv0sYf//tb/Nu88\nIF37DTlr0k0P3H/5kNCXTwIAAOibxmDXsOW588+642NnjLsw9x424bZhE24Lq7jzoMsee/Oy\nx2LcAgAAQHunLdjtnHfz3QdTnanPGdfc9IdzTz46N+iUYpb+UbUHAACAcGkKdj+88eoqt4iI\ndD9v7pr3r+kb4ynFAAAAEDlNd8Vu27ZNXfjVdQ+T6gAAAJKLpiN2jROUDRw4MB7NtFczZ84s\nLi4OWKkoijoTVMD8YyUlJQlsDQAAdCDapxTbJBpnsNW/4uLioqKitu4CAAB0dJqC3YDx44f8\nadNmkfXFxe4pY0JPtNyhZGRkZGdnhyxzOBzqBPBANMrKykTE6XTabLaQxU6ns3ETAICOabsr\n9ribHrt83gVv2PfPf2zWvefcflSr4xJ3RNnZ2YWFhSHLioqKXC5XAvqBvlVUVIiIy+Wy2+2a\nNgEA6JjGKcWyz59T9Mw5uSaXbcaoy15c6+TIE9A2LBaLiJhMpowwmEymxk0AADqm6YjdutlT\nXlor0u+EYzo5tm5/6/9Ofu/RE08vGNgvyJRiJ90wb9qw6NsEcDir1VpaWpqbmxvOcWKbzWa3\n261WawIaAwC0IU3BbsfKl19e3HSFa/f6lUvWB9ukcgzBDgAAICE0nooFAABAstJ0xO6cpzds\nuF/b/rPytdUDAAAgQpqCXbe8oUPj1QgAAACiw6lYAAAAndAU7Gx/Hjnyt789++yzH/o0Xv0A\nAAAgQppOxe7b8vFHH/tFxHJjnNoBAABApDQdsWucMcvn88WjGQAAAEROU7DLy8tTF8rLy+PQ\nCwAAAKKgKdj9atw49a7YdatW1cSlHQAAAERK0zV2csz1D1/24tj5u+uWPProut8/PiwjTl3p\nmdfrFRGn02mz2UIWO51OESkrK4t7WwAAoP3TFuykxwWzFz+xe+y9q756Ysxo35y5940/tqsh\nPp3plXp9osvlstvtYW5SUVERz44AAIBOaAp262ZPeWmtSN6wYzot/7rc9uTEQS/0Of7Uk47t\nm905rZVzuifdwFyxhzMajYqimEwms9kcstjtdiuKYrFYEtAYAABo7zQFux0rX355cdMV/rrd\nm/73n01BNqkcQ7A7XEpKisfjyc3NLSwsDFlss9nsdrvVak1AYwAAoL1j5gkAAACd0HTE7pyn\nN2y4X9v+s/K11QMAACBCmoJdt7yhQ+PVCAAAAKLDqVgAAACd0DjcSSBf7Z4dOxzl+6vc5q6W\nHn3y8ntnEhUBAADaRITBrsH+6WvPPz9v0UcbfjjgbbK3bvknjrxw6u23XHl639BjeQAAACCG\nIji+Vv/Nv6eeNHD49U8v+fKwVCci3gM71ix+cuoZA0+ZtuC7hhi1CAAAgHBoPWLn/2nB5LOv\nXOTwH9q+65GDj7fmdjO7Dzi2fbXlxyqviEj1pjmXn33AVDz/on7MSwEAAJAYGo/Ylb9x4/UH\nU50p56y7F6zf4/xxg23lsveXrbRt+KFiz4YFd5+VYxIR8e9aOPWmN52x7xgAAAAt0hbstr74\n5NJqERHp9ftXv1j5+CUnZDc95GfKHnrJ4ys/f+X8XiIiUvXuEy99E6tGAQAAEJymYLd7xYrN\nIiKSMuLB2ZPzTC0WmfKvnPPAWepjm1ascETZIAAAAMKjKdiVlZWpCyeNHdsnSF2fCy4YdmiL\nbRE2BgAAAG00BbvKykp1oVevXkELc3Jy1IX9+/dH1BYAAAC00hTssrKy1IXy8vKghfv27QvY\nAgAAAHGmKdj1799fXfhy2bJ9Qer2LV++NmALAAAAxJmmYNdv9OhBIiLiWfHwbe/+3ErVnndv\nfWiFR0REBo8Z3Teq9gAAABAubcOdDLnhzlGdRERk5/yLT5vw1H+/rz3s8drt/31ywukXL7CL\niEjnc6ffMDg2bQIAACAUjTNP5Fz14t+WFEx5v1yk4ft37hpT9FCfwScNHZDTJdVTtWfbxrVb\nHHWH5qToNfaFWVcEv8cCAAAAsaN1SjFj/2sXfui99sLb3txWLyL+ut2bP929ObAq4+jL/7Zo\n7lX5EcxECwAAgMhoDXYikjn0+oUbf3v13Oeef2XxJ6U/u/y/PGTI6D14xKRrb71j6qijOsWu\nydj47rvvxo8fH7BSURRFUQwGQ2pqavNNCgoKZsyYEds2vF6viDidTpvNFrLY6XRKk/EDAQAA\ngogg2ImIZA4Yc/usMbfP8lTs+Gb7bmdltSe1S1Z23wHH5nVvIR8lh4qKitWrV7d1F+Lz+UTE\n5XLZ7fYwN6moqIhnRwAAQCciDHaHpFryh1jyY9NKYmRkZGRnZ4csczqdLpcrHg0YjUZFUUwm\nk9lsDlnsdrsVRbFYLPHoBAAA6EyUwa79yc7OLiwsDFlms9nCP6KmSUpKisfjyc3NDb8Nq9Ua\nj04AAIDOcHsDAACATgQ5Yle7Z5ujJtr9d84dkJMZ7U4AAAAQWpBgt/wm64WLo93/xLf9iyZF\nuxMAAACExqlYAAAAnQhyxC4r/9e//nUEu/RX7yz9vkKJuCUAAABEIkiwG/nUxo0a9+basWLW\nQ/c9sfmXVGcwRNYXAAAANIrVqVjPbtvsG88ccMy50/+13ukTETH3G/5/s1f/fWyMngAAAADB\nRT+Ona983Rsz739o1orvD43na+p5yuR7/vLgDefkpUe9dwAAAIQpmmDnr9r6zrMP/OnZJVur\nD84Xa8gaPOmPjzxy27hjOsekOwAAAIQtwmDn2vHfFx66b+a/16mnXUUkc8DvbnnoL9MvHdqd\nG20BAADaguZg595tm/eX+/4yz+bwHFyTdsSI6//06L1/+E3vDjc/GQAAQBLRkMWU8vX/nnnf\nQ7NW/HDoYrqUXqdedc9fHrxh5BFpcWkOAAAA4Qsr2Pmrvl787J/+9OySbw5dTGfMGnLx9Ece\nvnWslfnCAAAAkkOoYOfasfyFh+6f+e/1v1xMZ73g1of/Mv2SIVkMUQe0mbKyMhFxOp02my1k\nsdPpbNwEAKBjQYJdefGLD0z/y7zVv1xMd+TIaX969N6rT+nFxXRAG6uoqBARl8tlt9s1bQIA\n0LEgEW3V0//30mp10dTjlCvvfvSBaWf1SxMRr9cb9v6NphQjR/aAmLNYLA6Hw2Qymc3mkMVu\nt1tRFIvFkoDGAABtKLxjb0r5mlfvPOfVO7Xvf+Lb/kWTtG8Wc4qi+P1+rVt5vd6amprGHz0e\nj4j4fL6mK0Vb0o1EmG20SFOx+kIojrjY7/cnrDg/P7+0tDQ3N7ewsDDkTmw2m91uz8/Pb7oT\nRVHCbyNJihP/PidzcZJ8KBEUiwjFFLdJsSHURKcJKK6trQ1eKSI+n6+14pCRg0HnAAAAdCLI\nEbuUjMzMqO95zUiSy/FMJlPINN1cSkpK586/TKJRW1vrcrmMRmPTlWpZDFqMuo0WaSquq6vz\ner0UR1xsMBgSVhzBb13AL5LL5fJ4PGG2kTzF4b91ui+ur68P/61LqmIRoVhrcUNDA8XRF4dM\nAgkozszMDFnsdrvdbneLxSG//IM8PO71MI5wAgAAIElwKhYAAEAnCHYAAAA6QbADAADQieiv\n+i9+ZtLTX4iI/ObORX8siHp3AAAAiEz0wc7+xeLFi0VE5JKo9wUAAICIcSoWAABAJwh2AAAA\nOkGwAwAA0AmCHQAAgE4Q7AAAAHSCYAcAAKAT0Q93MvjSRx4ZKiIycHDU+wIAAEDEog92x068\n//4YNAIAAIDoaDoVa/vzyJG//e3ZZ5/90Kfx6gcAAAAR0nTEbt+Wjz/62C8ilhvj1A4AAAAi\npemIXXZ2trrg8/ni0QwAAAAipynY5eXlqQvl5eVx6AUAAABR0BTsfjVu3FAREVm3alVNXNoB\nAABApLSNY3fM9Q9f1kdE6pY8+ug6V3w6AgAAQEQ0DnfS44LZi5/YPfbeVV89MWa0b87c+8Yf\n29UQn87akbKyMhFxOp02my1ksdvtjn9HAACgI9IU7NbNnvLSWpG8Ycd0Wv51ue3JiYNe6HP8\nqScd2ze7c1orh/5OumHetGGxaDSZVVRUiIjL5bLb7W3dCwAA6Lg0BbsdK19+eXHTFf663Zv+\n959NQTapHNMBgp3FYnE4HCaTyWw2hyyur6/3+/0J6AoAAHQ00c88AbFaraWlpbm5uYWFhSGL\ni4qKXC6uTwQAALGnKdid8/SGDRqnD8vK11YPAACACGkKdt3yhg6NVyMAAACIjrbhTgAAAJC0\norzGzle7Z8cOR/n+Kre5q6VHn7z83plERQAAgDYRYbBrsH/62vPPz1v00YYfDnib7K1b/okj\nL5x6+y1Xnt439P2hAAAAiKEIjq/Vf/PvqScNHH7900u+PCzViYj3wI41i5+cesbAU6Yt+K4h\nRi0CAAAgHFqP2Pl/WjD57CsXOQ4NxJbS9cjBx1tzu5ndBxzbvtryY5VXRKR605zLzz5gKp5/\nUT/mpQAAAEgMjUfsyt+48fqDqc6Uc9bdC9bvcf64wbZy2fvLVto2/FCxZ8OCu8/KMYmI+Hct\nnHrTm87YdwwAAIAWaQt2W198cmm1iIj0+v2rX6x8/JITspse8jNlD73k8ZWfv3J+LxERqXr3\niZe+iVWjAAAACE5TsNu9YsVmERFJGfHg7Ml5phaLTPlXznngLPWxTStWOKJsEAAAAOHRFOzK\nysrUhZPGju0TpK7PBRcMO7TFtggbAwAAgDaagl1lZaW60KtXr6CFOTk56sL+/fsjagsAAABa\naQp2WVlZ6kJ5eXnQwn379gVsAQAAgDjTFOz69++vLny5bNm+IHX7li9fG7AFAAAA4kxTsOs3\nevQgERHxrHj4tnd/bqVqz7u3PrTCIyIig8eM7htVewAAAAiXtuFOhtxw56hOIiKyc/7Fp014\n6r/f1x72eO32/z454fSLF9hFRKTzudNvGBybNgEAABCKxpkncq568W9LCqa8Xy7S8P07d40p\neqjP4JOGDsjpkuqp2rNt49otjrpDc1L0GvvCrCuC32OBDm/mzJnFxcUBKxVFURTFYDCkpqY2\n36SgoGDGjBkJ6Q4AgHZG65Rixv7XLvzQe+2Ft725rV5E/HW7N3+6e3NgVcbRl/9t0dyr8iOY\niRYdSnFxcVFRUVt3AQCATmgNdiKSOfT6hRt/e/Xc555/ZfEnpT+7/L88ZMjoPXjEpGtvvWPq\nqKM6xa5J6FxGRkZ2dnbIMqfT6XK5EtAPAADtVATBTkQkc8CY22eNuX2Wp2LHN9t3OyurPald\nsrL7Djg2r3sLJ88QqerqahEpKSkZP35840r9nanMzs4uLCwMWWaz2ex2ewL6AQCgnYow2B2S\naskfYsmPTStozu12i4jD4eB8JQAACCnKYIdE4EwlAAAIB8GuHeBMJQAACEeEwc5f80PJJ5+t\nK92+p6Kqpt7rb7Vw6B/+evWvI+wNAAAAGmgPdnXfLnzglrtf+vDHutbj3C8mnkGwAwAASAiN\nwc791VOjh9+1ujI+zSAqHeQWWgAA0Bptwa7s+an3Hkx1xt6nTp521ZiTrDnd0lMMrW7RY2BU\n7UEDbqEFAKCD0xTstr/5xhqviIhkjZn95bKpR7Qe6NBWdH8LLYckVWVlZSLidDptNlvIYqfT\n2bgJAEDHNAW77w79XfjVdX8m1SUp3d9CyyFJVUVFhYi4XK7wP0d1EwCAjmkKdo3HRAYOGhSP\nZoDQTCZTbm5uyLL2e0gyTBaLxeFwmEwms9kcstjtdiuKYrFYEtAYAKANaQp2+fn5IhtFxOv1\nxqkfIASz2azvQ5JhslqtpaWlubm54b8bVqs1AY0BANqQUUtx//ET1KFL1peUeOLSDgAAACKl\nKdjJoGkPXNRbRCrmz5z7YzjD2AEAACBRtAU76TnhlaKHT+4itR//cfRV8zZV+uLTFQAAADTT\nPPNEZsEDto1D7ph05YuvTz3hnUdOLiwY2Ld7uqm18hOvn33dCdG1CAAAgHBon1LMtXXRC7OX\nfl0jIv6an9Ys/2lNsOqJIwl2AAAACaEx2DVsee78s+742BmfZgAAABA5bcFu57yb7z6Y6kx9\nzrjmpj+ce/LRuUGnFLP0j6o9AAAAhEtTsPvhjVdXuUVEpPt5c9e8f03fWE0+4XGW/u8/ixcX\nfVCy9fsfdpY3pPXIG3zamCtuvWtqYW7TDitnj+x+w0ct7GD0P/avmJIVo24AAADaJU3Bbtu2\nberCr657OHapTqT+zWsHT363yYoGxzefL/7m8yX/evPpj5bfMTQjZs8EAACgX5qGO/H7D45d\nN3DgwFg2YTD3OG7UNQ/MeXf1xu927a+rq/hx7eI/n3ukyV/xyV1XP/ttYPlxj3ztD8DhOgAA\nAO1Tim0SEY8nphNPpF00b8tFTVccOWzCn4ry63994mPfbFr63933HdMnlk8HAACgS5qO2A0Y\nP36IiIisLy52x6WdJswnnFnQWUQaGhri/VQAAAB6oG3mieNueuzyfgaR/fMfm/W9EqeWDvlu\n3foakb6nn54X8MDOf10xOLdLmrlT937HFV48fc5njni3AgAA0A5oHMcu+/w5Rc/s/f30D20z\nRl2WtvDF60/KbnXSiaj4drz4fzO/ki6jH55+WuBdGlVl60pFRMS9a+vqt7aufvuf82euWD59\nWKeAumeffba+vl5d9nq9jVcIhs/r9dbU1DT+qJ6B9vl8TVeqZVr3nCQCXqD6Qpq/wNa2jb44\n3m9dgl+g3+9PWHEEb13Au6EoSvhtJElx4t/nZC5Okg8lgmIRoZjiNik2GELc+JmA4tra2uCV\nIuLz+VorDvnlrynYrZs95aW1Iv1OOKaTY+v2t/7v5PcePfH0goH9gkwpdtIN86YN0/IcIiKy\nb/mN5926suaIy9/+17VHNFlvyBxw3h0Trps4/ISBR/Xwlf9Yuur1mQ89t+z7T++aeFfBNy8U\nph+2l/fff7+qqkpdPv744zV3IeLz+RqjYZCVjR9Au9PiC/T7/c1XtibK4ni/dW3+AuNXHMFb\np+N3I1bFIkJxAor5uCluk2JNl3bFrzjKFxjyy19TsNux8uWXFzdd4dq9fuWS9cE2qRyjNdj5\ndr97wzkXz/0uZ/y8j1+d0Ouwx7pNnr10cuNPRww8c/JjZ5477KJBk97+8dU5K54pHJfWtPrE\nE0+sq6tTl7t27frdd99pakREDAZDampq44+Kovh8PoPBkJKSElCmdc9JIuAF+nw+RVGav8AW\ntVb817/+9csvv2xerx4xDXiv1q9fLyLV1dWR9R9SmJ9giyIoFpGmTxfX4gh+61r8uMNsIya/\nG8lfLFrejXgUa/rdSJKe412s449bU7Hf71cP1cS8OEleYAKKU1JSQn5zJknPQT7BkC9B+1yx\nceXZ/vqVo65Z+GO/i/758RuT88PprseEay7Ievvlyi1bdsi4Y5s+8vTTTzcuL1269LPPPtPa\nTmpqardu3Rp/rK2tdblcJpOp6UoJ70NKTgEvsK6urq6uzmg0BrzAFrVWvHHjxqVLl2pqw+2O\n17048XiBSVIcwW9dwLvhcrlqa2vDbEMtNhgMFCdPcX19vXoCSN/FIkKxiDQ0NKj/D0xxNMUh\nU1FjcdeuXduw2O12q6ccmxeH/PLXFOzOeXrDhvu1bCCSlR9+bd2mv04cc8cK54CrFnz08oVH\nxOfiPSRGRkZGdnZ2yDKn0+lyuRLQDwAAHYGmYNctb+jQeDVSsfr+353/aHH9cdOWrHzx/Jzw\nzzJVvPvae5UinQYNyotXb9AuOzu7sLAwZJnNZrPb7QnoBwCAjkDbcCdx4tu99Ibhox4t9pxw\nx/urXmo11dUuvK7g0nvnLSsu3fFzdYNrv/3bz+Y/OO43ly/cK3LEldedm97yZgAAAB1DUlxj\n9/2/Hpy9xSUiG549p+ezAQ92u/7DytkjRUT89btLFv6jZOHjARWG7DMfX/zUWcwoCwAAOrak\nCHZh6nzZy2u7Lnh9ftGqTWU/7Nxbb87q03/o6eddetMtV53amyvyAABAR6cp2H14x+DbP9C2\n/1HPbXn2nFBFA+5e6787jJ2Zew+bcNuwCbdpawEAAKBj0BTsDvxUWlqqbf/HHtBWDwAAgAhp\nCnYpGZmZma085vc21DU0TnNhSstMTxERyWhP53oBAADaM013xY57vaZVtfXu+n3ffDj7poIe\nBpGU/EmzNzprampeHxevzgEAAHCYGA53YkjrcczI6//+yWezRmY1fPPa5OF/WFIeu70DAAAg\nuNiPY2c++oaX7jnZILJ7wZQ7/lMb8/0DAACgRXEZoHjAuHGDRUT2vzX37cp4PAEAAACaic/M\nE3l5eSIi0rBhw9dxeQIAAAAEis9Nq43zupeXc5kdYsvtdttstpBlTqdTRMrKyuLfEQAAySIu\nwc716adr1SWLxRKPJ0AHpiiK3W4Ps7iioiKuzQAAkFRiH+yU3UtvmT5fvbTOXFBwQsyfAB2b\nwWBIT08PWeZ2uxVF4f8sAAAdiqZgt33FC8u3tfqo3+vab9/62X+WfFhW5RcRkdyrbxzfKbr+\ngADp6enjxoUeHtFms9ntdqvVmoCWAABIEpqC3YZ5N9+8ONziTsPuefOpc8h1AAAACRKPu2LN\nOSdd9OA7m1Y/Vtg1DnsHAABAizQdsTvl1teDTBFmMKVldrXk9B/y62N6ZRii7gwAAACaaAp2\nRxZecUVhvDoBAABAVIIEO5+n3q2IiJjM6anxGcgYAAAAMRMksC25NEN16ZLE9QMAAIAIcSQO\nAABAJwh2AAAAOhGfuWIRI16vV0ScTmc4s6O63e74dwQAAJIXwS6p+Xw+EXG5XOHPjtrmysrK\nJOww6nQ65VB+BQAAUSLYJTWj0agoislkMpvNIYvr6+v9fn8CugquoqJCNIZRNb8CAIAoEeyS\nWkpKisfjyc3NLSwMPYBgUVGRy+VKQFfBWSwWh8MRZhh1u92KohiNXOsJAEAMEOxiQNPJR91f\nCWe1WktLS8MMozabzW63p6TwewgAQAyE8wd18YWGiGcIm/i2f9GkSDduLyI4+QgAABBzHCmJ\nAU0nH5PkSjgAAKA/BLsY0HTyMUmuhAMAAPoTTrC74LX9r10Q4f7NmRFuCAAAAG3CCXapnbKy\nsuLeCQAAAKLCqVj90DRNhToysHo/LwAA0AeCnX5EME2Fej9vm3O73UxToVUEM3yQ4wFA9wh2\n+qFpmgp1ZGCLxZKAxkJSFIVpKrSKYJCdJMnxAID4Idjph6ZpKtSRga1WawIaC8lgMKSnp4cs\nY5qKpiKY4SNJcjwAIH4Idmh76enp48aNC1nGNBVNRTDDR5LkeABA/HDwAwAAQCeCHPz4/TyH\n4wURkfTuieoGAAAAEQsS7NKycnIS10j81dbWisa7CD/66KPx48c3rlQURVEUg8GQmpratLik\npEREqqurw2lD06Akbrc7nH0CAABIh7rGTg1Jmu4irK6uLioq0rT/kCIYlAQAACAcHSjYqTcP\narqL0Gg09unTJ2Sx0+kMf/pXTYOS1NfX+/3+MPfcQWga9I6R2wAAHUoHCnaZmZkioukuwrS0\ntPCLw2xD06AkRUVF4UfGDkLToHeM3AYA6FA6ULBDYkQwI4KmySQ0DXrHyG0AgA6FYIcYi2BG\nBE2TSWga9E7HI7cxpRgAoDmCHWIsghkRmEwiAkwpBgBojmCHGItgRgQmk4gAU4oBAJrjDyrQ\nLjGlGACgOU6BAQAA6ATBDgAAQCcIdgAAADpBsAMAANAJgh0AAIBOEOwAAAB0gmAHAACgEwQ7\nAAAAnSDYAQAA6AQzTwDtUllZmYg4nU6bzRay2Ol0Nm4CANAxgh3QLlVUVIiIy+Wy2+2aNgEA\n6BjBDmiXLBaLw+EwmUxmszlksdvtVhTFYrEkoDEAQBsi2AHtktVqLS0tzc3NLSwsDFlss9ns\ndrvVak1AY0HMnDmzuLg4YKWiKIqiiEiLCbWgoGDGjBmJaA4AdIFgByBBiouLi4qK2roLANAz\ngh2AhMrIyMjOzg5Z5nQ6XS5XAvoBAD0h2AFIqOzs7PBPHyegHwDQE4IdQgtyaZTBYEhNTW26\nvqSkJIGtAe0VVxwCiIeOEux8Pp/f72/rLpKOoihNz3Z5vV4R8fv9AafAPvvss/feey/RzcVC\nwAv0eDzS0gtsUZIXq3/+NWnzd6M99hy/4gj+WbX4D1ZEwmmD4oiL1U+wzYuT5N1o78UGg6Fd\nFDd+WzYvDvlF2lGCHamuRT6fz+12N/1RRPx+f9OVjevDvDTK4XBE8Pc7TgJeoNpY8xfYoiQv\nVj8UTcL8uFvbVl2IpjhWPUfZRpIUa/pnpV5xGP0n2B6LJVk/wQQXN/4Vi3lxkrzAxBSHTFSN\nxY2xu02KGz/B5sUhv0g7SrAzmUwhP84OKDU1tVu3bo0/1tXV1dXVqhs2ZQAAIABJREFUGY3G\npivVMgn70qiioqLkueY9zBfYoiQvDjgDHo6Ad8PlctXW1obZhlpsMBiiKY5Vz1G2kSTFmv5Z\nqVccBrwb9fX1NTU1YbbRfotFhGIRaWhoqK6upjjK4pBJoLG4a9eubVjsdrurqqpaLA75Rcpc\nsQAAADpBsAMAANAJgh0AAIBOEOwAAAB0oqPcPAHoTFlZmYg4nU6bzRay2Ol0Nm4CANAxgh3a\nGbfbHU6U+fnnn0Xko48+Gj9+fOPK1gZVVrWv0V8rKipExOVyhT89g7oJAEDHCHZoZxRFCT/K\nVFdX63XWeYvF4nA4TCZTi1MUBHC73YqiWCyWBDQGAGhDBDu0MwaDIT09PWRZfX293+/X8Xzz\nVqu1tLQ0Nzc3/FHQrFZrAhoDALQhgh3amfT09HHjxoUsU8dJZr55AECHQrADDiopKdHrBXkA\ngA6CYAcc5HA4Yn5B3syZM4uLiwNWqpFRRFq8PI7ICACIGMEuqXm9Xgl7SItw5kJGECaTKTc3\nN2SZpgvyiouL9Xr3BgAgCRHskprP5xONQ1ogYmazOU4X5On4Hg4AQFIh2CU1o9GoKEqYQ1qo\n94EmoCtoxT0cAIDEINgltZSUFI/HE+aQFup9oAnoCgAAJCfmigUAANAJjtgBSBDmtwWAeCPY\nAUgQ5rcFgHgj2OmHprFROByCxGN+WwCIN4KdfkQwNgqHQ5BISTK/LaNGA9Axgp1+aBobhcMh\n6LDiN2q0pshYUlISjx4AdHAEO/3QNDZK/A6HAO1CPEaNjiAycu0EgNgi2KHtud3u8P+2qZcS\ngjtMoxS/UaPDjIy7du3y+/1cO4HmWjz06/P51G8/rhZAcAQ7tD1FUcL/26ZeSgjuME1aYUbG\nRYsWeTwerp1Ac8wxjWgQ7BBjEdycazAY0tPTQxarf9uMRkbVFuEO0/aPaycQHHNMIzIEO8RY\nBDfnGo3GcePGhSxT/7alpPBLK5I0d5gCiBPmmEZk+BuJGIvg5lwOwgEAEBMEO8RYBCeYOAgH\nAEBMcKQEAABAJzhS0qGVlJSMHz++8Ud1JFWDwZCamhpQlvDWdIJBSQAAiUSw69AcDgc31ccV\ng5IAABKJYNehmUym3NzckGUOh0OdEwlaxW9QEo4FAgCaI9h1aGazOcyRVBVFCTNDuN3uWLSm\nE/EblIRjgQCA5gh2CC2CoekQbwxQDABojmCH0DQNTVdfX+/3+xPQVQfHAMUAgOYIdghN09B0\nRUVFzG8DAECbYBw7AAAAneCIHdoZt9vNPRwAALSIYIe2F2ZWU8fsUBSFezgAAGgRwQ5tT2tW\ny8jICFmj3sOhaZg3r9cbzrNXV1dLK5N2iEjA/SXqpB3qJgAAxBvBTj/UXBKPKBNvBoMhPT09\nZJk6Zkdqauq4ceNCFr/11luKomgaokUd1SWcNkTjpB1hnheOIDICANAUwU4/IhhtLswoE2/p\n6enhZDV1zI6UlLB+aTUN0aJGRqNRw71EGRkZ2dnZIcucTmf49whHEBkBAGiKYKcf8Y4y7Yum\nIVo0RUZVdnZ2+HsOf7cSdmTUNM+bpmOBZWVlFRUVFoul6bh3rRWrCgoKZsyYEbIN3U+DpukF\ncn8PWqP7fymIK4KdfsQ7yiAxwoyMmsYLjOBYoMPhKC0tDbO4pKSkuLi48cfWUuD27dtF40Hl\n7du3xzyMfvTRRxKfv5oRzPMGNMeEgYgGf9eBjkLT6WOTyZSbmxuyWD1wqCkyhnlJpXr7S319\nfZzCaDzyZU1NjYgYjca0tLSQ+9Q0R0v8jrnGr1gV5tFcNMWEgYgGwQ7oKDSdPjabzeEfONQU\nGcO8pDKCPYcZRnfv3u3z+TRNkacpX6alpYX/AsPcZ7yPucavOMyjuUkSRpOkWD381rlz5/PO\nO09CUf/BVlRUNA39Pp9PvTcunDYobkfFIlJeXi5BEewARCt+VxzGL4xqmiIvHvfKRCAeMTeC\nA7SaLgNtj2E0SYo1XYXJ+9xxivv375+VlRWkgGAHACHEI7lqGp9I/Rsfj5gbQSbWdBmophT4\n/+ydd0BUxxPHv0fv0kGKiGDvDXtQsWFXUOyK2DUx/tTYTdQoiRpjb4iKqIBg74q9995BQAUB\nUXqHe78/jnIcx90bvIttP3/Bu7m92d3ZffP27c4oQ/grcXPLoYZcMXGU55orbwvHF1fjK9GZ\nWkG5MsyxYzAKYMnKGP8l5YhP9C1C8gKVIfyVuLnlUEOumDjKc82Vt2r+xdX4SnSmVlCuDHPs\nGIwCvq1kZeVY72F8VZDiE5FOWjAYjB8Z5tgxGAWQTmv+B/rI5gdZ7/mOIcUnIp20YDAYPzLM\nsWMwCiCd1vwP9JENW+9hMBgMRmmYY8dgfJOw9R4Gg8FglIY5dowvD89TC6LTQKK9ZQwGg8Fg\nMErDHDvGl4d0akG0t4zBYDAYDEZpmGPH+PLwPLUgypyjoqLyH6jEYDAYDMa3CHPsGF8enqcW\nRDGH1NSY0TIYDAaDIR22+MFgMBgMBoPxncAcOwaDwWAwGIzvBObYMRgMBoPBYHwnMMeOwWAw\nGAwG4zuBOXYMBoPBYDAY3wnfoGOX/ixgjkerahYGWlr65o4t+83c9Tj1S+vEYDAYDAaD8eX5\n1iJHpFz5zbnjsvuFyZE+hF8L+fva4SN3j1/6p53RF9WMwWAwGAwG4wvzba3YZV+cM2TZ/UwV\n2+7LTr9MzMxMfHVmeW87lewnK4bOOMsyYTIYDAaDwfix+aYcu7T9K7ZEAvaTA4OndahqqKVl\n6Nh+amDQZEcgevvK4KQvrR+DwWAwGAzGl+Rbcuzyzx0/lQXUHTGupVj6Kc1m40c2AnJPHQtl\nyeEZDAaDwWD8yHxLjl34o0eZQAUnp2olr1dt1swIyH78OOzL6MVgMBgMBoPxVSDgOO5L68CX\ny7/atFkVXWv+wycL6pb44MXiujXmPjYbfyF+/U9ilwMCAnJyckR/f/z4cd++fVeuXNHW1jYx\nMZH7Wx8/fszMzFRVVa1YsaJihd+/f5+fn89TDeUJfyU6fyXt/C2W/JV0yndf8nevxlei83ff\nGspT4yvpwW9Rja9EZ2oFraysDA0N27Vrt2zZMulC3LfDqTHGABoufiH5QeTyJgB0hx8vebld\nu3aNC/H09GzVqpXcJmMwGAwGg8H4anFwcGjcuPG0adPKcpa+pXAn2traALKzsyU/yMrKAqCj\no1PycsWKFfX19UV/6+joGBoaduvWTeKrXOGCpUAgEL8eHh6elJRkaGjo4ODAhJkwE2bCTJgJ\nM2Em/MWFASQlJYm8nrL4lhw7c3NzIDo6Ohoo+So2OjoagJmZWUn5Xbt2Ff199OjR8+fPl163\nTE9Pz8zMVFNTMzQ0lKtAOYRVVVWNjOQH2PtKhDMyMjIyMphwuYVVVFSMjY2/oHBmZmZ6ejoT\nLrewQCDg8zbkKxHOyspKS0tjwuUWBmBqavqtCGdnZ6empjLhzxE2MTEp7Sd9ncI5OTkpKSlS\nhadPn37u3DkZ3/2WDk841K2rDSTfvPmy5PVXN24kApp161b9MnoxGAwGg8FgfBV8S46dajvX\nTlrAo+0br4otQmbf3LD1LqDeqWuHb2n5kcFgMBgMBkPRfEuOHfT6TBlVGYhYNaD/P6GvkrKz\nk8LPrhjQf1UYYD3iV3f5r0cZDAaDwWAwvmO+rUUuTefF/tMvdVr24PC0joenFV+uNcX/7/Y6\nMr7IYDAYDAaD8f3zTa3YATBovfTy7V2z+jd3MNPV0NA1rdLc/bedt66vaCd/ezuDwWAwGAzG\n9823tWIHANCrNWhJ0KAlX1oNBoPBYDAYjK+Mb23FjsFgMBgMBoNRBsyxYzAYDAaDwfhOYI4d\ng8FgMBgMxncCc+wYDAaDwWAwvhOYY8dgMBgMBoPxncAcOwaDwWAwGIzvBObYMRgMBoPBYHwn\nMMeOwWAwGAwG4zuBOXYMBoPBYDAY3wnMsWMwGAwGg8H4TmCOHYPBYDAYDMZ3AnPsGAwGg8Fg\nML4TmGPHYDAYDAaD8Z3AHDsGg8FgMBiM7wTm2DEYDAaDwWB8JzDHjsFgMBgMBuM7Qe1LK/Df\n8eTJk5kzZ0pczMvLy8/PV1FRUVdXl1tCOYQFAoGGhsa3Ipyfn5+Xl8eEmTATZsI/gjAATU3N\nb0VYKBTm5uYyYSb85MkT2d/9gRy7+Pj40NDQL60Fg8FgMBgMxmchY4FJwHHcf6nKlyIjIyMx\nMbH09W3bth04cMDe3n7lypVyC/Hz89u3b5+dnd3q1at5CleqVGnNmjU8hW1tbdeuXStXeMeO\nHXv37uVZsr+/f0hICM+Sd+7cGRwcbG1tvX79ernCu3bt2rNnj5WV1YYNG+QK7969OygoiKdw\nQEBAYGBgxYoVN27cKFc4MDAwICDA0tJy06ZNPEu2sLDYvHkzz5J5CgcFBe3evdvc3NzHx0eu\n8J49e3bt2mVmZrZlyxa5wsHBwTt37jQ1NfX19ZUrHBIS4u/vb2JisnXrVrnCe/fu3bFjh7Gx\n8bZt2+QK79u3z8/Pz8jIaPv27TyFDQ0N/fz8FCt84MCBbdu2VahQYceOHXKFDx48uHXrVgMD\nA39/f7nChw4d8vX11dfX37lzJ09hPT29Xbt2yRU+fPjwli1beAofPXp08+bNOjo6AQEByhDW\n1tYODAyUK3zs2LFNmzbxFD5+/PjGjRu1tLSCgoLkCp88eXL9+vUaGhrBwcE8hdXV1UNCQuQK\nnzp1at26dTyFT58+vXbtWjU1tb1798oVDg0NXbNmjaqq6r59+3gKq6io7N+/X67wmTNnVq9e\nzVP47Nmzq1atEggEBw4ckCt87tw50U3t4MGDcoXPnz//77//Ati/f7+KipwNWhcuXFixYgVP\n4YsXL/7zzz8A9u3bp6qqqkDhS5cuLV++HMDevXvV1OQsUV2+fHnZsmUAQkJC5L5wu3LlytKl\nSwEEBwfLXf29evXq33//DWDPnj1y1+GuXbv2119/AQgKCtLS0uIpHBgYqK2tXVpAX1/fwMBA\n6nd/lBU7HR0dHR2d0tf19PQAqKurW1tbyy1EecL6+vpUYTU1NSZMFRYNA1VVVSasVOEKFSpQ\nhVVUVJgwVdjQ0BCAQCBgwkoVNjIyEv3xIwjL9dXKLSzXVzM2Nhb9YWVlJddXExeW66uJC8v1\n1cSF5fpq4sJyfTVxYam+WlnCUr0XGbDDEwwGg8FgMBjfCT/Kil1ZVKpUycnJycbGho+wjY2N\nk5MTnwcgALa2tk5OTlZWVvyFK1asqHBhkc6Wlpb8hS0sLBQubG1t7eTkZG5uzl/YzMxM4cIi\nnU1NTfmXbGJionBhKysrJyenogcyPsJFD7581BCtRvAsmadwxYoVnZycROtJihUWqVHWOwWp\nJYuWwxUrbGlpSRXW1dXlL8zzmdvCwsLJyUnu07y4sNx1AnE1eAqTSjY3N3dycuKzH7xImM/5\nhiJhPofVioTlrvSUQ9jU1NTJyUnugpMIMzMzJycnuUtZ4sICgUDhwiKd+UiKC/MpnFSyiYkJ\n/5LLJ8ynqUklGxsbl0OYjxpGRkYiYT62VFQyT8MT50fZY8dgMBgMBoPx3cNexTIYDAaDwWB8\nJzDHjsFgMBgMBuM7gTl2/wVCofBLq8D4z0iOvH///v3I5C+tB6PcfJUD9thYGxsbm7HH/uvf\nJdnzt2j8StT56zCkb7FTvnuU2yk/+uEJaYSfWHs8DI6uk7o4EIQ7WKdl5QnUtXU1izY6ch+u\nrp4zf8OBK68+5Gha1HTu/6v3olENCjeUZ71/cPVetMCybtNGtqIN2/nx13ZsDL7yKkHFola7\nASM9mpiL+d25KTFv4rMNrCubaUvZ0Bn/KPRhHCzqdagr5XhCVszN40cvPXmXAn2bWq27uja3\nlrEzW4Zwfvzt4IAjD+LVbZr0HNK7bgUBkB97ad38P/0vPHufW6FK024jZ88aUb9ox3xG1PmQ\nXSHHLt559CLqQ1JqRp66jn4Fc7vqdRr/1NV9cL+2diXU4F1BqhqkpqPpLJXT0xr22wu3YC7E\nXa5sMRJW9191tww1lNnOJOMnjhSerZGXRRywyjMkfhXM+BgdHY2PGbK6Tx7lsQ2SPSvE+EnC\nnzFSpOucm5GUnsNXcw1dQ418siGRKvj5wpKdsv1/c1Oc+w/uVs+E5/2fOhUIk1+c2bvn8Pnb\nj55HxiWlZuRrGBhbVq7TpE3nAcPcmlhIHIX5/B6UbI1Jk7rwqFUJ4ey4p3effVAxq1qnlpWu\nAEB+7NXNf3r7n3/8JlXTpkEXz3nzxzQxLtDvs2akAsoYKQq4AQEAOIYkwW4A4BZMEQ44N9EK\n0Gy/Pqbok6TQCVUlD3PpNJx7M5PjuLQbSztVLBhUahXb/3Urg8t9saGDsZhdC0xc/rmXyXEc\nx6XcWT+ovrFo1tCybjN+y51k6Wq4zvfx8fEJjRD7IPnG8q62JU6radi6Lr+ZwnFcKEWYy326\nysW0SD9Va/eAd8LE0ImOJc/raNedcTWd4zgu/uwCF2tZB980rF0WnP3A0SroFkxUg1AyUWe5\nJsHPfqR8i7/OpO6mqaHEdiYZP3/hCJI9Z9MGrPIMiX8FSXZFaw2ZlGc+LLfxk4RJncL710X/\n8oQ68yuxNfjLiCqoWbHpgFmbQ8NShLJ/gTgVZDzZPrKhUZmnSTXseiw+F1f4kwrqQUlhXrLF\nwh9D57Q2K6iOinHjycfjuY8nxzuUrKBW7elX0jnqvVvur4vXS0E3II7jOObYlaYcltRrcl8d\noMLww0W9KbwzqxoAgYXLooP3o2Ki7h9c6GIhAFQaLXmed/O3KgCgWqFKw8ZVjdUgcPjtyIaO\nGoBefY9Zy9etmjugrh4AlRrz7uZx7/y6FkS6EGjp6agCgGbVwf5huaXVaN5cQvNo/27GAKBq\n1rCP16RJXn0amqsCgEnvwDhOspoyheO3ddMGoFap3aipU0e1s1WD0dAV8xsC+g3Gbzxx4+71\nY+tG1dEFIGjk/SL34eJGmgCg59hlwpIt+8/cePg8LDIyMuz5wxtn9m9ZMqGLoy4AaDZe8iiX\nUkE3X4oapJIDCToHkqZ+2gRN0ZnU3fxQejsHUYyfMlKCKPacupsyYJVmSLmfVUE+5sSrNUiu\njGiS4Yky3MFyjBSSzs2bAxCoa+vyYcD/SIakrNagdUpzoDiUh0C3SnuvxbuvxWRJ/wHSzM+9\n3dHNGICqZePenmM8eze2VBUYO01cH7xr3Z+TPVrYiiL+aNQcfyKB40jDSnmO3cc9/Y0AQKBj\n6ehoqaMCmHqtmFUb0KrWf4FP8P49Pn+4O2oCUGny10v6gOXbKaR5Q27dfijHLi8zlQ/+vQGg\n726CcLuu1gBar40t+q3Hc2sAMHAP/FR06WOguwGABr+vHWAAwGbQ3lghx3EJh0fYwdjMTBWV\nRp9MLZBNDBlgAsB09Inzv9gCUKnsvuVuYi4nTHlxYG4HK1Wo2LrviMgrKluqYye8MsUOgKDq\nmNMJBY9IwoTTox0FAKrPvutGEV7ZGoC686oIIcdxnDBi5U/qGurq0Oyw8V2RElFrnDUANFkU\n6K4LwLK3b3gZkwWXFebb2xKArnvAKUIFu3kS1PDeQyjZbQpB5ymUEUsypL4LKTpTuvs2xfiV\n1869pxKM/9AegvDcvoTW8JtMGLCLAigVpBiS367yV1A2wZTRvVRpjh3N+JU2UuiOHTStmw+c\n4xManip7PesaxZAWPlFWa5TDsXPb9vrs5pkejS0KlojUjOv0nLzy0KOPeSUq+J4y8y/ZMcoM\ngPWQffGiZhPG7x1sDUGlMScSOY7jct+f9+5iIQBgOepE8kXKXEdpDTfKXNdjU0dVAPaeR+KF\nHCeMP+JpD01NTai3XfumSInIlW3UADgt3kgesDwhzRuB6TKNkvvBHDvlPZi2bKMJwNWvuL0P\nD9YE0HZjgtjvf9jgDECnR/+qABzn3i+8/nB+NQBwmHOvWDbvws+WAGqOHVMNgNHg/WJvTPLe\nHRhbWxOqlQcFvskXr5qEYyeaYnR77/wo3goJ/r10ATRc3IUi/HNFAK3WvC/84P2aVgDQcnWM\nmPS7lc0B6LuOMwXQ4K8wmb3xyrs+ALP+QwgVbOtKUKP3QELJbp0IOvf+XyUNqJi1/nX3w7jE\nUvj1BICefgX/7uxDMKQOfSk6U7p7IMX4ldfOHfoQjH/MaIKwe0dCawztRRmwHoQK/kQxpP5D\nyBWsO2wZH2Z0JrTGGC+CPZ/+VVnGT5pySSPFuTtB58SoG4FLRndw1BcAEOjZtx+5aNeVt2W8\nWROZDU9Dcm6lrNYgVTC9D1BsGzlxt/f8NdrFQa/gXaKmdfOBc7aceZ0mcs2uUGb+7n1sANRa\n+FTsoycLagIqLf4p9JKyrk2tDkCz5+Jp5LmOJ26Uue6nsSYAai96VqjDs0W1AaDZv2/FavFm\nhROACn0GEgbsiOGETjkxhjBvjL8gU4rjOHYqViFom9gAuHfnTtEZKFGc+ZIh0wuirWckvAPg\n6OhYeL3gT7ErgGqNGlUBvAl7FQWgobOzvthn1r02ng0Z7fhu9xCXUfvflxlhOjIyEkAdZ+cS\nGQ5MnJ1rAwgLS6MIJyZCLO9fYapFlEyIIMqlkJmWkgJAXjoP0ecp8e8IFcxNJ6jxKYbSdBkE\nnXX/eXLPf2LV56sHtWw30f9ZroGhODrqAKCuU/CvJmWMZXxQVnfHErRQYjtnfiQYf/hrgnBC\nZokqyG6NFE3CgE2PJ1Qwj2JIH6LJFXy0YzofLpQ8bSe7NXL7E+y5w7/KMn4SpJFiOpygs2El\nJ49Zm0+/fP/67NZ5g+rlXd06b3ArO6taXX/+Z9/9D7kl1RB1JE9DUh6kCuqU6BR188b9ZmwO\nfRXz8tSG6W4NzITR1wMWj3JxqFi14xjvPbfCPxKmguSEOAD29vZiH1WpUgUQ3r//qOB/zeZj\nh9cDsm9cfVj+uU6B5CYmltS5sujPkqmdRP+lfIghDFhuAKFTctII80ZKirxa/VCOnb19ZQAN\nvV/myiSoLwDomBOEDRu6WgGxvrOXP8sW/VazLl2MgPvXrhXfbjKvXr0PoLqDlQGAnJyik1cF\n+YNKZjMS5WPKh5ompGUrMe++8ZRfv4ph2wZ0mHj8g/T6ivLllEpzJUq/lZvLUYStrQGEh4cX\nXi/48/Xr12LCov+MK9raArh16VKWdL0AAFmXL98GUMnSkFBBLWOCGhX0KE1nStC5EvRqDVl9\n+dnVtb1VDv/SumarSTsep5b1JXv7yuBtSKrqyupubYoaymtnNR2C8atqE4SFFHvWb0QYsHam\nhApqUwzJxoJcQbu2w/lQu2T+MjmjO5dgz1Ca8ZOmXOJIIVUQACDQrdzOc+HOK5Hvnx5bM7W3\nXULo2mluDa2tG7tP23D8ZeG367sSDKl2TWW1RnkqKFFdfceO45aG3Hv35kbA4pFtK2unhof6\nzPZw8ssgTAXGRvoA3r9/L/aR6D9x19bOzg5AchZH6UFSa9gThLl8IQCxfHhaoj9L5vESDSCO\nNCPl5xM6xZZy06xUSYaQCHlLet8TwpOjTQFBh42yz5UUrP3OpQgHR21sqwMAJk3Hbjz7OlXI\nCd8F9bdRVa8+POBpSj6Xn/I0YHh1dUCj9cprK1oCsJ91p6iQ8/OdnZ2d558XLzj874YAqo2b\nUBdA1XkPpP149rPVHU0A7QbTJrgAhav3nVY9E3FpViOUWGQWL7nqrA4U4UB3PQDW/XZG5XJc\nbtTOftawcXDQhK3nkYK9JMKPB4ZaA1Dv6nt7VjUBoGLbY+WVWGn7PHNjr6zsYasCCKrP3reA\nUMFeUwhqLJlFKNntb4LOt4sv57w9sahnZU2o23acd/h1FseV3t5EsbrOHhSdSd1NUUN57dxt\nOMH4f5lOEO5LsucHhAEbSjFRkiGdoEwFogqS9tjxbo2iK/LtWQwFGz9pyqWNlGLlSRUsUdm4\n28FLx3Wupi8oKUwwpDAl3oAoFeRR3/yk58fX/K93HRN3ylSwdk0XdUBQc/r1wn1iWden1xQA\nAuc1xce3ov5pCqDWxEmUHiS1BqWdm5dqDan2ULDJmzJ9zX5YdIXHSKHcNG9L+bwkP5Rjx2UG\ne2gBWn13Z8iSKtytSREO5rjYoxNqFqbM1jKv2aqbx/C+osA3Ktq62ioAILDsuvF5LvdsYT0A\nzZZFyCg397iXIaDTL+DwGDNAtdPmMmw07ebvzYoeFqRuy1Bz2fBe/Bs5R0YYADrugb0pwrnX\np4nWqtUMLC0N1AD1xt43gz0qAOp2bYb8PPXnIW0qqQOA6dCDyVzq5Rn1RfnRNc3rtu/n9cvM\n+QsXL168cP7MX7z6ta9rLno60m0w83Ja3ilCBd0CCWp8IpUcTNC5VEnPg6a0sVCFTo3+yy/F\nBkrMCxSr6zuPojOpu0nGr7x2Xkkw/qB7BOEpJHtOJwxYmomSDIkyFYgqSHLsCK1RokIy7Vmy\n9gozftKUSxspEsqTKigi79OTI2um9mlgpgYA7iFiH/Gf+ZV6A+JfQcI508ysNMrMn3pspAUA\n6NfvP33JsiXT+9fXBwCDvruLuyl8eQtVwPG36ycpPUhqDUo7ixw71/URRax3lbxSdJE2fUn8\nuhyr+4wbUGl+LMeOSzw6s1evXiM2P5UlVBgCiiTMcRzHpT3Z/T9X0SOdJJqWTYcuDX2Xw3Ec\nx71Y2rxCBcM2y8PLLDZz/2BDQG/QvvTcE6OMAPXOPp/Kkv145ufaok539nCRQodxu6OLpdOD\nPQwA/cH70xdQhDku4+5ad8eCFzwG9Ub4v8rjuLjDo6pqidVSv8m00wXbs5MfbJ3UzkardEuI\n5j+bdpO2PkjmOI6jVNAtmKIGsWSCzlIQfrq1cWT9ChAYGRlKjFiKIYVRdCZ1N834ldfOFOOn\njJRTRHvmON4DVomGRKjgTkrch4vlaI0SyLBnkrDSplzSSJGiPM8KClNehfrMGtjCShMA1Ezq\n9Jz878GHCRJrKnxnfiXfgHhWkGJIHHHmT7o4r0XJSMxatcebzqjvAAAgAElEQVQdKj40zL3Z\nNbFXr16/BL8jDitKa1DaeT4tdhVp+iqNbKv7nBtQSQQc919sUPyR4NLf3b965dbjiLjEtBwV\nbT0ji8o16jm1aGJvQNjQ+P6S38EnmdbOnj1qqoSfCbr2XsOxQ//mlmX9ZMy5jTtuJKNGn5m9\nq8spOfZawIkX2VatBnWqKn9Dr6Qwlx7z7FWChlXVKubaBbXJijoTEHzh6fucClWcuvbr1chc\nfGeCMC3yxrmLtx+9fBOflJaZr6qtZ2heqVrdJs7tmtnpFTZHLrmCfNWgl8xX5zLIizm3fNJ4\n72OR2egbkLWbciSwkHLpXDak7i71Q8ps57IRM35NOaJEYamtwWPA/ueGJK2C2fO0ypHUQQby\nbYNkzwowfhKKsDoZOme+vRKy3dd3654LkelQMajWYcBIL6/hvZ0syxxHCpn5FYzUCt4/cSIW\nlg27NLDgXQ5l5s+PvRG4bc/5p+8zNcyrtew1fEg7O+lDU8HzRvk4NNxw2EG+wr12JIlOs8pA\n/owke6QoZN5gjh2DoUS49PRMXV0d+YJ0cnJylH7ajvH1EOIuUKxjV4ASTfSbJPFh8JYNW30D\nTr1IFkLHrk0/Ty8vz35tKv2nLcQ6hfE5/FCnYmPfvs2VLwUAKfe2BN2W8Xl+enxkxJsP6fkK\n0YuhYO5tHjdu3LjN9xRWYDkSNmfH3AjyHtOxakVPflnbRTov2HkrnZd4bvhWj9+OEPT5QpRz\npCi8B78Deu1ITExM3NFLYQVSTfRbpByGdGZh/982nowxa+e1ePfVx7f2LZ/YraZORoJ0Unln\nleXJj9Ap5WGsjY2NzVjWJnzh9cL2OyHY3bDxxN3PZG6o5HKjQxe4VlKHW3B+3JNLly5dfVly\nB0D6Ax/PxqaiIEXqpo2HbrqXKv5x1ptzPr+Pd2vbqGYVG3NTc1uHmo3buU/4Y8uFd2XFk1YI\nYcfXrFmz5rgoumFmzP0zR4+evfOmSLO8uKtbF0zxGjJ09FTv3bfi8sv/QznJ0WGvXsdnSI/H\nHvfw9OnTpx9K/Uy2zpwim05iQzEvnWXm3CKktclNeHjg38k96pgUvpgosc9a3k80b27aacOz\nbHm/8drP3VaFWEHRP1mxT66eO3/9cXRB/FEu7/2V9RO7t6hd2bpS9WY9J2+89VFO6kgxCnuw\nXCNFJuVLOVoAxZAItkFruvyk56d8F/48tGfbpvVqVrW3q1K9bhPnHiOmLg24FZsj/hvCD9fX\nT+7VrJqViZGJVbVmvSavv/5BThd85kgpr4lKDthvgXIYEi1XLL+S5TddeTtFxPFJDg4ODpOO\nF1/5FH7r1q1b4WVuX5MPqbtLCqdHnvNbPNGjc/NalSuaGerp6hmZVaxcu3lnj4mL/c5Fyr4B\nlwGpHxe4uLi4LLhYlnoFHPI0MTEx8eRX5N1NY8eOHbvpLk91y42Cmu6HcuwODdIAoF1z0Lq7\nSVIF0p7snNjYSADAsOHiWxd+tgRUndeIbfzkPuwbZC4xtE3d9hQc60m99U8Pm7LejWlW6r3q\nLo/jLCLCT20QcarsTZrilC/DOkENjpKwmV8dxedcBTZdiZKVkyZcGvkpL05tmtG/aAuOqlHN\nTqMWbDvzMomfG12cO0TFtn/Qm7K/lBe5y8NOBVCxnXKV1imkXNf8dXbbQx4pPEtu7zmYP6tu\nchzJkCi2QWs6Qh70pNPjHSUTxmtUnRBa5hbpzxgpijFRPjdX0vSlPGGuXDov6FOBP8MOfqYa\nn9UpRTrP6yJZ/mc9GtFLKBZWYCZ78e4uVUFZUCKYlONuxVdnqj0rsOl+KMeOS7q3aXBNXQDq\nlbp6n48VHzn58ZeX9nHQBKBm3Xn+8bc5wou/2AAq7deLBwh4PK8mAMO2C09HJia+ubyym6UA\nQK35DzlOGL7eRXQ4W9ex89jf1/jvPXzy9MlDIX6r5o3uUEW0WcKgs08kP02Lnhpp44qUYZ2k\nBilhM6WObsEKbjqxkik6k1LWiH8/I+qy34IRP9mJ74dpufBeXA5HQqRGzwXTG2gBGjXGnZQ6\nfvPfBA62VwVUrHv7heWQOoWU65rUzgtpI4V/ydR88xRDojTdNlLTUfKg359bEwC0awxasuPQ\n8UM7Fg+opgUAtaRH9yrnSFGoifKZkUjTl/KEOaXpTEWKGorolCKdJZJJSv/Fz9dZrrBiM9mL\nd0qpCsriizp25bRnxTbdj+XYcRzHZbwMmNjUEICKmfOMQ1E5HMdlvto7rZWZCgC92kM23knk\nOI7jIrwbo0RWOI7jni6sBaD+oqL7XrxPZ3UATkvffNzVQxeAVr2Jh6NLu0y5bw+MraMBQL9P\nkPTFQglEYQ+dnZ3nnSLkMyZlWD/Jw7MrUmM+JWFzX0oOZrdgBTddsRqUNOF0x04UsLRL9Qqi\nQFX6Dh1Ge++5vbZnOefTookj7uBIRzVAt8mc6xKvLvOjQ4Y5qgECy+5bXmRzHEfpFFKu679e\nEXrQdTBppBBaw3Whvxx8vb2amqqCbEj5pKYbS2k6f0Ie9AeL6gMl86m/Wd9eE0CDP6W8AqOO\nFL4mmkcbsPK7r3jeOP8lhTnK/Zhasgh60yly3ijS2f0nya/LqjhF5767CcIKzmQv3imlKiiL\nL+rYlc+eZ81XaNP9eI4dx3Fc7ptjc9taqAKo0HD45AE1dQComDtP3/+6uE3PjzMF0GGLmDOR\n5OsqAKrNFnvPLsoNbDR6z9q2qoBq48UvylpAz3u6oKEKoCoRI1QepA0fpAzr855Q9JhNSNjc\nnLRJZbPim64g3zwlTbgbJYv2o5D/9alvJnqDpmHZuN/MTafDU4XipX2GY8dxeRHb+1gJAGOX\nf58UGaQwZr9XNXVAYN5l49OCq5ROIeW6bt+G0IOitPd8R8oZamuUgTDp4a6ZXe21AUDNsuW4\nddcfEwzpLKnpOhKarmdfQh70aYN1USLDOsdxMatbAtAbcaKU+tGECl6jmKgydpV9JXz+whWf\n8nnSZari5w1xNfg6dhSdSavmoqlAYZnsS+lcd9gyPhz6oo5d+RD5GwprOjVCr30/qNm6Ljrz\nqP3/OnRZdc9v1T1Ar+X8Ewf/aGUqti0mKSkJEgnj7t+7x0GzadN6xZfMzc0BpCbfOHslH6qu\nE8ZVK+ucsWrNiRM6Lhx98tKZC7njPCS31SgG6RnWX0pLURz75s0boBbvoqPKStgs/KmPzxAX\nTY0Lvn0qlrmrSAbCJ8pqurLThEvTefiTex6zR09ZPajlqUFLtqyc2MJMTB2xhM0AEBqwYv8D\nGDuNnPHbJM9eDc0UPIpUKw8POPXJtc3/zvyv02CTK0FD7VTijoxz8fB9mWvacc2Z/WML4yNR\nOkV6rusn0nNdZ/I8PS4qOZ0wUlJTCSWXQfbb0+vmz1i8494nIfRruC1Y4j21T1XdnAA3/oZ0\nwpzSdOmEpvsYnwGpedCf3b//CLAFCvKg/zP74Y3H0ekALCzE44lZWloCSEtLk9Q+5wJhpOwx\nCFGmiTIQfmLt8TDE8jvELiLpyooT33Wn5GXwzmT/gEcm+1I82jF9Og8xN4rn+pUgag6FNd13\nZ1o8SX20ffbUbQ9zINDR0crISLu5ddE6521z21csujuZmJgAca/Dw4H6oivvbt2KBZo0bCh2\nB0tOTgZgjNiHuUCdVq2MZfymSatW1XHy6cOHz+FRl6+i9vaVgciG3i9vTrOXIbbPQ91jnyjD\nemZOTg4gyk0iO0UxXx0AQFMTyC4jYXNS6yHbBnTQOnB+veiiDkXn1ChC0925FtlMfv5jfMgA\nCtKE89W5IIt2l8Hrp4ya9Uvrk7vHr/DxHlZHH9IQCADg070jgYEm+vq6gzpUq1B23KCMD5Hx\nPGZ/kc6FaNaecvBYQvsOS/aO7Dguf57G8tGbn+UYt/v3zMFJdYoDk1M6hZTrWtu8Mv8e1NAn\njBQDlcjISGprFCFMvLfLe8a8NaejsqBu1XbS73/P93IqONTw7CHBkN5mUZpOSEkTrqUPfHr/\n/j1Q1HRl5EF/mJyRCwACgfgTUcF/peOLkioYlQbwNlF7+8r8u/sroVzDSpHc2/Lzz3vRvmdl\natPx7JRykPGheHCJKi5+pQhdG4LOOpSpQNvUFgi/delS1uAOZeVPoGSyl8Su7fC2dvLFmqb5\n7SWX/YWxtVVo0/2Ajl1+7Plloz1/PxKZo2HXdfHWLZMrXZ09ZMyak390qHtiis/OJaIjFKjv\n5KSx7vCrXZvOzlzfXhfgXgftuQU4uLiIW1ZMTAwAa73c1wCsra1l/rKNjQ3w9MOHDwDy428H\nBxx5EK9u06TnkN51KwiA/NhL6+b/6X/h2fvcClWadhs5e9aI+hUadexoutzn/pmzSTPHmpZd\ntIoAAPQsHIC4iIgIoGBjuFltZ+cE1DYTl42OjkaxcWS9f3D1XrTAsm7TRrYi9y8//tqOjcFX\nXiWoWNRqN2CkRxNzFcDODngUFRUF1EMJVCoN2nnqY1LrXza4ddL1NAEAI4rO2Skf+DfdvXn2\n9hdlCoqhY0bQWYTAtNlE/7u9hvw9cdyfwxsd2jlz/aY53e1LxQ/vteHBgZZbtvjuOh6ybELI\nsmmVnft7jR7t6dbSWsqQPDbevl95phn95ouP7/3YusemLZ7DABi1WRp6+Nd62uIilE4hYVSv\no+khvj1o6OikAb4jJXW9vf0Jsj4AsiJOrpk3w3v3g0ROUKGuxzzvJb92qyK+8Vw0rngaUn4F\nZTWdYVUn9dAT97ZtuPHL0maaAJB9Y8O2e4CgQYPin4qNjQVQxVz3KUrdfcu6JYe9J1TQaPyD\nA658TZQ0yZRBVszN40cvPXmXAn2bWq27uja3FjfVjKjzIbtCjl288+hF1Iek1Iw8dR39CuZ2\n1es0/qmr++B+be20yyy4FAVLZUd/Xkw1JJ5zHQXSSLEa8+BAf76dUkRuSsyb+GwD68pm2lI6\nIP5R6MM4vMkGgOMT7CWcr9JXALjNHW0KvjrTpoIW/asd9H7pM7J/zUCfiS0tSrkXeXFX140e\n6RMHQfV+bgXDQZj84szePYfP3370PDIuKTUjX8PA2LJynSZtOg8Y5tbEQvwNTZOJ27fzic7t\n7jeJh5srupj/ITKS+oRAMiR+wvX79a+2lNp0MlDYO+JvgowXgb+0MBEA0K8/0udB0Qab3OiT\n8ztWVAWgV8/T91Eqx3FcyuHhZgBgULf3z7NmjW1fSR1Qabj4mXh5n7Z2VQNMRs3vrwr5L+CF\ne/oCUPXYn/t0lUvxa19Va/eAd8LE0ImOJaMnaNedcTWdljeamKKYEBuFlAedonOLloSma92O\nEIlg6AJlpwnPir66e7FXewc9AQCoGtXq/ot7fUnJg8MIOg/aUSLzdET4xb9+MgQ0qo3d8yhC\nkvjDhArScl2TUm7vIIyUP4aSY0nkJ9za/mt7Ww0A0Kzk8uv2OwnS9pjt9yAYUv8/KE3XnNJ0\n/oQ86N595U7QxZBGisd+0b+8TJQ0ybjOL5WcNPnG8q62JR5/NGxdl98smF4VGv+iSI2WzjRD\n4j/X0c5DkEZKMKVTOEJEHlprBFN0plWQmMmef2Ag0hY30iZC4gZTUkAxijCx6WTzQzl212dX\n0wagWrHdnCORpU6VCxOu/dvHQQuAZpU+K64mCLmEE7/UE18TMHBacKtE/Le0nb21AL0hB3bz\nM7sC6/fd1k0bgFqldqOmTh3VzlYNRkNXzG8I6DcYv/HEjbvXj60bVUcXgKCR9wta3mhKiuI8\nSmwUUsJmUg5mV0rTkfauknO3S8A3D7owNfyMz+xBzayKnrsrOv+8NOjaW3okTto29kBCBYnz\nFy0BOf+RQos1nR52dPGAugYCAALjBoOXnYgo+/s8LaQwLgOl6YhRV/jnQSc5/c4tKRUsKSbP\nRD9nwEb7dzMGAFWzhn28Jk3y6tNQlDXUpHdgHJer4PgXZVVQDpS5LkiZI0UceZ1CichD29RP\n0plaQf6Z7CmBgUjVpAyrdhThYfspN03SHZYjNZ08fijHLtgNOjUGrLohI7Z+6iNfz3p6KHrk\nzYq6sG3Jb5PGT5y6aMvZKMm7Svje36dOnbr+alpwqeUQqYie6bt5tgag7rwqQshxHCeMWPmT\nuoa6esmoB1FrnDUANPmLf/Q2fsRc3L5hw4ZDT7O4EEpsFC4nLNTf3z/omoyDqcLos+u9vb29\n91P0ITUdcf6i6Py8DIHc6LPefaobaGpqag7cJ/vn8j49Prz61171TQoe0QT6VX4aOufAa4LG\npDv9sIOECs6ilUzQWQTPkUIq8+BAdQBQNW0ycmXoq/gPMtnRh2RIlKbrTW66vPfXdy7536gh\nAwePnLxgy9lIRaSdUcRIUYCJSngSwitT7AAIqo45nVAwrwoTTo92FACoPvtqICGIw+6PlKUy\nylSQTpnr5lKWURVxTFJ6p+wPokRr+tqOKuenRlw95Ld68ZxpkyeOGzNu4uRpcxav9jt0NTK1\naLU99wQhMFDq11FNkiEd2lOu6GM8mk4+P5Rjd/9w8EseGReywvb9r7lJP/42JEzb2ZswF7R1\nrYgSMQ7er2kFAC1Xx4gV+m5lcwD6nicpNSQxixIbRWlafIehFrLf3wr8a2zHqgaCb0fnrxKS\nbVDX1b45FDlSPsNEJTyJx3NrANDtvfOjuFCCfy9dAA1HDSIEcejkrKwefECZ60Z5VgbQ0Ptl\nrkyC+irakEp2Ci1akyw18tLiIl5HxafxCUn/H3L9V0JgIFGKZLmtXZD4S0kaEw1pzGilRR+T\nyw91eKJ+dz4bL6Hp0Oefy83ff5IvmR1z44Cf7xbfwEybChUqyJcXoZKZCMDIyKjwQkEQDbEr\nAIyNjQFkZmbyLZbMO0psFKVpoaFLaDrdwq06fDYUW9TrUFcyq9V/goZlE48ZTTxmrIi8ELx1\nS2zhluOvWuf/HD6t8V6VYBva2iiHIf3HJEfej0iCoX2DyrxVLaJ8I6WMsqSbaDkQbUev4+xc\n4qyuibNzbRy8GRb5Phu8gziQguwUwG9n+jvKXJfrOIj/2QJFUrJTzl0LBiHClDD+6dWXn1Qt\nareoKnYbyXi4ZdLIWTvvJOQC6qaNByzesn5MA/EQCV+QuLg48A4MdGNSYuJ2aOjKKTP81KZN\ne+HGHVl4FN02v9vUVcE6kwwp/LWQvzAt+ph8FOom/ih8XsLmezMcANRcUOSiP/6jBkpeKbpo\nPuEiMU04f+GJFgDaby7eZCTt4S9rZ3cAWkNIJVOFSZDSv36LOouRlxxx7+Lpo/tC9h09ffFe\nRLKUZ+68uFsBK3+fOfvPtfseJom0zXt/cdXoTk2q2Vrb12nTf8a2+zwyduTnl1rn51tyTnrp\niM5lkp6jsAS+/wEKT3tfvnopXA0p8DZ+iSqcGKEHoOsOif2kmf7dAGh1c3cAYDH2tKw3JZmn\nx1oAqOo5vDIoS2X8d6ZfIs11+4jnIRQ1b5Qcg6dGGkAi7nehXNSufrYCaNQafyxeifmay8H9\nbVPmrDrwIIHXbsmzY4wBNFoqvg/g9dJGACp4nS66kr2rBwCtIUd4KVDQGpRRdnGBi4uLy4KL\nxVekDrdDniYmJiZduxEMqVN3itUd4RQ2mXM/2KvYz+Zzs2iLSAt01wNg3W9nVC7H5Ubt7GcN\nGwcHTdh6HimwAeHHA0OtAah33ZpEShNOEV7REoD9rDtFF0TpTUomQgn/uyGAarNp2crJqc15\nQ9pQrDyd065tnDFjxp8HIspUNOLAohkzZmy8llauTdB5MRfWTurgqF/ynLSqgWOHSesuxRb5\nd5Tj1RyXm5mampqWJe4dCuOvrBztUt1MSwUq2ha1u/zscy+JWjLxFaHytoQrHgk1cpKjw169\njs+QfteOe3j69OnTD+MIBZZPDb4QHA6C8YuU6bTqmYhLsxqhRFoOEaJ5o+qsgFnVBICKbY+V\nV2Kl3e5zY6+s7GGrAgiqz751crQpIOiwUbbrUY682G9Jc91D0nEB2lzHfwy+WFAXQFXpSYOz\nn63uaAJoN5g2wQWA4vM1ly+T/bwuAADNik0HzNocGpYi071N3NJFHRDUnH69cPtl1vXpNQWA\nwHlN8SCK+qcpgFrzZfZFEeVw7Hgm7RBd7DacYEi/TCdZHW0yl8MP5djdXDWYP6tuin1TQVm0\nC8i9XhDrUc3A0tJADVBv7H0z2KMCoG7XZsjPU38e0qaSOgCYDj2YTEoTThJ+RomNojw1KJBS\nfLotU5rOaYHu2oD+gJCyFyIygz30AO3+e1JIOgdzHJd8dZGzeeEIV9O3qFS1WtVKFvqF2yZU\nLNsvuZ7CcRwXTzlenX1uohWg2X598U7OpNAJVSXvSToN597MJJW82g2AQF1blw+DF5d/SziP\n9UsFU6wG71VGZWz1K5djR3E4KMYv1Y9Xk0j3l3NkhAGg4x6YTgniQIm6QsuL/ZgWB4oAad6g\njME8SoQphedrLl8me5HxFwbcFuhWae+1ePe1mDLOzaQSAgPd5Ld0omzHjhZQ7B5F+A0pVoY8\nfijHjr77mJiwOSv2ydVz568/jk4reFTJe39l/cTuLWpXtq5UvVnPyRtvFRzIzbi71t2xwEs0\nqDfC/1Uex8UdHlVV/KCzfpNppz9yryhpwknCHEeJjaI0NY5PcnBwcJh0vPjKp/Bbt27dCpcW\nh+IhJcVn5wGU1qDonLmnvy6gPSBE1i0gI9hDG9AbsGIGbRN0pE8XEwDQruq2MOhWTHrhU68w\nPfpW0B+igDww7bb1DcetJByvXri7rw5QYfjhIl9UeGdWNQACC5dFB+9HxUTdP7jQxUIAqDRa\ncplS8mBXANC0bj5wjk9oYe7LsiD1IGn9kvTY9u9l0gFMwiojybEjZVgv60b10G/q1KlT/STW\nYSgOB2nAil5dSdJh3O7oYpn0YA8DQH/w/nSOowRxICyVbZ5A2plOmetk/bgkpKZLpYzB55Ro\nTQrP11y+TPbuPwGA27bXZzfP9GhsUfBiS824Ts/JKw89+ljqSYx/YCD5SnAc9x84dsEkQyII\nv6ZMufJjZfxQjl3URX85+Hp7NTUV3T6oCZs/hs5pXZDcCCrGjScfj+c+nhzvUPJepFV7+pXC\neUOYFv3k3oNXcRnFzyKZkaFbl82b9r8Zi9buvROXx3Ecd3w4IU04SZgPxbFRlKYGKX314cGa\nKLlrQUTc4dHV1aBW1XNfjLCohNZtKa1B0fn5H3UA1PjjsRQVi3n0e3UA9Qb3JOjstuPAEGMA\nuk0X3EiVViiXfG1eYx0ApsMOpfxMOF7dfbI1gNZri2dN0XlGA/fAYs0+BrobAGgwZhih5OEB\nNwKXjO7gqC8AINCzbz9y0a4rb8tYyyT1IGn9UnlHaPsuJKwyOnevpAEVs9a/7n4YV3qLoV9P\nAOjpV/Dvzj4ENcq6UUkdL7SHK0XPG++v7t62bdvJl9nFlxQSxEGMo0O1AXTZXhRAJ2NHVwDo\nvE183MStawNAd/hxKSVIUDzXUSA13TXKGPwzjBKRp5uaZN3PT7IENAfvF/OmRNu51AYcIFWR\nQElTzIm7veev0S4FcZgLnv22nHmdJv7sp9jAQOKOXd1hy/gwg+bYydeBZEhFwlcok7n8WBk/\n1KnYSm2GDGlTxmdc8qOAv2b+uepYRCbULFuOmvfTy4mz+Sds/hQ8vt/iy4kQ6Fg6WCHm9Z1V\nQ2fZjb6+IVy9Wv/50/vVq5D0YM/f3iFhT/6ZvGb0rRlVAQh0rWo1sCpRipadi+c0lxKX0tMJ\nacJJwnyo2Gb4uDYFRX9BNYrQpGRHzcuiqEHRWZQQqmQO+NJUrFgRePE+JY+gc/4dv5BPEDSY\nsX2ek/TDawbN/9j+28G6fzzc43e0D+F4dUpCDgB9/eIzdhEREQAadehQLG3s4tIQIRdehr0T\n8i85W9vJY5aTx8x/I8/v2eq7ZfverfPObv3dqGbnwV6jvIb1aGAm/qaJ1IMAorZ49Jh34SO0\nq7rN+nPmqO6NK+qIUqlmxNw54rNk9l/7w8/O7j7Q8u4Rl8n+/rKjDuW8O7/xn+23EkhJkgGk\n3D/xFjAa+O9Wr4b6APSr9Vp0okmTiZ09Nnm2G6Zx0d/Dtrg6psOf3POYPXrK6kEtTw1asmXl\nxBZmYnXVUQcAdZ2C9tRUaKpQccLCwgA06dWrOAGZbe/ejSecvRoWFgY4lBBW+IC1bDFwRIuS\nl1T0KrfoUblFj/IUJw0DAwXnxS6a63IzktJz+KrxKZXQdAkJCeA9Bl++VHdwHeIgWUZJBFbt\nxs9sB1yetNKbd75mY1nJhhWIunnjfjM29/vtn7DQXZs3bdp+6P71gMXXA5ZMcXAZ4DV69Ije\nTStqQNWy2eBZzQbLLYye+OvRjunTeWhJitzOh+KbJkX4aKJCY2X8UI5dGWS/Pb1u/ozFO+59\nEkK/htuCJd5T+1TV3dtvIngnbP4U4rM3EbD3PHzDt5sZPhz1atZ956wV2ept14YGTbQFgN79\nOhr+5Pjrpdv7DrydMd2Wr3KiXOA804SThGl8HWrY0bOj8lWDorNoghSfoKUh+jwZhtX565z0\n6kYWUK//gFplB1NQqTPQo+4fDx9dv65mDYSHh4cXHpQPDw8HgNevX4sdnX/9+jUAY0sbfSD8\n3p07wmFtRIaso6MDZKuri/tdBYnqdUwIJRfcJwS6ldt5Lmzn+cfaZyd3+m7x3XF47bTja2eZ\nNeo5bJTXmCGu1fQBag9mHpw748RH6DZdcPbs/BKerkDHqkn/3/d16Ty/vcuiO0d/m3v4tR/h\nsW2M6rLr1/mmNs/4EAVC4Am9WkNWX+4yeP2UUbN+aX1y9/gVPt7D6kg3FXv7ytQU8jwh+WpK\nnDeUhoMDPS82Pw4OM+Kf3LnkrjIRZTadjY0N/zFIgpTZXE62YaFQWPrB6zMQ6Dt2HLe047g/\nY2/u27ppk0/g+fBQn9mhPneCuRBe8ccAAOPt7YkZt+3aDm9rJ19MLc3vOq1gpWBtTZ9yZfBj\nO3bCxHu7vGfMW3M6KgvqVm0n/f73fC+ngveppETvDw7YTlQAACAASURBVO/ezQdqj/ytm5kA\ngFm330bW3jbvCZr16lXswtn17dPo10s3X7x4URCkRzYFua7TAd5pwlPyCcK65pXNdACe8dVA\nUYMkTFk/cWjRwgyPXt+4kYB6pUJMadT4ef/xjy7tFyxfL3aVb2tQmk5gZATEx8bGyuxI0bqe\nkW2TFma7+eqck5KAAu9HBiLv6MMHy556CH+2+Y9dI3YProQ3uxb4PLdxcPjwbMvyoz9v7WYE\ngPt08G+f54B6k86uVS7/vTbGd/bycaG/1dQE0KxLFyOfgPvXrmWO7FyQgz3z6tX7AKo3bu1w\nPphvyU1KemgqhjVdJy13neQdf+eg35YtW3bt+2fC3kizgkmc1IO09cuNPQZISSUv/bGNO3V/\n2jq+scpU1WmrjAAEps0m+t/tNeTvieP+HN7o0M6Z6zfN6W6vKVEAGnXsaLpcKSHTyuGrEecN\nQu52uYjmOkfXSV0cAJ4zklOXerj68OzZSDSqDABwXnD+vIRo3suXEYBO/TpWSUlJfJXJFgIQ\nqGvraPDwbjQE6eDddFbtXK14j8Hq1XhpW9B0P40ZaHbYL2JDnxbvh3evmXNj19ZbQpWGI0c0\nEBNNvHcvCjBp3Lgy8rLSsvIE6tq6mkULetyHq6vnzN9w4MqrDzmaFjWd+//qvWhUA9EOuPz4\n28EBRx7Eq9s06Tmkd90KAiA/9tK6+X/6X3j2PrdClabdRs6eNaK+7CiLGpZOA2Y7DZi54sUp\n/82bNvkVWAi/SIQgBXFEMoAmE7dv5+E4hrj7+ZbsLKndJ74WCGJQUj7CBg4N+U/mElOuNPi8\nB/4eyXx9Yung+kYCAIIKdT0WHwkvY88sr4TN+/qrAOi+s+i1eubO7gDQO1B8v2huQC8Agn57\neWlYjr3Y5BN5/COKKS2tskhnnnvsSOlfaa1BEe6+tq0A0Oi5Q1ZMocTt3dUBQfv1sQSd27Sv\nAGlb0CTkN7UDYDjqJOV4NRe1sa0OAJg0Hbvx7OtUISd8F9TfRlW9+vCApyn5XH7K04Dh1dUB\njdYrw0kHt6XqmPfpyZE1U/s0EO1hKA70SOnBtl2tANRb8rIsUY7jOO7F4roArCdfk7ie/+nu\njukd7bQAQN2q7aRNN+KLRyPlAGZnD0rgCQm7TXseNKWNhSp0avRffik2UMK2KWqUtb9n30Ap\n+e6k5h8TZRsrnZTMl5I4xy2YkrudH+U4fUzaxr6TPiPxPAxEPJBHGYPEpiPkayYdkKdHPuJ3\naiEzK4sQiZAG/fAE7x4khuHkLxz42VOuOD+gY5efcGv7r+1tNQBAs5LLr9vvJPDYvis7YTPx\nZA0vPUXCLZ0JeSrbtSMltaREFFNaWmVRanPxm01Zt5+IiIj4dKWl+CQ1XdiyZiqAar0597PL\nUCLrzozaKoBK8+WvKSlrN05pCMBg2CFZO28zDw7VB9DY+zXheDXHcVzs0Qk1Cz/RMq/ZqpvH\n8L5NjAUAVLR1tUUnvy27bnyey1EObkvUJOVVqM+sgS2sNAFAzaROz8n/HnwoFrKU0Bqzu2sA\n6LGrrEYuaOqd3QFoDDok1kLyH9sIBzBP+RMCT0gb3cJPtzaOrF8BAiMjQwmZ8qeQlw3pdkWa\nZIYdpORup2ir8BiHRTvTgykReQZtJRwGIiZ3po1BStNxHP98zaTDueTIR7zj8uQRIhHSQhtR\n1CD2IMlEafZc3ilXGj+WY5cednTxgLoGAgAC4waDl52IIJ/BkZ6w+bcukpYkw7FTSIwDBUCK\nr6Y0Lb7NXLHJBwaYAFCp1Gv17UTJZ3rhp5v/drcRADAbfDhF6vfL5N6c6gA0mi15VlaMxOwn\nC5tqAKgxtyCuA7/j1QWkPdn9P9dq+tIWWzQtmw5dGvpO7HdJJWe8ubxjoadzZV0AUDGo1mnM\nX0E33sv2yeRw2ouyfhnKceV9bJMDZZWxTBPNjT7r3ae6gZTVNWVAdjh4Q8rdnpRJmOtIp4/L\nkd+W5yJcAcK0iLNb5w1uaasFACpGNV0nLd97L758oUtLwG8M5lGajjYxUg7nzpxLCMMx99jx\n48eP3xPPflE26SGUSIT/zjwUyXsyUVp4c1Ig1fLYM2nKlcEP5dgdHKgOAKqmTUauDH0V/0Em\nKbKtqGTCZtHqvdxlJ9HFryVbOSmimNK0UMYdSJj4YPdvrtP5ZaEpJx9PjHdUAwCBQfVOI6Yt\nWbXRd5vvxlWLp43oVE1kFOrVfglN5F1eoc6x27voAVCx67Xq5ifJO1D+x+sretiqANBz9ZOZ\n4kD2b6W9vXsqcNMK74Xz5sz9/c+/VvoEnrjxOrmcHtCnB3uKYz3q2LUZ/sf2i1GkYGBSNEx8\nsPs319HDKOuXTz7/sa0sCKuM+58r6kd5khl9Y9/m5Yvmz1+0bPPea+9I0XXLAyl3+8+9CHOd\n4tLeS5IdQ1iEkyA/8emxNVP7FhzuVjdr5DZ1/bEXxOe10sgdg0p84h2sCcDVr3iMioIQtd0o\nvsT6YYMzAB3XngoNwyHGg1mUSIQdAYP6Xtse8mp4pTl2pDCcyrNn+fxQjp0yhkp6xPnt84YM\n/olQ8tfi2JEiiilNCwWTGXlq2dBGRirym05KdlQawvjzi13tpMdd1a7czfsS3zdREjrH7B1i\nWxAQ27HDsKl/rtq4ddvWjav+nDrMxVG001PFdsi+GPnl/keI3nPpO7p4Ld599fXnPS9xJVtj\nGWH9cqUCH9u+SqS8kE2+sbyrbYkjGRq2rstvfrbXIQvR43E3f3G3SJQZVnvo0aIrL5fUA2DR\nxZkw17VsQ41xSIgJz3GfuQgnClbfWbTS9h+8OlCiYzfDAYDlLxeLJsAzo40AdPQV37uV7NsR\ngE779loouX3cvxtKGYAoiCAtQh4tEuHk7pU1AGja9156MV7ezJ1emJZaCnlpcRGvo+LTiHlr\nRI+afdsSTLQ89qwofijHTnmvJ0gld+tTGZRc19KQ+ZjOc7IjJZku+NmY+2eOHj17503R0MuL\nu7p1wRSvIUNHT/XefStO1ohT0NJC7tszS0d3aVTF0sSkokPDzuNWXYgVVTP1ye7pnSoVpCiq\n1nveiXek7Ki8KMgPXXwh5/31wBUzvPp2+ql5owaNmv/Uqa/XjH8Db8SWnFQoOnMclx99an4H\nm1JnKAEAmtYu805GKz+bFn/IdyBCaxDWL4lqkLL9EiBlUikm6805n9/Hu7VtVLOKjbmpua1D\nzcbt3Cf8seXCu6I7aqkbQLR/N2MAUDVr2Mdr0iSvPg3NVQHApHdgHDGdMS9Exv/XAELu9q59\nCXNdm/aUGSmYHhO+qLhyLMKVdRhIBMm/5MftaYSmc/aQlg6kDBasnmQFQLf134VBdNP2DjQC\nzEadKJ6UM457mQFoOGmsA4CaC54UfvD4jxooeaXoonn3vgTjvzTRAiX9HmkujmgHrdaQI1zK\nfV+vevoAVMzbTDv4WuZyfH7ck0uXLl19WfJ30x/4eDY2FZ0RUTdtPHTTPelB4CUQe9QkmShJ\nuHWXcs0bZfFDOXYKQXoCHwpCSq5r1/m0x3TCZEdKMs0RTjARlxZId6C4fQOsJKIQqNf43/nU\nqKChjloAoGbZevz6q7G5HC0zo8yukOwU4volRedismOu7l4+3bNPxzbNGjVo1KxNxz6e05fv\nvhJTeq0pPfKc3+KJHp2b16pc0cxQT1fPyKxi5drNO3tMXOx3LpLmQEu8xeaT9n51f9LzErE1\neK9f0h7bggjZfkmeHSmTiojUW//0sCkrdplmpd6r7qZJKUd4ZYodAEHVMacTCrpHmHB6tKMA\nQPXZdynpjHlWUKRAz/GU3O0+hLmOdvp4257+RgAg0LF0dLTUUQFMvVbMqg1oVeu/wCd4/x6f\nP9wdNQGoNPmrjGPVfBbh5B4GIvmXhLmOdJsgvgUiHM59FOiuB8C6386oXI7LjdrZzxo2Dg6a\nsPU8UuBzCD8eGGoNQL3rhFLJNmUYf8yKlgDsZ90puiJKTVYyiVn43w0BVJstuuFmRx6Z2dZS\nFYB+3ZFb7pfli+df+NkSUHVeI77b78O+QeYSLWHqtucDxxEeNX2nEkyUZM+06BByYY5dae5u\nGjt27NhNd6V/qoCFU0qMg1L9LfMx/SNlsqMkmXajnGAKIi0tUO5AqQVHwvXreS7bdeTEoZ1/\nDautC6jXb95YG1Czaj895EXRXYqWmZFHv3FEx67ASfqHoDOZ+LMLXKxlBTTVsHZZcFb2vaEA\niTfCpFP9vBFSelCEMtYvSdl+F80jLId4ONMmaGH4ehfRMNN17Dz29zX+ew+fPH3yUIjfqnmj\nO1QRnZEz6OwTWaoc0YZ33d47SxySS/DvpQug4YI1hAqG8NuMWKCAPyV3ezphrus7jzAj9Rjb\nURWAveeReCHHCeOPeNpDU1MT6m3XFue4j1zZRg1lpr2XvQjH8zAQacoleduU20TXRQES/NoM\nAJr9Knk9ICDg8lvK4VxK5KMdpKeaZwvrAWi2LEJG9XKPexkCOv2CihtBmHB1Zf+q2gA0Kneb\n+peULGGHLv5iA6i0Xy++J/bxvJoADNsuPB2ZmPjm8spulgIAteY/pDx451FumiR7Zo6d0pHd\noIp4I06IcTDftcTPyX5MP7eJMtm9ppz1o5xgmktZWrhOucWunFMLgHa3bcVLAzFbuogie9p4\nBEWXWFiiZWZ8QjmDJkNVEWJOUid3gs4S5CVH3Lt4+ui+kH1HT1+8F5Es4cHkPlzcSBMA9By7\nTFiyZf+ZGw+fh0VGRoY9f3jjzP4tSyZ0cdQFAM3GSx7lcqR3oAoOPFHE0/nlaw3+65e8IGX7\n9egM/hAn6I+7eugC0Ko38bAU/zT37YGxdTQA6PcJSpIo58gQLQDN/n1b8iuiRO/6fYcSKvjH\nI9oBTErudko8lzBK2vuOJgBqL3pW+NGzRbVRqkFErSH+jpiTuwhHOgz0kTLlLtlA8rY/KxSO\n/LHJ+4A8/zAcxOVqQiRCyfZPuzazdpkxsN28G6PEsQyO454urAWg/qKixdt4n87qAJz+Jj1q\nkg7IB5KjrjLHTomUw7HLSS+d77tMytjUyevnZD+mjxpImuz4n/VbEEQ5weRO0XnKZMIdaGhv\nHZT01YpOaYnVuoDDlMNfw1qVNUtImzg4gpO0yJ2gc1FTxlxYO6mDo37J9zmqBo4dJq27FFvo\nAQS66wKw7O0bXtaSS1aYb29LALrugZQHU9KpfrdgjhN+uL5+cq9m1axMjEysqjXrNXn99Q9S\nfbRjw8rRGopGmHZilCmAdptkh4RK2NgWgNnQtYTlkEVdKRN09Nq2qoBq48Uvytqdmvd0QUMV\nQNVlg0/Jck6M0APQdYeEkyDa267V0ZVQwU6UIw4iBRSbu70QwunjGa70mPD8FuFIh4GOjiZM\nue7DCHPdQtlhueXB0y3ge0CeXxiOcuxDkE1RJELxi7lvTy7oaK0GQLd65yHDS7N2nCkkNrcl\n+boKgGqzxd7DiTrFqB/tUZN0QJ4gPL2TQpuOOXalKYdjp7wTTKTH9PY/USa7v2/yewOYE+bb\nezLlBFNbis6u3Qh3oM5tAaDvHvHBJtzTFwB6BUiG9LxHOfxFdOwITpIolRZPnUUqXV3kbF74\nIK2mb1GparWqlSz0C9P/qVi2X3I9heM4bpwpgAZ/hclsulfe9QGYjac8mJJO9bsFJ50e7yj5\n+KxRdUKolADp5Nbg7TLyISv6euCS0R0c9Fu1VgfQc7fsZyzRCYASoY9FyJgiSPe27N191QFV\nV19Z1p/g01kV0HCf0gcAOq16JuLSrEaQ4g2L9iRVdXclVLB1S4rxfx2xJEkx4XssJyzCkSbz\nFi0JU27bzoS5bvwFapNIqcV/3FkKd+xKkf5858SmhgJAYNxictCrMl5pD1CDxL3p/CRLQHPw\nfrFeER3LUHP+Gh41Fd50P3au2PKgqqGpqQkN1dKf8E0vqK2Wfn3TogMR+i3GzelVWbpM5ME/\nN15Ls+89z6TkdTU1NQAmJhKXzczMAOTm5QsBaGoW7UfSEv2pqqpaqgxOuL9rX4NLh8fVkJly\nOi9ix6B2ow84jbcAMnNycgBdUSW0tQFAT088h2eFChUA5AspOmemZQHQ15eeJb0Q0efJ6TlA\nGRkwRb8iTn1XSmbGmom4wjsd+41lUwNjhNCv5zl/hltd/aT7Id6LdjxZM7njhZfXw/Ks2k9f\nveF3t2qilpKRtbO0zgCitnj0mHfhI7Srus36c+ao7o0r6ohSfGbE3Dnis2T2X/vDz87uPtDy\n7hHPlBQUZBaXgY2NDfAg5e2xYxGAdrfVp7eOMAeAzj3am8Y7jDpx/Q5sPIJuBPS3KtYwKoqS\n9j5y+S8bwnKhXWPQvNkDGhom3d3956LAl6/W/7J87JOFElkNia2RHDqxRdcNYbkF/yZ+PPjy\nxvFjj4/dXudiILPWJcn7+Oiov6+v785jjz/mA0BbR3P+2X6NjAg/JUJu6kkRkXce5gJ1WrWS\nldTbpFWr6jj59GFUKgCcmlyzptiHL86fj51bw7Lo/9xnz8IBnfp1bJNDeFfQyk4bV3kbvxIp\n/8Qomw8h/X+7LtB3bO/1m5fXwA5V9QUAMhISMqQJ52tX4J+WVAPJ4D3l5qQng/dcl5zMV4ev\nDZ7GX5SsXCrJkfcjkmBo36ByYV9wH6+vGT9sZvCrTPVKrn9u853ZvqKUuzAAwMQEiHsdHg7U\nF114d+tWLNCkYUOxb4ha2FgrIx6Aubn4wQoLCwsAcHR0lFtZxaKQpgMA5thR6bM7K0v6J5yK\ncd2eI7y8Rg5oX0VPVtru9KB+Zn+HqA0I+aNMEcvcx+v+DsqPaOwDAEiNef78OQDoVqwG3I2K\nigJqiInHxMQAsDXRfkWpSsKpiZ2GGl8J6G9bhjuaH7V7SDvPkLewdXewBeIiIiKAgrucWW1n\n5wTUNhOXj46OBlDJFC/562xjmcz/FqunHgf+2cqbTp/fduu485dntGnzevHfMwa3te+zbGP/\nS4P8Jo932b+mf03d9Gd7fv51xwdotB4+oN+Tf7fwTceeSnGSRPDVWfvB3BknPkK36YKzZ+eX\nyHwv0LFq0v/3fV06z2/vsujO0d/mHna3tQXCb126lDW4g/RwegCyLl++DaCSTmQkgMaursUT\nWMVu3RrhxBXUHvuHhMKamoS092l3Dj8DNDv8G7prrDUAdOnR1ii+6oSzTw8fDl9Yz0GKUnxb\nI86Xv8soDWHqyzMBW7f4+h24GZsDQNWoZie3AQMHDvzp6RjHk/H3z59PnjK0zJt40rlz9wFB\n7do1y5Iok+MT7O3lXUFhamBra2uZhdnY2ABPP+TauLi4lPpQ8OBcDAZaFfyXcdB/Xwr0Bw8e\nWS9hAfhW0Lmvw9kAvsZfBCUPujD5xZm9ew6fv/3oeWRcUmpGvoaBsWXlOk3adB4wzK2JRcFy\nb/qRf6bQJkZK7nYuNeyM75wzvnPKriEA0SZCfzkyRYS4Cy7wlYW6rhHpcSIl+nlMqqCCTfWK\negDwaMc0v4cSwgLzTlN/62RZupTPJ/zE2uNhcHSd1EXaEJYqHJsO8DZ+t+CC9XupnJ7WsN9e\nMZnTczsO++tMbL5u7aEbdq4Z10Cm6+3kpLHu8Ktdm87OXN9eF+BeB+25BTi4uNiJCYluQNbG\nXDwoD958+LJNBwDsVWxplB3ZnHQib2ofKZ2m5rKhxFv7nCMjDAAd9ym9Ad4JMNwWTG+gBWjU\nGHdS6qmd/DeBg+1VARXr3n5hOZQTTFOkvcsoS2efFW0FgEbPHbKiySVu764OCNqP7iHbmiWt\nn3D4i3IGrRVl9Z70Zqf35D5agKDBwidlv27Mf/RHPQBabgFXZlUTACq2PVZeiZX2GjM39srK\nHrYqgKD67KWUd6AvKKFwWrfVRYnY9BzHxaxuCUBvxAnpDciTzgPqAyWTF71Z314TQIM/Zb9/\nzoi67LdgxE924o+1LRfeiyt6M0nL9iu9FlKnCFLUlXatVcsqRwxRN6l67JctxnEc9/7q7m3b\ntp18mU2rIMX4ySemM55sH9nQqMwnXQ27HovPxQk5RUyMZUFMhiu/nSWqyXPK7e5FmOvWJ9ye\naQ+gsXdEid+SxKiHn7QtXJ//DlTZmc1lt7Pkr7sBKqYt/7c3jM9uzpTDw80AwKBu759nzRrb\nvpI6oNJwcYmp+dPWrmqAyag5pXpQRr7yeH4Zdb5s03HcD7bHLj1eSl750qx3JfRKQcRa0W2G\nX2Rz0ok8r1+kxVToMG53tFi9gj0MAP3B+3cSt/rFHRzpqAboNplzXSJSY350yDBHNUBg2X3L\ni2yOI51gOrWAoHM65Q70f/bOO5Dq743j72vLKBkpCYVoa9G0mhqIQjS1d1/tvdDeaRilqahE\nQylpp/qmtIvQQFL2yPr8/rjGxb3X51z3U/p97+s/n/s49znnnnM+z3nOOc9zSIAQ03QvfxHc\nQVtNYiQRvekHD20GoIMH/4PT79zbA9CY+yD77uKO7A1fabX2FiNd5yxZtc7d3X3dqiVzXEda\ntFcru8LRacndHD4nkGp2daJb/ez7XFxbo3oICcLWsLQgMBkpiiqPTFZ+moql0KrfZM8zT/bW\nCK9Vx2y/wjo5RLMcgb6OqIJEFzCJbkx/PjqkMQBx9S42E6ZMsOmiLs5q3H2mV+CJfRvmOvTQ\nZFveUobTw9LIJsbRNuRTAX1on+wkO129l8TafrmmHYD2lYGA2d9lOHJ1Bcts9FmAzqJHWTVe\nbbxME0atEwGOj9NtuuFD3SNT6Mc1Sgub04FzVafYfe3jKo6WnOM2MoC8S/BJBs7HM910tfOf\nMuyYuOLA9VfhH9k8cpqQj9BWLNPJU2sUxx+xbcYCGlvueFWxFCpNOu+qLwmw1AYdeE3/uhvX\nG0y16lzHVyxNhJsdlchIIiJktBSAYSf4R/FgH/tlH+rPfO43y7w5r51Ymebms/yeZ1LcXAv8\nFqaZdbqoL6zW4HPToqbJGBv0j21H9oiDlHqXkUsOhpene+euTB2y/QrXsOP69hV4qVkZYlrY\n6YzZkOVBXz5JFYCGy7lU9k9RmnrWWQOsFlPC0imKooqSIz0HNWEBUJ8UdkXYE6OgEFwGIpxy\nCea673tMAajNuV3xOZdel7TPXBxoMmM10avtJEF0mxEkwgwadgc+0QwmkfnUO4CiKIoqSLx1\n2GPRrOkz3db7RCRWfyvFnV3t5ubmdT+H6BccE1gvmq52/lOGHRMpxfhO8dwjm7OTLQp8I0/Y\nFLzcbq4EsDTsjiYUU1RpSugUQykAKv33vKCbkKGOMPMGYg4yI4ne+phNuGtDcEsvWJUfB80B\nNJp0vfxBSXb8/RD/3e7LF8ydOW3KtJlzFyx33+0fcj8hu8J0JVzVEF/UZ6I1iAxo9vPG3Sdu\nCnqaWkTrX2hm+63p6a+7O4RTMdo/Sm3F1UiULLR0xhwQ3Zg2H9IcQJt1nJ7AV2sNAbEe28rj\nvBU8cGsNQHr4vJF/aGKsljb62QpDAJA1GO1xNORKyFF3R30ZAGjD/WgCGbTnuudL9VD1zju3\nbsyOrtLW0Zrk1UZyCZq5zOZ3/mkhBTHV3vNOxnyrGRTMfzgADPcvjxE2olGXmSff8E+jU/T1\n+trBLSRJMgOV5giQV+aPN13t/KcMOyKK8+tsbvOIbH57hhqA7tu5RkOvgJ2Tp8nMOwQ6R+5a\nEpIgQMjWrAfLusoBEnqTDh+d0VYaQGPzHc/5DKJaAueSw8QbqC7wryBz0W3i3I0AKI4N4ef2\nzL8wRgFAF8+ah794UZpzmrFEyUzH+qFpMvqxD11JqhnZL/S6+i6jpHo5vFZftWb7Za6CxEtN\nwqTDNCtIBDsYJN086L0lUT1hPDvSnuyYSxVP3nt0ANDE1papiZGiCNJGf1hPcLJTgCmX5lx3\nbaIiqkSYoi5N0dDQ0JhyibMwdrQmxYnXSDSoL9ZJ9qtjs3uqiEG+zejd91Orbp9UH7OjpQDI\nGo7e95T7AcWcV8dndlFiAWhk5E7juysiH40k0fivMexEt2J5cd5FYeRZwf6Vyo6NOO3n6+d/\n7kHSL0got7Oau8zVdYxV2ceGbdqwaF9Yq7yR9+tz5DHfM2G3ol5/Sv2RVSjdUFmtRVtj00EO\nk1z6arBn9O+3N45cecV117Ed49vzv1JfFQUT9ytnf/QedtBnwlgASn02Xw+d10GWi2RJ8u0D\nHut3+t+MzS6pfCquqGs+dv7qFVN7N6l6AZ2GzmxYqqbLLn+YGHXu1Pnwu9EfktKyCqUUVZrp\nGfXpP8JpRPfya3OPd7vsekS3Vt3nHp/TjUwNmhWUkmtIPyBC/jUXl2DaOg+2ao3odwHu25cO\nWmrANbR64estG05nAwaDB/GLTVHGr6SoYH9fH98ApdisDLpakMFca0ilAzzuhdV8OMLveXAf\nHx/fE1eCtswI2rJA23SU6+TJE+x6avC8MQwAkFQ3dphv7DCfjxrM/dxzMzL86QoDqeedjO0D\nksrCCf34kRwXHRGZGP547rcZluOOxRZAQr339FWbV0/u0YRjYq+9gpXQGClEN6YlZBSAn8nJ\nyUDFz5WcnAxASqoyzJKWlhYQkynRxIT+xCh9sV+/Nei7+vqqPmWfcL2KGDpRZUIIhnsHNZhl\nvu+HhVf8jelN2Z9k3pjVa7DXh7JIOvnfXoXtmXz7bmLk/V6xsQC6WltX3lXWtLHpMiPifmxs\nLFD1nqMAUy7Nua6goACAhETljGp18MuX6oWxm7GAV6QG7uhoA3Sj2zRQIxBOv0zU+Y/Pcdl9\nd5Cz1/xJS+f0vnpy+nZvz7HteLSiV9RB1uh/TpycaXz30rqjfotMm1R0wNLv97ZNHbfyfNwv\nCY2Bq3x8Vgzi8601Ih/Vcru0Kjo62vWl6brxFxGqmfj/hCDLdJrpBclv5NFME/720FBtKQDS\nOjabb6fWdoCs2gZT3O2NfRsBUvpTz7youcFEUQSBc0l0ZvpHIVGDrILM6JxyZJA8ADEt612P\nflY/hVPy4+H2YZpiAOQH+3/j+nVlFKXFBO+YIRwEtwAAIABJREFUO6ydcvl7wZ5Ub4qiqNyE\nm/7uMx0GmrTRbqraSF5OXkm1qXZbk4EOM939bybw3xfhDlFrCHRTrODr/ZPurhat2BGHxJXa\nDJ1j37GyPzANc+49AdLsEkFzpBDdmB4+fZAkwDJc+LDcBV3wcKEhC2CZ7qnsv2wnXJtVVwkm\nxn0kAYqt5xKkjd5NcrKTbMqlBztZ+cJRTQAYbeJ9W42iKCp2YycATWffI/mCq5NVAFa/A/xT\nSJe15woCYUF9VIWfw9YP15aGpGb/laEfCzi+vcqvmff+1MxujQCIqZouDkkspCgq/8PZBb1U\nxQDIt3U58C/v4zolWe+uHVw8qrt6WQ8XVzIcMGnt4Rvv+d1QrkFpvWs6Hog8drzQ0dGmb24X\nJgZume7ne+rau8xSdmRzV9cJI/u04B5EsNWUpaM22gTEeA4f1eTckVldGlW5sUmlP941dsTm\nV6VQdV4+RQegPu63MXe7kQPI6Q50cR7au4O2mqLUr8yU+Od3Q0+cuP7xU/BcM7sGMWGTWk8O\njenuN3/sPN/gRWb3Q//x9t8wXId79nQA03V0uLkk3x8c1f5g9Yd2FLWNfuBcTQKdtWpqUElJ\nburn1AI5NQ1VOXEA6D73GHvvmyeFXyIPbDvyOI3tbiNSgyAyMJ8oVNWgcttNJdK5yTg//+vG\nI48nXphrrLXH0tbarIteM0WpwqykD//evHAuIjaHgpimy1HfsWrcyuIZua1adIiSrIQXz2OT\n0jJ/STdUaabbsb22YrVon99vrnMa437ja2GVp7k56d+TE149vHp639pllsuPBawy5xP5rCZE\nv2DTWRn3iBbUACDdrIfTsh5OS3d+vBng6+tz5NzF3a8BAPf3ztlSOtppmElzTl80gTeXFqRd\nlD5vyaMn0of+SGnVo4cqXnyMikpDhxo/vZTB7PNXflharN3KdtlJ9pwz5lyY35st/Y0Tpzl0\nU0p/fPrAmTcUFEdMdazovx8Dg54CukOH9pvSmfbEqHZkAf3aZX2IygN6d+tW4bx9ff78e0DR\nblfgiuFKAFoMX3kmP0bHMejpmQdqAI/AZuzw2pyQTbn0iLt28OBZDJ/aXwzhL27d+rmoJc/4\n1T8iI18CYh06tCX5gr4DLGW8T9+9Fp4/1YnbvkwV2hAIK1kdOzaTrwT3zi/ZfOCKCy9Hnlk5\neY7HsHaBo9Z57+YWdV1Wz3Hv/V5D1o6d4Bm5aXiHa+PGt37kHfAmT0zNdOHBw+ttuDZ8/qd7\ngUd8fP3O3E6siGnYc130+SWd1Hgmm+UJq941HS9IzNX/FES2uYkJ6KcXzPpFdF2AIE142bNf\nCReXmKmLA1BoP9HnGS8fE9H5nsxgl8YA5LqtjcrmWlrmg5VdGgBQGRuSRaZzybdXd+7cuf++\n6qGd3OfeE7qosIeepEqXMQejuX9vBaUZMSeWWOnIAoCEes9p+x6mkahBVsHaqTzDwWd1xUVn\niqKokq/XVvVrzv31IK1hufIql9rUGrmtDHpZaIti3DtLA4C87qAZHj7nb0TFvI1NSEiIfRsT\ndeO8j8eMQbpyACDdxeMF17RogsCjNepG8c+XobvnWXdULlvDshRa9h2zPPgjRQnHqVyc8y3+\nY2JqDp1jpjUq6OfhTJ+pVszlPiIZKUR50AOpjNsre1TdWJVpOy2EoxKfTsy0traeE/iFoggm\nRqKUYj37EKSNNu0FkF4Gojnl0qOsIl47eokDYu2X8fFfLmrDAsT77Eok+wai6DZEwrwlaI3u\n0p+PD0zs2BAsJaVGXH7NMkpSI+Z2KB+38j1X3eUWioZ+5CMi6m3TVUVk2PGEJGinAH5UutcF\nSNKEc1xhLE27v3OUniwAKe0hbhu31CTkHUlrnCAJnEukc8mt2eqAuOkezhfW93OjqzukVOzO\n8LKxCz5d2zbeqLEYACgY2K099z6HogibjqiCPEW47YHW2L7hp3Mlv5Lun9y6cIJt/z7GnTt1\nNu7T33bCwq0n7yVVm+WJ5i+6e825AfZyANRtfON43eIoiPW1UQcgZx9Acg+UV2m1tUad+ZX8\nOGDj1P56bOvALpAqjfOyZMfik9MdOHX1nmNnQ6+GXw0J8t+1cnK/lmz7WHGgdwJFCWPtwb2C\nApzFJkk6TBuySYYoDzpFUVRx8sPjHv9McnFynjh3rU9EAt+ASDQnRiLDztKGIG20KVHa6Mqv\nE9qUW16RrJDRygDEtEd4PcusERsl4+le6xZiAFTHXKplzfunIRzdRV8jPG1bK0pLS0s7nav5\ncVaM7yQjRQCsBg1kAUg0H7jmRlKV5Qhp5KP6i6ATo8iw4w2Bub1uhACHgSg6F9aI0oQHVHPP\n5DxY0panu5msc88hCJw7i0TnUxFzmgNiFl6cr4mXKw0BNDJbF56Qnv7p7s4h6iwAbVbFVC+l\n5OfTowv7s18Dks3MZh2MSq0Y4WRNN4skMjCXj3if4aj+ruSnMxmE81eC9yBlAJDVs1t3+nFS\nbvm7ojT36+PTa2xbyQCAyhC/T2WhFjlDLXDjg2dH1DmimPBagx658ZFHVrpsvEnio6rT2oNf\nBRNvH6sFX0/XbirsNQJz8QLrNMkwAt2ryjQNO7u1s5oBkOu9qTzKZs5ZJyVAdVJY5bI974qr\nKgAjF1vBJnN2uUKYcisr8uPK1JYSACDWyHDwxEUeuw/4HfY7sNtj0YRBhuyVnITuzGv1Jw5U\nDYQ8uouTb3qyzzRKaVm530jK+RA0x7gxC2Apm/xzrjIpBXnko/pH3ZpOZNjVc54t0QXQbsNb\nvlKv17UBoL+Mw/Ap+nx1bX8NCQByrQe6jKvJ3sckepAEzh1MovPMuV0A6K54VuOjjusrrKxU\n74GSALpv5oiEkP8xbLNzRyUWAFbD9g7uF+OqeY7Ims6KJDIw5zO6e6C0dCaDaP4i2Ws+PVoK\nwNDj/GNNs+NWSDuHCqi+kFujgoJPN71XT7cz62zYsrmaippmK8Mu5vYz1vjc+lJZHxIfVdJt\nAdcedapgjf0Xj2EAM/ECBZ5k6kq1AHIkkBl2gYkHzBoAgHK3qQciPmaXUqVfTo9qLi7Zetyp\n11klVEnW61PjWksCUr138r+twAdhTbmcFSlNubGmvyb3UxkyLQauu5nKe4fhzyLs0Z33LmBO\nD2UWAIWOE72fV+x2F329uqp/U3EA8h0m+L7IpiiKYh/MJY58VF8QQtOJDLt6Djti7cDD/J3t\nWX4DAChNvsH+M/ft8ZndGrEAVuMec09/4B9muCTj7TXfdbPHDDfr1sFQT0erZev2XU2HjXfb\nfOoxxwKZJHDuYBKdhw5TAdDPh+NyUobvYBagv+xp5aNP27tXVrAk7fGReRaaUgAg3cJy3pF/\n07i9H8iabjBJZGCKIj3DQVNnMkjmrzySveaNbq0ANJkazq/r5IdPbQJAbyl52FZGWoOiKNrH\n5oh8VHs2EK896lZBIezbEr26BJpkqMLMr7EfPqbmce9P32LCw8PDY74RBJAjgihJK1naaEEg\nmnJpVY3jF/z19d7JbQsn2PYv81/2t524aNvJ+9VPZdCGXmrN+ISEz8nfw1Zxyw/Jg7W3KUrY\no7ssaed0fVkA4k3Nl19MqLFmLk17sIO94yDd0nb7/bTvz4O3zx7atiybcQNt0/Hrj9/7kk8J\nzbAjChdAICy0phMZdrxI/kSSw4TI+0UPdodeNYQsTXhp2oNdI/VkAUi2GLyh2tGDGtDOz02R\nBM4d40Cgc+/eEtVfKpGz1AFp5/McurNdZRKOp2MvuTu2V2QBYDXu5LwlLJ63QudJ1HBwJqjg\nAqI90FwCnW9zTbPLaxoNJZi/7pNsps89vFSfBYhpDtt5L4Xbm64o5d7OYZpiAKv1sid8S6wO\nSWuQQv/YHJGPymUUydrjcl0qyG//hYnEOWzIRsp5isr612t0x7JuJ6PRZ7rPvzWybZX1wFM3\nZzYDpC28kio+ybg+Q6/6bmUDoxWPKowhWktNQcxcummjyaA/5TKRrFwQSJoOJDet7Y4Lf3SX\ndSQTNDBw3BX1g/eyNPuF74QO8gDEHcqeMBP5KDViraUGr7UjAEhpWK6N+E4qLNyJUWTY8SLQ\nniSHCQODkPNaBs004UNXLrdUFwcg13bM/toXwQT5uano5a0BSBl7vOE19/16ta6bFACDFVtI\nUpubDmoCQI/DHfJ5izGArlsTOGS/7esDQM2qnyQAiKt0nbjz+odabh8ftSVQw24LcQVp7oGy\nM8gR6UyT8m+hNX8RZqHNvru4oxwAQFqtvcVI1zlLVq1zd3dft2rJHNeRFu3VynIcdFpyl+yW\nA1FrZJG5JAiOzRH5qAaYE6w9+vQRsIJMbUzTgKYbo1zsi7+VErsHsmTkG4gDgLSe87HYoprC\nRAHk3lIES02BzVxaaaNLvz/0mmttrN9MWUm5mb6x9Vyvh9xuXlJUyjWCKZc5nysZtJtOQVay\n4qpVs+Fr/U+dOnXq1DxjADCed6omG62FP7rLet2SwPc03KAFsef+MVGulkyiNDvuhvey0cbN\nKm7kNDWdvfn0g88CBOIkChdAInxOuBOjyLDjRQhRDpN/ha8Ap2FHM024iQkgptLzn7OxdIz9\nMIL83NkUQeBcotTm1rOGSQHQmX6DbSGUxm3tJga0WhzN+QXRy1sBMHLiF1O8OkRNRxQZOIhk\nD5RoNrdaX32u5DON3v1cRUf+8xd5FtrM536zzJvzStsg09x8lt9zLmnR+cPgu43k2NxhEh+V\n5WCCtcdAU+IKMrcxTROiATti3RxNAGLa9j5P04uo0qx3wSv6NROHmKb90fjiamWaW2mgaoiW\nlysMACjaB1R2xB8B9ooAOm2IJVlqMkhG+HTd6j5FKb0Z17n09kA7gilXIGOU3lEZpigtSH11\naXmfhgAkB3pTFMV3GUA0ukecrHPSTq4UJyXx+IB75COS5iAKF+DvTyA8n3/wS27zBj9Ehh1P\nMqIPOhvKAZBsYeUZmcI51Zak3t1s20oagITGwFVXPpONL6IstESBVHoOH+oemUL37sw8gvzc\nRymKSjrroll2nky331i3DbsO+B32O7Brg9tYS122s0hM0+VcEunL+2joOFUAUGxvM3vp0qkW\nLSQBMSP3KoG5fvpZSQDKk9aMIZgZTUlyXdsFElSwiOQMRx130MgPhXCfvw4KloW2JDv+foj/\nbvflC+bOnDZl2sy5C5a77/YPuZ+QLZjtwdx+ItGxufkk3lyitQfZncozDG5M04dowPYboQ9A\nyfk8R7C24i/BU9tKQ1x7dMCnEs4yiQLIjVlPtNRkjGcrDAFA1mC0x9GQKyFH3R31ZQCgDZds\nGxcWkEy5pNA/KsMsr9YaovxsMb8ZiWh0k83PB0hORvGXqBb5iKQhiMIFWFsTCA8eKNSJUWTY\n8aUuOUx4wnxWJXo4EeTnnklRFO3AuaQv77SwOR0475Qqdl/7uIrbPee4jQwg7xJM9NYTwIYg\njAz8O7JXCX7at9r8RbKZHs1Doj5DdGxuRD+AvjeXZO0RRmLwCrgxTefaLwlEI8XCVBrcXL/f\nQie3loCE3oRzSaWUQAHkrGxIlpr0LLvS9OcnFw1eeJGiqOJvj0/tXL1k2Ya952Iy2NZQcfLt\nXZMHdNXX1NBp12fU4sPPMiiK+rC+IwDpfge+VJTyyctCGkCnDfzf0UKmnvgvKYoqDrABytMS\nCutiKdn634TkZBQ9BcoiH5HoHEISLsDM9HfEFuCOyLCrlaJPl1eYNREH0NBo3FxHwwYAxNRM\nF57/KODimsmTFjkPDixevHhDcDxPifjg9YsXLz7wIIea0hhA580fOT78uLkzgIau4RVPfp0Y\nBkDGpVKGZuBcMgoSbx32WDRr+ky39T4RidUbNu7sajc3N6/7wg5ayx3iCgrzDEcN6j6N5sZH\nHllhu5JkM52sI9UPiI7N9etLNgYZWnswnPuYEa5NVES1qyRllCSeGKnJglSb6ZdTBQogN4xk\nqXmnNkXzE65tGdNZSQyAXWDR612WKhWuL3EN+1NfStOvz9StmoBFtv3i+7lXxskB6LWHM7hN\n0u6eAOTHh9X8GmGNlOopTIrIjsoIAs17zRRFFaQnJycns70YwjLsnizQBmDk+b6IL6dHAIBd\nb5KTUXVVjSfRiwnCBUycyGxsAX6IDDta0MphQheyDk00fnIC7GUBBccg3l0pP9BBHpAddSbH\nhyQ/t0A1/Y/BL3uVwNRxGq3MbEYR7DWTdSQBVRMyRFc7e5sTe3Nprj1+xj1+/PRjrReXPlzc\nsWPHjpWEsXBJsmUwxbu17QHocdmWpCjq15vd/ZUB2U4LZliy+y1BALkTREvNixRV9PnG5smD\nOrdUV1Zu2spo4LRdt1LY83L2q5MLB7Qou+Cjb7My7PnhIbIAJFqYT3Jzm2SuKQGlMdtXGQEK\nnaYfCIt6+vDyvknt5ACwOnvutgd4ZPjgkkWGbKQQpDB5SHRUhtSyo3+vucaA4jMjEXX+0MME\nSTvt/Jk6GUVE6ROCcAGPSYQjSJru4ofa5ESGHQ1o5DAhgSgLLdEbPf/MKDlA1jGIn6soL9BB\nFpB3DEq7PLEJACh0HLXQY4vHwlEdFQBAccTJSsXitvYQB3QXPfqrXDgc+y/0EV4Fa2SvogMv\nnQU07GpmNqMoivZeM1lHYuBYmAC/IOHVzrpoV2v5Uq1GH3nDt1kEUoM8bTQDFF+bogqIDzjE\nY/rKebTaWL68TxEFkEsnWmq+/nbOsZlY1S4safBPZHbi6TG6MgAgod57utf9lCKKSt7ZG4Ck\n6a74UoqiqNL4nX0lpSQlq+63Ju4xlQLQ1ZlbtBFevxfZSMkjSGFygeioTK3+yyoQ3GsmMuzI\nOv9JgqSddoEUQyejCCEKF0AgLOR5ozxLpAjulKREbpk8YfXFhEIpLSt3P5+5Le4vc5my5+qa\nfu3D5nsf92AvFMhg9R1gKeN9+u618PypTrK1CKdfdnEJpluydoPXuYCBgQG/UmUNDFoA716/\n/jl4e+DKN8PWP3h+ZsvzM+wPZdpOO+7lpFIu+/lhvNpQ6zkuc7rlXhw5f1OQhGPQGp4Fqxe9\n3LfpdEl8F8NeFw49oqtz97nH53SjK1w7BYnhe1cu8TjxNL3U4ruLyynaakwZdod2BV1Oj5Tj\nU5aUeleHxV0dFm9PuBXo55Oiwke0us52dW2K0uz3N075+fj6Bz9KKQQ7s5mdo5OTEzuOiliz\n/mvD46Y9OHvyfPi9Z7FJP7KLJBWUm+l26tXfZrRdz6ZlXunE1yQd6RPs9OqoNgd1a4287wkJ\nCfwEvucJqhgBhXEnx/eIf3sm2KO/Gr84YCkHmzefhyGHvhy0olFq0invy7kQ77IycPfQ6iYN\nAInm1vvOLH/UbnV0yKFTKaOmqdNW9/Ful130B+ysMdZKh3xung1OnzxJqebnct3WXL7ws6/V\nnle/2A+aWO17/G/vVQvWeIe9z059c+/Sm3tloqX5ufnS6t1G/ePpOc9SQwIac8YsD/N7s6W/\nceI0h25K6Y9PHzjzhoLiiKmOFabPx8Cgp4Du0CEZWxwDkkqh0GHCqsV27RUyngV5rj/6as/c\n/rfeP4wtbmaxcPf+1Xb67HH6MS4OQHf7UdosAGBpO4zsPu/2PfQcPlyjQvEWtradZ996+C4p\nG6jRkdjdpmbvSn5OMFJeBZ89nwIxCwf7JpUfvtq9/GQqGpmtCzwyu6vYK//p9vMvpZxds2dw\nbwXgZ3JyMqBT8W3JyQCkpCq34rW0tICYzMxMPl9fgzubV1xOh5i2/aFz3uOMGua/D9k8c4bn\niXHmv6jbAWO0q2xPl2RVrzKvpgCQVQL6nR+S/cbOsy54o5iTABjylOo63dt7EHS6ApDVc9x7\nv9eQtWMneEZuGt7h2rjxrR95B7zJE1MzXXjw8HobHfKXMTHyvTbevt96+bxVPje/vIgIfBFR\nXUCmufmkdTvdJ3SQA0BfOGgHCJqudoRnyv7fQZLDhAyCLLSrBhP8mH0HqAAwP8jvXiBFpR0w\nQ0WKT7r5uUkWpm6Mhmuivf+ynM7LskKNf0hW3oQq09f5wnPu5/e5HvOvkjaKJLNZrbAvfxF1\nJALo76B9qaUkTgjPqzHlgWarYdCzV2NAQsvW6wX3aHREUSrZMJfRlbDpCmOvHzt27PSDZN4l\nln6N8PL09PQ8z3GVhU4AuYzbK3s0rPJ1Mm2nhXD4tz6dmGltbT0n8MvrVW0AyA45XOnLS/IZ\nxDavmjuc/lrlrMxFFxlUOcbOdn1V84flHbUCINGrB0FrEE25LlMIUpis3Uzkv+T7/dVYRnCv\nmSgsA1uYZucX3Gsu1JNRgupAEi6AhjDRvCHy2AlO1PJO5h7v88Wbmi/3Prx6iFZlXCOJZgPW\nXn0xeNdkl6XnD7t2vXXR87j3PLYFSJNGVp7BtVod2paTJgGfWsvojeYrV/gl8sC2I4/TSoDi\nvEwACgoKfP+B/XnZIk9c3dh5qbFzrSqTuHAw99gx/lF5OHQmJPX8GGP7gKRS9l8/fiTHRUdE\nJoY/nvtthuW4Y7EFkFDvPX3V5tWTezRJuqNv4ERbjS8EFXyRl2FZSFdjKbnCq2Pp6hxsz9I5\ny6WQKzN0dGo8tAukguyLUv+94O/j63vq2rvMUnZms0lTpioc7zorBE0NO6nxzEjOD3bnIOpI\ntCH4BYlmJym5hg0b1i7GRq704jZhOWi50Xb+jZCpk4ZOPj6jZ583ASE7rDTEa/+nWngTE1ME\ntOvVqzEfIeVevVrj6uuYmLdwaE+35O5kA1aylaVLK/4lspqZT19iXu2ZXHOj/g5G/fn9X8M+\n6+68HRJw+Ezk6+R8KTX9ntbjXMy1OBwxmqP3Bo8GgCuhCQC6DB5cuY3ZdMiQzgi7h7ZT14xq\nVmU61tDQAOLi4uKANgCAuLg4APj48WP5k/K/0Lhhw1/0O5J4AcFISfmcCEBbW7vyk8wHD94A\n+kOGVPi8VQcONMLVRx++tDkxpgld/+XQ1nQVBoDERABGpqYcSotrWB+ICCrta+vtYiktdcvX\ntmlZA4pJEwwrabFMMNP5Och+cWSZ2+GYQrAaNJDJy8t55Ld+n+nhFRZNhfottSEmr91jmHaP\nYcIVFl7T1cFm/f8m0I4kh8n536hZBTXShB+YqAag+/ZPfP+NvcgjPJZB5MIh0fkhyV390ocL\ndABAocOELScuhoUc3zi2rRwg2dGkiywg0cxiYdA7Wg6WmmqMJ6jgACIPhweBzmQhWhaTZDYj\n4fYMpjqS0H7BusHcIULOlv95Z72FCgvizQbveJLJTYzIYydYRte6UrcByxxBvK84WJ+qflI9\nJ8BeHoDGyOOJRRRVlHh8pAaat2olDc0JF8vuMZT+CB6jAUDSyo/kgCLRSLGyJEmfGEzgvyTQ\nmKIoonvNJHMIUecXYHYqTr7pOVRbCoCUlpX7jaScD0FzjBuzAJayyT/naIXlr68IuelEhh0v\nnoWS5DBhLv0Lj6/lmib8+14zFiA1/Ci/qSn9yFBJgGXhRTRBO0kCGH6S/wYP+8Ka1GgSnYkg\n2n/hAQ81SCrY2w4AS1JWjg7DRtVdZx6QZDYjgrmOJIxfUAi8XdMOgMGal3ylXqxuDaDDOv75\ndatSreUL485MbiMLNGg7NSi+qLoYkWFHnNG1rvAfsMLMiFCW6J0kShzXHs6z2xeVrScgoaiu\nrigBSHbxfBTo0BCQ1OrjMttttkufFpIAoDLmAlE2FaKR4uZKkMJkxm2Kon9Uhgiye80EBRN1\n/nKx3ISb/u4zHQaatNFuqtpIXk5eSbWpdluTgQ4z3f1vJlSsvJg7GSUANHUmEBao6XgjMuyE\nQHFSUir5f+UnPbtx6VLEv58qumLxt/t+a+e7uoyZ7OZ58vE37pFO+aUJp2K3GIsB4h2WP+MV\ndK3g38VtxQAxk60VQQVoRTMicuEQ6UzC5bENUDVDEUUl7+kFAG3Xv+H5b3TUIKngIDsAkNYw\ncVrufb3cScaMznwhyWxGhkAdiQ4MtgYJzB0i5NLyGY+2DlAXA0vV0v1eehUxtmHXfuwWOiwe\nSOs3Fca139oGrLAzIvDVOf9r1LlDW9evWrV+y6GzD75UeRFWO3vK60Bqai6V93SvvW7ZAVTF\nDuOPfSimqG+hk/Q4U+YpdF0Qzr9H1IRkpLzwJ0hh4h5HqAl9SO8104ao89sFUlRqxFpLDV5R\nGQFASsNybcR3iqKW6csCEG9qvvxiQo11Q2nagx22rWQASLe03X6fWZ8yfZ1JhImbjj8iw+6P\nkBO1eUDTshNEEk0tNj7Oo4re7e/XmGOqZClbbouu6jKkkSY8M9hRGYBYC+vdT9Krz6ylPx/t\nGNqcBUDVOTSLokiiGRG5cMh0JoBo/4WT2tUgqeDupKgAj8n9dBVYAFjyOhYT15+495mHf1dg\nnWuHJLMZIYQdiTYMtgYJF4g80CEEJXNv+eJPITM7yQPSrccc+/CLqmrY0YQw9zFxm7ChMWCF\nnxGhzEpb5e3t7X09nuODzKitVppV7jpKaQ7e+ihLsDDvpTlfX0U///Atr3IJlJ9w3W/LygX/\nLF6/9+y/3wRZbpKMlCymUpiQURQ2SQmQHOjNM3H0jxuz20pzNl11+FnbNDu/XUCMe2dpAJDX\nHTTDw+f8jaiYt7EJCQmxb2Oibpz38ZgxSFcOAKS7eLwoIjoZRdwitCki0ZlEOICo6USGncA8\n2uVMn12PSIoufrSoJQCIN2xp1EWvsQRYrRZd3N9fCpDv6LB0675dKxzbywMQM1j5lD3VEKQJ\n/xE2XVcCAFiKrQeMX+Cx64DvYd8Du9wXjB+gzw6sJqk/53o6RZFFMyJ34TCR2pxs/4VIDQF8\nVKU58RF+K517asoAgJiS4eBZW89Gp1YzFgTQmRBmMpsRdCQCmG8NWjB3iJB3XXKe7R6uKQ40\n7r088vsZDsNOy2wcHVwtyE0ZEmiOFKKMCBnkebE5NP96bEhjABBXNbJ1nTXL1dZITRwAlG0C\nvjGXdJgYkpFCmMKEbho0QgjuNR88TGzpdiMQAAAgAElEQVRt0+z8NvPt5QCo2/jG8dpgLoj1\ntVEHIGcfEERyMqp2QQHJDSDQ2d+fQHi+DUHTiQw7wWEu8VdukKMigOajz6aUUhSVFjpeC41V\nVcXRYvLV8m3Z9CBHZQAqk69mEacJL02NdB+sJcNVUVntIZ53yhbSt+doAhDTtvd5ml5ElWa9\nC17Rr5k4xDTtj8ZXLl3LxirRwjSXqdTmZPsv34nUENxHVZL++vIetxGd2PcYJFU727l5XX5X\nLkW6ZyQwQs9sRrcjkfDbWoM/zB0i5GukliRfceuuCEjq6GgAhGfsGDRlSAYsUUaE2dYEs2i1\n1ii9N18LAEtvSnha2XgsTQufrMsC0HrZU5L68ScjPjo6Ojq+DkGdiUYK7fSJBGnQBFe9Nmr0\nZ37WNlHnZ0eK6bSR/8nKD54dQXYcojgpibYsKewjHDR1trYmEB5gStB0IsNOcBJvH6sFX0/X\nbirsQUZk2D1fqocq0YxiVukDQKvlHEctim/NVgdg6GAlUJpwqjD5YcD2xa4jBvQ16dyps0nf\nASNcF+8IiOI81kwSzciOIliYCpjanAZE1nbP3oRq1NVHVfjtSeDmaQP1FVicXYLJ1MBc4ZHZ\nTMDSaHQkEn57a/CAsUOEtXof8176jNIuC0JDZNgxB9GADXAgyIgwyJTg567WGi9XGACQszle\n5dhb2jFrOQj3CJqQHMZCHilUKkEatHd11J031RqHv7W9maTz9+wthSrBBbnC7lrSzqFCqk/d\nCBlNoLOZKYFwn54ETScy7JihbiEALo2RBTDoSMUVM3ZgzGqBDNh3o+TMehPMjL36aWhoaEy5\nxOVLueAsDcDiUPVzFt9CJ7eWgITehHNJpRSnYUd7Ycrcy5vIaWHak1iNOvmoin++urjHzbaT\nqgTAkVbyj+0ZVctsJryC60K92UFj6hDhtflt27ZtO/8aP5nS1IgVPRuD0LArLhY4i2EtEA3Y\nvv0IMrpajdAG7bzY1VqDHVXYeMfnqsp+2t4dgMKEq0ILMV0Xw+7SFJIplwSiNGgbBUkPTOte\nc7XG4W9tT5hI0PktbVoBaDI1nN8Wa3741CYA9JZyvcL724leTKDzxIkEwo5jCJpOZNgJHSHE\n7LgzswmqWlTcZhZ2NCMZMwtiU4buDEUUzajy09oXpvXk5S2oGqQr79KsD9e9lzr1aCYNABLK\n7YbP3XEhJu333QSohdz4yCMrXTb+aTXqHcwcIqRLcX5mevpxW4IBG+TYsGWP4ZOW7Txx/XkS\njzvsgkE0UhxnkWRE8CbIi13NsAsbLw/A6mi1kwRsD4eMS1CAvSyg4BjE+7WZH+ggD8iOOnOc\nsaUmc8dD781uCqDXnopzcGWXx3vu5txp/LLThG3mEpZO+15ztQrWZm3T+/bi/Mz09JyHS/VZ\ngJjmsJ33UrjNlkUp93YO0xQDWK2XPSGsH0OUPiHQ+TGJMN0KFudnpqfn1uoFFmWeoE1pevQJ\nz8Ur94QnFkCymdms1ZtWuXZXFSAwdKtWrYBv8fHxQNndBdW2pqZpaKvKKfX161cALXrufHeT\ndgT5IHvWLfp6aGkBLxITE4EOVT8QazH6+LUfGb3n7LcbIDdBudq/SaobO8w3dpjPu+Dh/hkZ\n9NUg4fLU5lMu0c2tKagatVewjPzP94KO+Pr6nbmVkAsxRf1+Uza6uo6z6a5e5Xo7kc5M0EDb\ndNw6ki0x0uShJNl+/3hrVNJ44L7715uPG7f+SuK7a0feXTtS5VNZ7SGrjvkv6d2IoW8Xl1Fs\nJCNdM+Mrb6iizI8PQj4+CPHxAKRVDUzMLCzMLcwtzIxbK/MLqFA7ZCMl54prIO2MCKO7vZtN\nNy82m+ykt2/fAoBcU33gaWJiImDA8XlSUhIAzabx57zyIetob8XdwQ4AMkPsh8iePn35XDTf\ndBf1k/T0dABKShXpeBs1aoSqTwA0btwYQH5+PlHZX46N7DP+0k9x9S42g7sopf97JfRpYZcZ\nGxb2pN49un3pXOiDz4mhyweafQm541XtPyUkJAAoK1d7K6iqqgIoKiqi9/3iMoqNZGC8zG/R\n5YGbnofO66Xl0b6Xafd2rTRUFKTFS35lp32Ne/no1r0Xqb8AuU5LfJd1IaofY7C6EOjctQsl\n/Aqym652aNqJ/22EG7ODerOuAwDjLfF8ZIquuDYCGoxcMpHA1U+2fCSKZkSy48Dc9gRRBcnU\nIBH++fzM5mmDWjcUA4AGWn3GrTlyO5FXl2DwymdqLZEvqlxEICmYuc30338BtjaEfTSK6Ltz\n09PprL8piqKoX9/f3Ar0WjtjpFkbNc6pndWgWcf+zvM8fUOiPmYItFtLOmBJMiLUNS+2hOX+\nKnc3Cy+OVwQa2G9fThBi2nVCCymIqfaedzLmW3oN/IcDwHD/sj9p/hxsmOvP7F0/w7Wvyh+8\nXGOAqk8qHpZFM6YLyb3mY3YAMGDXGzZ3lnYGl5CTcZuMINCGaeZzv1nmzXlZKjLNzWf5PSeK\nGP0bINL5D1VQ5LHjT+mPJ8c2LF7pFfG5ENItLOet27xiTGdlknU2FwwcXEy2ub8NOftxgVtL\n7iIFF4+fzYD8aKd2mYFfv+JHXt2+kSvmI6yVDvncPBucPnmSUs2P5bqtuXzhZ1+rPa9+Afjx\nlb4aeSTCzEGmBonwjXWjFp1lKehauC5ydXXqp6fAApCXlsb1v/NL6SpMzHQdrolluWJHUUG0\nCyZMHvpXQ9tByxiFuRl0Mg9LNTToa2/Q1376KqDg26sHkTcjb968GXk76l3S8/ATz8NP7ATE\nFbU79zW3MLewdXYxbkJXAdIBSz+jKwD6ebHvJAXcq9kQrOc3k+DUrFzVC8fOZUHB2bnNj8kA\nmjZtyrfgpk2bAu+SpcZ/jLZYNnn+7tE9r4328Nk5s4cqxxzegH2bvUGZS6yeoGdkJI+4N4fW\nnBh/0rkFPp1Y6/22eatW39/4bL0022+IEgDq54VN3m8Bya5dO9RaHgdhYd+BNlOX2aqy92JZ\nqiOWTTE8sdpnrd+Kgf9oSqibLgkOTuvYY9u7Y16PBgHAtbmGhhwFvIuMTFlhoF7xd9GbN3FA\ng44ddUlrqdhhwp6IcZ4JUTdvP3nx/lNqRk5+ibisfCO1Fvrtu5qaG2vJ1/FlywBEOv+hCooM\nO57kxV3euWLJptMvsihW407OSzdumD1QW7r2/6OB/sIHGQv5SqQr9/fc31vD1OrXSuLS34du\n3ZpQu1jr4XOXBB4zTZbS/cVLpLHFrmtXDI9GZcLgyVLaJsR/BCo79obv8hu+y2uRIwpCSwZR\n2nuSglv0cXHpw+MzKvPFqY1LNuy6HJ8PCfWek1YuNOchWa/JfXhwfXC8Qo9py621uUskXNhw\n4EGOjs3KqSZEjUfAhbFKI+mb5oFl4Z0ByDRpa+7Q1txh1lpQ+ckv7t+8eTPy5s2btx/HJjy+\nePjxxcOxLVwqhJlAXN3Yeamxs1DL7LPq+vXaZLI0rHcdHtSsl1XB6kwACgoKfMXZn2dmZsq3\ncdl9d5Cz1/xJS+f0vnpy+nZvz7Ht+P8vAbSn3AXD9OmWKWe3YJpO0Nb4QJdWVxeoIC0li9XF\n896SZ/1HHrY1inUY3l0p/dGF03e+AiqOU+1ozwMA8O0bAB0dHY5HLVu2BN48e/YC0AQAaZOp\n4zpsWxYT9UXF0tKyRgk8rO1hDWpI0kFMXrvHMO0ewwT65z8Ekc6/v4Iiw44XIZMMrU8VQVyl\n68QVG5eO7tiIhey0tGzuwtKKKgp1O+ZSjaZ9xk3rAwD0vSwVvDi6kL/VyMZOe0GQvUsr/kKs\nZubTl5gDsF9Krsj/LVIkFpW0WCZTehAcjSqk4xOqjV+fw/etWux+NPpnKRQM7NZ6eLrZ6jFl\n9DBL7sVt8zcFSTgGreEpol70ct+m0yXxXVxOj2SykixJ2QZSNJbtstwna5Zs0w6Wo9t269mj\nRw/ji8f2H775medK7e9HvYfT+B4AcEdJCUhNSUkps0S4k5KSgopjaSwV45nHnlq7bJo5bcO4\nziHHl3gdXD5URxhrdfpTLn3DDhLG687uTRi1KCg2KyUFih3G7wtY2E33x/FJT0f63Dm+5w5b\nSqHrglM7hysSqaugAPxMTk4GKmy75ORkAFJSlW8xLS0tICZTzvb6uSG1lFdhbQtm14kQPiLD\njheFhUUAUJL2xG9eP795/IU5F9N/HC2zcWZatYt102ZcE4aguT5Oy2FKAaLD5uwbLUys6fH4\ncW63bjRsjqI4P4c9E8/vpF1wDYR2c4gNI61BQsHlc1doH70P+zXSTjiueq5QYo3bDx/v6jrR\n0aKlPM97ilwozf387G7kzZs3b96MuPNvYlYJAJaMWhvTvmZmYzozpW45eYmRQSeCLt/+98W7\nxO8Z2XnFkg0UGqpptW7Xpa+VvfNIMy1ZQYVpYdimDQupzyIjM+eP4bnIyrh58xnAatu2ch9R\nsvnAFRdejjyzcvIcj2HtAket897dnPS7a8DQlCtrNDPw/fikNx/SpJrptVSTFQOgNtQ75q3j\nqcBbr5MLG7bsbjXSurMa6Tjs3l3yUFj04f1RczYbSwPAr6j9h6MBVqdOlVu6bJu4ZUseh4U4\nqbC2BUL4fYN5/nDnrx0WRVHCL/X/gZBxjcZeoCtsfTSDfQKXiKKspE+pvxQ1tFVlucznqS+u\nx3xDzK7+bhfRfuyWsTTuxqZdX7jpKqmVWZr57sbZM6GRT168TfiWkZ1XIqXYWF27Xdc+Ax3H\n2nVtUhYS0Z7FOktbjYcLN9EWJnp5B9mz6G9dmZjg4UPaalxfOPwqbeEFC+h71Yl0JvvtRqoO\ntLwTOs2Ar7O4OP6ok+mEoO4lBGfsOCmIv7pn5WLPk8/TKVbD9qMWeXrMG9JS4KU5g61Bwru1\n7Q3WvDRY8/LN6ra8pV6uMWi/9l2Hde+fr9RjRI3C5Efnj/j4+AXciM2mWPI65qMmurqOH9GT\n52Fr9sYr25i7eetxbEYRAJZsk7YmpmampqZmZqYmbVSJjVD2j0IwYJVurnMa437jK083sJSG\n5fJjAavMVQB8JxEG6M5IcVtN9BdGsTos//fxho5cR8Cvp0u6dNv0CiZbYx+46VT/lEp/cmjB\npMV+MWJKDdPTMwTsbOymq1cL+9rJueKqa+X3DQodR1Xca36eDcURJ+POOpX9Ch+39dRf8EBn\n0aN3m7oxd8yNuG/UA4h0/lMVFBl2f4bsp/unTVxx+vnPEkBGo8+EtTs3unau6k9nTxls64Qm\nbGGCWSb/tf8sl/mHo9O5dwIprWGrj/gsNVNjsQ07umrY4SEzL292m9BcH0t89veNoK2GCc7S\nbme+FxFKclM/pxbIqWmoyokDhDp3m3VkZle6asCexTqvOerUvVOjNHnMvSWJJ51Nx5xOhOb8\nkk/baRfMptrNoenCuDnEYGuQcGu6qtmBNPODPyKmNOYt9eOgucq0SNXpt1K9+jKiRhlUbkLk\nGT9fnyNn738ugJiS4UBn10muY4eVJakrI3qv/bx9kQ/f/igEwJJt2q5HXzMzMzMz077Ghip1\nOghCZm0HxHTe3G3501+Q1x00dqJ9f+P2rTSUFWUkiguyfnyNexEVHuTnHxabC+kuHk8eLqQ2\nG9MWXtpOgv6MhKwLTi1tAn6ItbDeee7IrC6NqiyOqfTHu8aO+OfiF0rVOTTu+FAex+mKk25u\nnTXd83LCL4w4VXDSlqDR2PyVhh2QeWfV4GHrH3AcEpFpO+3Mjf3Dyi/cfD45a/aZL1ou+3bZ\nazClRPELD6K+wZQeJBDpTNr5hainyLD7E3w9OqT9uMvpAFgy8rJFOXklkNZz9rlyxKVV5Y/L\nadgRmTK0Z5kvx4Z2HHvpp7h6l2E8ohnlAVKG00PueA2cymLRt6hy/H2ZeXkTTaNkNsRn/1kR\ntIWP+I18ff/9T/EmbXvocVwozovxmTVx6fF/04oASZUuju4+XlM6hTE39d9aZDRoy7NSg2mh\nd/YPqLnkK/18eqyp84l4SsPm8K0zY1tJcimCB1VvDo0W3s2hevIiDBktZX2qaPjJwrJsWtwp\nPDlc2jlUanQIO48C45RmvLl63NfH92jos+9FkFTtPHzsJNcpLoP1FYDyppPWNHNdumy2g6lB\nY6Gd6iUaKR1b565cFpSrbuN7L2BiS6594lecn2Nv1+AUOXt/L8wYR1c4IHVH4SjaM5Iy8PPq\nDOOh+2OLwVJs3X/EMDMj3SaKkkVZKbHRt0LOhb/PoiCpP+dK1C5L5m67/vH+HBe290osdAfP\nGlTLcenqlKRE8bnXXAPh7yfmnR6p5ki/bwQ61IMTfEQ6E3Z+4VZQ+BFU/ls8PTh16tSpB0lS\nUpfcnqMJQEzb3udpehFVmvUueEW/ZuIQ07Q/Gl8Zh4prNHb+kAVVIolmlE1SMnOxnYhKJlOD\nSLjk1mx1QNx0D0fsLur7udFq1UaXit2Z74xGbvt2YaKuBCDXdfnD7KqflHwNGqsrAbDUh/q8\nI8nHS1HMZ/v943Hsbs9QA9B9+ye+Uuz0CU1m3vlNWpXDPenwtRnq5Uc2pJRb97adumLXqevP\nk4WQg4LoR2EuD/rULSQzUplEHXIAUlSMv5ubm5t/DK2Kc+WP9+ffoUBqxFpLDX7LCCkNy7UR\n/JOL1ISoI02/Jbj6QoS5zi/kCta/IDF/GXHXDh48ePBaHMG/vAoL+wwoOe3wczVqJAGWgr71\n+rBHZ6capgZNMB97+jNzcc+qwj2aEfXJZ63fZwDsaEb/tAZSjnmd/006/R1Q986eT4GYqYM9\nR6iwV7uXn0xFI7N14Qnp6Z/u7hyizkLa2TV7PjGpidrwQ+E+ts3ynrhb2ex8XXEbkkoOnmLu\ndDS2RG3Q/oggV31S306Vm0N6aqp8mRAi1Cr9FgzbtGEBzyIj+d1Y5nL0/rdQkpOWkvLt27f0\nfAoAq3yLsf++5Ixvr2+d2bdmxvB2Yu8uH9ww16lfx6aNVA36jJi2cnfAjZiU/N+w/ZKVlQWg\neXP+Vw7Yn2dlZhAIJ9wlmZFy2BKmyy5/iOcaYjr+/cUlvavnzanKu5Bt27ZtC3lHs+r/SYpf\neAwavPrG10J53UEzPHzO34iKeRubkJAQ+zYm6sZ5H48Zg3TlCr/eWD14kOfLYpKCiTpSVlYd\nqiA8mOv8wq6gUM3E/yACLJhCnaVRNVcsm2+hk1tLQEJvwrmkyiR9RB47okD2lJMkgCHHOFMt\nsvMwyo6pjEH/3qMDgCYzRR47DuI9uwDQXfGM49nrdW0AdFz/vvxBqvdASQDdx1gx1RrlFLzc\nbq4EsDTsjiYUU1RpSugUQykAKv33vOCXf5o3zGX7/eMejjJitxiLAeIdlj/j5W8s+HdxWzFA\nzGTrRx4SwoYo6XBpfsqLiFO7V04d0Vu/IqNYpSfvXc0U0Lwh+lGYy4NuP5hkRhKGG7XuvZFs\nyiWhOD+bDuxA4kwMqNgre/bs2RO8y14OgLqNb1wBD8GCWF8bdQBy9gEkGZmIOhJ5TgtGYK7z\nC7mCIsOujggwNVybqAign0/Nqbck8cRITRak2ky/nCq4YUeT3EmNAXTezPna+ri5M4CGruEV\nT9ini2RcRIYdB2yHfJVfMMN3MAvQX8axJ89OjK3Ur+9vMGWyHizrKgdI6E06fHRGW2kAjc13\nPM+r/R9/N/XFsKMygx2VAYi1sN79JL36bmbpz0c7hjZnAVB1Ds1iXJe8T3ePrptgqi0HAGKK\n+gOmbDwdlUx/h7s0L+lZ+IkdyyZZ92ilxD4zyFyeN+byoB+YQjIjXSSoIC+EZdjRnXLJk5XR\nhLnJtu8ApvYTiTrSEyLVGYO5zi/kCtaLmyb/MbS0tIAXiYmJQLVEMGItRh+/9iOj95z9dgPk\nJvDfReAGWSD76YMkQTuaEV4Ta8MA1kfT030g9adj4mZkZAAQF+eIH/UsOpqCdLduHL+ompoa\ngOzGM9PTLzCts4KJ+5WzP3oPO+gzYSwApT6br4fO61APA0DVk18QULTed2L6k6H7Yy/M6aa1\nj+fR+1N7eV2oFAbpMYE++/18T117l1nKTjrs6jphZJ8WhKeoS4t/FRYWFhYW/iosLGI6yRtz\nedAd3j6dLdT4ar8BgXOH/C0U59HeT3xOtp9I1JG61KkSQoO5zi/sCgrXTvzvIcCar/jaFFVA\nfMAhHqdNcx6tNpYv/31sjxMs9QLtALAkZeXo4HLq8sQmAKDQcdRCjy0eC0d1VAAAxREnKxWL\n29pDHNBd9Oh7fHx8/NdUWgvTDMa2J4jWxxkZJGqQVDB8ZhMAehxbsZ+3GAPoujWBo8Rv+/oA\nUJt2jUBnskZLja9C3O2NfRsBUvpTz7yIr04qScHMwZyHQwDqePS+7rAHrIKupav7yfsfiS6p\nlOR8fnr12LYlE4ca6yhyRqiVbKzX03bKkVe8vpML5PuJzORBzyaZkUoIKsiLc07S0tLSTucE\nL4Fsyj1PUPKTBdoAjDzfF/Hl9AjiFxBFsslracPofiJRR6onMNP5hYso3EkdEeS6e/HVyWqD\nfHIGen8Lm6TEVeJnxJy+Vnte/QJsj6f71ZbRpYJLE5VczkNaw2QEvUD2BNGMAprTj2Nnm5vu\nRzuDlZRcowa043AQBdwiajqpiUpytG+IWPsPuzIutFBn+o0XXhZyAPVxm7Hegsc6i6NjN3aq\nkHq2QtfIPc7IaVD0qTC6JZN1JJLggnxj71Un73tCai5dYTk1bVXaLqZ6EqCYg6KUqHOnzoff\njf6QlJZVKKWo0kzPqE//EU4jujchiA8jGIStUXqs98sH5dGJH31ILyr/SFxRu3Mfc3MLcwtz\ni94dNeQI78QV5WXkCjBgS3MIUpvTE64X8dVIKItBQ3vKpQ91bYraQO8f/Q6khk/lE71WkBcQ\n0byxaemLJZ7vWZrDtgd4z+zZpMYeX/G3+/smO/4T+plqvezxW3dBPE9EHamewEDnFyYiw44X\nNN9tV2bozLhC+gYqirtx+kGylG6/USbqPESopJsHjkZl4ufNpVuu0S3X9lCUQxpRIHva0Yzs\nWayzdJNadhmUe5vBAMW01eiQe/sBbTVMcPYh3ZJtkwPEWw3z/w7F9jbjhhoWRp3wi/hUYuT+\n6ukygwqh9MND1CZebjhpuamP+znaJR/PYS+UaTGuUSP6yVEyMvxpF8yc+UX0C5K1xl8IUXqb\nbj0k74SlVdx8ZslpdOxlZm5uYW5hYdpFW1Hg/G71zNomjK/2hyHPHUKbgiBHpZGnMeLkz7NO\nvA9VMG7YBWbPf9J74KbnuYC0Gt/9xKt3PXv9+TMWIgCItmJ58sfPrnKoQejqL82Jj/Bb6dxT\nUwYAxJQMB8/aejY6tU77WnYAIK1h4rTc+3pcNv/QWcxtTwQyp0YPgpIpikoLm9OB002l2H3t\n4yqbFTnHbWQAeZfgkyQ61xOYvhX7d7VGPSHQDpBRa2M+ata6/WfvvPshvH1qwgGb8+DA4sWL\nNwTH8ywwPnj94sWLDzzIoSgi4T8M+x7oFf73BLjCxJRLpV9aYm1tPf7Qa35C8de9vb29r8eT\nFEy8yfs3bpgyxF/S+UWGHS+Ye7cRUZcXYUn668t73EaUJSaSVO1s5+Z1+V1tl/y4B+1Migrw\nmNxPV4EFgCWvYzFx/Yl7n3kcu2Du5f2LOTVISmZTkHjrsMeiWdNnuq33iUisHgsg7uxqNzc3\nr/s5RDr/3yNqDYFJ/fDyGzMNRTZScgLsZQEFxyDeyuQHOsgDsqPO5BAJC6q/sKj7JVnBptzf\nzdXJKgCr3wH+IYWrt0ZJdvz9EP/d7ssXzJ05bcq0mXMXLHff7R9yPyFb8OOOpd8fes21NtZv\npqyk3Ezf2Hqu18Pv9Xqx97d0fpFhV3/galEJ40XIPZA9L/jNbvQWpoy/vJlTg5GVN8Ml/42I\nWqM+QTRS8s+MkgNkHYP4BdPJC3SQBeQdT50gEA7iESit7vkhaCK8WDxkU+7vJj/QQQaQGXGS\nbzwk5iMTZYRP161+jlVKb8b1+uv8+/2dX0BEhl39oe4WFXeKf766uMfNtpOqBADYB9VBjXJo\nLUyZf3kzpwZzK++/Y01PDwHy6VXj/6k1/nrojZS3a9oBMFjzkm9ZL1a3BtBhzmwC4XXvuX9c\ndwvjdwf7JZxyiRCGmcvYJi8Rz1YYAoCswWiPoyFXQo66O+rLAECblfUjGjEXfn/nFxCRYVd/\nEJpFVQZRIHsyNcqhtTBl/uXNnBrMrbzr95qeHv8VD8d/DP4jhR2d2/zgD75lpB0wQ0W6TJrC\nvMLb1r2b/a4D0wJOuUTUmxDfVRDgeOKH9R0BSPc78KXi0ScvC2kAnTYIcMzxt/D7O7+A1NO7\nxCJ4INaIvYz++uVJ4OZpFkofzm2bYbXsalWh/M/3jq2faNayqV6/yZ6nX8mbTtl4Oupz0osL\nO+cNb68sxJjUPJJaCqJz/VSDXsnM6fxfQdQa9Qn+IyUzMxOAggL/uM3szzPT0wmEM/kl7q3f\n/LYpt74S7TN79uzZPtEE/xIbGwugq7V1ZdwaTRubLhWf1Ef+ms7/H+hy/4/weBEKJZC9uJS0\ntDSk+EVPoLJjI077+fr5n3uQ9AsSyu2s5i5zdWUnRSXVuQ4wp4ZAJTOn8/8totaox/AYKUpK\nSkBqSkoKoMn7n9n5IZSUlSn6wkrcg3oKAR0dbSDByPP9owU6fMTOOUg6nCMrWUi5Q/6L5Obm\nAmjSpAnHM3V1dQA5OTl/SKfa+Hs6v1D9fyLqAo0w6LW4+usQyJ4egiS1ZGB7gjk16pq1U7g6\n13cE3xX6f2yN/xdqGSnf95qxAKnhR2vmuq4k/chQSYBlsXkTgbAXj0Qfdc8PUSrYPVAaMD7l\nCkNJXhRmfo398DE1j/s91G8x4eHh4c8+MXU8kWtF6udGcwW/v/MLiMiw+zug9yJk8CjJ8zOb\npw1q3VAMAHtheuR2Yq4wdCbiJ3NqCFAyczr/JQgwC/8ft8ZfD82RErvFWAwQ77D8Ga+xXPDv\n4rZigJjJ1o9EwsKrSnUYuwf6+y9eSeUAABhESURBVMOd1t3MpSiKyvrXa3THxuxtGRmNPtN9\n/q1xFZVdNRMTpirILn+wV5XMh16DuTyMj49PrSdzxN/S+UWGXT2H6EUoSOy9/KRnNy5divj3\nU3Z5KcXf7vutne/qMmaym+fJx9/YQYpIFqafGHt5E62PPz0hUYNo5c2YzkJZ0/82/goPh4ha\nIbS2M4MdlQGItbDe/SS9uren9OejHUObswCoOodmEQozB1P3QOtJuFNCvvhble38sWTkG4gD\ngLSe87HYKvsXv8ewY6JkJvlLOr8opVi9JS5s75VYpFya7R7GUtC1GDXB1dWpn54Cv7Np0ooq\nClIEX5H7aMsIm2XXkosBSDS12BBycXGnzwcG95hx/Wd5r2ApW269fvGfTi4EWWhM7PDwLCM6\nE+U+MjHBw4e01Ziiqkg7VyxR3tV6la+JJszl0/sbW+P/HnaeN4IBm3N1hvHQ/bHFYCm27j9i\nmJmRbhNFyaKslNjoWyHnwt9nUZDUn3MlapdlIwA/SYS5UJD06MqlO6++ZEGheZveVoNNNHgn\n2BJBhztzW/Td/VlM2/7QOe9xRg3z34dsnjnD83pKM/sjtwPGaJcdr2YPVYvh2hEhdI8nEg1Y\nonx61kcz/IfTFWYWov5c184vMEI2FEUIDaYXTFTxo0UtAUC8YUujLnqNJcBqteji/v5SgHxH\nh6Vb9+1a4dheHoCYwcqnxSQLU1PGlmJE62PTniRqmJKsvEma+W9c0zO3mP4bW+P/HgF+7tLU\nSPfBWtyTTMlqD/G8w3lkiKYwF4dZZtRWK80qqWKlNAdvffRfCHR4ZVarVq1azbpS+eRn3OPH\njx/H/axz0cv0ASg5n+doxuIvwVPbSkNce3TAp7JEEmX++BVMHU/8e2Gi8wsZ0a3Yeo6YdMOG\nDekKy5G46xC8/cBHoPnoM0+Oj2jC+nFxQpdhPhNWZRa2mBx699AAeQAzxnaCrn3A2/37b3zP\nyKBdcMi4Rs+Y0Xm4P6Ear2ir8U9GBjMrQiKd6wlSckz1ur+xNf7vEeDnZqmaLrv8YWLUuVPn\nw+9Gf0hKyyqUUlRppmfUp/8IpxHdm1RJKEBT+Mn+yZPPwi5wkqU2+0HS8dGDF1z+CXFVo+HD\ne2ng673QkOjPVxYMHtv89XkHNSHVvp6SkxwXF4dkjvuhNxZ1G3lWGG7sxEQARqamHHE4xDWs\nD0QElfa19XaxlJa65WvbtNxr22aApYz36bvXwvOnOv0pVymVm5svJ1dvbhoz0fmFDRPWogi+\n/O4w6LxYqgdAd8Wz8r9jVukDQKvl0Ry63pqtDsBwJWNaiBAhQgRVw+tTem++FgCW3pTwtLLz\nSaVp4ZN1WQBaL6tDuhOeCBBllzlq+sCE5hWbqAign0/Ny5oliSdGarIg1Wb65dSKr/uTaSoK\nvj4M8Jjcr5XCyP+GL1BoiDx2v5/zLgr0jxkxyJcvAHR1dcv/1tXVBd5zPgHEDQz0gJRPn/6A\nfiJEiPibIXK0ULm/Sqo8eH3lSiIgZ73as59ymfuIpdzPc/Xwk2MuvLty5aO7UUsh6xvtM3v2\nWdgFzhrUSsgl1y+0tIAXiYmJQIeqH4i1GH382o+M3nP22w2Qm6DMftjIyjO41siS2paTJglR\nw+IfLy4d8/X1PX755Y8SALD/64KWE3Z+IbskRZkn/rsoKgIoLCws/1tWVhYA5OXlOYTYezQl\nJRAhQoQIWvxKijrtOaW/XtMJlwmE96VUeZ6QkACgnalpY86nyqambVGfkxP8BfTooQp8jIpK\n4/KZlMHs81dWG4s/2+p147crhtLs9+GHljgYazbrYDN/V+jLDEXDAZPWHr7x3sf29ysjIIJ1\nfjrCBIg8dr8fHR1thsKgk9GqFfAtPj4eKLv6rtrW1DQNbVU5hb5+/QqgRQsmFREhQsT/A0SO\nlhrCJlVmHkhISABQVlau+m+qqqoAioqKCPQqKcjJL65drOA/sn41H2GtdMjn5tng9MmTuCQ8\nkOu25vKFn32t9rz69dtUyv90L/CIj6/fmduJeeXPeq6LPr+kkxpDh9CETt06v5BdkiLD7vfT\nuX9/la3ez25EZCyZqsJbTIxp5/PgQR3+uR8TEZGAztoAANO1kZHVZIrfv48HGnTsyLAuIkSI\n+GspzX5/45Sfj69/8KOUQgDiSoYD7BydnJxszQiExfbpPwSyk96+fQsAck31gaeJiYmAAce/\nJyUlAdDU5JOlqQb15fQLOXnf2Y5LAPieV/1JBXJq2qr0N/IkLJYEHjNNltLlabg1tth17Yrh\n0ahMGLQHABQkP78f/ZWl3r5bZ032jk5J6oOjBwLvfUgTa9LG3HGiQ1c1Qbb/ilL/veDv41uW\nk42l0Krf5ElTpioc7zorBE0N/wqrTjidn5twXfjTh/z+izAWBp2Qd5tNGjZs1GdrHE+J/PPO\njQD50efqSdxvESJE1CPyEu/6rx3fV4vTqOi5LvpboSDCXKOuSFjuT+YspPDieEWggX0AyZT0\nN8bCrT8650RtHtC0zAUk0dRi4+M8qujd/n6NOTwPLGXLbdH5ZMXGBv1j21GVbblJqXcZueRg\neFw2+5bM3xE8RaidX8iIPHZ/AJl+Y+dZF7xRzEkADHlKdZ3u7T0IOl2Z00N/4YOMhXwl0pX7\ne+7vrWFqVW9umosQIeKPQ+RooSvcpIOlZc1YOKznN5Pg1Kzsr7wLx85lQcHZeRjJlKSjo10v\nTr+QwFzIITJKHq9zWnQtGeINW3bQFU98HrHUcU27BdFzr/+U6+gwe0xf9Yw7PjsDXtxY6ORh\n/nKdkTjtgqNPbT//HI27T1y8aNYEayPVv8YWYaTzCxtm7EURIkSIEPH/B5GjRehemeT7Jw8f\nPnz1PVmyudKroii7tRPj7+bm5uYfU+VhbpCjIoDmo8+mlFIUlRY6XguNVVXF0WLy1fIslOlB\njsoAVCZfLSb4urJofJJqRvYLva6+yyip/Kje/hB/tvMTIDLsRIgQIUIETdjvpMbdJ24Keppa\nxOWjmqHXaAozSH05/VKv4Vr950ThTl8RfF3R9+fB22cPbduY7eRroG06fv3xe1/yeWlSH/hr\nOr8o3IkI/sSF7d27d2/Yn1ZDhAgR9QEWCwB+Rl8MCDgVFPE+s1RowgBApUXtn2dj0lpDpbGK\nRmsTm3n7o9KEkM1cpt/YedbWAxVzEvhJdZ3u7e3tPZ3B0y/CJ/rQtGnTph2KZqr8L1zDnYJb\nuFN8Iop3KqHSwXr+7tCXSZ/un3R3tWiadvvISpdeWs3aDpt7ur7GsmG28wsTxkxGEXWknoRB\nL1tb/GEtRIgQUS8gcrQQemUywqfrVj90JKU343rm763i34QQfT9ci7ozswkAi0M/+YoVHB8K\nQMblouDfXpodd8N72WjjZhVpVZuazt58+sFnvk7W3wyTnV+oiAy7eks9cUeLDDsRIkTUoODr\n/ZPurhat5FkAIK7UZugc+468pix6ws9WGAKArMFoj6MhV0KOujvqywBAm5XPf1u1/jaYNuyS\ntvcEoLP034onkatMTU1NV0VySsVtMgKgv6zq+TyBKP75MnT3POuOymWXKVgKLfuOWR78se4l\nCxEGOr9wERl29RbGDTuilLWMaSFChIi/FyJHC3/hD+s7ApDud+BLxT988rKQBtBpw5/euKi3\nCPE1cc5JWlpa2ulc1adv1nUAYLwlns9/Fl1xbQQ0GHlaiN61X8mPAzZO7a+nyKoXDg6uCLHz\nCxmRYff7KSayqJjr0ERxkhjTQoQIEX8/RI4W7sKr7OQA9NrDGbcuaXdPAPLjw+qsYOn3/7V3\n72FR1Xkcx7+zokgKaXgBJMW8pFmUV3R11TLzgoYJqZVF9aggbl62XLVETS289ZSaIrn1eAkV\nI/NSg/dLayVpopSYhnlXVjAuAiEos3/gbiYyzIE5Z84c3q//YH5z/D7jPMxnzvmd7/fA0nFB\nAS19POt6+rQMCBq39EB6caWP6nCGb3ead3rvisjhc/aocGi7scOb386nJAl22tNL50mCHQD7\nUnSi5c+LO3cWERm8/va4Vbx+sIhISHwlyzLs7r1KBbuCc3uWTx8d3LNd6wd8G9RrcH+z1u0f\nD4mY8a99FwqUHObS1yuio6M3pyh6khFV4s1v5w96gp329BLs3vATkbZRJ4usihtMsAOgjKIT\nLSWLX+h+l7949jkjZdzdexV+fa4dfG+gb1l9jV0bD1p4OFeFcquGCrz57XxK0mm6PRtI06Z+\numiDrpORtQAM5x6/HqEzeyhaXCvk09hSs1DLGpCqaDpq6pYtx0Vcn3x/Z2xYIxGRvgN71r3S\nImJ3ypYtp2b6N7P1OFrKTz9zJa/8ZSWvj1KWX6MHPf76rlyRWs37DH9hQDd/vwYeNa5np50+\nun9LbOzOX89tHNcz+J7krSOalHGEH1e9sTJZ/EMXvPRIRQowtgq8+e1dgl1jImyhlzboipp2\nqlYFAFjUvJSREKrm7j11qHlh52rswFoiUtN/zJaLpYdFFJ3fGPZwDRFxfyYuy3p5+rynAZyx\ncwBT96d61Vwet3/7jt/DnnNzXB2KRtZqVxaAqki96ah5eXki0rBhw9t+5+XlJSK5ubkKjqMl\nFWfFXlq73Jwn1dpHfrZogE/pGQUuvkFL1r/1/cPTkzZ/tDZtSLiXkmNDFwh2DqAoUanYBr1O\n/6iN/ctb5NdrxAj1SgAAERF5emVWljpHtlhEREym27eV3PrJYoe5FqpQ79WQfbu/uSnV+kWE\ntyxr8lS11mMies8cue3fu/YVhQ9Va1A9VEOwcwQSFQBoSo3de04pOblI5OGuXe+zssaza9cH\nZVtKcvLPMpRddE6HYFelWTISl82OWplwMDX9umv95h37hU6ZGh5Qj/slAOiTJSt5XdTkpO7m\neYHKnpgQ0bT0lpLSvwz+zBIfUon69C89XUQaNWpkdZGvr69ISnp6+t0frlbD1dVValSzf3Ww\nA4KdY+giUWXvHNOlf3Rq0a0fM69uOpmYYP7JfGhJLw9NCwGAchWc3fFh5OR3Yw9nFgd3VPA8\nFferqebgouELv7d1cadxn461/QXJzRWR2rVrW11U8nhOTs7dH35mTUGBzf8gtEawcwR9JKqj\nC8ZGpxaJW6vnI98c1rZO1uE1s2etO/nL0rELwo7N9NeuDgAQEZEbF3a/P3P+ul1Hzmab6jT2\n7/3ymzNe697QJJKbsnbmhMmLtp+7LlKr5aB/jOyi4Kgq7ldTzdmvY2M/t3VxwSAlwe7GTRsW\nlexBvHnjhs2HhX4Q7BxAJ4nKCXs7ATCuK1+8GBCy7lJxyU9Xr14+lbR779kdB8f9J6JX6OrU\nAnHx6jZ62rzpI7s0NPwnV6dxq0umSpap8MLeZe+tOJhhS0wrrfTmwjvc2SHv+vm9qz9ev3Vf\nYsq5K1dzCl3v9WzQuE1Aj75DRwzv3si1QjVANY7ut1IF6WXWtRP2dgJgVMUHSjq2u/u/Mj/2\ny62bP53zUptaItUf7dzeTcTF54mJ8SdUnYZQnHl0zT/7TfxSzX/DLoqzkmMn92/qJiLi4vXX\n8CUHMpQ8XXmHPMZUOBnDf+/RodTUVBHpEBT0x+bV+wcNah+x+9vU1FQRzU6VOWFvJwBG9bPZ\nfFrELXDRjk9ebiAi0mfgE/WuNBux9cAP4js0LnHtEB/VNiFXdPee5q6f37Fk2qR3ViX9Vizu\nrYLffjfq9Wda1FJ2DIU7Dis/pgKac3SyrIJKbrlSZ9a1EndrHU4/cQAOYX7pHhHp9mHabb+7\nvLiriEibWccre/Si87vmjezb7gEvT0/vZm37hC/cl1byF/jasTUTn2pcci2xVstBkVsvlHMk\nB7n52+FVE3s3qSkiUt2n599jEq+UnhqhAjuMqYDWOGOnPV11y7S1t5OfX30NqwJQ1eTn54tI\ngwYNbvvdrSsKzZs3r9yxnXv3XsHpbYsjJ0WtOZppMd37yNDIqHfHBz6gUa89xlQ4Ix2+iasI\nnXTLtLW3k8USr2IVAKo6K994XVwq9UllSZz/+rpLxeLu/8q0ScGPuGcdiY+aterY4nG99508\nkHrD54mJi6KnB7dUeEVTC8VXD62ePSly6e7zheLauNf4mfOmvtjOs6yBETYK8/X9SgI/uhBT\nbpd8kULGVDgjgp2jOL5bpqKdFuqUAAC3U+MbryN371VY/inzB1Mnz437Mcdiuu+xF6bMmf1a\nHz+73H169eLFi3I1v/yFInKcMRXOiGCnPb10y3TC3k4AjE2Nb7wlqbB9v35/XOX1DgxsJ1u/\nkTZhM/SY6kQ2j2gdtLZIqtXr8OrUOVOef7SOSa5lZFy7+2JXj3ru6nxSpNtjTAW0RrDTnjN2\nywQAVan3jVfN3XtqKSwsEhG5mXHok/FPfjLe+mL1Luzk2mNMBbRGsAMAOJx633jV272nHp1c\n2LnBmApnpNM3NXTj1sjtueZ5jq4EACpMJ/er2UjtCzsntyxYcKb8ZRm5IhUYUwHHMlm077AB\nm9xKVN3N8wIdVMHtTTu5KxaAc4oPMT1r89xVFe9XU0HSR+Exh6Vd2LJRbW1+TojJZPOr0bmz\nHDhg62LneumMjDN2eqRZG3TbR26rWQUAqEcnlzXVcGp7TMznEvykkmAnIiJNeob2tGFSRM2M\njccN+tIZGcHOMWxPVF3UK8K5m3YCgC24X620DmNWrLDt7NoylSuB/fGB7Qj6SFRO27QTAKAB\nc5jvKJu7GUMvCHba00sbdGds2gkA0Eq+km7G0AuCnfb00gbdCZt2AgAAayo5dA4VUFYbdBFt\nE5UTNu0EAADWcMZOe3ppg+6ETTsBoCrITz9zJa/8ZRVpILfqQkLHWebv3tnU4a0gv7svObNp\n9rLvcpsOivRUfng4Hh/g2tNVG3Rbm3b6+dXXsCoAqMrMo5va3ntPGcv+j9+OiXcZ1uOdMpd4\nFf20ZG7czdPtl4uIzd2MH3z6jYEt7VQkKoNg5yg6aYNu68htGhQDgFZU7L1n3pDwu7gNC+lf\ns8wlNQNDAt3i4swbknqLiPy4auJEGw4c7Eew0weCnaPYmqjU6+Wt6A+HOiUAAEpTsfdeSkqe\nSKtWrdysrHFr1aqxyImUC/kiNncz7uhnnwJRWQQ77emlDTpNOwGgqklLExFvb2+ri7y9vUVO\nXM4sEFHSzRi6QLDTHm3QAQCVV5FZsdnZIuLu7m51Ucnj2flFlasPDkG7EwAAnNGp7TExMTHb\nTyl5Tt26IpKWlmZ1UcnjdWtXr0RxcBSCHQAAVcVDD5lEjuzdm21lTdaePUdETG3u99CsLNgP\nwQ4AgKqib2Cnv0jhVwvmHy0sY8X1w3Pmm4vkLwH9H1NvkzfUQ7ADAKCqaDZqyhBPuZkc9fSQ\nxT9kWe541JJ58IOQoHnHiqX+c2+NGrkqMzMzc1WQQwpFRXHzBAAAVYZH0JLY0YcGRKduGtux\nyZLegwf2bNu8oUf1opy01KR9mzfsOJljkeotx679cIB7dcn7efWsjafdu4SXP6YirDN9sfSB\nYAcAQBVyX58l3+70DQ2dlXD2xPYVJ7av+NOjbn6B01avnNytjojkffnehLnxLsPiZ5R5sP+P\nqRge9yzJThcIdgAA6IqKs2JFRMRUv8eb5l9eTdyw9osd+5N+uZSRU1jDo55Pi7Z/6z34ucGd\nGt66G7ZAyZiKrdefDXatYD2wJ4IdAAC6ouKs2D9U9woYOiFg6ISyV5xVMqYi5ZwEt7B7kVCO\nYAcAgK7oZEBRmpIxFZcvixDs9IBgBwCAruhkQFG2kjEV2dY640FDtDsBAACl1VUypqJuXU1q\nQrkIdgAAoLTWSsZUtGmtWV2wimAHAABKq6dkTEVfT01rQ5kIdgAA4C6UjKlo6pAKUZrJYrnz\nvwoAAEBEftsWETAgOvWGmDweLHNMRULiwl51HF0pbiHYAQCAsljS90WFhs5KOFtQ+rH/jang\nMqyOEOwAAIBVRWnljamAXhDsAAAADIKbJwAAAAyCYAcAAGAQBDsAAACDINgBAAAYBMEOAADA\nIAh2AAAABkGwAwAAMAiCHQAAgEEQ7AAAAAyCYAcAAGAQBDsAAACDINgBAAAYBMEOAADAIAh2\nAAAABkGwAwAAMAiCHQAAgEEQ7AAAAAyCYAcAAGAQBDsAAACDINgBAAAYBMEOAADAIAh2AAAA\nBkGwAwAAMAiCHQAAgEEQ7AAAAAyCYAcAAGAQBDsAAACDINgBAAAYBMEOAADAIAh2AAAABkGw\nAwAAMAiCHQAAgEEQ7AAAAAyCYAcAAGAQBDsAAACDINgBAAAYBMEOAADAIAh2AAAABkGwAwAA\nMAiCHQAAgEEQ7AAAAAyCYAcAAGAQBDsAAACD+C9AK1UUxH8tsgAAAABJRU5ErkJggg==", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["\n", "# pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/vcf.pdf\")\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/wgs/RLGS10-recurrent_wgs_seq/pass_intersect.bed\", sep='\\t')\n", "input_df$gene = fct_reorder(input_df$gene, input_df$count, mean, .desc=TRUE)\n", "input_df = input_df[input_df$gene %in% levels(input_df$gene)[1:50],]\n", "# genes_of_interest = c(\"EGFR\", \"VEGF\", \"PDGFRA\", \"OLIG2\", \"TP53\", \"PI3K\", \"IDH1\", \"PTEN\", \"CHKN2\", \"NFKB\", \"NF1\", \"TERT\", \"CDKN2A\", \"ATRX\", \"FGFR\", \"RB1\")\n", "\n", "# input_df = input_df[input_df$gene %in% genes_of_interest,]\n", "\n", "# head( input_df)\n", "# p = ggplot(input_df, aes(x = gene)) + plot_theme() +\n", "# # geom_histogram() +\n", "\n", "# # geom_bar(position=\"stack\", stat=\"identity\") +\n", "\n", "# ylab(\"Number of mutations\") + xlab(\"Gene\") + ggtitle('')\n", "\n", "p = ggplot(input_df, aes(x = gene, y = count)) + plot_theme() +\n", "geom_bar(stat=\"identity\", width=1, color=\"black\") +\n", "\n", "ylab(\"Tumor-Normal Ratio\") + xlab(\"\") + ggtitle('') +\n", "theme(axis.text=element_text(size=10))\n", "\n", "\n", "print(p)"]}, {"cell_type": "code", "execution_count": null, "id": "f925a8b4-fe6a-4ce7-b048-64b2de1c05e5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 20, "id": "c003496b-0062-4288-b00c-4fa3b74861c2", "metadata": {}, "outputs": [{"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/top_50_sv_genes.pdf\")\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t1_only.tsv\", sep='\\t')\n", "input_df$gene = fct_reorder(input_df$gene, input_df$coverage, sum, .desc=TRUE)\n", "input_df = input_df[input_df$gene %in% levels(input_df$gene)[1:50],]\n", "\n", "p = ggplot(input_df, aes(x = gene, y = coverage, fill = type)) + plot_theme() +\n", "geom_bar(stat=\"identity\", width=1, color=\"black\") +\n", "\n", "ylab(\"Read coverage\") + xlab(\"\") + ggtitle('Primary tumor only') +\n", "theme(axis.text=element_text(size=10))\n", "\n", "print(p)\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t2_only.tsv\", sep='\\t')\n", "input_df$gene = fct_reorder(input_df$gene, input_df$coverage, sum, .desc=TRUE)\n", "input_df = input_df[input_df$gene %in% levels(input_df$gene)[1:50],]\n", "\n", "p = ggplot(input_df, aes(x = gene, y = coverage, fill = type)) + plot_theme() +\n", "geom_bar(stat=\"identity\", width=1, color=\"black\") +\n", "\n", "ylab(\"Read coverage\") + xlab(\"\") + ggtitle('Recurrent tumor only') +\n", "theme(axis.text=element_text(size=10))\n", "\n", "print(p)\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t1_t2.tsv\", sep='\\t')\n", "input_df$gene = fct_reorder(input_df$gene, input_df$coverage, sum, .desc=TRUE)\n", "input_df = input_df[input_df$gene %in% levels(input_df$gene)[1:50],]\n", "\n", "p = ggplot(input_df, aes(x = gene, y = coverage, fill = type)) + plot_theme() +\n", "geom_bar(stat=\"identity\", width=1, color=\"black\") +\n", "\n", "ylab(\"Read coverage\") + xlab(\"\") + ggtitle('Primary and recurrent')+\n", "theme(axis.text=element_text(size=10))\n", "\n", "print(p)\n", "\n", "dev.off()\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 29, "id": "4130402e-f240-4e21-ad78-d52c7570000b", "metadata": {}, "outputs": [{"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/top_sv_support.pdf\")\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t1_only.tsv\", sep='\\t')\n", "input_df$gene = fct_reorder(input_df$gene, input_df$support, sum, .desc=TRUE)\n", "input_df = input_df[input_df$gene %in% levels(input_df$gene)[1:50],]\n", "\n", "p = ggplot(input_df, aes(x = gene, y = support, fill = type)) + plot_theme() +\n", "geom_bar(stat=\"identity\", width=1, color=\"black\") +\n", "\n", "ylab(\"Support\") + xlab(\"\") + ggtitle('Primary tumor only')\n", "print(p)\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t2_only.tsv\", sep='\\t')\n", "input_df$gene = fct_reorder(input_df$gene, input_df$support, sum, .desc=TRUE)\n", "input_df = input_df[input_df$gene %in% levels(input_df$gene)[1:50],]\n", "\n", "p = ggplot(input_df, aes(x = gene, y = support, fill = type)) + plot_theme() +\n", "geom_bar(stat=\"identity\", width=1, color=\"black\") +\n", "\n", "ylab(\"Support\") + xlab(\"\") + ggtitle('Recurrent tumor only')\n", "print(p)\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t1_t2.tsv\", sep='\\t')\n", "\n", "input_df$gene = fct_reorder(input_df$gene, input_df$support, sum, .desc=TRUE)\n", "input_df = input_df[input_df$gene %in% levels(input_df$gene)[1:50],]\n", "\n", "p = ggplot(input_df, aes(x = gene, y = support, fill = type)) + plot_theme() +\n", "geom_bar(stat=\"identity\", width=1, color=\"black\") +\n", "\n", "ylab(\"Support\") + xlab(\"\") + ggtitle('Primary and recurrent')\n", "print(p)\n", "\n", "p = ggplot(input_df, aes(x = gene, y = coverage, fill = type)) + plot_theme() +\n", "geom_bar(stat=\"identity\", width=1, color=\"black\") +\n", "\n", "ylab(\"coverage\") + xlab(\"\") + ggtitle('Primary and recurrent')\n", "print(p)\n", "\n", "dev.off()\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 30, "id": "3956a9d4-757d-4230-8790-b3c4819594b0", "metadata": {}, "outputs": [{"ename": "ERROR", "evalue": "Error in library(maftools): there is no package called 'maftools'\n", "output_type": "error", "traceback": ["Error in library(maftools): there is no package called 'maftools'\nTraceback:\n", "1. library(maftools)"]}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5d0d5fc5-8f4e-4bea-9dde-7d98501b13b3", "metadata": {}, "outputs": [], "source": ["gbm = read.maf(maf = \"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/wgs/RLGS10-recurrent_wgs_seq/somatic.snvs.maf\")\n", "\n", "\n", "plot_spec_gene = function(gene){\n", "try(\n", "lollipopPlot(\n", "  maf = gbm,\n", "  gene = gene,\n", "  AACol = 'AAChange.refGene',\n", "  showMutationRate = FALSE)\n", ")\n", "}\n", "\n", "\n", "genes_of_interest = c(\"EGFR\", \"VEGF\", \"PDGFRA\", \"OLIG2\", \"TP53\", \"PI3K\", \"IDH1\", \"PTEN\", \"CHKN2\", \"NFKB\", \"NF1\", \"TERT\", \"CDKN2A\", \"ATRX\", \"FGFR\", \"RB1\")\n", "\n", "# create a pdf\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/recurrent_snvs.pdf\")\n", "\n", "plotmafSummary(maf = gbm, rmOutlier = TRUE, addStat = 'median', dashboard = TRUE, titvRaw = FALSE)\n", "\n", "oncoplot(maf = gbm, top = 20)\n", "\n", "lapply(genes_of_interest, plot_spec_gene)\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": null, "id": "3916adea-c243-465c-b1bf-bcb59a20eb74", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d7d4abbd-228d-4f50-a416-b052e1f7c12d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 30, "id": "02a9f39d-8496-479d-a61d-529d9d0eb98d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[1m\u001b[22m`stat_bin()` using `bins = 30`. Pick better value with `binwidth`.\n", "Warning message in `[.data.frame`(input_df, !is.na(as.numeric(input_df$support)), :\n", "\"NAs introduced by coercion\"\n", "Warning message:\n", "\"\u001b[1m\u001b[22mRemoved 3 rows containing missing values (`geom_point()`).\"\n"]}, {"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/tmp/t2_genes.tsv\", sep='\\t')\n", "\n", "# pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/sig_sv_genes.pdf\")\n", "\n", "p = ggplot(input_df, aes(x = (length), fill = type)) + plot_theme() +\n", "geom_histogram() +\n", "\n", "# geom_bar(position=\"stack\", stat=\"identity\") +\n", "\n", "# scale_colour_manual(values=sig_colours) +\n", "\n", "# geom_hline(yintercept=1, linetype='solid', color='grey', size=0.5) +\n", "# geom_errorbar(aes(ymin=lower_ci, ymax=upper_ci),width=0)+\n", "# geom_point(size=2) +\n", "\n", "# scale_x_binned(n.breaks = 10000) +\n", "# scale_y_continuous(trans='log10') +\n", "scale_x_continuous(trans='log10') +\n", "\n", "\n", "ylab(\"Recurrent - Germline events\") + xlab(\"Length (bp)\") + ggtitle('')\n", "print(p)\n", "\n", "\n", "\n", "p = ggplot(input_df, aes(x = length, y = coverage, color = type)) + plot_theme() +\n", "geom_point() +\n", "\n", "scale_x_continuous(trans='log10') +\n", "\n", "ylab(\"Sequencing coverage\") + xlab(\"Length (bp)\") + ggtitle('')\n", "print(p)\n", "\n", "\n", "sub_df = input_df[!is.na(as.numeric(input_df$support)),]\n", "sub_df$support = as.numeric(sub_df$support)\n", "p = ggplot(sub_df, aes(x = support, y = coverage, color = type)) + plot_theme() +\n", "geom_point() +\n", "\n", "xlim(0,25)+\n", "ylab(\"Coverage\") + xlab(\"Read support\") + ggtitle('')\n", "print(p)\n", "\n", "# theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), legend.position='right')\n", "\n", "\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": null, "id": "237df693-e3d8-4404-ab7c-eaa4c7cce6dd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dea7c419-715c-4403-8c95-f0832cc18089", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6cee1caf-9cc9-4224-8b56-c784e8e59864", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cf1d4e22-c210-4f19-9a89-cfa25f26090c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0e75089b-4c0f-4bee-93c5-5926a67f1038", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3423e7d2-bf2e-4b57-8a2c-89ddaff9f1ef", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dc8058f0-358d-4e58-aaa6-a1c297e176a4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "6e3bbf32-96f8-4ae8-bfe5-4720de85fcd5", "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A data.frame: 6 × 7</caption>\n", "<thead>\n", "\t<tr><th></th><th scope=col>chr</th><th scope=col>start</th><th scope=col>stop</th><th scope=col>type</th><th scope=col>length</th><th scope=col>support</th><th scope=col>coverage</th></tr>\n", "\t<tr><th></th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><th scope=row>1</th><td>chr1</td><td>140033</td><td>140033</td><td>INS</td><td>3397</td><td>1</td><td>38</td></tr>\n", "\t<tr><th scope=row>2</th><td>chr1</td><td>492199</td><td>492199</td><td>INS</td><td>1121</td><td>2</td><td>13</td></tr>\n", "\t<tr><th scope=row>3</th><td>chr1</td><td>727291</td><td>727291</td><td>INS</td><td>  94</td><td>4</td><td>82</td></tr>\n", "\t<tr><th scope=row>4</th><td>chr1</td><td>740741</td><td>740741</td><td>INS</td><td>5668</td><td>2</td><td>91</td></tr>\n", "\t<tr><th scope=row>5</th><td>chr1</td><td>744868</td><td>744868</td><td>INS</td><td>  43</td><td>4</td><td>80</td></tr>\n", "\t<tr><th scope=row>6</th><td>chr1</td><td>748218</td><td>748218</td><td>INS</td><td> 331</td><td>3</td><td>73</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A data.frame: 6 × 7\n", "\\begin{tabular}{r|lllllll}\n", "  & chr & start & stop & type & length & support & coverage\\\\\n", "  & <chr> & <int> & <chr> & <chr> & <int> & <chr> & <int>\\\\\n", "\\hline\n", "\t1 & chr1 & 140033 & 140033 & INS & 3397 & 1 & 38\\\\\n", "\t2 & chr1 & 492199 & 492199 & INS & 1121 & 2 & 13\\\\\n", "\t3 & chr1 & 727291 & 727291 & INS &   94 & 4 & 82\\\\\n", "\t4 & chr1 & 740741 & 740741 & INS & 5668 & 2 & 91\\\\\n", "\t5 & chr1 & 744868 & 744868 & INS &   43 & 4 & 80\\\\\n", "\t6 & chr1 & 748218 & 748218 & INS &  331 & 3 & 73\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A data.frame: 6 × 7\n", "\n", "| <!--/--> | chr &lt;chr&gt; | start &lt;int&gt; | stop &lt;chr&gt; | type &lt;chr&gt; | length &lt;int&gt; | support &lt;chr&gt; | coverage &lt;int&gt; |\n", "|---|---|---|---|---|---|---|---|\n", "| 1 | chr1 | 140033 | 140033 | INS | 3397 | 1 | 38 |\n", "| 2 | chr1 | 492199 | 492199 | INS | 1121 | 2 | 13 |\n", "| 3 | chr1 | 727291 | 727291 | INS |   94 | 4 | 82 |\n", "| 4 | chr1 | 740741 | 740741 | INS | 5668 | 2 | 91 |\n", "| 5 | chr1 | 744868 | 744868 | INS |   43 | 4 | 80 |\n", "| 6 | chr1 | 748218 | 748218 | INS |  331 | 3 | 73 |\n", "\n"], "text/plain": ["  chr  start  stop   type length support coverage\n", "1 chr1 140033 140033 INS  3397   1       38      \n", "2 chr1 492199 492199 INS  1121   2       13      \n", "3 chr1 727291 727291 INS    94   4       82      \n", "4 chr1 740741 740741 INS  5668   2       91      \n", "5 chr1 744868 744868 INS    43   4       80      \n", "6 chr1 748218 748218 INS   331   3       73      "]}, "metadata": {}, "output_type": "display_data"}], "source": ["input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/tmp/sniffles.csv\")\n", "head(input_df)"]}, {"cell_type": "code", "execution_count": 30, "id": "85f5dedb-d6d9-4598-bfc5-16fd4d89ea91", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[1m\u001b[22m`stat_bin()` using `bins = 30`. Pick better value with `binwidth`.\n", "Warning message in `[.data.frame`(input_df, !is.na(as.numeric(input_df$support)), :\n", "\"NAs introduced by coercion\"\n", "Warning message:\n", "\"\u001b[1m\u001b[22mRemoved 3 rows containing missing values (`geom_point()`).\"\n"]}, {"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/sniffles.pdf\")\n", "\n", "p = ggplot(input_df, aes(x = (length), fill = type)) + plot_theme() +\n", "geom_histogram() +\n", "\n", "# geom_bar(position=\"stack\", stat=\"identity\") +\n", "\n", "# scale_colour_manual(values=sig_colours) +\n", "\n", "# geom_hline(yintercept=1, linetype='solid', color='grey', size=0.5) +\n", "# geom_errorbar(aes(ymin=lower_ci, ymax=upper_ci),width=0)+\n", "# geom_point(size=2) +\n", "\n", "# scale_x_binned(n.breaks = 10000) +\n", "# scale_y_continuous(trans='log10') +\n", "scale_x_continuous(trans='log10') +\n", "\n", "\n", "ylab(\"Recurrent - Germline events\") + xlab(\"Length (bp)\") + ggtitle('')\n", "print(p)\n", "\n", "\n", "\n", "p = ggplot(input_df, aes(x = length, y = coverage, color = type)) + plot_theme() +\n", "geom_point() +\n", "\n", "scale_x_continuous(trans='log10') +\n", "\n", "ylab(\"Sequencing coverage\") + xlab(\"Length (bp)\") + ggtitle('')\n", "print(p)\n", "\n", "\n", "sub_df = input_df[!is.na(as.numeric(input_df$support)),]\n", "sub_df$support = as.numeric(sub_df$support)\n", "p = ggplot(sub_df, aes(x = support, y = coverage, color = type)) + plot_theme() +\n", "geom_point() +\n", "\n", "xlim(0,25)+\n", "ylab(\"Coverage\") + xlab(\"Read support\") + ggtitle('')\n", "print(p)\n", "\n", "# theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), legend.position='right')\n", "\n", "\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": null, "id": "e93fb33a-9971-40a4-83f5-b7431387f81f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f89d4141-26cc-4b3b-a940-18abbb2357f8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "588b086e-a8af-4e0a-97dc-15522a06fe83", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 47, "id": "70d02a9c-bd21-46a3-8b9f-49466c90d6bd", "metadata": {}, "outputs": [{"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/glass_primary_recurrent.tsv\", sep='\\t')\n", "\n", "\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/glass_primary_recurrent.pdf\")\n", "\n", "\n", "p = ggplot(input_df, aes(x = tumor, y = signature_name, fill=signature_name)) + plot_theme() +\n", "geom_bar(position=\"stack\", stat=\"identity\") +\n", "\n", "# scale_colour_manual(values=sig_colours) +\n", "\n", "# geom_hline(yintercept=1, linetype='solid', color='grey', size=0.5) +\n", "# geom_errorbar(aes(ymin=lower_ci, ymax=upper_ci),width=0)+\n", "# geom_point(size=2) +\n", "\n", "# scale_x_binned(n.breaks = 10000) +\n", "# scale_y_continuous(trans='log10') +\n", "\n", "ylab(\"Expression subtype\") + xlab(\"\") + ggtitle('') +\n", "\n", "theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), legend.position='right')\n", "\n", "print(p)\n", "\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": 4, "id": "5186fa0b-78d1-40c3-bdc7-62ddd53bdf04", "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A data.frame: 6 × 3</caption>\n", "<thead>\n", "\t<tr><th></th><th scope=col>sample</th><th scope=col>class</th><th scope=col>score</th></tr>\n", "\t<tr><th></th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;dbl&gt;</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><th scope=row>1</th><td>primary_rna_seq  </td><td>mesenchymal</td><td> 0.14171423</td></tr>\n", "\t<tr><th scope=row>2</th><td>primary_rna_seq  </td><td>classical  </td><td> 0.23119252</td></tr>\n", "\t<tr><th scope=row>3</th><td>primary_rna_seq  </td><td>proneural  </td><td> 0.51256372</td></tr>\n", "\t<tr><th scope=row>4</th><td>recurrent_rna_seq</td><td>mesenchymal</td><td> 0.14601973</td></tr>\n", "\t<tr><th scope=row>5</th><td>recurrent_rna_seq</td><td>classical  </td><td>-0.01757637</td></tr>\n", "\t<tr><th scope=row>6</th><td>recurrent_rna_seq</td><td>proneural  </td><td> 0.56030892</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A data.frame: 6 × 3\n", "\\begin{tabular}{r|lll}\n", "  & sample & class & score\\\\\n", "  & <chr> & <chr> & <dbl>\\\\\n", "\\hline\n", "\t1 & primary\\_rna\\_seq   & mesenchymal &  0.14171423\\\\\n", "\t2 & primary\\_rna\\_seq   & classical   &  0.23119252\\\\\n", "\t3 & primary\\_rna\\_seq   & proneural   &  0.51256372\\\\\n", "\t4 & recurrent\\_rna\\_seq & mesenchymal &  0.14601973\\\\\n", "\t5 & recurrent\\_rna\\_seq & classical   & -0.01757637\\\\\n", "\t6 & recurrent\\_rna\\_seq & proneural   &  0.56030892\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A data.frame: 6 × 3\n", "\n", "| <!--/--> | sample &lt;chr&gt; | class &lt;chr&gt; | score &lt;dbl&gt; |\n", "|---|---|---|---|\n", "| 1 | primary_rna_seq   | mesenchymal |  0.14171423 |\n", "| 2 | primary_rna_seq   | classical   |  0.23119252 |\n", "| 3 | primary_rna_seq   | proneural   |  0.51256372 |\n", "| 4 | recurrent_rna_seq | mesenchymal |  0.14601973 |\n", "| 5 | recurrent_rna_seq | classical   | -0.01757637 |\n", "| 6 | recurrent_rna_seq | proneural   |  0.56030892 |\n", "\n"], "text/plain": ["  sample            class       score      \n", "1 primary_rna_seq   mesenchymal  0.14171423\n", "2 primary_rna_seq   classical    0.23119252\n", "3 primary_rna_seq   proneural    0.51256372\n", "4 recurrent_rna_seq mesenchymal  0.14601973\n", "5 recurrent_rna_seq classical   -0.01757637\n", "6 recurrent_rna_seq proneural    0.56030892"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/tmp/class_scores.tsv\", sep='\\t')\n", "head(input_df)\n", "\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/class_scores.pdf\")\n", "\n", "\n", "p = ggplot(input_df, aes(x = sample, y = score, fill=class)) + plot_theme() +\n", "geom_bar(position=\"dodge\", stat=\"identity\") +\n", "\n", "# scale_colour_manual(values=sig_colours) +\n", "\n", "# geom_hline(yintercept=1, linetype='solid', color='grey', size=0.5) +\n", "# geom_errorbar(aes(ymin=lower_ci, ymax=upper_ci),width=0)+\n", "# geom_point(size=2) +\n", "\n", "# scale_x_binned(n.breaks = 10000) +\n", "# scale_y_continuous(trans='log10') +\n", "\n", "ylab(\"Expression subtype score\") + xlab(\"\") + ggtitle('') +\n", "\n", "theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), legend.position='right')\n", "\n", "print(p)\n", "\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": null, "id": "e5b5fdf9-1abb-4b1a-8dd4-c2aa9cf20c23", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "78cf29fd-d19d-48e5-937d-11249864df40", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 31, "id": "bf938d50-16af-4951-a0cd-ed3d44433423", "metadata": {}, "outputs": [{"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/nanopore_coverage.tsv\", sep='\\t')\n", "\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/nanopore_coverage_t1.pdf\")\n", "\n", "# input_df$t1_gl[input_df$t1_gl > 20] = 20\n", "\n", "sig_colours = c(\"firebrick\", 'black')\n", "names(sig_colours) = c(\"True\", 'False')\n", "\n", "\n", "for (chr in unique(input_df$chr)){\n", "\n", "sub_df = input_df[input_df$chr == chr,]\n", "\n", "\n", "p = ggplot(sub_df, aes(x = start, y = t1_gl, color=t1_gl_high)) + plot_theme() +\n", "geom_point() +\n", "\n", "scale_colour_manual(values=sig_colours) +\n", "\n", "# geom_hline(yintercept=1, linetype='solid', color='grey', size=0.5) +\n", "# geom_errorbar(aes(ymin=lower_ci, ymax=upper_ci),width=0)+\n", "# geom_point(size=2) +\n", "\n", "# scale_x_binned(n.breaks = 10000) +\n", "# scale_y_continuous(trans='log10') +\n", "\n", "ylab(\"Mean difference (T1 - GL)\") + xlab(\"Position\") + ggtitle(chr) +\n", "\n", "theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), legend.position='none')\n", "\n", "print(p)\n", "\n", "}\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": 33, "id": "d1bbf807-89c5-4ed3-8c63-5e825869345a", "metadata": {}, "outputs": [{"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/nanopore_coverage.tsv\", sep='\\t')\n", "\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/nanopore_coverage_t2.pdf\")\n", "\n", "# input_df$t1_gl[input_df$t1_gl > 20] = 20\n", "\n", "sig_colours = c(\"firebrick\", 'black')\n", "names(sig_colours) = c(\"True\", 'False')\n", "\n", "\n", "for (chr in unique(input_df$chr)){\n", "\n", "sub_df = input_df[input_df$chr == chr,]\n", "\n", "\n", "p = ggplot(sub_df, aes(x = start, y = t2_gl, color=t2_gl_high)) + plot_theme() +\n", "geom_point() +\n", "\n", "scale_colour_manual(values=sig_colours) +\n", "\n", "# geom_hline(yintercept=1, linetype='solid', color='grey', size=0.5) +\n", "# geom_errorbar(aes(ymin=lower_ci, ymax=upper_ci),width=0)+\n", "# geom_point(size=2) +\n", "\n", "# scale_x_binned(n.breaks = 10000) +\n", "# scale_y_continuous(trans='log10') +\n", "\n", "ylab(\"Mean difference (T2 - GL)\") + xlab(\"Position\") + ggtitle(chr) +\n", "\n", "theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), legend.position='none')\n", "\n", "print(p)\n", "\n", "}\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": 24, "id": "2af08ad2-506a-4637-adc7-fe9bbd7ac09e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 27, "id": "860b88ca-7592-464d-9cd0-4d9a76b7a5d8", "metadata": {}, "outputs": [{"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/umap.tsv\", sep='\\t')\n", "\n", "\n", "cancer_colours = c('darkorange', 'green', 'beige', 'grey', 'violet', 'white', 'dodgerblue', 'firebrick')\n", "names(cancer_colours) = c('Classical', 'G-CIMP', 'Mesenchymal', 'Neural', 'Proneural', 'nan', 'primary', 'recurrent')\n", "\n", "# plot\n", "p1 = ggplot(input_df, aes(x=x_dim, y=y_dim, fill=subtype)) + plot_theme() +\n", "geom_point(pch=21, size = 4) +\n", "\n", "scale_fill_manual(values=cancer_colours) +\n", "# scale_colour_manual(values=cancer_text_colours) +\n", "\n", "# geom_label_repel(data=df_text, aes(label=cancer_type, fill=cancer_type, colour = cancer_type), show.legend=FALSE, segment.color = NA,label.size=NA,\n", "#         seed              = 1234,\n", "#         size\t\t\t\t= 7,\n", "#         force             = 1,\n", "#         nudge_x           = 1,\n", "#         hjust             = 0,\n", "#         segment.size      = 0.2,\n", "#         max.overlaps = 30\n", "#                ) +\n", "\n", "\n", "xlab(\"UMAP 1\") +\n", "ylab(\"UMAP 2\") +\n", "ggtitle('') +\n", "\n", "# guides(fill=guide_legend(title=\"Cancer type\", byrow = FALSE, override.aes = list(size = 3))) +\n", "\n", "theme(panel.grid = element_blank(), \n", "            axis.line = element_line(colour = 'black', size = 0.5))\n", "\n", "# legend = cowplot::get_legend(p1)\n", "\n", "# p1 = p1 + theme(legend.position='none')\n", "\n", "# print plots to file\n", "pdf('/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/umap_subset.pdf', width=10, height=7.5)\n", "\n", "print(p1)\n", "# print(legend)\n", "\n", "dev.off()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "2ba4d427-0eff-4004-bc73-24bf0b6a9d76", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7f560079-0e46-423c-b4c4-7ec2a0b038f1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.2.2"}}, "nbformat": 4, "nbformat_minor": 5}