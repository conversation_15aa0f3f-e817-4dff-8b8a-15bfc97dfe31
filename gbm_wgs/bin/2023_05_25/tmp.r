# <PERSON>
library(optparse)
library(ggplot2)
library(dplyr)
library(grid)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




fill_values = c('grey', '#d6b153', "silver", '#b5eab5', '#1b5e1b')
names(fill_values) = c("GJB2_soma", "GJB2_filpodia", "SCN9A_soma", "SCN9A_filipodia")



doplot = function(gene, raw_input_df, raw_p_val_df){

# subset to gene
input_df = raw_input_df[raw_input_df$gene == gene,]
p_val_df = raw_p_val_df[raw_p_val_df$gene == gene,]

# plot raw expression
p1 = ggplot(input_df, aes(x = type, y = rank)) + plot_theme() +

geom_boxplot(aes(fill=type), colour = 'black') + 

ggtitle(paste(p_val_df$rank[2], p_val_df$rank[2])) +
xlab("") + ylab('Expression (TPM)')




print(p1)

return()

}





pdf(opt$figure_file, width = 5, height=5)


input_df = read.csv(opt$figure_data_file, sep='\t', row.names=1)
p_val_df = read.csv(opt$figure_stats_file, sep='\t', row.names=1)

lapply(unique(p_val_df$gene), doplot, input_df, p_val_df)

dev.off()


print(opt$output_file)

