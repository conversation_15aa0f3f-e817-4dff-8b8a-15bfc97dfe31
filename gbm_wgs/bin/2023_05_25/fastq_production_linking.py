# <PERSON>i

import sys, getopt, time, os, re

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''



def main():
    print('XXX-XXX.py')
    t1 = time.time()


    # list of files
    files = os.listdir(production_dir)

    # uniq file types
    sample_types = []

    for file in files:
        pattern = r'RLGS_[0-9]{4}_[0-9]{2}'
        sample_types.extend(re.findall(pattern, file, re.IGNORECASE))


    # Iterate through file types in the directory
    for sample_type in np.unique(sample_types):
        sample_name = sample_type.split("_")[0] + re.sub(r'^0+', '', sample_type.split("_")[1])
        
        # define primary vs. recurrent tumor
        if int(sample_type.split("_")[2]) == 1:
            tumor_type = 'blood'
        if int(sample_type.split("_")[2]) == 2:
            tumor_type = 'primary'
        else:
            tumor_type = 'recurrent'

        # define new files to check for them
        new_files = list(map(lambda x: os.path.join(rawdata_dir, sample_name, f"{sample_name}-{tumor_type}_{sequencing_type}_{file.split('_')[-1]}"), ['R1.fastq.gz', 'R2.fastq.gz']))
        
        # remove files if they already exist
        for file in new_files:
            if os.path.islink(file) or os.path.isfile(file):
                try:
                    os.unlink(file)
                except:
                    os.remove(file)
        


        # collect list of files
        files_of_interest = []
        
        # relevant files
        for file in files:
            pattern = sample_type
            if re.match(pattern, file):
                files_of_interest.append(file)


        
        # link or concatenate
        for file in files_of_interest:
            # define new file
            sample_name = sample_type.split("_")[0] + re.sub(r'^0+', '', sample_type.split("_")[1])
            
            # define primary vs. recurrent tumor
            if int(sample_type.split("_")[2]) == 1:
                tumor_type = 'blood'
            if int(sample_type.split("_")[2]) == 2:
                tumor_type = 'primary'
            else:
                tumor_type = 'recurrent'
            
            # these are all primary tumors
            new_file = os.path.join(rawdata_dir, sample_name, f"{sample_name}-{tumor_type}_{sequencing_type}_{file.split('_')[-1]}")
            
            # define full path to file
            file = os.path.join(production_dir, file)
            
            # how many files for each reading
            if len(files_of_interest) == 2:
                # symlink to new file
                if not os.path.islink(new_file):
                    os.symlink(file, new_file)

            else:
                cline = f"cat {file} >> {new_file}"
                os.system(cline)
            
            print(file.split("/")[-1], tumor_type, new_file.split("/")[-1])


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # files and output directory
    # production_dir = '/.mounts/labs/reimandlab/production/230606_A00469_0498_BHHJV2DMXY.RLGS.fastqs'
    production_dir = '/.mounts/labs/reimandlab/production/230512_A00469_0485_BHWMKCDSX5.RLGS.fastqs'
    rawdata_dir = "/.mounts/labs/reimandlab/private/generated_raw_data/GBM_primary_recurrent_genomic/das_lab_st_michaels"
    sequencing_type = "rna_seq"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["production_dir=", "rawdata_dir="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--production_dir"):
            production_dir = str(arg)
        if opt in ("--rawdata_dir"):
            rawdata_dir = str(arg)

    main()





