# <PERSON>cheli

import sys, getopt, time, os, re

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''

# list files in directory and first level of sub-directories
def list_files_in_directory(directory):
    file_list = []
    for root, dirs, files in os.walk(directory):
        if len(root.split("/")) <= (len(directory.split("/")) + 1):
            # Add files in the current directory
            file_list.extend([os.path.join(root, file) for file in files])
    
    return file_list


def main():
    print('XXX-XXX.py')
    t1 = time.time()


    # collect only fastq files
    files = [file for file in list_files_in_directory(rawdata_dir) if file.endswith("fastq.gz")]

    # copy fastq files if they have not been copied
    for file in files:
        # new file path
        new_file = os.path.join(data_dir, file.split("/")[-1])
        
        # if it doesn't exit, symlink it 
        if not os.path.islink(new_file) or not os.path.isfile(new_file):
            # symlink requires original file
            if os.path.islink(file):
                file = os.readlink(file)
                
            # link to new file
            os.symlink(file, new_file)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # files and output directory
    rawdata_dir = "/.mounts/labs/reimandlab/private/generated_raw_data/GBM_primary_recurrent_genomic/das_lab_st_michaels"
    data_dir = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/data/raw_data/all_fastqs"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["rawdata_dir=", "data_dir="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--rawdata_dir"):
            rawdata_dir = str(arg)
        if opt in ("--data_dir"):
            data_dir = str(arg)

    main()






