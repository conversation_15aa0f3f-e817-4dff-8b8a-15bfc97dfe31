# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from common_utils.pre_processing import read_vcf_alt, write_vcf


help_message = '''
Failed
'''



def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # read vcf 
    header, vcf = read_vcf_alt(strelka_file)

    # modify for ANNOVAR
    vcf.loc[:,'FORMAT'] = vcf['FORMAT'] + ":GT"
    vcf.loc[:,'INFO'] = vcf['INFO'] + ":0/1"

    # write to output 
    write_vcf(vcf, header, output_vcf)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    strelka_file = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_05_25/wgs/RLGS10-recurrent_wgs_seq/results/variants/test.vcf"
    output_vcf = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_05_25/wgs/RLGS10-recurrent_wgs_seq/results/variants/test_mod.vcf"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["strelka_file=", "output_vcf="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--strelka_file"):
            strelka_file = str(arg)
        if opt in ("--output_vcf"):
            output_vcf = str(arg)

    main()





