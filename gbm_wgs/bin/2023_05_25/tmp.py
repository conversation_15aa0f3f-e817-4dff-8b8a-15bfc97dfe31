# Alec Ba<PERSON>cheli

import sys, getopt, time, os, subprocess

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import scipy.stats as stats


help_message = '''
Failed
'''



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load genes of interest from file
    genes_of_interest = open(genes_of_interest_file, 'r').read().strip("\n").splitlines()

    # load expression file
    expression_df = pd.read_csv(expression_file, sep='\t', index_col=0)


    # save dfs
    expression_df.to_csv(figure_data_file, sep='\t')
    stats_df.to_csv(figure_stats_file, sep='\t')

    expression_df = pd.read_csv(figure_data_file, sep='\t', index_col=0)
    stats_df = pd.read_csv(figure_stats_file, sep='\t', index_col=0)

    # # rename columns to split at "." and be the first index
    # expression_df.columns = expression_df.columns.str.split(".").str[0]



    # save dfs
    expression_df.to_csv("/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/xi_piezo_metastatic/2023_11_16/_figure_data/expression.tsv", sep='\t')
    stats_df.to_csv("/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/xi_piezo_metastatic/2023_11_16/_figure_data/stats.tsv", sep='\t')


    # create figures
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]


    print(" ".join(cline))
    # subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # r environment
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["expression_file=", "genes_of_interest_file=", "r_script=", "figure_stats_file=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--expression_file"):
            expression_file = str(arg)
        if opt in ("--genes_of_interest_file"):
            genes_of_interest_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()




