# Alec Ba<PERSON>cheli - <EMAIL>

#####################
# linking germline control files for easy use
#####################


rule link_bam_primary:
    input:
        recalibrated_bam_germline = RES_DIR + "/wgs/{sample_code}-blood_wgs_seq-merged_alignment.bam",
        recalibrated_bam_index = RES_DIR + "/wgs/{sample_code}-blood_wgs_seq-merged_alignment.bai"
        
    output:
        bam_germline_link = RES_DIR + "/wgs/{sample_code}-primary_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bam",
        bam_index_link = RES_DIR + "/wgs/{sample_code}-primary_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bai"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '10G'
    
    run:
        shell("ln -s {input.recalibrated_bam_germline} {output.bam_germline_link}")
        shell("ln -s {input.recalibrated_bam_index} {output.bam_index_link}")

rule link_bam_recurrent:
    input:
        recalibrated_bam_germline = RES_DIR + "/wgs/{sample_code}-blood_wgs_seq-merged_alignment.bam",
        recalibrated_bam_index = RES_DIR + "/wgs/{sample_code}-blood_wgs_seq-merged_alignment.bai"
        
    output:
        bam_germline_link = RES_DIR + "/wgs/{sample_code}-recurrent_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bam",
        bam_index_link = RES_DIR + "/wgs/{sample_code}-recurrent_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bai"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '10G'
    
    run:
        shell("ln -s {input.recalibrated_bam_germline} {output.bam_germline_link}")
        shell("ln -s {input.recalibrated_bam_index} {output.bam_index_link}")

#####################
# Mutect2
#####################

rule Mutect2:
    input:
        RES_DIR + "/" + GENOME_VERSION + ".fa.fai",
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        
        tumor_bam = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq-merged_alignment.bam",
        normal_bam = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bam"
        
    output:
        output_vcf_gz = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-mutect2_{chromosome}.vcf.gz"
        
    singularity:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-4.4.0.0/gatk.sif"
        
    params:
        java_memory = '200g',
        normal_sample = "{sample_code}-blood_wgs_seq",
        chromosome = "{chromosome}",
        sample_tmp_dir = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '205G'
        
    shell:
        "gatk --java-options '-Xmx{params.java_memory}' Mutect2 -R {input.genome_fasta} --input {input.tumor_bam} --input {input.normal_bam} --output {output.output_vcf_gz} -normal {params.normal_sample} --tmp-dir {params.sample_tmp_dir} --intervals {params.chromosome}"
# gatk  Mutect2 -R /path/to/hg38fasta -I tumor.bam -O tumour_id -I normal.bam -O normal_id -L chrN -L intervals.bed  -O sample.vcf


rule merge_mutect2_prep:
    input:
        RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/null.txt",

        script = BIN_DIR + "/mutect2_combined_vcfs.py"
        
    output:
        mutect2_list_of_chromosomes = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/mutect2_chromosomes.txt"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '10G'
        
    shell:
        "python {input.script} --output_file {output.mutect2_list_of_chromosomes}"

rule merge_Mutect2:
    input:
        expand("{res_dir}/wgs/{{sample_code}}-{{tumor}}_wgs_seq/{{sample_code}}-{{tumor}}_wgs_seq-mutect2_{chromosome}.vcf.gz", res_dir = RES_DIR, chromosome = chromosome_list),
        mutect2_list_of_chromosomes = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/mutect2_chromosomes.txt"

    output:
        # RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-mutect2.vcf.gz.tbi",
        output_vcf_gz = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-mutect2.vcf.gz"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '30G'
        
    shell:
        "bcftools merge --force-samples -l {input.mutect2_list_of_chromosomes} -Oz -o {output.output_vcf_gz}"
        

rule annotate_Mutect2:
    input:
        # RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-mutect2.vcf.gz.tbi",
        output_vcf_gz = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-mutect2.vcf.gz"
                
    output:
        RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/mutect2.hg38_multianno.txt"
        
    params:
        annovar_db = "/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/annovar_db/humandb/",
        table_annovar_script = "/.mounts/labs/reimandlab/private/users/abahcheli/software/annovar/table_annovar.pl",
        
        output_prefix = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/mutect2"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '60G'
        
    shell:
        "{params.table_annovar_script} {input.output_vcf_gz} {params.annovar_db} -buildver hg38 -out {params.output_prefix} -remove -protocol refGene,cytoBand,avsnp147,dbnsfp30a -operation g,r,f,f -nastring . --vcfinput"



#####################
# Strelka
#####################


rule init_Strelka:
    input:
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        tumor_bam = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq-merged_alignment.bam",
        normal_bam = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bam"

    output:
        RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/runWorkflow.py"
        
    params:
        working_directory = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '10G'

    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/snv_callers/bin/configureStrelkaSomaticWorkflow.py --normalBam {input.normal_bam} --tumorBam {input.tumor_bam} --referenceFasta {input.genome_fasta} --runDir {params.working_directory}"

rule Strelka:
    input:
        run_file = RES_DIR + "/wgs/{sample}/runWorkflow.py"
        
    output:
        RES_DIR + "/wgs/{sample}/results/variants/somatic.snvs.vcf.gz",
        RES_DIR + "/wgs/{sample}/results/variants/somatic.indels.vcf.gz"
        
    resources:
        threads = WGS_THREADS,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '5G'

    shell:
        "{input.run_file} -m local -j {resources.threads}"


rule prepare_strelka_output:
    input:
        strelka_snvs = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/results/variants/somatic.snvs.vcf.gz",
        strelka_indels = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/results/variants/somatic.indels.vcf.gz"
                
    output:
        strelka_combined = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/strelka_combined.vcf.txt"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '20G'
        
    run:
        shell("""zcat {input.strelka_snvs} | grep -v "##" | awk '{print $1"\t"$2"\t"$2"\t"$4"\t"$5"\t"$6"\t"$7"\t"$8"\t"$9"\t"$10"\t"$11}' > {output.strelka_combined}""")
        shell("""zcat {input.strelka_indels} | grep -v "##" | awk '{print $1"\t"$2"\t"$2"\t"$4"\t"$5"\t"$6"\t"$7"\t"$8"\t"$9"\t"$10"\t"$11}' >> {output.strelka_combined}""")



rule annotate_strelka:
    input:
        strelka_combined = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/strelka_combined.vcf.txt"
                
    output:
        RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/strelka.hg38_multianno.txt"
        
    params:
        annovar_db = "/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/annovar_db/humandb/",
        table_annovar_script = "/.mounts/labs/reimandlab/private/users/abahcheli/software/annovar/table_annovar.pl",
        
        output_prefix = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/strelka"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '60G'
        
    shell:
        "{params.table_annovar_script} {input.strelka_combined} {params.annovar_db} -buildver hg38 -out {params.output_prefix} -remove -protocol refGene,cytoBand,avsnp147,dbnsfp30a -operation g,r,f,f -nastring ."



#####################
# VarScan2
#####################


rule Varscan2:
    input:
        CENTROMERE_BED,
        EXOME_BED,
        RES_DIR + "/" + GENOME_VERSION + ".fa.fai",
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        
        tumor_bam = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq-merged_alignment.bam",
        normal_bam = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bam"
        
    output:
        output_cnv = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq.cnv"
        
    singularity:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/varscan2/varscan2.sif"
        
    params:
        sample_id = "{sample_code}-{tumor}",
        sample_tmp_dir = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '120G'
        
    shell:
        "run_varscan -c {input.normal_bam} -t {input.tumor_bam} -q {params.sample_id} -i {input.genome_fasta} -b {CENTROMERE_BED} -w {EXOME_BED} -n {params.sample_tmp_dir} > {output.output_cnv}"
# docker run  -v /path/to/input/files:/data jeltje/varscan2 -c normal.bam -t  tumor.bam -q sampleid -i genome.fa -b centromeres.bed -w targets.bed -s tmpdir > varscan.cnv




#####################
# MuSe
#####################


rule Muse2_call:
    input:
        RES_DIR + "/" + GENOME_VERSION + ".fa.fai",
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        
        tumor_bam = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq-merged_alignment.bam",
        normal_bam = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bam"
        
    output:
        RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-muse2.MuSE.txt"
        
    params:
        output_call_prefix = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-muse2"
        
    resources:
        threads = WGS_THREADS,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '7G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/MuSE2/MuSE call -f {input.genome_fasta} -O {params.output_call_prefix} -n {resources.threads} {input.tumor_bam} {input.normal_bam}"
# ./MuSE call -f Reference.Genome -O Output.Prefix -n 20 Tumor.bam Matched.Normal.bam


rule Muse2_sump:
    input:
        dbSNP_VCF,
        muse2_sump = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-muse2.MuSE.txt"
        
    output:
        muse2_vcf = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-muse2.vcf"
        
    params:
        output_call_prefix = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-muse2"
        
    resources:
        threads = WGS_THREADS,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '7G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/MuSE2/MuSE sump -I {input.muse2_sump} -O {output.muse2_vcf} -G -n {resources.threads} -D {dbSNP_VCF}"
# ./MuSE sump -I Output.Prefix.MuSE.txt -O Output.Prefix.vcf -G -n 20 -D dbsnp.vcf.gz



rule annotate_muse2:
    input:
        muse2_vcf = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-muse2.vcf"
                
    output:
        RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/muse2.hg38_multianno.txt"
        
    params:
        annovar_db = "/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/annovar_db/humandb/",
        table_annovar_script = "/.mounts/labs/reimandlab/private/users/abahcheli/software/annovar/table_annovar.pl",
        
        output_prefix = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/muse2"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '60G'
        
    shell:
        "{params.table_annovar_script} {input.muse2_vcf} {params.annovar_db} -buildver hg38 -out {params.output_prefix} -remove -protocol refGene,cytoBand,avsnp147,dbnsfp30a -operation g,r,f,f -nastring ."





