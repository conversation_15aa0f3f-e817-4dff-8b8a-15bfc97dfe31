# <PERSON>i

import sys, getopt, time, os, re

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # define sample name
    res_dir = "/".join(output_file.split("/")[:-1])

    # list files that are mutect2 related
    files = os.listdir(res_dir)
    files = [file for file in files if file.endswith(".vcf.gz")]
    files = [file for file in files if re.search("mutect2", file)]

    # write list of files to output file
    with open(output_file, "w") as outfile:
        outfile.write("\n".join(files))


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["output_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--output_file"):
            output_file = str(arg)

    main()





