{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d3419a12-ba6e-4c45-9014-8f3d07b85215", "metadata": {}, "outputs": [], "source": ["# load packages\n", "import sys, getopt, time, subprocess, os, pathlib, glob, re\n", "import gzip, io\n", "\n", "\n", "import pandas as pd \n", "import numpy as np\n", "from lifelines import CoxPHFitter, utils\n", "\n", "# from common_utils.a_data_preprocessing import generate_cancer_abbreviation, generate_cancer_tissue_source_dict\n", "import scipy.stats as stats\n", "import statsmodels.stats.multitest as multitest\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "19098046-c875-4ddc-862b-f49b0745ccd2", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>circ_id</th>\n", "      <th>chr</th>\n", "      <th>start</th>\n", "      <th>end</th>\n", "      <th>coverage</th>\n", "      <th>size_bp</th>\n", "      <th>copy_number</th>\n", "      <th>sample_name</th>\n", "      <th>patient</th>\n", "      <th>tumor_type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>327</td>\n", "      <td>chr8</td>\n", "      <td>12904309</td>\n", "      <td>13324360</td>\n", "      <td>88</td>\n", "      <td>4874614.0</td>\n", "      <td>91</td>\n", "      <td>RLGS1-primary</td>\n", "      <td>RLGS1</td>\n", "      <td>primary</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>327</td>\n", "      <td>chr8</td>\n", "      <td>58311533</td>\n", "      <td>59372911</td>\n", "      <td>75</td>\n", "      <td>4874614.0</td>\n", "      <td>91</td>\n", "      <td>RLGS1-primary</td>\n", "      <td>RLGS1</td>\n", "      <td>primary</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>327</td>\n", "      <td>chr8</td>\n", "      <td>66487076</td>\n", "      <td>66492297</td>\n", "      <td>123</td>\n", "      <td>4874614.0</td>\n", "      <td>91</td>\n", "      <td>RLGS1-primary</td>\n", "      <td>RLGS1</td>\n", "      <td>primary</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>327</td>\n", "      <td>chr8</td>\n", "      <td>36697451</td>\n", "      <td>36701259</td>\n", "      <td>74</td>\n", "      <td>4874614.0</td>\n", "      <td>91</td>\n", "      <td>RLGS1-primary</td>\n", "      <td>RLGS1</td>\n", "      <td>primary</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>327</td>\n", "      <td>chr8</td>\n", "      <td>66490297</td>\n", "      <td>66930891</td>\n", "      <td>116</td>\n", "      <td>4874614.0</td>\n", "      <td>91</td>\n", "      <td>RLGS1-primary</td>\n", "      <td>RLGS1</td>\n", "      <td>primary</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>436</th>\n", "      <td>29</td>\n", "      <td>chr10</td>\n", "      <td>86366945</td>\n", "      <td>86885562</td>\n", "      <td>53</td>\n", "      <td>518617.0</td>\n", "      <td>53</td>\n", "      <td>RLGS8-recurrent</td>\n", "      <td>RLGS8</td>\n", "      <td>recurrent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>437</th>\n", "      <td>15</td>\n", "      <td>chr7</td>\n", "      <td>39909097</td>\n", "      <td>40186681</td>\n", "      <td>54</td>\n", "      <td>294355.0</td>\n", "      <td>56</td>\n", "      <td>RLGS8-recurrent</td>\n", "      <td>RLGS8</td>\n", "      <td>recurrent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>438</th>\n", "      <td>15</td>\n", "      <td>chr7</td>\n", "      <td>40193854</td>\n", "      <td>40210625</td>\n", "      <td>58</td>\n", "      <td>294355.0</td>\n", "      <td>56</td>\n", "      <td>RLGS8-recurrent</td>\n", "      <td>RLGS8</td>\n", "      <td>recurrent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>439</th>\n", "      <td>39</td>\n", "      <td>chr16</td>\n", "      <td>77166130</td>\n", "      <td>77289769</td>\n", "      <td>59</td>\n", "      <td>123639.0</td>\n", "      <td>59</td>\n", "      <td>RLGS8-recurrent</td>\n", "      <td>RLGS8</td>\n", "      <td>recurrent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>440</th>\n", "      <td>2</td>\n", "      <td>chr3</td>\n", "      <td>115906238</td>\n", "      <td>116143503</td>\n", "      <td>36</td>\n", "      <td>237265.0</td>\n", "      <td>36</td>\n", "      <td>RLGS9-primary</td>\n", "      <td>RLGS9</td>\n", "      <td>primary</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>441 rows × 10 columns</p>\n", "</div>"], "text/plain": ["     circ_id    chr      start        end  coverage    size_bp  copy_number  \\\n", "0        327   chr8   12904309   13324360        88  4874614.0           91   \n", "1        327   chr8   58311533   59372911        75  4874614.0           91   \n", "2        327   chr8   66487076   66492297       123  4874614.0           91   \n", "3        327   chr8   36697451   36701259        74  4874614.0           91   \n", "4        327   chr8   66490297   66930891       116  4874614.0           91   \n", "..       ...    ...        ...        ...       ...        ...          ...   \n", "436       29  chr10   86366945   86885562        53   518617.0           53   \n", "437       15   chr7   39909097   40186681        54   294355.0           56   \n", "438       15   chr7   40193854   40210625        58   294355.0           56   \n", "439       39  chr16   77166130   77289769        59   123639.0           59   \n", "440        2   chr3  115906238  116143503        36   237265.0           36   \n", "\n", "         sample_name patient tumor_type  \n", "0      RLGS1-primary   RLGS1    primary  \n", "1      RLGS1-primary   RLGS1    primary  \n", "2      RLGS1-primary   RLGS1    primary  \n", "3      RLGS1-primary   RLGS1    primary  \n", "4      RLGS1-primary   RLGS1    primary  \n", "..               ...     ...        ...  \n", "436  RLGS8-recurrent   RLGS8  recurrent  \n", "437  RLGS8-recurrent   RLGS8  recurrent  \n", "438  RLGS8-recurrent   RLGS8  recurrent  \n", "439  RLGS8-recurrent   RLGS8  recurrent  \n", "440    RLGS9-primary   RLGS9    primary  \n", "\n", "[441 rows x 10 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["file = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/ecdna/decoil/combined_decoil_results.tsv'\n", "\n", "df = pd.read_csv(file, sep='\\t')\n", "\n", "df"]}, {"cell_type": "code", "execution_count": 4, "id": "8c3b5351-461f-4d4a-8c28-d18dc99a51ee", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['RLGS1-primary', 'RLGS1-recurrent', 'RLGS10-primary',\n", "       'RLGS10-recurrent', 'RLGS12-primary', 'RLGS12-recurrent',\n", "       'RLGS2-primary', 'RLGS2-recurrent', 'RLGS4-primary',\n", "       'RLGS4-recurrent', 'RLGS5-primary', 'RLGS6-recurrent',\n", "       'RLGS7-primary', 'RLGS7-recurrent', 'RLGS8-primary',\n", "       'RLGS8-recurrent', 'RLGS9-primary'], dtype=object)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df['sample_name'].unique()"]}, {"cell_type": "code", "execution_count": 25, "id": "61449db5-6c77-4c55-8a58-00ccfd7e8820", "metadata": {}, "outputs": [], "source": ["urls = open(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/HMF_RNASeq_2025/data/rna_seq_fastq_urls.txt\").readlines()\n", "urls = [url.strip(\"\\n\").strip('\"') for url in urls]"]}, {"cell_type": "code", "execution_count": 26, "id": "172e3296-39c0-438a-b063-bc48f5ed3e9a", "metadata": {}, "outputs": [], "source": ["ser = pd.Series(urls).str.split(\"/\").str[-1].str.split(\"_\").str[:1].str.join(\"_\")"]}, {"cell_type": "code", "execution_count": 2, "id": "789bde66-0073-4ded-8e85-f9c80afa02fc", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["os.path.getsize(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/ecdna/exon_intersections/RLGS3-recurrent_exon_relative_coordinates.tsv\")"]}, {"cell_type": "code", "execution_count": 28, "id": "ff21699b-c517-480a-afab-2d132f905ebb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sample</th>\n", "      <th>gc_url</th>\n", "      <th>local_path</th>\n", "      <th>lane</th>\n", "      <th>read</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>**********</td>\n", "      <td>gs://gaya01010070t-rna-reads/2.1.0/fastq/FR342...</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "      <td>L004</td>\n", "      <td>R2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>**********</td>\n", "      <td>gs://gaya01010070t-rna-reads/2.1.0/fastq/FR342...</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "      <td>L002</td>\n", "      <td>R1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>**********</td>\n", "      <td>gs://gaya01010070t-rna-reads/2.1.0/fastq/FR342...</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "      <td>L001</td>\n", "      <td>R2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>**********</td>\n", "      <td>gs://gaya01010070t-rna-reads/2.1.0/fastq/FR342...</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "      <td>L003</td>\n", "      <td>R2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>**********</td>\n", "      <td>gs://gaya01010070t-rna-reads/2.1.0/fastq/FR342...</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "      <td>L002</td>\n", "      <td>R2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32197</th>\n", "      <td>CPCT02070141T</td>\n", "      <td>gs://cpct02070141t-rna-reads/1.3/CPCT02070141T...</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "      <td>L002</td>\n", "      <td>R2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32198</th>\n", "      <td>CPCT02070141T</td>\n", "      <td>gs://cpct02070141t-rna-reads/1.3/CPCT02070141T...</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "      <td>L003</td>\n", "      <td>R1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32199</th>\n", "      <td>CPCT02070141T</td>\n", "      <td>gs://cpct02070141t-rna-reads/1.3/CPCT02070141T...</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "      <td>L003</td>\n", "      <td>R2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32200</th>\n", "      <td>CPCT02070141T</td>\n", "      <td>gs://cpct02070141t-rna-reads/1.3/CPCT02070141T...</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "      <td>L004</td>\n", "      <td>R1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32201</th>\n", "      <td>CPCT02070141T</td>\n", "      <td>gs://cpct02070141t-rna-reads/1.3/CPCT02070141T...</td>\n", "      <td>/.mounts/labs/reimandlab/private/users/abahche...</td>\n", "      <td>L004</td>\n", "      <td>R2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>32202 rows × 5 columns</p>\n", "</div>"], "text/plain": ["              sample                                             gc_url  \\\n", "0         **********  gs://gaya01010070t-rna-reads/2.1.0/fastq/FR342...   \n", "1         **********  gs://gaya01010070t-rna-reads/2.1.0/fastq/FR342...   \n", "2         **********  gs://gaya01010070t-rna-reads/2.1.0/fastq/FR342...   \n", "3         **********  gs://gaya01010070t-rna-reads/2.1.0/fastq/FR342...   \n", "4         **********  gs://gaya01010070t-rna-reads/2.1.0/fastq/FR342...   \n", "...              ...                                                ...   \n", "32197  CPCT02070141T  gs://cpct02070141t-rna-reads/1.3/CPCT02070141T...   \n", "32198  CPCT02070141T  gs://cpct02070141t-rna-reads/1.3/CPCT02070141T...   \n", "32199  CPCT02070141T  gs://cpct02070141t-rna-reads/1.3/CPCT02070141T...   \n", "32200  CPCT02070141T  gs://cpct02070141t-rna-reads/1.3/CPCT02070141T...   \n", "32201  CPCT02070141T  gs://cpct02070141t-rna-reads/1.3/CPCT02070141T...   \n", "\n", "                                              local_path  lane read  \n", "0      /.mounts/labs/reimandlab/private/users/abahche...  L004   R2  \n", "1      /.mounts/labs/reimandlab/private/users/abahche...  L002   R1  \n", "2      /.mounts/labs/reimandlab/private/users/abahche...  L001   R2  \n", "3      /.mounts/labs/reimandlab/private/users/abahche...  L003   R2  \n", "4      /.mounts/labs/reimandlab/private/users/abahche...  L002   R2  \n", "...                                                  ...   ...  ...  \n", "32197  /.mounts/labs/reimandlab/private/users/abahche...  L002   R2  \n", "32198  /.mounts/labs/reimandlab/private/users/abahche...  L003   R1  \n", "32199  /.mounts/labs/reimandlab/private/users/abahche...  L003   R2  \n", "32200  /.mounts/labs/reimandlab/private/users/abahche...  L004   R1  \n", "32201  /.mounts/labs/reimandlab/private/users/abahche...  L004   R2  \n", "\n", "[32202 rows x 5 columns]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.DataFrame([list(ser), urls]).transpose()\n", "df.columns = ['sample', 'gc_url']\n", "df['local_path'] = \"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/HMF_RNASeq_2025/data/fastqs/\" + df['gc_url'].str.split(\"/\").str[-1]\n", "df['lane'] = df['local_path'].str.split(\"_\").str[-3]\n", "df['read'] = df['local_path'].str.split(\"_\").str[-2]\n", "\n", "df.to_csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/HMF_RNASeq_2025/data/hmf_samples_urls_20250721.tsv\", sep='\\t', index=False)\n", "df"]}, {"cell_type": "code", "execution_count": 22, "id": "77ab7b4e-5111-4ca9-8cd0-25e5134b3eeb", "metadata": {}, "outputs": [{"data": {"text/plain": ["'/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/HMF_RNASeq_2025/data/fastqs/**********_HL5GNDSXC_S2_L004_R2_001.fastq.gz\"\\n'"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["df.loc[0,'local_path']"]}, {"cell_type": "code", "execution_count": 5, "id": "5de72475-8ff1-437b-8f8a-58aea1866834", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSNK1A1, Original: 124 New: 65 Common: 64\n", "FBXW7, Original: 154 New: 224 Common: 114\n", "METTL14, Original: 94 New: 53 Common: 44\n", "PLGRKT, Original: 15 New: 43 Common: 11\n"]}], "source": ["genes = ['CSNK1A1', 'FBXW7', 'METTL14', 'PLGRKT']\n", "\n", "for gene in genes:\n", "    df1 = pd.read_csv(f'/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/schramek_brca_expression/2024_06_25/ap/{gene}_Basal_q25vs25-pathways.txt', sep='\\t')\n", "    df2 = pd.read_csv(f'/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/schramek_brca_expression/2025_06_17/ap/{gene}_Basal_q25vs25-pathways.txt', sep='\\t')\n", "\n", "    common = np.sum(np.isin(df1['term_id'], df2['term_id']))\n", "\n", "    print(f'{gene}, Original: {df1.index.size} New: {df2.index.size} Common: {common}')\n", "    "]}, {"cell_type": "code", "execution_count": 2, "id": "d615dc69-29b5-454b-a407-b07674bb6161", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original: 124 New: 124 Common: 124\n", "Original: 154 New: 154 Common: 154\n", "Original: 94 New: 94 Common: 94\n", "Original: 15 New: 15 Common: 15\n"]}], "source": ["genes = ['CSNK1A1', 'FBXW7', 'METTL14', 'PLGRKT']\n", "\n", "for gene in genes:\n", "    df1 = pd.read_csv(f'/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/schramek_brca_expression/2024_06_25/ap/{gene}_Basal_q25vs25-pathways.txt', sep='\\t')\n", "    df2 = pd.read_csv(f'/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/schramek_brca_expression/2025_06_17/ap/{gene}_Basal_q25vs25-pathways.txt', sep='\\t')\n", "\n", "    common = np.sum(np.isin(df1['term_id'], df2['term_id']))\n", "\n", "    print(f'Original: {df1.index.size} New: {df2.index.size} Common: {common}')\n"]}, {"cell_type": "code", "execution_count": 15, "id": "67d5eb8c-d483-4f09-b1a7-e9a37b0f947a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>term_id</th>\n", "      <th>term_name</th>\n", "      <th>adjusted_p_val</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>GO:0001501</td>\n", "      <td>skeletal system development</td>\n", "      <td>0.000039</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>GO:0001503</td>\n", "      <td>ossification</td>\n", "      <td>0.000016</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>GO:0001649</td>\n", "      <td>osteoblast differentiation</td>\n", "      <td>0.003038</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>GO:0001704</td>\n", "      <td>formation of primary germ layer</td>\n", "      <td>0.000522</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>GO:0002009</td>\n", "      <td>morphogenesis of an epithelium</td>\n", "      <td>0.008805</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61</th>\n", "      <td>REAC:R-HSA-422475</td>\n", "      <td>Axon guidance</td>\n", "      <td>0.024107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62</th>\n", "      <td>REAC:R-HSA-446728</td>\n", "      <td>Cell junction organization</td>\n", "      <td>0.039614</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>REAC:R-HSA-6806834</td>\n", "      <td>Signaling by MET</td>\n", "      <td>0.000010</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64</th>\n", "      <td>REAC:R-HSA-9006934</td>\n", "      <td>Signaling by Receptor Tyrosine Kinases</td>\n", "      <td>0.005964</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65</th>\n", "      <td>REAC:R-HSA-9675108</td>\n", "      <td>Nervous system development</td>\n", "      <td>0.020769</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>66 rows × 3 columns</p>\n", "</div>"], "text/plain": ["               term_id                               term_name  adjusted_p_val\n", "0           GO:0001501             skeletal system development        0.000039\n", "1           GO:0001503                            ossification        0.000016\n", "2           GO:0001649              osteoblast differentiation        0.003038\n", "3           GO:0001704         formation of primary germ layer        0.000522\n", "4           GO:0002009          morphogenesis of an epithelium        0.008805\n", "..                 ...                                     ...             ...\n", "61   REAC:R-HSA-422475                           Axon guidance        0.024107\n", "62   REAC:R-HSA-446728              Cell junction organization        0.039614\n", "63  REAC:R-HSA-6806834                        Signaling by MET        0.000010\n", "64  REAC:R-HSA-9006934  Signaling by Receptor Tyrosine Kinases        0.005964\n", "65  REAC:R-HSA-9675108              Nervous system development        0.020769\n", "\n", "[66 rows x 3 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "df2 = pd.read_csv(f'/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/schramek_brca_expression/2025_06_17/ap/MRPS18B_Basal_q25vs25-pathways.txt', sep='\\t')\n", "\n", "df2"]}, {"cell_type": "code", "execution_count": 13, "id": "cfe60a6f-0f79-4431-97fe-94d7813792de", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gene</th>\n", "      <th>methylation_site</th>\n", "      <th>best_k</th>\n", "      <th>component_counts</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>RBL2_0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>[490]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>VDAC3_0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>[409, 82]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ACTN1_0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>[92, 399]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ATP2A1;ATP2A1-AS1_0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>[354, 137]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>SFRP1_0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>[229, 262]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>308270</th>\n", "      <td>LINC01422_0</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>[33, 183, 183, 57, 35]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>308271</th>\n", "      <td>ENSG00000238220_0</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>[37, 117, 150, 37, 150]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>308272</th>\n", "      <td>AGXT2_6</td>\n", "      <td>6</td>\n", "      <td>4</td>\n", "      <td>[23, 186, 78, 204]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>308273</th>\n", "      <td>ENSG00000259200_9</td>\n", "      <td>9</td>\n", "      <td>5</td>\n", "      <td>[28, 137, 128, 28, 170]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>308274</th>\n", "      <td>MIR3681HG_38</td>\n", "      <td>38</td>\n", "      <td>4</td>\n", "      <td>[143, 101, 155, 92]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>308275 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                       gene  methylation_site  best_k         component_counts\n", "0                    RBL2_0                 0       1                    [490]\n", "1                   VDAC3_0                 0       2                [409, 82]\n", "2                   ACTN1_0                 0       2                [92, 399]\n", "3       ATP2A1;ATP2A1-AS1_0                 0       2               [354, 137]\n", "4                   SFRP1_0                 0       2               [229, 262]\n", "...                     ...               ...     ...                      ...\n", "308270          LINC01422_0                 0       5   [33, 183, 183, 57, 35]\n", "308271    ENSG00000238220_0                 0       5  [37, 117, 150, 37, 150]\n", "308272              AGXT2_6                 6       4       [23, 186, 78, 204]\n", "308273    ENSG00000259200_9                 9       5  [28, 137, 128, 28, 170]\n", "308274         MIR3681HG_38                38       4      [143, 101, 155, 92]\n", "\n", "[308275 rows x 4 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 14, "id": "f755b65e-0064-4197-821a-ce341e9acc91", "metadata": {}, "outputs": [{"data": {"text/plain": ["24.5"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["490*0.05"]}, {"cell_type": "code", "execution_count": null, "id": "45aa0664-ea4d-4222-bde7-5e5854680450", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2d9f63fe-c3e0-428d-a2d5-8f051908db64", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d4a86788-09ab-49df-83a7-c263b0f55e04", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "id": "fda699d5-f3e0-4fd6-9932-c9e1c04d0b19", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gene</th>\n", "      <th>p_value</th>\n", "      <th>logFC</th>\n", "      <th>fdr</th>\n", "      <th>data_type</th>\n", "      <th>dgea</th>\n", "      <th>data_type_dgea</th>\n", "      <th>log_fdr</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>169521</th>\n", "      <td>A1BG</td>\n", "      <td>0.690830</td>\n", "      <td>0.092293</td>\n", "      <td>0.912230</td>\n", "      <td>gene_counts</td>\n", "      <td>deseq2_paired</td>\n", "      <td>gene_counts-deseq2_paired</td>\n", "      <td>0.039896</td>\n", "    </tr>\n", "    <tr>\n", "      <th>169522</th>\n", "      <td>A1CF</td>\n", "      <td>0.202384</td>\n", "      <td>2.851122</td>\n", "      <td>0.601315</td>\n", "      <td>gene_counts</td>\n", "      <td>deseq2_paired</td>\n", "      <td>gene_counts-deseq2_paired</td>\n", "      <td>0.220898</td>\n", "    </tr>\n", "    <tr>\n", "      <th>169523</th>\n", "      <td>A2M</td>\n", "      <td>0.166172</td>\n", "      <td>-0.467418</td>\n", "      <td>0.557798</td>\n", "      <td>gene_counts</td>\n", "      <td>deseq2_paired</td>\n", "      <td>gene_counts-deseq2_paired</td>\n", "      <td>0.253523</td>\n", "    </tr>\n", "    <tr>\n", "      <th>169524</th>\n", "      <td>A2ML1</td>\n", "      <td>0.475763</td>\n", "      <td>0.298752</td>\n", "      <td>0.822782</td>\n", "      <td>gene_counts</td>\n", "      <td>deseq2_paired</td>\n", "      <td>gene_counts-deseq2_paired</td>\n", "      <td>0.084715</td>\n", "    </tr>\n", "    <tr>\n", "      <th>169525</th>\n", "      <td>A3GALT2</td>\n", "      <td>0.794453</td>\n", "      <td>-0.495953</td>\n", "      <td>0.947556</td>\n", "      <td>gene_counts</td>\n", "      <td>deseq2_paired</td>\n", "      <td>gene_counts-deseq2_paired</td>\n", "      <td>0.023395</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>186511</th>\n", "      <td>ZYG11A</td>\n", "      <td>0.947480</td>\n", "      <td>-0.051275</td>\n", "      <td>0.988537</td>\n", "      <td>gene_counts</td>\n", "      <td>deseq2_paired</td>\n", "      <td>gene_counts-deseq2_paired</td>\n", "      <td>0.005007</td>\n", "    </tr>\n", "    <tr>\n", "      <th>186512</th>\n", "      <td>ZYG11B</td>\n", "      <td>0.449077</td>\n", "      <td>0.190184</td>\n", "      <td>0.808568</td>\n", "      <td>gene_counts</td>\n", "      <td>deseq2_paired</td>\n", "      <td>gene_counts-deseq2_paired</td>\n", "      <td>0.092284</td>\n", "    </tr>\n", "    <tr>\n", "      <th>186513</th>\n", "      <td>ZYX</td>\n", "      <td>0.010012</td>\n", "      <td>-1.281832</td>\n", "      <td>0.143880</td>\n", "      <td>gene_counts</td>\n", "      <td>deseq2_paired</td>\n", "      <td>gene_counts-deseq2_paired</td>\n", "      <td>0.842000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>186514</th>\n", "      <td>ZZEF1</td>\n", "      <td>0.714608</td>\n", "      <td>-0.069571</td>\n", "      <td>0.920197</td>\n", "      <td>gene_counts</td>\n", "      <td>deseq2_paired</td>\n", "      <td>gene_counts-deseq2_paired</td>\n", "      <td>0.036119</td>\n", "    </tr>\n", "    <tr>\n", "      <th>186515</th>\n", "      <td>ZZZ3</td>\n", "      <td>0.308226</td>\n", "      <td>0.115673</td>\n", "      <td>0.703883</td>\n", "      <td>gene_counts</td>\n", "      <td>deseq2_paired</td>\n", "      <td>gene_counts-deseq2_paired</td>\n", "      <td>0.152500</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>16995 rows × 8 columns</p>\n", "</div>"], "text/plain": ["           gene   p_value     logFC       fdr    data_type           dgea  \\\n", "169521     A1BG  0.690830  0.092293  0.912230  gene_counts  deseq2_paired   \n", "169522     A1CF  0.202384  2.851122  0.601315  gene_counts  deseq2_paired   \n", "169523      A2M  0.166172 -0.467418  0.557798  gene_counts  deseq2_paired   \n", "169524    A2ML1  0.475763  0.298752  0.822782  gene_counts  deseq2_paired   \n", "169525  A3GALT2  0.794453 -0.495953  0.947556  gene_counts  deseq2_paired   \n", "...         ...       ...       ...       ...          ...            ...   \n", "186511   ZYG11A  0.947480 -0.051275  0.988537  gene_counts  deseq2_paired   \n", "186512   ZYG11B  0.449077  0.190184  0.808568  gene_counts  deseq2_paired   \n", "186513      ZYX  0.010012 -1.281832  0.143880  gene_counts  deseq2_paired   \n", "186514    ZZEF1  0.714608 -0.069571  0.920197  gene_counts  deseq2_paired   \n", "186515     ZZZ3  0.308226  0.115673  0.703883  gene_counts  deseq2_paired   \n", "\n", "                   data_type_dgea   log_fdr  \n", "169521  gene_counts-deseq2_paired  0.039896  \n", "169522  gene_counts-deseq2_paired  0.220898  \n", "169523  gene_counts-deseq2_paired  0.253523  \n", "169524  gene_counts-deseq2_paired  0.084715  \n", "169525  gene_counts-deseq2_paired  0.023395  \n", "...                           ...       ...  \n", "186511  gene_counts-deseq2_paired  0.005007  \n", "186512  gene_counts-deseq2_paired  0.092284  \n", "186513  gene_counts-deseq2_paired  0.842000  \n", "186514  gene_counts-deseq2_paired  0.036119  \n", "186515  gene_counts-deseq2_paired  0.152500  \n", "\n", "[16995 rows x 8 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["file = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_rna/_figure_data/002-volcano_plot_rnaseq.tsv'\n", "\n", "df = pd.read_csv(file, sep='\\t')\n", "df = df[df['data_type_dgea'] == 'gene_counts-deseq2_paired']\n", "# df['fdr'] = multitest.fdrcorrection(df['dpm_pval'])[1]\n", "# df['log_fdr'] = -np.log10(df['fdr'])\n", "# df.to_csv('/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_rna/_figure_data/tmp.tsv', sep='\\t', index=False)\n", "\n", "df"]}, {"cell_type": "code", "execution_count": 4, "id": "cd857b54-43a5-4e7d-b4bc-20a28245021a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gene</th>\n", "      <th>fdr</th>\n", "      <th>logFC</th>\n", "      <th>log_fdr</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>170566</th>\n", "      <td>ASPM</td>\n", "      <td>0.039116</td>\n", "      <td>-1.731985</td>\n", "      <td>1.407645</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174549</th>\n", "      <td>FGFR2</td>\n", "      <td>0.022631</td>\n", "      <td>1.126181</td>\n", "      <td>1.645289</td>\n", "    </tr>\n", "    <tr>\n", "      <th>185631</th>\n", "      <td>WIF1</td>\n", "      <td>0.019510</td>\n", "      <td>3.707251</td>\n", "      <td>1.709744</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         gene       fdr     logFC   log_fdr\n", "170566   ASPM  0.039116 -1.731985  1.407645\n", "174549  FGFR2  0.022631  1.126181  1.645289\n", "185631   WIF1  0.019510  3.707251  1.709744"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# genes_oi = ['ASPM', 'FANCA', 'WIF1', 'BRCA2', 'POLE', 'FGFR2', 'ROS1', 'SLC45A3', 'PRKACA']\n", "genes_oi = ['WIF1', 'FGFR2', 'ASPM']\n", "\n", "df.loc[df['gene'].isin(genes_oi), ['gene', 'fdr', 'logFC', 'log_fdr']]"]}, {"cell_type": "code", "execution_count": 19, "id": "e67ad1f3-a3bd-4861-9d1a-f33db5ed5474", "metadata": {}, "outputs": [{"data": {"text/plain": ["25"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["sub = df[df['fdr'] < 0.05]\n", "len(sub[sub['gene'].isin(df2['GENE_SYMBOL'])]['gene'].to_list())\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 26, "id": "61219ae8-c715-405d-b68d-220f399b731f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>GENE_SYMBOL</th>\n", "      <th>NAME</th>\n", "      <th>COSMIC_GENE_ID</th>\n", "      <th>CHROMOSOME</th>\n", "      <th>GENOME_START</th>\n", "      <th>GENOME_STOP</th>\n", "      <th>CHR_BAND</th>\n", "      <th>SOMATIC</th>\n", "      <th>GERMLINE</th>\n", "      <th>TUMOUR_TYPES_SOMATIC</th>\n", "      <th>...</th>\n", "      <th>CANCER_SYNDROME</th>\n", "      <th>TISSUE_TYPE</th>\n", "      <th>MOLECULAR_GENETICS</th>\n", "      <th>ROL<PERSON>_IN_CANCER</th>\n", "      <th>MUTATION_TYPES</th>\n", "      <th>TRANSLOCATION_PARTNER</th>\n", "      <th>OTHER_GERMLINE_MUT</th>\n", "      <th>OTHER_SYNDROME</th>\n", "      <th>TIER</th>\n", "      <th>SYNONYMS</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A1CF</td>\n", "      <td>APOBEC1 complementation factor</td>\n", "      <td>COSG68236</td>\n", "      <td>10</td>\n", "      <td>50799409.0</td>\n", "      <td>50885675.0</td>\n", "      <td>10q11.23</td>\n", "      <td>y</td>\n", "      <td>n</td>\n", "      <td>melanoma</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>E</td>\n", "      <td>NaN</td>\n", "      <td>oncogene</td>\n", "      <td>Mis</td>\n", "      <td>NaN</td>\n", "      <td>n</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>A1CF,ENSG00000148584.14,29974,ACF,ACF64,ACF65,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ABI1</td>\n", "      <td>abl interactor 1</td>\n", "      <td>COSG100962</td>\n", "      <td>10</td>\n", "      <td>26746593.0</td>\n", "      <td>26861087.0</td>\n", "      <td>10p12.1</td>\n", "      <td>y</td>\n", "      <td>n</td>\n", "      <td>AML</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>L</td>\n", "      <td>Dom</td>\n", "      <td>TSG, fusion</td>\n", "      <td>T</td>\n", "      <td>KMT2A</td>\n", "      <td>n</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>ABI1,ENSG00000136754.17,Q8IZP0,10006,ABI-1,E3B1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ABL1</td>\n", "      <td>ABL proto-oncogene 1, non-receptor tyrosine ki...</td>\n", "      <td>COSG106650</td>\n", "      <td>9</td>\n", "      <td>130713946.0</td>\n", "      <td>130887675.0</td>\n", "      <td>9q34.12</td>\n", "      <td>y</td>\n", "      <td>n</td>\n", "      <td>CML, ALL, T-ALL</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>L</td>\n", "      <td>Dom</td>\n", "      <td>oncogene, fusion</td>\n", "      <td>T, Mis</td>\n", "      <td>BCR, ETV6, NUP214</td>\n", "      <td>n</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>ABL1,ENSG00000097007.17,P00519,25,JTK7,c-ABL,p150</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ABL2</td>\n", "      <td>ABL proto-oncogene 2, non-receptor tyrosine ki...</td>\n", "      <td>COSG93778</td>\n", "      <td>1</td>\n", "      <td>179099327.0</td>\n", "      <td>179229684.0</td>\n", "      <td>1q25.2</td>\n", "      <td>y</td>\n", "      <td>n</td>\n", "      <td>AML</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>L</td>\n", "      <td>Dom</td>\n", "      <td>oncogene, fusion</td>\n", "      <td>T</td>\n", "      <td>ETV6</td>\n", "      <td>n</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>ABL2,ENSG00000143322.19,P42684,27,ARG</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>ACKR3</td>\n", "      <td>atypical chemokine receptor 3</td>\n", "      <td>COSG97311</td>\n", "      <td>2</td>\n", "      <td>236567787.0</td>\n", "      <td>236582358.0</td>\n", "      <td>2q37.3</td>\n", "      <td>y</td>\n", "      <td>n</td>\n", "      <td>lipoma</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>M</td>\n", "      <td>Dom</td>\n", "      <td>oncogene, fusion</td>\n", "      <td>T</td>\n", "      <td>HMGA2</td>\n", "      <td>n</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>,ENSG00000144476.5,P25106,57007,<PERSON>R159,RDC1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>743</th>\n", "      <td>ZNF429</td>\n", "      <td>zinc finger protein 429</td>\n", "      <td>COSG92428</td>\n", "      <td>19</td>\n", "      <td>21496682.0</td>\n", "      <td>21556270.0</td>\n", "      <td>19p12</td>\n", "      <td>y</td>\n", "      <td>n</td>\n", "      <td>GBM</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>O</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Mis</td>\n", "      <td>NaN</td>\n", "      <td>n</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON>429,ENSG00000197013.9,Q86V71,353088</td>\n", "    </tr>\n", "    <tr>\n", "      <th>744</th>\n", "      <td>ZNF479</td>\n", "      <td>zinc finger protein 479</td>\n", "      <td>COSG111702</td>\n", "      <td>7</td>\n", "      <td>57119614.0</td>\n", "      <td>57139864.0</td>\n", "      <td>7p11.2</td>\n", "      <td>y</td>\n", "      <td>n</td>\n", "      <td>lung cancer, bladder carcinoma, prostate carci...</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>E</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Mis</td>\n", "      <td>NaN</td>\n", "      <td>n</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON>479,ENSG00000185177.12,Q96JC4,90827,KR19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>745</th>\n", "      <td>ZNF521</td>\n", "      <td>zinc finger protein 521</td>\n", "      <td>COSG65491</td>\n", "      <td>18</td>\n", "      <td>25061926.0</td>\n", "      <td>25352190.0</td>\n", "      <td>18q11.2</td>\n", "      <td>y</td>\n", "      <td>n</td>\n", "      <td>ALL</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>L</td>\n", "      <td>Dom</td>\n", "      <td>oncogene, fusion</td>\n", "      <td>T</td>\n", "      <td>PAX5</td>\n", "      <td>n</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON>521,ENSG00000198795.10,Q96K83,25925,EHZF,Evi3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>746</th>\n", "      <td>ZNRF3</td>\n", "      <td>zinc and ring finger 3</td>\n", "      <td>COSG102839</td>\n", "      <td>22</td>\n", "      <td>28883592.0</td>\n", "      <td>29057487.0</td>\n", "      <td>22q12.1</td>\n", "      <td>y</td>\n", "      <td>n</td>\n", "      <td>colorectal cancer, adrenocortical carcinoma, g...</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>E</td>\n", "      <td>NaN</td>\n", "      <td>TSG</td>\n", "      <td>N, F, Mis</td>\n", "      <td>NaN</td>\n", "      <td>n</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON>F<PERSON>,ENSG00000183579.15,Q9ULT6,84133,BK747E2....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>747</th>\n", "      <td>ZRSR2</td>\n", "      <td>zinc finger CCCH-type, RNA binding motif and s...</td>\n", "      <td>COSG85804</td>\n", "      <td>X</td>\n", "      <td>15790472.0</td>\n", "      <td>15823260.0</td>\n", "      <td>Xp22.2</td>\n", "      <td>y</td>\n", "      <td>n</td>\n", "      <td>MDS, CLL</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>L</td>\n", "      <td>Rec</td>\n", "      <td>TSG</td>\n", "      <td>F, S, Mis</td>\n", "      <td>NaN</td>\n", "      <td>n</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>ZRSR2,ENSG00000169249.12,Q15696,8233,U2AF1-RS2...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>748 rows × 21 columns</p>\n", "</div>"], "text/plain": ["    GENE_SYMBOL                                               NAME  \\\n", "0          A1CF                     APOBEC1 complementation factor   \n", "1          ABI1                                   abl interactor 1   \n", "2          ABL1  ABL proto-oncogene 1, non-receptor tyrosine ki...   \n", "3          ABL2  ABL proto-oncogene 2, non-receptor tyrosine ki...   \n", "4         ACKR3                      atypical chemokine receptor 3   \n", "..          ...                                                ...   \n", "743      ZNF429                            zinc finger protein 429   \n", "744      ZNF479                            zinc finger protein 479   \n", "745      ZNF521                            zinc finger protein 521   \n", "746       ZNRF3                             zinc and ring finger 3   \n", "747       ZRSR2  zinc finger CCCH-type, RNA binding motif and s...   \n", "\n", "    COSMIC_GENE_ID CHROMOSOME  GENOME_START  GENOME_STOP  CHR_BAND SOMATIC  \\\n", "0        COSG68236         10    50799409.0   50885675.0  10q11.23       y   \n", "1       COSG100962         10    26746593.0   26861087.0   10p12.1       y   \n", "2       COSG106650          9   130713946.0  130887675.0   9q34.12       y   \n", "3        COSG93778          1   179099327.0  179229684.0    1q25.2       y   \n", "4        COSG97311          2   236567787.0  236582358.0    2q37.3       y   \n", "..             ...        ...           ...          ...       ...     ...   \n", "743      COSG92428         19    21496682.0   21556270.0     19p12       y   \n", "744     COSG111702          7    57119614.0   57139864.0    7p11.2       y   \n", "745      COSG65491         18    25061926.0   25352190.0   18q11.2       y   \n", "746     COSG102839         22    28883592.0   29057487.0   22q12.1       y   \n", "747      COSG85804          X    15790472.0   15823260.0    Xp22.2       y   \n", "\n", "    GERMLINE                               TUMOUR_TYPES_SOMATIC  ...  \\\n", "0          n                                           melanoma  ...   \n", "1          n                                                AML  ...   \n", "2          n                                    CML, ALL, T-ALL  ...   \n", "3          n                                                AML  ...   \n", "4          n                                             lipoma  ...   \n", "..       ...                                                ...  ...   \n", "743        n                                                GBM  ...   \n", "744        n  lung cancer, bladder carcinoma, prostate carci...  ...   \n", "745        n                                                ALL  ...   \n", "746        n  colorectal cancer, adrenocortical carcinoma, g...  ...   \n", "747        n                                           MDS, CLL  ...   \n", "\n", "    CANCER_<PERSON><PERSON><PERSON><PERSON>ME TISSUE_TYPE MOLECULAR_GENETICS    ROLE_IN_CANCER  \\\n", "0               NaN           E                NaN          oncogene   \n", "1               NaN           L                Dom       TSG, fusion   \n", "2               NaN           L                Dom  oncogene, fusion   \n", "3               NaN           L                Dom  oncogene, fusion   \n", "4               NaN           M                Dom  oncogene, fusion   \n", "..              ...         ...                ...               ...   \n", "743             NaN           O                NaN               NaN   \n", "744             NaN           E                NaN               NaN   \n", "745             NaN           L                Dom  oncogene, fusion   \n", "746             NaN           E                NaN               TSG   \n", "747             NaN           L                Rec               TSG   \n", "\n", "    MUTATION_TYPES TRANSLOCATION_PARTNER OTHER_GERMLINE_MUT OTHER_SYNDROME  \\\n", "0              Mis                   NaN                  n            NaN   \n", "1                T                 KMT2A                  n            NaN   \n", "2           T, Mis     BCR, ETV6, NUP214                  n            NaN   \n", "3                T                  ETV6                  n            NaN   \n", "4                T                 HMGA2                  n            NaN   \n", "..             ...                   ...                ...            ...   \n", "743            Mis                   NaN                  n            NaN   \n", "744            Mis                   NaN                  n            NaN   \n", "745              T                  PAX5                  n            NaN   \n", "746      N, F, Mis                   NaN                  n            NaN   \n", "747      F, <PERSON>, Mis                   NaN                  n            NaN   \n", "\n", "    TIER                                           SYNONYMS  \n", "0      2  <PERSON><PERSON>,ENSG00000148584.14,29974,ACF,ACF64,ACF65,...  \n", "1      1    ABI1,ENSG00000136754.17,Q8IZP0,10006,ABI-1,E3B1  \n", "2      1  ABL1,ENSG00000097007.17,P00519,25,JTK7,c-ABL,p150  \n", "3      1              <PERSON>L2,ENSG00000143322.19,P42684,27,ARG  \n", "4      1   <PERSON><PERSON><PERSON><PERSON>,ENSG00000144476.5,P25106,57007,<PERSON>R159,RDC1  \n", "..   ...                                                ...  \n", "743    2             <PERSON><PERSON>429,ENSG00000197013.9,Q86V71,353088  \n", "744    2        <PERSON><PERSON>479,ENSG00000185177.12,Q96JC4,90827,<PERSON>R19  \n", "745    1   <PERSON><PERSON>521,ENSG00000198795.10,Q96K83,25925,<PERSON><PERSON><PERSON><PERSON>,Evi3  \n", "746    2  <PERSON><PERSON><PERSON>3,ENSG00000183579.15,Q9ULT6,84133,BK747E2....  \n", "747    1  ZRSR2,ENSG00000169249.12,Q15696,8233,U2AF1-RS2...  \n", "\n", "[748 rows x 21 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["file2 = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/data/ref_data/cgc_v100_17102024.tsv'\n", "df2 = pd.read_csv(file2, sep='\\t')\n", "\n", "df2"]}, {"cell_type": "code", "execution_count": null, "id": "04b8e6f8-24ab-4dc6-9b34-07eee4aa5a2b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "98df1e47-2329-4fa2-8095-bfc6bfb764b8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 32, "id": "dbfee059-e960-47a2-bcd5-48838fecbbe3", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'p_value'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[32], line 42\u001b[0m\n\u001b[1;32m     39\u001b[0m results_df \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mDataF<PERSON>e(results)\n\u001b[1;32m     41\u001b[0m \u001b[38;5;66;03m# Save or display\u001b[39;00m\n\u001b[0;32m---> 42\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[43mresults_df\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msort_values\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mp_value\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m)\n\u001b[1;32m     43\u001b[0m results_df\u001b[38;5;241m.\u001b[39mto_csv(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmutational_signature_pairwise_chi2.tsv\u001b[39m\u001b[38;5;124m\"\u001b[39m, sep\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\t\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, index\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m)\n", "File \u001b[0;32m/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev/lib/python3.10/site-packages/pandas/core/frame.py:7189\u001b[0m, in \u001b[0;36mDataFrame.sort_values\u001b[0;34m(self, by, axis, ascending, inplace, kind, na_position, ignore_index, key)\u001b[0m\n\u001b[1;32m   7183\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m lexsort_indexer(\n\u001b[1;32m   7184\u001b[0m         keys, orders\u001b[38;5;241m=\u001b[39mascending, na_position\u001b[38;5;241m=\u001b[39mna_position, key\u001b[38;5;241m=\u001b[39mkey\n\u001b[1;32m   7185\u001b[0m     )\n\u001b[1;32m   7186\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(by):\n\u001b[1;32m   7187\u001b[0m     \u001b[38;5;66;03m# len(by) == 1\u001b[39;00m\n\u001b[0;32m-> 7189\u001b[0m     k \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_label_or_level_values\u001b[49m\u001b[43m(\u001b[49m\u001b[43mby\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maxis\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   7191\u001b[0m     \u001b[38;5;66;03m# need to rewrap column in Series to apply key function\u001b[39;00m\n\u001b[1;32m   7192\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m key \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m   7193\u001b[0m         \u001b[38;5;66;03m# error: Incompatible types in assignment (expression has type\u001b[39;00m\n\u001b[1;32m   7194\u001b[0m         \u001b[38;5;66;03m# \"Series\", variable has type \"ndarray\")\u001b[39;00m\n", "File \u001b[0;32m/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev/lib/python3.10/site-packages/pandas/core/generic.py:1911\u001b[0m, in \u001b[0;36mNDFrame._get_label_or_level_values\u001b[0;34m(self, key, axis)\u001b[0m\n\u001b[1;32m   1909\u001b[0m     values \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39maxes[axis]\u001b[38;5;241m.\u001b[39mget_level_values(key)\u001b[38;5;241m.\u001b[39m_values\n\u001b[1;32m   1910\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1911\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON>eyError\u001b[39;00m(key)\n\u001b[1;32m   1913\u001b[0m \u001b[38;5;66;03m# Check for duplicates\u001b[39;00m\n\u001b[1;32m   1914\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m values\u001b[38;5;241m.\u001b[39mndim \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n", "\u001b[0;31m<PERSON>eyError\u001b[0m: 'p_value'"]}], "source": ["from scipy.stats import chi2_contingency\n", "\n", "# Ensure the sample names are in the index or in a column\n", "df = df.set_index(df.columns[0])  # assumes first column has sample names\n", "\n", "# Extract patient IDs from sample names (e.g., RLGS1 from RLGS1-primary)\n", "df[\"patient_id\"] = df.index.str.extract(r\"(RLGS\\d+)\")\n", "df[\"tumor_type\"] = df.index.str.extract(r\"-(primary|recurrent)\")\n", "\n", "# Filter only complete patient pairs (primary and recurrent)\n", "patient_counts = df[\"patient_id\"].value_counts()\n", "paired_patients = patient_counts[patient_counts == 2].index\n", "df_paired = df[df[\"patient_id\"].isin(paired_patients)]\n", "\n", "# Prepare results\n", "results = []\n", "\n", "# Loop through each patient\n", "for patient in paired_patients:\n", "    sub_df = df_paired[df_paired[\"patient_id\"] == patient]\n", "    \n", "    # Drop metadata columns to get just signature counts\n", "    sig_matrix = sub_df.drop(columns=[\"patient_id\", \"tumor_type\"])\n", "\n", "    # Convert to count-like values if proportions (optional, only if needed)\n", "    # sig_matrix = (sig_matrix * 1000).astype(int)\n", "\n", "    # Chi-square test\n", "    chi2, p, dof, expected = chi2_contingency(sig_matrix.values)\n", "\n", "    results.append({\n", "        \"patient_id\": patient,\n", "        \"chi2_stat\": chi2,\n", "        \"p_value\": p,\n", "        \"degrees_of_freedom\": dof\n", "    })\n", "\n", "# Convert to DataFrame\n", "results_df = pd.DataFrame(results)\n", "\n", "# Save or display\n", "print(results_df.sort_values(\"p_value\"))\n", "# results_df.to_csv(\"mutational_signature_pairwise_chi2.tsv\", sep=\"\\t\", index=False)"]}, {"cell_type": "code", "execution_count": 29, "id": "19579a20-08ac-434f-9ade-528626bf56cc", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sample</th>\n", "      <th>description</th>\n", "      <th>variable</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>288</th>\n", "      <td>RLGS1-primary</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>IDH-1 wildtype</td>\n", "    </tr>\n", "    <tr>\n", "      <th>289</th>\n", "      <td>RLGS1-recurrent</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>IDH-1 wildtype</td>\n", "    </tr>\n", "    <tr>\n", "      <th>290</th>\n", "      <td>RLGS2-primary</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>IDH-1 wildtype</td>\n", "    </tr>\n", "    <tr>\n", "      <th>291</th>\n", "      <td>RLGS2-recurrent</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>IDH-1 wildtype</td>\n", "    </tr>\n", "    <tr>\n", "      <th>292</th>\n", "      <td>RLGS3-primary</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>IDH-1 wildtype</td>\n", "    </tr>\n", "    <tr>\n", "      <th>293</th>\n", "      <td>RLGS3-recurrent</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>IDH-1 wildtype</td>\n", "    </tr>\n", "    <tr>\n", "      <th>294</th>\n", "      <td>RLGS4-primary</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>IDH-1 wildtype</td>\n", "    </tr>\n", "    <tr>\n", "      <th>295</th>\n", "      <td>RLGS4-recurrent</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>IDH-1 wildtype</td>\n", "    </tr>\n", "    <tr>\n", "      <th>296</th>\n", "      <td>RLGS5-primary</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>unknown</td>\n", "    </tr>\n", "    <tr>\n", "      <th>297</th>\n", "      <td>RLGS5-recurrent</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>unknown</td>\n", "    </tr>\n", "    <tr>\n", "      <th>298</th>\n", "      <td>RLGS6-primary</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>GBM/PNET</td>\n", "    </tr>\n", "    <tr>\n", "      <th>299</th>\n", "      <td>RLGS6-recurrent</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>unknown</td>\n", "    </tr>\n", "    <tr>\n", "      <th>300</th>\n", "      <td>RLGS7-primary</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>IDH-1 wildtype</td>\n", "    </tr>\n", "    <tr>\n", "      <th>301</th>\n", "      <td>RLGS7-recurrent</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>IDH-1 wildtype</td>\n", "    </tr>\n", "    <tr>\n", "      <th>302</th>\n", "      <td>RLGS8-primary</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>GBM/PNC</td>\n", "    </tr>\n", "    <tr>\n", "      <th>303</th>\n", "      <td>RLGS8-recurrent</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>Giant cell Glioblastoma</td>\n", "    </tr>\n", "    <tr>\n", "      <th>304</th>\n", "      <td>RLGS9-primary</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>IDH-1 wildtype</td>\n", "    </tr>\n", "    <tr>\n", "      <th>305</th>\n", "      <td>RLGS9-recurrent</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>IDH-1 wildtype</td>\n", "    </tr>\n", "    <tr>\n", "      <th>306</th>\n", "      <td>RLGS10-primary</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>IDH-1 wildtype</td>\n", "    </tr>\n", "    <tr>\n", "      <th>307</th>\n", "      <td>RLGS10-recurrent</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>IDH-1 wildtype</td>\n", "    </tr>\n", "    <tr>\n", "      <th>308</th>\n", "      <td>RLGS11-primary</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>Epithelioid GBM</td>\n", "    </tr>\n", "    <tr>\n", "      <th>309</th>\n", "      <td>RLGS11-recurrent</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>IDH-1 wildtype</td>\n", "    </tr>\n", "    <tr>\n", "      <th>310</th>\n", "      <td>RLGS12-primary</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>unknown</td>\n", "    </tr>\n", "    <tr>\n", "      <th>311</th>\n", "      <td>RLGS12-recurrent</td>\n", "      <td>tumor_data</td>\n", "      <td>Clinical subtype</td>\n", "      <td>unknown</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               sample description          variable                    value\n", "288     RLGS1-primary  tumor_data  Clinical subtype           IDH-1 wildtype\n", "289   RLGS1-recurrent  tumor_data  Clinical subtype           IDH-1 wildtype\n", "290     RLGS2-primary  tumor_data  Clinical subtype           IDH-1 wildtype\n", "291   RLGS2-recurrent  tumor_data  Clinical subtype           IDH-1 wildtype\n", "292     RLGS3-primary  tumor_data  Clinical subtype           IDH-1 wildtype\n", "293   RLGS3-recurrent  tumor_data  Clinical subtype           IDH-1 wildtype\n", "294     RLGS4-primary  tumor_data  Clinical subtype           IDH-1 wildtype\n", "295   RLGS4-recurrent  tumor_data  Clinical subtype           IDH-1 wildtype\n", "296     RLGS5-primary  tumor_data  Clinical subtype                  unknown\n", "297   RLGS5-recurrent  tumor_data  Clinical subtype                  unknown\n", "298     RLGS6-primary  tumor_data  Clinical subtype                 GBM/PNET\n", "299   RLGS6-recurrent  tumor_data  Clinical subtype                  unknown\n", "300     RLGS7-primary  tumor_data  Clinical subtype           IDH-1 wildtype\n", "301   RLGS7-recurrent  tumor_data  Clinical subtype           IDH-1 wildtype\n", "302     RLGS8-primary  tumor_data  Clinical subtype                  GBM/PNC\n", "303   RLGS8-recurrent  tumor_data  Clinical subtype  Giant cell Glioblastoma\n", "304     RLGS9-primary  tumor_data  Clinical subtype           IDH-1 wildtype\n", "305   RLGS9-recurrent  tumor_data  Clinical subtype           IDH-1 wildtype\n", "306    RLGS10-primary  tumor_data  Clinical subtype           IDH-1 wildtype\n", "307  RLGS10-recurrent  tumor_data  Clinical subtype           IDH-1 wildtype\n", "308    RLGS11-primary  tumor_data  Clinical subtype          Epithelioid GBM\n", "309  RLGS11-recurrent  tumor_data  Clinical subtype           IDH-1 wildtype\n", "310    RLGS12-primary  tumor_data  Clinical subtype                  unknown\n", "311  RLGS12-recurrent  tumor_data  Clinical subtype                  unknown"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["df[df['variable'] == 'Clinical subtype']"]}, {"cell_type": "code", "execution_count": 22, "id": "04ab764b-b72b-4132-ae23-17a481553988", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["41.0\n"]}, {"data": {"text/plain": ["21.0"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["print(d1['sv_type'].median())\n", "d2['sv_type'].median()"]}, {"cell_type": "code", "execution_count": 23, "id": "6b082d40-7787-4cd0-ab6b-9f86f41656b1", "metadata": {}, "outputs": [], "source": ["file1 = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_sarek/consensus_vcfs/vcf_contingency_table_stats.tsv'\n", "file2 = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_sarek/vcf_figure_data/snv_nb_differentially_mutated.tsv'\n", "\n", "df1 = pd.read_csv(file1, sep='\\t')\n", "df2 = pd.read_csv(file2, sep='\\t')"]}, {"cell_type": "code", "execution_count": null, "id": "ea54bd06-c323-4334-a3ff-066e0e65f0f4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 24, "id": "f128a1b7-a26a-40dc-a132-fdd4c5a3fd41", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>gene</th>\n", "      <th>pvalue</th>\n", "      <th>foldchange</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>,</td>\n", "      <td>1.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>,,</td>\n", "      <td>1.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>,,,</td>\n", "      <td>1.0</td>\n", "      <td>-0.125531</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>,,,,</td>\n", "      <td>1.0</td>\n", "      <td>0.152003</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>,,,,,</td>\n", "      <td>1.0</td>\n", "      <td>0.169925</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37695</th>\n", "      <td>ZSWIM5P1</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37696</th>\n", "      <td>ZSWIM5P3</td>\n", "      <td>1.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37697</th>\n", "      <td>ZXDB</td>\n", "      <td>1.0</td>\n", "      <td>-1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37698</th>\n", "      <td>ZYG11A</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37699</th>\n", "      <td>ZYG11B</td>\n", "      <td>1.0</td>\n", "      <td>0.584963</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>37700 rows × 3 columns</p>\n", "</div>"], "text/plain": ["           gene  pvalue  foldchange\n", "0             ,     1.0    0.000000\n", "1            ,,     1.0    0.000000\n", "2           ,,,     1.0   -0.125531\n", "3          ,,,,     1.0    0.152003\n", "4         ,,,,,     1.0    0.169925\n", "...         ...     ...         ...\n", "37695  ZSWIM5P1     1.0    1.000000\n", "37696  ZSWIM5P3     1.0    0.000000\n", "37697      ZXDB     1.0   -1.000000\n", "37698    ZYG11A     1.0    1.000000\n", "37699    ZYG11B     1.0    0.584963\n", "\n", "[37700 rows x 3 columns]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["df1"]}, {"cell_type": "code", "execution_count": 25, "id": "5864583b-3dc5-4fec-8dfc-adad2d785a0d", "metadata": {}, "outputs": [{"data": {"text/plain": ["count    37700.000000\n", "mean         0.978145\n", "std          0.105539\n", "min          0.027191\n", "25%          1.000000\n", "50%          1.000000\n", "75%          1.000000\n", "max          1.000000\n", "Name: pvalue, dtype: float64"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["df1['pvalue'].describe()"]}, {"cell_type": "code", "execution_count": 26, "id": "6495a445-8930-429d-aacf-250d6009c336", "metadata": {}, "outputs": [{"data": {"text/plain": ["count     1.557700e+04\n", "mean      6.473309e-01\n", "std       3.356260e-01\n", "min      1.146618e-161\n", "25%       3.067649e-01\n", "50%       7.862058e-01\n", "75%       9.459630e-01\n", "max       1.000000e+00\n", "Name: pvalue, dtype: float64"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df2['pvalue'].describe()"]}, {"cell_type": "code", "execution_count": null, "id": "1d1f257d-11bf-40a2-8eee-4895788a8886", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9bb317a6-7f74-423e-9057-bd7c28ee6896", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}