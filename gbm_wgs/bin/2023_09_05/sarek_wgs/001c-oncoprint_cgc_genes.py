# Alec <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

help_message = '''
Failed
'''


def summarize_variants(vcf_translated_subset_file, snv_genes):
    # read dfs
    df = pd.read_csv(vcf_translated_subset_file, sep='\t')

    # list samples
    samples = df['sample'].unique()

    # subset to genes of interest
    df = df[df['gene'].isin(snv_genes)]

    # subset to coding mutations
    df = df[~np.isin(df['detailed_mutation_type'], 'Non-coding')]

    # subset to mutations of interest (ignore 'synonymous_variant')
    mutations_of_interest = ['missense_variant', 'frameshift_variant', 'stop_gained', 'start_lost', 'inframe_deletion', 'inframe_insertion', 'splice_donor_region_variant', 'splice_acceptor_variant', 'splice_region_variant', 'stop_lost']

    df = df[df['detailed_mutation_type'].isin(mutations_of_interest)]

    # add none gene with mutation "none" for each sample 
    for sample in samples:
        df = pd.concat([df, pd.DataFrame([{'sample': sample, 'gene': 'none', 'specific_mutation_type': 'none'}])], ignore_index=True)

    # add a sample "null_13" that has "none" for each gene of interest
    for gene in snv_genes:
        df = pd.concat([df, pd.DataFrame([{'sample': 'null_13', 'gene': gene, 'specific_mutation_type': 'none'}])], ignore_index=True)

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # list all files of interest
    df = summarize_variants(vcf_translated_subset_file, all_genes)

    # save to file
    df.to_csv(cgc_gene_muations_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # genes of interest 
    # snv_genes = ['TP53', 'IDH1', 'PTEN', 'ATRX', 'EGFR', 'NF1', 'TERT', 'PIK3CA', 'CIC', 'RB1', 'PIK3R1', 'FUBP1', 'none']
    all_genes = ['EGFR', 'ATRX', 'PTEN', 'CDK6', 'MET', 'CDKN2A', 'RB1', 'MDM4', 'CDK4', 'TP53', 'PDGFRA', 'FUBP1', 'NF1', 'MDM2', 'MYCN', 'PIK3CA', 'PIK3R1', 'CCND2', 'CIC', 'IDH1', 'TERTp', 'none']
    
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["vcf_translated_subset_file=", "cgc_gene_muations_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--vcf_translated_subset_file"):
            vcf_translated_subset_file = str(arg)

        if opt in ("--cgc_gene_muations_file"):
            cgc_gene_muations_file = str(arg)

    main()


