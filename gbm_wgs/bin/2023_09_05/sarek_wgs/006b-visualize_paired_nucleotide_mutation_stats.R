# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)

library(grid)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



boxplot = function(count_type, input_df){

# subset to type of count
input_df = input_df[input_df$type_of_count == count_type,]

# plot
p = ggplot(input_df, aes(x = tumor_type, y = mutation_count)) + plot_theme() +
geom_boxplot(outlier.shape = NA, colour = 'black') + 
geom_jitter(size = 3, alpha = 0.6) + 

# facet
facet_wrap(~mutation_pval, scales='free_y', ncol = length(unique(input_df$mutation))) +

ggtitle(count_type) +
xlab('') + ylab('Mutation count') +

theme(legend.position = 'none')

print(p)

return()
}




pdf(opt$figure_file, width=18)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')

# # sort type_of_count by w_pvalue increasing
# input_df$mutation_pval = fct_reorder(input_df$mutation_pval, input_df$w_pvalue, mean, .desc=FALSE)

# plot boxplots for each mutation
lapply(unique(input_df$type_of_count), boxplot, input_df)


dev.off()


print(opt$figure_file)




