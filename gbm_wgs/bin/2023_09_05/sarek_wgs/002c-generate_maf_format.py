# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import re

help_message = '''
Failed
'''


maf_variant_dict = {'inframe_deletion':'In_Frame_Del',
 'inframe_insertion':'In_Frame_Ins',
 'missense_variant':'Missense_Mutation',
 'start_lost':'Nonsense_Mutation',
 'stop_gained':'Nonsense_Mutation',
 'stop_lost':'Nonsense_Mutation'}

def extract_consequence(consequence):
    # Variant list of interest
    variant_list = ['NMD_transcript_variant', 'frameshift_variant', 'inframe_deletion', 
                    'inframe_insertion', 'missense_variant', 'start_lost', 'stop_gained', 'stop_lost']

    # Create a regex pattern to match any of the variants, with word boundaries to match exact words
    pattern = r'\b(' + '|'.join(variant_list) + r')\b'

    # Use re.search to find the first matching variant
    match = re.search(pattern, consequence, flags=re.IGNORECASE)  # Case insensitive match

    if match:
        return match.group()
    else:
        return 'other'

# Function to extract the required aa/aa pair and position/position pair
def extract_information(row):
    # Split the values based on comma delimiter
    protein_positions = row['Protein_position'].split(',')
    amino_acids = row['Amino_acids'].split(',')

    # hugo gene names
    try:
        hugo_names = row['Hugo_Symbol'].split(',')
    except:
        hugo_names = np.repeat(None, len(amino_acids))

    # protein version
    try:
        protein_version = row['MANE_SELECT'].split(',')
    except:
        protein_version = np.repeat(None, len(amino_acids))

    # ensembl protein id
    try:
        ensp = row['ENSP'].split(',')
    except:
        ensp = np.repeat(None, len(amino_acids))

    # consequence of mutation
    try:
        consequence = row['Consequence'].split(',')
    except:
        consequence = np.repeat(None, len(amino_acids))

    # Iterate over the protein positions and amino acids
    for idx, pos in enumerate(protein_positions):
        if '/' in pos:
            # extract the amino acid and position
            aa = amino_acids[idx]
            if '/' in aa:
                # Split the values based on the '/' delimiter
                pos_parts = pos.split('/')
                aa_parts = aa.split('/')
                
                # Convert one-letter AA codes to three-letter and form the final string
                if len(aa_parts) == 2 and len(pos_parts) == 2:
                    return (f'p.{aa_parts[0]}{pos_parts[0]}{aa_parts[1]}', hugo_names[idx], protein_version[idx], ensp[idx], extract_consequence(consequence[idx]))

    # Return default values if no valid entry is found
    return ('', '', '', '')


def filter_for_protein_coding_variants(df):
    # Function to check if the cell has non-empty values after splitting by comma
    def has_non_empty_values(cell):
        # Split the cell by commas
        components = cell.split(',')
        # Return True if any of the split components are non-empty (after stripping spaces)
        return any(comp.strip() for comp in components)

    # Filter out rows where protein_position is NA
    df = df[~df['Protein_position'].isna()]

    # Filter out rows where protein_position is empty
    df = df[df['Protein_position'].apply(has_non_empty_values)]

    # filter NAs again
    df = df[~df['Protein_position'].isna()]

    return df


def add_cols_subset(df):
    # add protein position
    df['Protein_position'] = df['HGVSp_Short'].str.extract(r'(\d+)')

    # adjust the Transcript_version column to exclude decimal points
    df['Transcript_version'] = df['Transcript_version'].str.split('.').str[0]

    # add end position
    df['End_Position'] = df['Start_Position'].to_numpy()

    # add Tumor_sample_barcode
    df['Tumor_Sample_Barcode'] = df['sample']

    # rename Variant_Classification to general_variant_classification
    df['general_variant_classification'] = df['Variant_Classification'].copy()


    # replace frameshift_variant insertion and deletion depending on if it's an insertion or deletion
    deletion = df['Variant_Type'] == 'DEL'
    insertion = df['Variant_Type'] == 'INS'

    mask = np.logical_and(df['Variant_Classification'] == 'frameshift_variant', deletion)
    df.loc[mask, 'Variant_Classification'] = 'Frame_Shift_Del'

    mask = np.logical_and(df['Variant_Classification'] == 'frameshift_variant', insertion)
    df.loc[mask, 'Variant_Classification'] = 'Frame_Shift_Ins'

    # map variant classification to maf format
    df['Variant_Classification'] = df['Variant_Classification'].apply(lambda x: maf_variant_dict.get(x, x))


    # replace SNV and multi-SNV with SNP in "Variant_Type" column because it's Maftools
    df['specific_variant_type'] = df['Variant_Type'].copy()
    df['Variant_Type'] = df['Variant_Type'].replace('SNV', 'SNP').replace('multi-SNV', 'SNP')

    # subset to only include required columns
    df = df[['Chromosome', 'Start_Position', 'End_Position', 'Hugo_Symbol', 'Reference_Allele', 'Tumor_Seq_Allele2', 'Variant_Classification', 'Variant_Type', 'Tumor_Sample_Barcode', 'HGVSp_Short', 'Transcript_version', 'ENSP', 'Protein_position', 'general_variant_classification']]

    return df


def generate_maf_df(expanded_vcf_file):
    # load expanded vcf file
    df = pd.read_csv(expanded_vcf_file, sep='\t')

    # filter to only include protein-coding variants
    df = filter_for_protein_coding_variants(df)

    # add nucleotide variant classification
    df['Variant_Type'] = df['VARIANT_CLASS'].str.split(",").str[0].str.replace('SNV', 'SNV').str.replace('substitution', 'multi-SNV').str.replace('insertion', 'INS').str.replace('deletion', 'DEL')

    # rename columns
    df = df.rename(columns={'CHROM': 'Chromosome', 'POS': 'Start_Position', 'SYMBOL': 'Hugo_Symbol', 'REF': 'Reference_Allele', 'ALT': 'Tumor_Seq_Allele2'})


    # create protein short form and extract protein version and HUGO symbol
    df[['HGVSp_Short', 'Hugo_Symbol', 'Transcript_version', 'ENSP', 'Variant_Classification']] = df.apply(lambda row: pd.Series(extract_information(row)), axis=1)

    # add columns and subset
    df = add_cols_subset(df)

    # subset to only variants with valid HGVSp_Short values
    df = df[~df['HGVSp_Short'].isna()]
    df = df[~(df['HGVSp_Short'].str.len() == 0)]

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # generate maf format df
    df = generate_maf_df(expanded_vcf_file)

    # save to files
    df.to_csv(maf_format_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["expanded_vcf_file=", "maf_format_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--expanded_vcf_file"):
            expanded_vcf_file = str(arg)

        if opt in ("--maf_format_file"):
            maf_format_file = str(arg)

    main()




