# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob, re
from concurrent.futures import ProcessPoolExecutor


help_message = '''
Failed
'''

impacting_variants = ['transcript_ablation', 'splice_acceptor_variant', 'splice_donor_variant', 'stop_gained', 'frameshift_variant', 'stop_lost', 'start_lost', 'transcript_amplification', 'feature_elongation', 'feature_truncation', 'inframe_insertion', 'inframe_deletion', 'missense_variant', 'protein_altering_variant', 'splice_donor_5th_base_variant', 'splice_region_variant', 'splice_donor_region_variant', 'splice_polypyrimidine_tract_variant', 'incomplete_terminal_codon_variant', 'coding_sequence_variant', 'NMD_transcript_variant', 'coding_transcript_variant']

non_impacting_variants = ['start_retained_variant', 'stop_retained_variant', 'synonymous_variant', '5_prime_UTR_variant', '3_prime_UTR_variant', 'non_coding_transcript_exon_variant', 'intron_variant', 'non_coding_transcript_variant', 'upstream_gene_variant', 'downstream_gene_variant', 'regulatory_region_amplification', 'regulatory_region_variant', 'intergenic_variant', 'sequence_variant', 'TFBS_ablation', 'regulatory_region_ablation', 'TFBS_amplification', 'TF_binding_site_variant', 'mature_miRNA_variant']

# Mapping of one-letter amino acids to three-letter codes
amino_acid_map = {
    'A': 'Ala', 'R': 'Arg', 'N': 'Asn', 'D': 'Asp', 'C': 'Cys',
    'Q': 'Gln', 'E': 'Glu', 'G': 'Gly', 'H': 'His', 'I': 'Ile',
    'L': 'Leu', 'K': 'Lys', 'M': 'Met', 'F': 'Phe', 'P': 'Pro',
    'S': 'Ser', 'T': 'Thr', 'W': 'Trp', 'Y': 'Tyr', 'V': 'Val',
    'X': 'Ter'  # For stop codons (termination)
}


def classify_mutation_type(info_dict):
    # are any Consequence fields in the impacting_variants list?
    if np.any(np.isin(info_dict.get('Consequence'), impacting_variants)):
        mutation_type = 'Impacting'
        spec_mutation_type = info_dict.get('Consequence')[np.where(np.isin(info_dict.get('Consequence'), impacting_variants))[0][0]]
    else:
        mutation_type = 'non_impacting'
        spec_mutation_type = info_dict.get('Consequence')[0].split("&")[0]

    # replace numbers with words
    spec_mutation_type = re.sub(r'3', 'three', spec_mutation_type)
    spec_mutation_type = re.sub(r'5', 'five', spec_mutation_type)
    
    return [mutation_type, spec_mutation_type]


def extract_info_vep(info_str):
    """Helper function to extract relevant information from the INFO field."""
    info_dict = {}
    
    # vep fields
    vep_fields = ['Allele', 'Consequence', 'IMPACT', 'SYMBOL', 'Gene', 'Feature_type', 'Feature', 'BIOTYPE', 'EXON', 'INTRON', 'HGVSc', 'HGVSp', 'cDNA_position', 'CDS_position', 'Protein_position', 'Amino_acids', 'Codons', 'Existing_variation', 'DISTANCE', 'STRAND', 'FLAGS', 'VARIANT_CLASS', 'SYMBOL_SOURCE', 'HGNC_ID', 'CANONICAL', 'MANE_SELECT', 'MANE_PLUS_CLINICAL', 'TSL', 'APPRIS', 'CCDS', 'ENSP', 'SWISSPROT', 'TREMBL', 'UNIPARC', 'UNIPROT_ISOFORM', 'GENE_PHENO', 'SIFT', 'PolyPhen', 'DOMAINS', 'miRNA', 'AF', 'AFR_AF', 'AMR_AF', 'EAS_AF', 'EUR_AF', 'SAS_AF', 'gnomADe_AF', 'gnomADe_AFR_AF', 'gnomADe_AMR_AF', 'gnomADe_ASJ_AF', 'gnomADe_EAS_AF', 'gnomADe_FIN_AF', 'gnomADe_NFE_AF', 'gnomADe_OTH_AF', 'gnomADe_SAS_AF', 'gnomADg_AF', 'gnomADg_AFR_AF', 'gnomADg_AMI_AF', 'gnomADg_AMR_AF', 'gnomADg_ASJ_AF', 'gnomADg_EAS_AF', 'gnomADg_FIN_AF', 'gnomADg_MID_AF', 'gnomADg_NFE_AF', 'gnomADg_OTH_AF', 'gnomADg_SAS_AF', 'MAX_AF', 'MAX_AF_POPS', 'FREQS', 'CLIN_SIG', 'SOMATIC', 'PHENO', 'PUBMED', 'MOTIF_NAME', 'MOTIF_POS', 'HIGH_INF_POS', 'MOTIF_SCORE_CHANGE', 'TRANSCRIPTION_FACTORS']

    # collect the CSQ fields (VEP annotations)
    info_str = info_str.split(";")[-1].split(",")

    # for each annotations, collect field information
    for n, annotation in enumerate(info_str):
        # split the fields
        for i, item in enumerate(annotation.split('|')):
            if vep_fields[i] in info_dict:
                info_dict[vep_fields[i]].append(item)
            else:
                info_dict[vep_fields[i]] = [item]

    return info_dict


def classify_specific_variant(info_dict, filter_status, min_freq, cgc_genes):
    """Classify the variant based on INFO and FILTER fields."""
    # impact of variant
    sift = ";".join(info_dict.get('SIFT'))
    polyphen = ";".join(info_dict.get('PolyPhen'))

    if 'deleterious' in sift.lower():
        pathogenic_variant = 'Likely damaging'
    elif 'damaging' in polyphen.lower():
        pathogenic_variant = 'Likely damaging'
    else:
        pathogenic_variant = 'Likely benign'


    # Population frequencies from INFO field including the 1000 Genomes Project (first 5 fields) and gnomAD (MAX_AF)
    pop_freq_keys = ['AFR_AF', 'AMR_AF', 'EAS_AF', 'EUR_AF', 'SAS_AF', 'MAX_AF']
    pop_freqs = [info_dict.get(key) for key in pop_freq_keys]

    final_pop_freqs = []
    # convert to floats and unpack the list of lists if the frequency is present
    for freq in pop_freqs:
        for f in freq:
            try:
                f = float(f)
                final_pop_freqs.append(f)
            except:
                pass

    # Consider a variant benign if it is germline
    if filter_status.lower() == 'germline':
        population_variant = 'Population'
    # Consider a variant benign if any population frequency is above a threshold (e.g., 1%)
    elif any(freq > min_freq for freq in final_pop_freqs):
        population_variant = 'Population'

    else:
        population_variant = 'Somatic'
    

    # classify the variant type
    variant_type = info_dict.get('VARIANT_CLASS')[0]

    if re.search('deletion|insertion', variant_type):
        variant_type = 'Indel'
    else:
        variant_type = 'SNV'


    # classify mutations
    mutation_type, spec_variant_type = classify_mutation_type(info_dict)

    # classify as cgc gene
    mask = np.isin(info_dict.get('SYMBOL'), cgc_genes)
    if np.any(mask):
        cgc_gene = ",".join(np.array(info_dict.get('SYMBOL'))[mask])
        is_cgc_gene = True
    else:
        cgc_gene = ''
        is_cgc_gene = False
    
    return [population_variant, pathogenic_variant, variant_type, mutation_type, spec_variant_type, is_cgc_gene, cgc_gene]


def load_df(args, cutoff = 0.1):
    file, cgc_genes = args

    # read the vcf file
    df = pd.read_csv(file, sep='\t', comment='#', compression='gzip', header=None)
    df.columns = ['CHROM', 'POS', 'ID', 'REF', 'ALT', 'QUAL', 'FILTER', 'INFO', 'FORMAT', 'BLOOD', 'TUMOR']

    # subset to unique variants
    variant_ids = df['CHROM'].astype(str) + '_' + df['POS'].astype(str) + '_' + df['REF'] + '_' + df['ALT']
    df['variant_id'] = variant_ids
    df = df.drop_duplicates(subset='variant_id')
    
    # classify the variants
    classifications = []

    # for each variant, extract the INFO field and classify the variant
    for _, row in df.iterrows():
        info_dict = extract_info_vep(row['INFO'])
        classification = classify_specific_variant(info_dict, row['FILTER'], cutoff, cgc_genes)
        classifications.append(classification)

    # add the classifications to the dataframe
    classifications = pd.DataFrame(classifications)
    new_cols = ['population_classification', 'pathogenic_classification', 'variant_type', 'mutation_type', 'specific_mutation_type', 'is_cgc_gene', 'cgc_gene']
    df.loc[:,new_cols] = classifications.values

    # add sample id
    df['sample'] = file.split("/")[-1].split("_")[0]
    
    return df


def expand_info_field(rows):
    res = []
    
    for row in rows:
        # Extract the INFO field from the row
        info_dict = extract_info_vep(row['INFO'])  # Access INFO using the row tuple

        # For each value in the info_dict, turn it into a CSV string
        for key, value in info_dict.items():
            info_dict[key] = ",".join(value)

        # Create a dictionary for expanded info
        expanded_info = {**row.to_dict(), **info_dict}  # Combine the original row with the expanded info
        res.append(expanded_info)

    return res  # Return the expanded row as a dictionary


# Function to extract the required aa/aa pair and position/position pair
def extract_hgvs(row):
    # Split the values based on comma delimiter and extract the relevant pair (assuming it's the non-empty)
    protein_positions = row['Protein_position'].split(',')
    amino_acids = row['Amino_acids'].split(',')

    for pos, aa in zip(protein_positions, amino_acids):
        if '/' in pos and '/' in aa:
            pos_parts = pos.split('/')
            aa_parts = aa.split('/')
            
            # Convert one-letter AA codes to three-letter and form the final string
            if len(aa_parts) == 2 and len(pos_parts) == 2:
                ref_aa = amino_acid_map.get(aa_parts[0], aa_parts[0])  # Convert reference amino acid
                alt_aa = amino_acid_map.get(aa_parts[1], aa_parts[1])  # Convert alternate amino acid
                return f'p.{ref_aa}{pos_parts[0]}{alt_aa}'
    return None


def generate_expanded_vcf(df, threads):
    # Convert DataFrame to list of tuples (or lists)
    params = [row for _, row in df.iterrows()]

    # Define a batch size
    batch_size = len(params) // threads + 1

    # Split parameters into batches
    batches = [params[i:i + batch_size] for i in range(0, len(params), batch_size)]

    # Multi-thread expand the INFO field
    with ProcessPoolExecutor(max_workers=threads) as executor:
        expanded_results = list(executor.map(expand_info_field, batches))

    # Concatenate results into a single DataFrame
    expanded_df = pd.DataFrame([item for sublist in expanded_results for item in sublist])

    # Apply the function to create the HGVSp_extracted column
    expanded_df['HGVSp_extracted'] = expanded_df.apply(extract_hgvs, axis=1)

    return expanded_df


def load_and_process_dfs(vcf_csv, cgc_file, expanded_vcf_file, threads):
    file_list = vcf_csv.split(",")

    # load cgc genes
    cgc_genes = pd.read_csv(cgc_file, sep='\t')
    cgc_genes = cgc_genes['GENE_SYMBOL'].to_numpy(dtype='<U64')
    
    params_list = [[file, cgc_genes] for file in file_list]

    # multi-thread calculate p-values and fc
    with ProcessPoolExecutor(max_workers=threads) as executor:
        res_df = list(executor.map(load_df, params_list))

    # collect converted dataframes
    res_df = pd.concat(res_df, ignore_index=True)

    # generate and save the expanded vcf file
    expanded_df = generate_expanded_vcf(res_df, threads)

    # save the expanded vcf file
    expanded_df.to_csv(expanded_vcf_file, sep='\t', index=False)

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()


    # process and combine all dfs
    res_df = load_and_process_dfs(vcf_csv, cgc_file, expanded_vcf_file, threads)

    # save to files
    res_df.to_csv(figure_data_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["vcf_csv=", "cgc_file=", "expanded_vcf_file=", "figure_data_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--vcf_csv"):
            vcf_csv = str(arg)
        if opt in ("--cgc_file"):
            cgc_file = str(arg)

        if opt in ("--expanded_vcf_file"):
            expanded_vcf_file = str(arg)
        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
            
        if opt in ("--threads"):
            threads = int(arg)

    main()




