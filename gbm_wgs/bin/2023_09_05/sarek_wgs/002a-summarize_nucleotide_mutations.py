# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import re

help_message = '''
Failed
'''



def load_and_process_file(maf_file, dna_translation):
    # Load the TPM file
    df = pd.read_csv(maf_file, sep='\t')

    # subset to columns of interest
    cols_oi = ['REF', 'ALT', 'sample']
    df = df.loc[:,cols_oi]

    # create a new column describing the nucleotide mutation
    mask = np.logical_and(np.isin(df['REF'], ['A', 'T', 'G', 'C']), np.isin(df['ALT'], ['A', 'T', 'G', 'C']))

    # subset to only single nucleotide mutations
    df = df[mask]
    
    # separate between purines and pyrimidines
    mask = np.isin(df['REF'], ['A', 'G'])

    # convert the reference and alternate alleles to the complementary nucleotides
    df.loc[mask, 'REF'] = df.loc[mask,'REF'].map(dna_translation)
    df.loc[mask, 'ALT'] = df.loc[mask,'ALT'].map(dna_translation)

    # create a new column describing the nucleotide mutation
    df['mutation'] = df['REF'] + '->' + df['ALT']

    print(np.unique(df['mutation'].astype('str')))

    # create a new df describing the number of each mutation per sample
    summary_df = df.groupby(['sample', 'mutation']).size().reset_index(name='count')

    # get the total number of each mutation type regardless of sample
    stat_df = df.groupby('mutation').size().reset_index(name='count')

    return summary_df, stat_df



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and process file
    summary_df, stats_df = load_and_process_file(maf_file, dna_translation)

    # save to files
    summary_df.to_csv(mutation_summary_file, sep='\t', index=False)
    stats_df.to_csv(mutation_summary_file_stats, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"
        
    dna_translation = {
        "A": "T",
        "T": "A",
        "C": "G",
        "G": "C"
    }


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["maf_file=", "mutation_summary_file=", "mutation_summary_file_stats="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--maf_file"):
            maf_file = str(arg)

        if opt in ("--mutation_summary_file"):
            mutation_summary_file = str(arg)
        if opt in ("--mutation_summary_file_stats"):
            mutation_summary_file_stats = str(arg)

    main()


