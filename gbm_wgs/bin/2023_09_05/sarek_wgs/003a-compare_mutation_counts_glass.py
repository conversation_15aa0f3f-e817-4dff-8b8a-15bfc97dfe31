# Alec Bahcheli

import sys, getopt, time, os

import pandas as pd 
import numpy as np

import scipy.stats as stats

help_message = '''
Failed
'''


def process_glass_mutation_summary_stats_file(file, clinical_file):
    # load the files
    df = pd.read_csv(file, sep='\t')
    clinical_df = pd.read_csv(clinical_file, sep='\t')

    # subset to WGS only
    df = df[df['tumor_pair_barcode'].str.contains('WGS')]

    # filter for glioblastoma and IDHwt only
    mask = clinical_df['histology'].str.contains('Glioblastoma', na=False) & clinical_df['idh_status'].str.contains('IDHwt', na=False)
    samples = clinical_df[mask]['case_barcode'].unique()

    df = df[df['case_barcode'].isin(samples)]

    # Create a new DataFrame for the expanded rows
    expanded_rows = pd.DataFrame()

    # Iterate over each row in the original DataFrame
    for _, row in df.iterrows():
        # Create the first row (for aliquot_barcode)
        row1 = row[['tumor_pair_barcode', 'case_barcode']].copy()
        row1['tumor_barcode'] = row['tumor_barcode_a']
        row1['aliquot_barcode'] = row['aliquot_barcode']
        row1['cumulative_coverage'] = row['cumulative_coverage']
        row1['mutation_count'] = row['mutation_count']
        row1['coverage_adj_mut_freq'] = row['coverage_adj_mut_freq']

        row1['sample'] = row['case_barcode']

        # Create the second row (for aliquot_barcode2)
        row2 = row[['tumor_pair_barcode', 'case_barcode']].copy()
        row2['tumor_barcode'] = row['tumor_barcode_b']
        row2['aliquot_barcode'] = row['aliquot_barcode2']
        row2['cumulative_coverage'] = row['cumulative_coverage2']
        row2['mutation_count'] = row['mutation_count2']
        row2['coverage_adj_mut_freq'] = row['coverage_adj_mut_freq2']

        row2['sample'] = row['case_barcode']

        # add details on tumor type
        row1['tumor_type'] = 'primary'
        row2['tumor_type'] = 'recurrent'

        # Append the new rows to the expanded DataFrame
        expanded_rows = pd.concat([expanded_rows, row1.to_frame().T], ignore_index=True)
        expanded_rows = pd.concat([expanded_rows, row2.to_frame().T], ignore_index=True)

    return expanded_rows


def process_rlgs_summary(file):
    # load the file
    df = pd.read_csv(file, sep='\t')

    # add tumor type
    df['tumor_type'] = df['sample'].str.split("-").str[1]

    # create a new df with "sample", "tumor_type" and "mutation_count" columns with the number of mutations per "sample"
    df = df.groupby(['sample', 'tumor_type']).size().reset_index(name='mutation_count')

    # add source
    df['source'] = 'rlgs'

    return df


def compare_mutation_counts(glass_df, rlgs_df):
    # set mutation_counts to numeric
    glass_df['mutation_count'] = pd.to_numeric(glass_df['mutation_count'])
    rlgs_df['mutation_count'] = pd.to_numeric(rlgs_df['mutation_count'])

    # perform u-test on the mutation counts in total
    all_u_test = stats.mannwhitneyu(glass_df['mutation_count'], rlgs_df['mutation_count'])[1]
    primary_u_test = stats.mannwhitneyu(glass_df[glass_df['tumor_type'] == 'primary']['mutation_count'], rlgs_df[rlgs_df['tumor_type'] == 'primary']['mutation_count'])[1]
    recurrent_u_test = stats.mannwhitneyu(glass_df[glass_df['tumor_type'] == 'recurrent']['mutation_count'], rlgs_df[rlgs_df['tumor_type'] == 'recurrent']['mutation_count'])[1]

    # perform t-test on the mutation counts in total
    all_t_test = stats.ttest_ind(glass_df['mutation_count'], rlgs_df['mutation_count'])[1]
    primary_t_test = stats.ttest_ind(glass_df[glass_df['tumor_type'] == 'primary']['mutation_count'], rlgs_df[rlgs_df['tumor_type'] == 'primary']['mutation_count'])[1]
    recurrent_t_test = stats.ttest_ind(glass_df[glass_df['tumor_type'] == 'recurrent']['mutation_count'], rlgs_df[rlgs_df['tumor_type'] == 'recurrent']['mutation_count'])[1]

    # create a df with the results, rounding all values to 4 significant figures
    stats_df = pd.DataFrame({
        'comparison': ['all', 'primary', 'recurrent'],
        'mannwhitneyu_p_value': [np.format_float_scientific(all_u_test, precision=2, unique=False), np.format_float_scientific(primary_u_test, precision=2, unique=False), np.format_float_scientific(recurrent_u_test, precision=2, unique=False)],
        'ttest_p_value': [np.format_float_scientific(all_t_test, precision=2, unique=False), np.format_float_scientific(primary_t_test, precision=2, unique=False), np.format_float_scientific(recurrent_t_test, precision=2, unique=False)]
    })

    # subset glass df to only include the columns of interest
    cols_oi = ['sample', 'tumor_type', 'mutation_count']
    glass_df = glass_df[cols_oi]

    # add source
    glass_df['source'] = 'glass_wgs'

    # add patient columns
    glass_df['patient'] = glass_df['sample']
    rlgs_df['patient'] = rlgs_df['sample'].str.split("-").str[0]

    # combine the two dfs
    df = pd.concat([glass_df, rlgs_df])

    return df, stats_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process glass file
    glass_df = process_glass_mutation_summary_stats_file(glass_mutation_summary_stats_file, glass_clinical_summary_stats_file)

    # process rlgs file
    rlgs_df = process_rlgs_summary(rlgs_mutation_summary_stats_file)

    # compare the two
    df, stats_df = compare_mutation_counts(glass_df, rlgs_df)

    # save to files
    df.to_csv(figure_data_file, sep='\t', index=False)
    stats_df.to_csv(figure_stats_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["glass_mutation_summary_stats_file=", "glass_clinical_summary_stats_file=", "rlgs_mutation_summary_stats_file=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--glass_mutation_summary_stats_file"):
            glass_mutation_summary_stats_file = str(arg)
        if opt in ("--glass_clinical_summary_stats_file"):
            glass_clinical_summary_stats_file = str(arg)
        if opt in ("--rlgs_mutation_summary_stats_file"):
            rlgs_mutation_summary_stats_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
            
    main()

