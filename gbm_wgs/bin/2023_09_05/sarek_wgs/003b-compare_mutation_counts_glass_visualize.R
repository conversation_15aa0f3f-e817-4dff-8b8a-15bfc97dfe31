# Alec <PERSON>i - <EMAIL>

library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}





boxplot = function(type, input_df, stats_df){

if (type == 'all'){
    df = input_df
} else {
    df = input_df[input_df$tumor_type == type,]
}

utest_pval = stats_df[stats_df$comparison == type,'mannwhitneyu_p_value']
ttest_pval = stats_df[stats_df$comparison == type,'ttest_p_value']

# density polygons
p = ggplot(df, aes(x = source, y = log10(mutation_count))) + plot_theme() +
geom_boxplot(outlier.shape = NA, colour = 'black') + 
geom_jitter(size = 3, alpha = 0.6) + 

ylim(2.5, 5.8) +

ggtitle(paste0(type, ', u-test p=', utest_pval, ' t-test p=', ttest_pval)) +
xlab('') + ylab('Mutation count (log10)')
# theme(axis.text.x = element_text(angle = 0))

print(p)

return()
}






pdf(opt$figure_file, height=7, width=3)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')


# plot all tumor types
boxplot('all', input_df, stats_df)

# plot primary and recurrent tumors
lapply(unique(input_df$tumor_type), boxplot, input_df, stats_df)


dev.off()


print(opt$figure_file)




