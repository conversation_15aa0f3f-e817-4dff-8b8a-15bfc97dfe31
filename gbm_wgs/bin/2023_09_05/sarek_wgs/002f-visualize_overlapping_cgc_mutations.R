# Alec <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}






subset_df = function(input_df){
# Sort the DataFrame by fraction mutated
input_df <- input_df %>% arrange(desc(fraction))

# Identify the rows with our mutations
our_mutation_rows <- input_df[input_df$our_mutation == 'True', ]

# If the number of our mutations is greater than or equal to 20, keep only those
if (nrow(our_mutation_rows) >= 20) {
selected_rows <- our_mutation_rows[1:20, ]
} else {
# Combine our mutation rows with the top 20 excluding the lowest one if necessary
top_rows <- head(input_df, 20)

# Remove the lowest row in the top rows if it’s not part of our mutations
if (!any(top_rows$AA_Pos %in% our_mutation_rows$AA_Pos)) {
# Get the row with the minimum fraction among the top rows
min_row <- top_rows[which.min(top_rows$fraction), ]
top_rows <- top_rows[!top_rows$AA_Pos %in% min_row$AA_Pos, ]
}

# Combine our mutation rows and top rows
selected_rows <- rbind(our_mutation_rows, top_rows)

# Ensure we have only 20 rows
selected_rows <- selected_rows[1:min(nrow(selected_rows), 20), ]
}

# subset to selected rows
input_df = input_df[input_df$AA_Pos %in% selected_rows$AA_Pos,]

# sort factor by fraction
input_df$AA_Pos = fct_reorder(input_df$AA_Pos, input_df$fraction, .fun = mean, .desc = TRUE)

return(input_df)
}



barplot = function(gene, input_df){

# subset to gene
input_df = input_df[input_df$GENE_SYMBOL == gene,]

# subset to top 20
input_df = subset_df(input_df)

# create plot
p = ggplot(input_df, aes(x = AA_Pos, y = counts, fill = our_mutation)) + plot_theme() +
geom_bar(stat = "identity") +
ggtitle(paste0('Mutated loci in ', gene)) +
xlab("Amino acid") + ylab('# of COSMIC patients') +

# Add labels above bars for True mutations
geom_text(data = input_df[input_df$our_mutation == 'True', ], 
        aes(label = num_samples), 
        vjust = -0.5,  # Adjust vertical position
        size = 4,      # Adjust text size
        color = "black")  # Text color

    
print(p)

return()
} 




pdf(opt$figure_file)

# load file
input_df = read.csv(opt$figure_data_file, sep='\t')

# add a percentage column
input_df$percentage = input_df$fraction * 100


# create summary plot
lapply(unique(input_df$GENE_SYMBOL), barplot, input_df)


dev.off()


print(opt$figure_file)

