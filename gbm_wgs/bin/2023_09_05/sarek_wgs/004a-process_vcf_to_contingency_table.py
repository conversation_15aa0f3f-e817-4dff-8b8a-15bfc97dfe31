# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np



help_message = '''
Failed
'''


def process_snvs_to_contingency(combined_vcf_file, gene_translation_file):
    # load dfs
    df = pd.read_csv(combined_vcf_file, sep="\t")
    translation_df = pd.read_csv(gene_translation_file, sep="\t")

    ## NOTE remove RLGS8
    df = df[~df['sample'].str.contains('RLGS8')]
    
    # subset to columns of interest
    df = df[['Gene', 'specific_mutation_type', 'sample']]

    # subset to non-intronic and non-intergenic; drop NA genes 
    df = df[~df['specific_mutation_type'].isin(['intergenic_variant', 'intron_variant'])]
    df = df.dropna(subset=['Gene'])
    

    # remove rows with no gene name
    translation_df = translation_df.dropna(subset=['Gene name'])
    
    # create a dictionary with gene names as keys and the corresponding gene names as values
    gene_dict = dict(zip(translation_df['Gene stable ID'], translation_df['Gene name']))

    # map the gene names to the gene stable IDs
    df['gene_id'] = df['Gene'].apply(lambda x: gene_dict.get(x, x))

    
    
    # Group by 'gene' and 'sample' and count occurrences
    classification_df = df.groupby(['gene_id', 'sample']).size().unstack(fill_value=0)

    # Convert count to binary: 1 if paired at least once, 0 if not at all
    classification_df = (classification_df > 0).astype(int)

    # add gene column
    classification_df['gene'] = classification_df.index.to_numpy()

    # Melt the DataFrame so that each sample type (primary/recurrent) is a row
    melted_df = classification_df.melt(id_vars=["gene"], var_name="sample", value_name="mutation_count")
    
    # Extract mutation type ('primary' or 'recurrent') from the column names
    melted_df['mutation_type'] = melted_df['sample'].str.split("-").str[1]
    
    # Group by gene and mutation_type to get the total mutation count
    mutation_counts = melted_df.groupby(['gene', 'mutation_type'])['mutation_count'].sum().reset_index()

    pivoted_df = mutation_counts.pivot(index='gene', columns='mutation_type', values='mutation_count')

    return pivoted_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load data and process into contingency table
    df = process_snvs_to_contingency(combined_vcf_file, gene_translation_file)

    # save the contingency table
    df.to_csv(snv_contingency_file, sep="\t")

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["combined_vcf_file=", "gene_translation_file=", "snv_contingency_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--combined_vcf_file"):
            combined_vcf_file = str(arg)
        if opt in ("--gene_translation_file"):
            gene_translation_file = str(arg)

        if opt in ("--snv_contingency_file"):
            snv_contingency_file = str(arg)
            

    main()



