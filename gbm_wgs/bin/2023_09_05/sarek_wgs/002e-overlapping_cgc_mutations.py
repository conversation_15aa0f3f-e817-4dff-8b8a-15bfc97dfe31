# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def summarize_snvs_amino_acids(cosmic_snv_file, maf_summary_file, genes_of_interest):
    # load dfs
    df = pd.read_csv(cosmic_snv_file, sep='\t', compression='gzip')
    rlgs_df = pd.read_csv(maf_summary_file, sep='\t')

    # get unique genes
    our_genes = rlgs_df['Hugo_Symbol'].unique()
    genes_of_interest = list(set(genes_of_interest) & set(our_genes))

    # subset to genes of interest
    df = df[df['GENE_SYMBOL'].isin(genes_of_interest)]

    # Filter the DataFrame for confirmed somatic variants
    somatic_df = df[df['MUTATION_SOMATIC_STATUS'] == 'Confirmed somatic variant']
    
    
    # Extract the relevant columns
    somatic_summary = somatic_df[['GENE_SYMBOL', 'MUTATION_AA']]
    
    # Extract the leading amino acid and position from the MUTATION_AA column
    somatic_summary[['Amino_Acid', 'Amino_Acid_Position']] = somatic_summary['MUTATION_AA'].str.extract(r'([A-Z])(\d+)')

    # Count the occurrences of mutations per gene and amino acid position
    summary_counts = somatic_summary.groupby(['GENE_SYMBOL', 'Amino_Acid', 'Amino_Acid_Position']).size().reset_index(name='counts')

    # for each gene, generate a fraction per amino acid position
    summary_counts['fraction'] = summary_counts.groupby('GENE_SYMBOL')['counts'].apply(lambda x: x / float(x.sum())).reset_index(drop=True)

    # round to 3 significant figures
    summary_counts['fraction'] = summary_counts['fraction'].apply(lambda x: round(x, 3 - int(np.floor(np.log10(abs(x))))) if x != 0 else 0)


    # for each gene, add a column with if the amino acid position overlaps with the RLGS
    rlgs_df['Protein_position'] = rlgs_df['Protein_position'].fillna(0).astype('int')

    summary_counts['our_mutation'] = summary_counts.apply(lambda x: int(x['Amino_Acid_Position']) in rlgs_df[rlgs_df['Hugo_Symbol'] == x['GENE_SYMBOL']]['Protein_position'].values, axis=1)

    # add a column of amino acid and position together
    summary_counts['AA_Pos'] = summary_counts['Amino_Acid'] + summary_counts['Amino_Acid_Position'].astype(str)

    # Convert Amino_Acid_Position to int
    summary_counts['Amino_Acid_Position'] = summary_counts['Amino_Acid_Position'].astype(int)

    # Count the number of samples for each gene and position
    num_samples_df = rlgs_df.groupby(['Hugo_Symbol', 'Protein_position']).size().reset_index(name='num_samples')

    # Merge the num_samples back into summary_counts
    summary_counts = summary_counts.merge(num_samples_df, how='left', left_on=['GENE_SYMBOL', 'Amino_Acid_Position'], right_on=['Hugo_Symbol', 'Protein_position'])

    # Fill NaN values in num_samples with 0 if no samples found
    summary_counts['num_samples'] = summary_counts['num_samples'].fillna(0).astype(int)

    return summary_counts


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # generate maf format df
    df = summarize_snvs_amino_acids(cosmic_snv_file, maf_summary_file, genes_of_interest)

    # save to files
    df.to_csv(cosmic_stats_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    # genes_of_interest = ['TP53', 'IDH1', 'PTEN', 'ATRX', 'EGFR', 'NF1', 'TERT', 'PIK3CA', 'CIC', 'RB1', 'PIK3R1', 'FUBP1']
    genes_of_interest = ['EGFR', 'ATRX', 'PTEN', 'CDK6', 'MET', 'CDKN2A', 'RB1', 'MDM4', 'CDK4', 'TP53', 'PDGFRA', 'FUBP1', 'NF1', 'MDM2', 'MYCN', 'PIK3CA', 'PIK3R1', 'CCND2', 'CIC', 'IDH1', 'TERT']

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["cosmic_snv_file=", "maf_summary_file=", "cosmic_stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--cosmic_snv_file"):
            cosmic_snv_file = str(arg)
        if opt in ("--maf_summary_file"):
            maf_summary_file = str(arg)

        if opt in ("--cosmic_stats_file"):
            cosmic_stats_file = str(arg)

    main()




