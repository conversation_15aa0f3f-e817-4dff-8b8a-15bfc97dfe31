# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)
library(tidyr)
library(ComplexHeatmap)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



oncoprint_plot = function(input_df, small=FALSE){

# create matrix for oncoprint
df <- input_df %>% mutate(mutation_info = paste(specific_mutation_type))

mutation_matrix <- df %>%
  group_by(cgc_gene, sample) %>%
  summarise(mutation_info = paste(unique(mutation_info), collapse = ";")) %>%
  pivot_wider(names_from = sample, values_from = mutation_info, values_fill = list(mutation_info = ""))


print(head(mutation_matrix))
    
mutation_matrix <- as.data.frame(mutation_matrix)
rownames(mutation_matrix) <- mutation_matrix$cgc_gene
mutation_matrix$cgc_gene <- NULL
mutation_matrix <- as.matrix(mutation_matrix)

print(head(mutation_matrix))

# # Calculate mutation frequency for each gene
# gene_mutation_frequency <- rowSums(mutation_matrix != "") / ncol(mutation_matrix)


# Define the color palette
col = c(
  "non_coding_transcript_exon_variant" = "#F7599D", 
  "synonymous_variant" = "#10efb1", 
  "missense_variant" = "#BA6D45", 
  "stop_gain" = "#ed1219", 
  "inframe_in" = "#B047B8", 
  "inframe_indel" = "#B047B8", 
  "Indel" = "#f2960d", 
  "frameshift" = "#D5C92A",
  "non_coding" = "#FFD700",        
  "frameshift_variant" = "#f2960d", 
  "stop_gained" = "#ed1219",       
  "start_lost" = "#32CD32",        
  "splice_acceptor_variant" = "#FF69B4",
  "inframe_deletion" = "#10efb1",  
  "splice_donor_variant" = "#7B68EE", 
  "stop_lost" = "#FF8C00",         
  "inframe_insertion" = "#8B4513", 
  "intron_variant" = "#8A2BE2",                  
  "intergenic_variant" = "#FF4500",             
  "regulatory_region_variant" = "#20B2AA",     
  "upstream_gene_variant" = "#FF6347",         
  "downstream_gene_variant" = "#D2691E",       
  "TF_binding_site_variant" = "#FFDAB9",       
  "five_prime_UTR_variant" = "#B22222",            
  "three_prime_UTR_variant" = "#6A5ACD",           
  "splice_donor_region_variant" = "#FFC0CB",    
  "splice_region_variant" = "#F0E68C",         
  "splice_polypyrimidine_tract_variant" = "#6495ED", 
  "stop_retained_variant" = "#00FA9A",         
  "mature_miRNA_variant" = "#FF69B4"
)

# Define the alter_fun list
alter_fun = list(
  non_coding_transcript_exon_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.9, 
                                        gp = gpar(fill = col["non_coding_transcript_exon_variant"], col = NA)),
  synonymous_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.9, 
                                               gp = gpar(fill = col["synonymous"], col = NA)),
  missense_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.9, 
                                                     gp = gpar(fill = col["missense_variant"], col = NA)),
  stop_gain = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.9, 
                                              gp = gpar(fill = col["stop_gain"], col = NA)),
  inframe_in = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.9, 
                                               gp = gpar(fill = col["inframe_in"], col = NA)),
  inframe_indel = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.9, 
                                               gp = gpar(fill = col["inframe_indel"], col = NA)),
  Indel = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                          gp = gpar(fill = col["Indel"], col = NA)),
  frameshift = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                               gp = gpar(fill = col["frameshift"], col = NA)),
  # New variants added
  non_coding = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                               gp = gpar(fill = col["non_coding"], col = NA)),
  frameshift_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                       gp = gpar(fill = col["frameshift_variant"], col = NA)),
  stop_gained = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                gp = gpar(fill = col["stop_gained"], col = NA)),
  start_lost = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                               gp = gpar(fill = col["start_lost"], col = NA)),
  splice_acceptor_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                            gp = gpar(fill = col["splice_acceptor_variant"], col = NA)),
  inframe_deletion = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                      gp = gpar(fill = col["inframe_deletion"], col = NA)),
  splice_donor_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                         gp = gpar(fill = col["splice_donor_variant"], col = NA)),
  stop_lost = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                              gp = gpar(fill = col["stop_lost"], col = NA)),
  inframe_insertion = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                      gp = gpar(fill = col["inframe_insertion"], col = NA)),
  intron_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                   gp = gpar(fill = col["intron_variant"], col = NA)),
  intergenic_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                       gp = gpar(fill = col["intergenic_variant"], col = NA)),
  regulatory_region_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                              gp = gpar(fill = col["regulatory_region_variant"], col = NA)),
  upstream_gene_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                          gp = gpar(fill = col["upstream_gene_variant"], col = NA)),
  downstream_gene_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                            gp = gpar(fill = col["downstream_gene_variant"], col = NA)),
  TF_binding_site_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                             gp = gpar(fill = col["TF_binding_site_variant"], col = NA)),
  five_prime_UTR_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                           gp = gpar(fill = col["five_prime_UTR_variant"], col = NA)),
  three_prime_UTR_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                             gp = gpar(fill = col["three_prime_UTR_variant"], col = NA)),
  splice_donor_region_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                                 gp = gpar(fill = col["splice_donor_region_variant"], col = NA)),
  splice_region_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                            gp = gpar(fill = col["splice_region_variant"], col = NA)),
  splice_polypyrimidine_tract_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                                        gp = gpar(fill = col["splice_polypyrimidine_tract_variant"], col = NA)),
  stop_retained_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                           gp = gpar(fill = col["stop_retained_variant"], col = NA)),
  mature_miRNA_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                          gp = gpar(fill = col["mature_miRNA_variant"], col = NA))
)


# Sort rows (genes) by the total number of mutations
row_order <- order(rowSums(mutation_matrix != ""), decreasing = TRUE)

# Sort columns (samples) by the total number of mutations
column_order <- order(colSums(mutation_matrix != ""), decreasing = TRUE)



row_size = 5
if (small){
    row_size = 0.5
}
    

# Generate the OncoPrint with column names
print(oncoPrint(mutation_matrix, 
          alter_fun = alter_fun, 
          col = col,
          row_order = row_order,
          row_names_gp = grid::gpar(fontsize = row_size),  # Adjust the fontsize for row names

          column_order = column_order,
          column_title = "Mutation OncoPrint",
          column_names_gp = grid::gpar(fontsize = 10),  # Adjust the fontsize for column names
          column_names_rot = 45,  # Rotate column names for better readability
                
          heatmap_legend_param = list(title = "Mutations", 
                                      at = names(col), 
                                      labels = names(col)),
          alter_fun_is_vectorized = FALSE,  # Set this to FALSE
            show_column_names = TRUE
))

return()

}




pdf(opt$figure_file)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')

# subset to somatic, non-synonymous variants
# input_df = input_df[input_df$mutation_type != 'Non-impacting',]
mutations_of_interest = c('missense_variant', 'synonymous_variant', 'frameshift_variant', 'stop_gained', 'start_lost', 'inframe_deletion', 'inframe_insertion', 'splice_donor_region_variant', 'splice_acceptor_variant', 'splice_region_variant', 'stop_lost')
input_df = input_df[input_df$specific_mutation_type %in% mutations_of_interest,]


# subset to cgc genes
df = input_df[input_df$is_cgc_gene == 'True',]

# create oncoprint
oncoprint_plot(df, small=TRUE)

# repeat for select genes
genes_oi = c('TP53', 'IDH1', 'PTEN', 'ATRX', 'EGFR', 'NF1', 'TERT', 'PIK3CA', 'CIC', 'RB1', 'PIK3R1', 'FUBP1')

df = input_df[input_df$cgc_gene %in% genes_oi,]

# create oncoprint
oncoprint_plot(df)


dev.off()


print(opt$figure_file)





