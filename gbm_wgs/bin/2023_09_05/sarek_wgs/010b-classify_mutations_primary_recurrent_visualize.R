# Alec <PERSON>

library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}





stacked_barplot = function(input_df){

# plot
p = ggplot(input_df, aes(x = patient_id, y = value, fill = variable)) + plot_theme() +
geom_bar(stat = "identity", position = "stack") +

# Set colors for the bars
scale_fill_manual(values = c("primary_fraction" = "#df7b20", "recurrent_fraction" = "#229a32", "shared_fraction" = "#b98acd")) +

ggtitle('') +
xlab('') + ylab('Proportion of mutations')

print(p)

return()
}




sort_samples = function(df){
# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# order samples
sorted_vector <- unique(df$patient_id)[order(sapply(unique(df$patient_id), extract_numeric))]
df$patient_id = factor(df$patient_id, levels = sorted_vector)

return(df)
}



pdf(opt$figure_file)

# load df
input_df = read.csv(opt$figure_stats_file, sep='\t')

# subset to fractions
sub_df = input_df[input_df$variable == 'total_mutations',]

# reorder by total mutations
sub_df$patient_id = fct_reorder(sub_df$patient_id, sub_df$value, .fun = mean, .desc = TRUE)

# subset to fractions
input_df = input_df[input_df$variable_type == 'fraction',]

# set factors
input_df$patient_id = factor(input_df$patient_id, levels = levels(sub_df$patient_id))

# plot
stacked_barplot(input_df)




# repeat after sorting by name
input_df = sort_samples(input_df)

stacked_barplot(input_df)



dev.off()


print(opt$figure_file)




