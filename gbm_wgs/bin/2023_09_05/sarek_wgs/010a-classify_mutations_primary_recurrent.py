# Alec <PERSON>cheli - <EMAIL>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import scipy.stats as stats

help_message = '''
Failed
'''


def process_rlgs_summary(file):
    # load the file
    df = pd.read_csv(file, sep='\t')

    # add tumor type and sample id
    df['tumor_type'] = df['sample'].str.split("-").str[1]
    df['patient_id'] = df['sample'].str.split("-").str[0]

    # create a mutation_id column containing chrom, pos, ref, alt
    df['mutation_id'] = df['CHROM'].astype(str) + '-' + df['POS'].astype(str) + '-' + df['REF'] + '-' + df['ALT']

    # add a column for mutation classification
    df['mutation_recurrence_classification'] = 'shared'

    # create a list to store patient dfs
    res_patient_df = []
    stats_df = []

    # for each patient compare primary and recurrent mutations
    for patient in df['patient_id'].unique():
        # get primary and recurrent dfs
        primary_df = df[(df['patient_id'] == patient) & (df['sample'].str.contains('primary'))].copy()
        recurrent_df = df[(df['patient_id'] == patient) & (df['sample'].str.contains('recurrent'))].copy()

        # determine which mutations are shared between primary and recurrent
        common_mutations = primary_df['mutation_id'][primary_df['mutation_id'].isin(recurrent_df['mutation_id'])]

        # classify mutations in primary_df as primary_only or shared
        primary_df['mutation_recurrence_classification'] = 'primary'
        primary_df.loc[primary_df['mutation_id'].isin(common_mutations), 'mutation_recurrence_classification'] = 'shared'

        # classify mutations in recurrent_df as recurrent_only or shared
        recurrent_df['mutation_recurrence_classification'] = 'recurrent'
        recurrent_df.loc[recurrent_df['mutation_id'].isin(common_mutations), 'mutation_recurrence_classification'] = 'shared'

        # create a new df with primary_df and recurrent mutations in recurrent_df
        patient_df = pd.concat([primary_df, recurrent_df[recurrent_df['mutation_recurrence_classification'] == 'recurrent']], ignore_index=True)

        # calculate number of mutations in just primary, just recurrent, and both
        primary_mutations = len(primary_df[primary_df['mutation_recurrence_classification'] == 'primary'])
        recurrent_mutations = len(recurrent_df[recurrent_df['mutation_recurrence_classification'] == 'recurrent'])
        shared_mutations = len(common_mutations)

        total_mutations = primary_mutations + recurrent_mutations + shared_mutations

        # calculate fractions
        primary_fraction = primary_mutations / total_mutations
        recurrent_fraction = recurrent_mutations / total_mutations
        shared_fraction = shared_mutations / total_mutations

        # add to res_patient_df
        res_patient_df.append(patient_df)

        # add to stats_df
        stats_df.append([patient, total_mutations, primary_mutations, recurrent_mutations, shared_mutations, primary_fraction, recurrent_fraction, shared_fraction])

    # concat res_patient_df
    res_patient_df = pd.concat(res_patient_df, ignore_index=True)

    # create stats_df
    stats_df = pd.DataFrame(stats_df, columns=['patient_id', 'total_mutations', 'primary_mutations', 'recurrent_mutations', 'shared_mutations', 'primary_fraction', 'recurrent_fraction', 'shared_fraction'])

    # melt stats_df
    stats_df = stats_df.melt(id_vars=['patient_id'], var_name='variable', value_name='value')

    # classify variable as fraction or total
    stats_df['variable_type'] = 'total'
    stats_df.loc[stats_df['variable'].str.contains('fraction'), 'variable_type'] = 'fraction'

    return res_patient_df, stats_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process rlgs file
    df, stats_df = process_rlgs_summary(rlgs_mutation_snv_file)

    # save to files
    df.to_csv(figure_data_file, sep='\t', index=False)
    stats_df.to_csv(figure_stats_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["rlgs_mutation_snv_file=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--rlgs_mutation_snv_file"):
            rlgs_mutation_snv_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
            
    main()


