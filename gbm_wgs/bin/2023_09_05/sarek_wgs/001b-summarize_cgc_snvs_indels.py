# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

help_message = '''
Failed
'''



def summarize_variants(vcf_expanded_file, ensembl_gff3_file):
    # read dfs
    df = pd.read_csv(vcf_expanded_file, sep='\t')

    gff_df = pd.read_csv(ensembl_gff3_file, sep='\t', header=None, comment="#")
    gff_df.columns = ['chromosome', 'source', 'type', 'start', 'end', 'score', 'strand', 'phase', 'attribute']


    # the enriched mutations sites for our samples are likely: chr5:1295135 G>A, chr5:1295113 G>A
    # we want the "info" column to contain both "TERT" and "promoter"
    mask = df['INFO'].str.contains('TERT', na=False) & df['INFO'].str.contains('promoter', na=False)


    # subset to columns of interest
    df = df[['CHROM', 'POS', 'ID', 'REF', 'ALT', 'variant_id', 'population_classification', 'pathogenic_classification', 'variant_type', 'mutation_type', 'specific_mutation_type', 'sample', 'Gene', 'Feature_type', 'Feature', 'BIOTYPE', 'EXON', 'INTRON']]
    
    # rename columns and change column order
    df.columns = ['chromosome', 'position', 'id', 'ref', 'alt', 'variant_id', 'population_classification', 'pathogenic_classification', 'variant_type', 'mutation_type', 'detailed_mutation_type', 'sample', 'ensembl_gene', 'feature_type', 'feature_id', 'gene_type', 'exon', 'intron']
    df = df[['chromosome', 'position', 'id', 'ref', 'alt', 'variant_id', 'population_classification', 'pathogenic_classification', 'variant_type', 'mutation_type', 'detailed_mutation_type', 'feature_type', 'feature_id', 'gene_type', 'exon', 'intron', 'ensembl_gene', 'sample']]

    # subset to "gene" type
    gff_df = gff_df[gff_df['type'].str.contains('gene')]

    # create dictionary to map gene names to gene ids
    gene_id_dict = dict(zip(gff_df['attribute'].str.extract(r'ID=gene:([^;]+)')[0], gff_df['attribute'].str.extract(r'Name=([^;]+)')[0]))

    # add gene id from the dictionary
    df['gene'] = df['ensembl_gene'].map(gene_id_dict)


    # add gene name as TERTp and modify other columns to meet cutoffs
    df.loc[mask, 'gene'] = 'TERTp'
    df.loc[mask, 'detailed_mutation_type'] = 'missense_variant'

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # list all files of interest
    df = summarize_variants(vcf_expanded_file, ensembl_gff3_file)

    # save to file
    df.to_csv(vcf_translated_subset_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["vcf_expanded_file=", "ensembl_gff3_file=", "vcf_translated_subset_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--vcf_expanded_file"):
            vcf_expanded_file = str(arg)
        if opt in ("--ensembl_gff3_file"):
            ensembl_gff3_file = str(arg)

        if opt in ("--vcf_translated_subset_file"):
            vcf_translated_subset_file = str(arg)

    main()


