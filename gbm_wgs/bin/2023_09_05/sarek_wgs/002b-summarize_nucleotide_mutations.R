# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)
library(ggrastr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




barplot = function(df){

# density polygons
p = ggplot(df, aes(x = mutation, y = count, fill = sample)) + plot_theme() +
geom_bar(stat = 'identity', color='black') +

# scale_fill_manual(values = c("#E41A1C", "#377EB8", "#4DAF4A", "#FF7F00", "#984EA3", "#FFFF33")) +

ggtitle('') +
xlab('Mutation class') + ylab('Number of mutations') +
theme(axis.text.x = element_text(angle = 0))

print(p)

return()
}



barplot_stats = function(df){
    
# density polygons
p = ggplot(df, aes(x = mutation, y = count, fill = mutation)) + plot_theme() +
geom_bar(stat = 'identity', color='black') +

scale_fill_manual(values = c("#E41A1C", "#377EB8", "#4DAF4A", "#FF7F00", "#984EA3", "#FFFF33")) +

ggtitle('') +
xlab('Mutation class') + ylab('Number of mutations') +
theme(axis.text.x = element_text(angle = 0))

print(p)

return()
}


pdf(opt$figure_file)

# load df
df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')

# sort mutations by count
df$mutation = fct_reorder(df$mutation, df$count, .desc=TRUE, .fun=sum)
stats_df$mutation = fct_reorder(stats_df$mutation, stats_df$count, .desc=TRUE)

# apply to all patients
barplot_stats(stats_df)
barplot(df)

dev.off()


print(opt$figure_file)




