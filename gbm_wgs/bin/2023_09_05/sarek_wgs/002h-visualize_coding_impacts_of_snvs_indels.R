# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)

library(grid)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}


fill_cols = c('Missense_Mutation'='#34A048', 
        'Frame_Shift_Del'='#2078B4', 
        'Nonsense_Mutation'='#E21F26',
        'In_Frame_Del'='#FBF49C', 
        'Frame_Shift_Ins'='#6B3E98',
        'In_Frame_Ins'='#D43E4F',
        'SNV'='#BEBAD9', 
        'DEL'='#2078B4', 
        'INS'='#6B3E98',
        'T>G'='#F8981D',
        'T>A'='#4CAF4E',
        'T>C'='#FEC111',
        'C>T'='#478FCC',
        'C>A'='#6B3E98',
        'C>G'='#4555A5')


barplot = function(count_type, input_df){

# subset to type of count
sub_df = input_df[input_df$data_type == count_type,]

    
# plot
p = ggplot(sub_df, aes(x = protein_impact, y = count, fill = protein_impact)) + plot_theme() +
geom_bar(stat = 'identity') +

# Add count labels above each bar
geom_text(aes(label = count), vjust = -0.5, size = 4) +  # Adjust the position of the labels


scale_fill_manual(values = fill_cols) +

ggtitle(count_type) +
xlab('') + ylab('Mutation count') +

theme(legend.position = 'none')

print(p)

return()
}




pdf(opt$figure_file)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')

# sort by protein_impact increasing count
input_df$protein_impact = fct_reorder(input_df$protein_impact, input_df$count, mean, .desc=TRUE)

# plot boxplots for each mutation
lapply(unique(input_df$data_type), barplot, input_df)


dev.off()


print(opt$figure_file)




