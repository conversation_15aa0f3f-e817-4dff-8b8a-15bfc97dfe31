# <PERSON>
library(optparse)
library(dplyr)
library(maftools)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




mafplot = function(maf_object){

# create summary plot
plotmafSummary(maf = maf_object, rmOutlier = TRUE, addStat = 'median', dashboard = TRUE, titvRaw = FALSE)

sample_order = c('RLGS1-primary', 'RLGS1-recurrent', 'RLGS2-primary', 'RLGS2-recurrent', 'RLGS3-primary', 'RLGS3-recurrent', 'RLGS4-primary', 'RLGS4-recurrent', 'RLGS5-primary', 'RLGS5-recurrent', 'RLGS6-primary', 'RLGS6-recurrent', 'RLGS7-primary', 'RLGS7-recurrent', 'RLGS8-primary', 'RLGS8-recurrent', 'RLGS9-primary', 'RLGS9-recurrent', 'RLGS10-primary', 'RLGS10-recurrent', 'RLGS11-primary', 'RLGS11-recurrent', 'RLGS12-primary', 'RLGS12-recurrent')
    
# create another summary oncoplot
oncoplot(maf = maf_object, top = 20, showTumorSampleBarcodes = TRUE, sampleOrder=sample_order)

return()
} 



lolliplot_maf = function(gene, df, maf_object){

# subset to the gene of interest
sub_df = df[df$Hugo_Symbol == gene,]

# extract transcript ID
transcript = sub_df$Transcript_version[1]

positions_to_label = sub_df$Protein_position

# Plot with updated HGVSp_Short containing the ref_aa and alt_aa
lollipopPlot(
maf_object,  # MAF object
gene = gene,  # Specify gene of interest
# refSeqID = transcript,  # Use a transcript based on hg38 (if this is aligned with hg38)
AACol = "HGVSp_Short",  # Updated amino acid position column with ref_aa > alt_aa
showMutationRate = TRUE,  # Optionally show mutation rate
labelPos = positions_to_label  # Show position labels
)

print(gene)

return()
} 





pdf(opt$figure_file)

# load files
input_df = read.csv(opt$figure_data_file, sep='\t')


# Read the MAF file with the corrected classifications
maf_object <- read.maf(
  maf = input_df,
  vc_nonSyn = c("Missense_Mutation", "Nonsense_Mutation", "Frame_Shift_Ins", "Frame_Shift_Del", "Splice_Site", "In_Frame_Ins", "In_Frame_Del", "Splice_Region", "5'UTR", "3'UTR", "Intron", "RNA")
)


# create summary plots
mafplot(maf_object)


# subset to genes of interest
mutation_genes <- c('TP53', 'IDH1', 'PTEN', 'ATRX', 'EGFR', 'NF1', 'TERT', 'PIK3CA', 'CIC', 'RB1', 'PIK3R1', 'FUBP1')
cna_genes <- c('CDKN2A', 'EGFR', 'PDGFRA', 'CDK4', 'CDK6', 'PTEN', 'MDM2', 'MET', 'CCND2', 'MYCN', 'MDM4', 'ATRX')
genes_of_interest <- c(mutation_genes, cna_genes)

input_df = input_df[input_df$Hugo_Symbol %in% genes_of_interest,]

# create lollipop plot
lapply(unique(input_df$Hugo_Symbol), lolliplot_maf, input_df, maf_object)


dev.off()


print(opt$figure_file)






# lolliplot_maf = function(gene, df){

# # subset to the gene of interest
# sub_df = df[df$Hugo_Symbol == gene,]

# # extract transcript ID
# transcript = sub_df$Transcript_version[1]

# # Correct regular expressions to extract reference and alternate amino acids
# sub_df$ref_aa <- gsub("p\\.([A-Z])[a-z]{2}[0-9]+.*", "\\1", sub_df$HGVSp_Short)  # Extract reference amino acid
# sub_df$alt_aa <- gsub(".*[0-9]+([A-Z])$", "\\1", sub_df$HGVSp_Short)            # Extract alternate amino acid

# # Create a label showing ref_aa > alt_aa
# sub_df$HGVSp_Short <- paste0(sub_df$ref_aa, ">", sub_df$alt_aa)
  

# print(gene)
# print(transcript)
# print(sub_df)


# # Plot with updated HGVSp_Short containing the ref_aa and alt_aa
# lollipopPlot(
# data = sub_df,
# gene = gene,  # Specify gene of interest
# refSeqID = transcript,  # Use a transcript based on hg38 (if this is aligned with hg38)
# # AACol = "HGVSp_Short",  # Updated amino acid position column with ref_aa > alt_aa
# showMutationRate = TRUE,  # Optionally show mutation rate
# labelPos = TRUE  # Show position labels
# )

# return()
# } 
