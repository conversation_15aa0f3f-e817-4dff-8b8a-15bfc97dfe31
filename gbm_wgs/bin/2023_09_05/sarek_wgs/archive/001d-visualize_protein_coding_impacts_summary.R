# Alec <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




barplots = function(variant_type, input_df){


input_df$sample = fct_reorder(as.factor(input_df$sample), input_df$counts, sum, .desc=TRUE)

# all combined
p = ggplot(input_df, aes(x = sample, y = log10(counts), fill = mutation_type)) + plot_theme() +
geom_bar(stat = "identity", position = position_dodge(width = 0.8)) +  # position_dodge for side-by-side bars

ggtitle(variant_type) +
xlab("Sample") + ylab('Number of variants (filtered, log10)') +
theme(axis.text.x = element_text(size = 6))


print(p)


# SNVs
sub_df = input_df[input_df$mutation_details %in% c('synonymous', 'missense', 'stop_gain'),]

sub_df$sample = fct_reorder(as.factor(sub_df$sample), sub_df$counts, sum, .desc=TRUE)

p = ggplot(sub_df, aes(x = sample, y = log10(counts), fill = mutation_details)) + plot_theme() +
geom_bar(stat = "identity", position = position_dodge(width = 0.8)) +  # position_dodge for side-by-side bars

ggtitle(paste0(variant_type, " SNVs")) +
xlab("Sample") + ylab('Number of variants (filtered, log10)') +
theme(axis.text.x = element_text(size = 6))


print(p)


input_df = input_df[input_df$variant_type == 'Indel',]
# indels
sub_df = input_df[input_df$mutation_details == 'frameshift',]

sub_df$sample = fct_reorder(as.factor(sub_df$sample), sub_df$counts, sum, .desc=TRUE)

p = ggplot(sub_df, aes(x = sample, y = counts)) + plot_theme() +
geom_bar(stat = "identity") + 
    
ggtitle(paste0(variant_type, " frameshift Indels")) +
xlab("Sample") + ylab('Number of variants (filtered)') +
theme(axis.text.x = element_text(size = 10))


print(p)


# indels
sub_df = input_df[input_df$mutation_details == 'inframe_in',]

sub_df$sample = fct_reorder(as.factor(sub_df$sample), sub_df$counts, sum, .desc=TRUE)

p = ggplot(sub_df, aes(x = sample, y = counts)) + plot_theme() +
geom_bar(stat = "identity") +  
    
ggtitle(paste0(variant_type, " inframe Indels")) +
xlab("Sample") + ylab('Number of variants (filtered)') +
theme(axis.text.x = element_text(size = 10))


print(p)




return()
}





pdf(opt$figure_file, width = 10)

# load df
df = read.csv(opt$figure_data_file, sep='\t')

df$mutation_details = factor(df$mutation_details, levels = rev(c('synonymous', 'missense', 'stop_gain', 'inframe_in', 'frameshift')))

# create plots
barplots('All variants', df)

# subset to Somatic variants
df = df[df$population_classification == 'Somatic',]

# create plots
barplots('Somatic variants', df)

dev.off()


print(opt$figure_file)




