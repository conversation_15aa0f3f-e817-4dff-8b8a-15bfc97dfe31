# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)
library(tidyr)
library(ComplexHeatmap)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



oncoprint_plot = function(df, desired_row_order, small=FALSE){

# create matrix for oncoprint
mutation_matrix <- df %>%
  group_by(gene, sample) %>%
  summarise(mutation_info = paste(unique(detailed_mutation_type), collapse = ";")) %>%
  pivot_wider(names_from = sample, values_from = mutation_info, values_fill = list(mutation_info = ""))


# Convert to a matrix and set row names
mutation_matrix <- as.data.frame(mutation_matrix)
rownames(mutation_matrix) <- mutation_matrix$gene
mutation_matrix$gene <- NULL
mutation_matrix <- as.matrix(mutation_matrix)


# Define the color palette
col = c(
  "synonymous_variant" = "#10efb1", 
  "missense_variant" = "#BA6D45", 
  "frameshift_variant" = "#f2960d", 
  "stop_gained" = "#ed1219", 
  "start_lost" = "#32CD32", 
  "inframe_deletion" = "#10efb1",  
  "inframe_insertion" = "#8B4513", 
  "splice_donor_region_variant" = "#FFC0CB",    
  "splice_acceptor_variant" = "#FF69B4",
  "splice_region_variant" = "#F0E68C",         
  "stop_lost" = "#FF8C00"
)

# Define the alter_fun list
alter_fun = list(
  synonymous_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.9, 
                                                       gp = gpar(fill = col["synonymous_variant"], col = NA)),
  missense_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.9, 
                                                     gp = gpar(fill = col["missense_variant"], col = NA)),
  frameshift_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                       gp = gpar(fill = col["frameshift_variant"], col = NA)),
  stop_gained = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                               gp = gpar(fill = col["stop_gained"], col = NA)),
  start_lost = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                              gp = gpar(fill = col["start_lost"], col = NA)),
  inframe_deletion = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                   gp = gpar(fill = col["inframe_deletion"], col = NA)),
  inframe_insertion = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                     gp = gpar(fill = col["inframe_insertion"], col = NA)),
  splice_donor_region_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                               gp = gpar(fill = col["splice_donor_region_variant"], col = NA)),
  splice_acceptor_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                           gp = gpar(fill = col["splice_acceptor_variant"], col = NA)),
  splice_region_variant = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                                          gp = gpar(fill = col["splice_region_variant"], col = NA)),
  stop_lost = function(x, y, w, h) grid.rect(x, y, w*0.9, h*0.4, 
                                             gp = gpar(fill = col["stop_lost"], col = NA))
)


# Sort rows (genes) by the total number of mutations
# row_order <- order(rowSums(mutation_matrix != ""), decreasing = TRUE)
row_order = match(desired_row_order, rownames(mutation_matrix))

# Sort columns (samples) by the total number of mutations
# column_order <- order(colSums(mutation_matrix != ""), decreasing = TRUE)


# Define the specific column order
desired_order <- c('RLGS1-primary', 'RLGS1-recurrent', 'RLGS2-primary', 'RLGS2-recurrent',
                   'RLGS3-primary', 'RLGS3-recurrent', 'RLGS4-primary', 'RLGS4-recurrent',
                   'RLGS5-primary', 'RLGS5-recurrent', 'RLGS6-primary', 'RLGS6-recurrent',
                   'RLGS7-primary', 'RLGS7-recurrent', 'RLGS8-primary', 'RLGS8-recurrent',
                   'RLGS9-primary', 'RLGS9-recurrent', 'RLGS10-primary', 'RLGS10-recurrent',
                   'RLGS11-primary', 'RLGS11-recurrent', 'RLGS12-primary', 'RLGS12-recurrent',
                   'null_13')

# Reorder columns based on the desired order
column_order <- match(desired_order, colnames(mutation_matrix))


row_size = ifelse(small, 0.5, 10)


# Generate the OncoPrint with column names
print(oncoPrint(mutation_matrix, 
          alter_fun = alter_fun, 
          col = col,
          row_order = row_order,
          row_names_gp = grid::gpar(fontsize = row_size),  # Adjust the fontsize for row names

          column_order = column_order,
          column_title = "CGC SNV / Indel OncoPrint",
          column_names_gp = grid::gpar(fontsize = 10),  # Adjust the fontsize for column names
          column_names_rot = 45,  # Rotate column names for better readability
                
          heatmap_legend_param = list(title = "Mutations", 
                                      at = names(col), 
                                      labels = names(col)),
          alter_fun_is_vectorized = FALSE,  # Set this to FALSE
            show_column_names = TRUE,

))

return()

}




pdf(opt$figure_file)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')

all_genes = c('EGFR', 'ATRX', 'PTEN', 'CDK6', 'MET', 'CDKN2A', 'RB1', 'MDM4', 'CDK4', 'TP53', 'PDGFRA', 'FUBP1', 'NF1', 'MDM2', 'MYCN', 'PIK3CA', 'PIK3R1', 'CCND2', 'CIC', 'IDH1', 'TERTp', 'none')

# create oncoprint
oncoprint_plot(input_df, all_genes)


dev.off()


print(opt$figure_file)





