# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''

def mutations_per_gene(combined_vcf_file, pcg_bed_file, gene_translation_file):
    # load df
    df = pd.read_csv(combined_vcf_file, sep="\t")

    ## NOTE remove RLGS8
    df = df[~df['sample'].str.contains('RLGS8')]

    # split the "Gene" column into unique rows for each gene separated by ","
    df = df.assign(Gene=df['Gene'].str.split(',')).explode('Gene')

    # add a column for the tumor type and gene
    df['gene_tumor'] = df['Gene'] + '_' + df['sample'].str.split('-').str[1]

    # Create the full set of possible gene_tumor pairs (all combinations of gene and 'primary'/'recurrent')
    all_gene_tumor_pairs = {f"{x}_{y}" for x in df['Gene'].unique() for y in ['primary', 'recurrent']}

    # calculate total mutations for primary and recurrent tumors
    total_mutations_primary = df[df['sample'].str.contains('primary')].shape[0]
    total_mutations_recurrent = df[df['sample'].str.contains('recurrent')].shape[0]

    # calculate number of times each gene appears in the df under col "gene"
    df = df.groupby('gene_tumor').size().reset_index(name='mutations')

    
    
    # Get the unique gene_tumor pairs already in the dataframe
    gene_tumor_pairs = set(df['gene_tumor'].unique())
    
    # Determine the missing gene_tumor pairs using set difference (faster than list comprehension)
    missing_gene_tumor_pairs = all_gene_tumor_pairs - gene_tumor_pairs

    # add missing gene_tumor pairs to df
    missing_df = pd.DataFrame(missing_gene_tumor_pairs, columns=['gene_tumor'])
    missing_df['mutations'] = 0
    df = pd.concat([df, missing_df])

    # rename columns and add more
    df.columns = ['gene_tumor', 'mutations']
    df['gene'] = df['gene_tumor'].str.split('_').str[0]
    df['tumor'] = df['gene_tumor'].str.split('_').str[1]
    
    # drop rows with no gene name
    mask = df['gene'] != ''
    df = df[mask]
    
    
    
    # load gene translation file
    translation_df = pd.read_csv(gene_translation_file, sep="\t")

    # remove rows with no gene name
    translation_df = translation_df.dropna(subset=['Gene name'])

    # create a dictionary with gene names as keys and the corresponding gene names as values
    gene_dict = dict(zip(translation_df['Gene stable ID'], translation_df['Gene name']))

    # map the gene names to the gene stable IDs
    df['gene'] = df['gene'].apply(lambda x: gene_dict.get(x, x))


    # load pcg bed file
    pcg_df = pd.read_csv(pcg_bed_file, sep="\t", header=None)

    # subset to genes in the pcg bed file
    mask = df['gene'].isin(pcg_df[3])
    df = df[mask]


    # add total mutations to df
    df.loc['total_mutations_primary',:] = ['total_mutations_primary', total_mutations_primary, '', 'primary']
    df.loc['total_mutations_recurrent',:] = ['total_mutations_recurrent', total_mutations_recurrent, '', 'recurrent']

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and process file
    df = mutations_per_gene(combined_vcf_file, pcg_bed_file, gene_translation_file)

    # save stats table
    df.to_csv(mutations_per_gene_file, sep="\t", index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["combined_vcf_file=", "pcg_bed_file=", "gene_translation_file=", "mutations_per_gene_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:        
        if opt in ("--combined_vcf_file"):
            combined_vcf_file = str(arg)
        if opt in ("--pcg_bed_file"):
            pcg_bed_file = str(arg)
        if opt in ("--gene_translation_file"):
            gene_translation_file = str(arg)

        if opt in ("--mutations_per_gene_file"):
            mutations_per_gene_file = str(arg)


    main()



