# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

help_message = '''
Failed
'''


nucleotide_mutation_dict = {
    'G>A': 'C>T',
    'G>C': 'C>G',
    'G>T': 'C>A',
    'A>G': 'T>C',
    'A>C': 'T>G',
    'A>T': 'T>A',
    'C>T': 'C>T',
    'C>G': 'C>G',
    'C>A': 'C>A',
    'T>C': 'T>C',
    'T>G': 'T>G',
    'T>A': 'T>A'
    # # special cases
    # 'TC>AA': 'T>A',
    # 'CG>TA': 'C>T',
    # 'CT>TG': 'C>T',
}


def subset_df(df):
    # subset df
    df = df[df['Variant_Type'] == 'SNP']

    # translate SNVs to be consistent
    df['snv_class'] = df['Reference_Allele'] + '>' + df['Tumor_Seq_Allele2']

    # if ther are 2 nucleotides, split into 2 lines
    mask = df['snv_class'].str.split('>').str[0].str.len() == 2
    df2 = df[mask].copy()
    df3 = df[mask].copy()

    # split into 2 lines
    df2['snv_class'] = df2['Reference_Allele'].str[0] + '>' + df2['Tumor_Seq_Allele2'].str[0]
    df3['snv_class'] = df2['Reference_Allele'].str[1] + '>' + df2['Tumor_Seq_Allele2'].str[1]

    # combine
    df = pd.concat([df[~mask], df2, df3])

    # only contain the 6 possible classes
    df['snv_class'] = df['snv_class'].map(nucleotide_mutation_dict)

    return df


def summarize_maf_df(maf_summary_file):
    # load df
    df = pd.read_csv(maf_summary_file, sep='\t')

    print(df.shape)

    # remove hypermutated samples RLGS8 and RLGS11-recurrent
    df = df[~df['Tumor_Sample_Barcode'].isin(['RLGS8-primary', 'RLGS8-recurrent', 'RLGS11-recurrent'])]

    print(df.shape)

    # create a new df that classifies major variant classes
    stats_df = df['Variant_Classification'].value_counts().reset_index()
    stats_df.columns = ['protein_impact', 'count']

    # describe this data type
    stats_df['data_type'] = 'variant_class'

    # repeat for variant type
    stats_df2 = df['Variant_Type'].value_counts().reset_index()
    stats_df2.columns = ['protein_impact', 'count']
    stats_df2['data_type'] = 'variant_type'

    # subset df
    df = subset_df(df)

    # repeat for SNV class    
    stats_df3 = df['snv_class'].value_counts().reset_index()
    stats_df3.columns = ['protein_impact', 'count']
    stats_df3['data_type'] = 'snv_class'

    # combine all
    stats_df = pd.concat([stats_df, stats_df2, stats_df3])

    # replace SNP with SNV
    stats_df['protein_impact'] = stats_df['protein_impact'].str.replace('SNP', 'SNV')

    return stats_df

def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # summarize maf df
    df = summarize_maf_df(maf_summary_file)

    # save to files
    df.to_csv(protein_impact_summary_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["maf_summary_file=", "protein_impact_summary_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--maf_summary_file"):
            maf_summary_file = str(arg)

        if opt in ("--protein_impact_summary_file"):
            protein_impact_summary_file = str(arg)

    main()




