# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import scipy.stats as stats

help_message = '''
Failed
'''

def calculate_stats(df, type_of_counts):
    stats_df = []
    res_df = []

    # iterate through mutation types
    for mutation in df['mutation'].unique():
        # Extract primary and recurrent samples for C->T mutations, drop index
        df_ct = df[df['mutation'] == mutation].reset_index(drop=True)

        # Split into primary and recurrent samples
        df_ct['tumor_type'] = df_ct['sample'].str.split('-').str[1]
        df_ct['sample'] = df_ct['sample'].str.split('-').str[0]

        # Pivot to align primary-recurrent pairs
        df_ct_pivot = df_ct.pivot(index='sample', columns='tumor_type', values='count')

        # Fill missing values with 0
        df_ct_pivot = df_ct_pivot.fillna(0)

        # Extract primary and recurrent mutation counts
        primary_counts = df_ct_pivot['primary']
        recurrent_counts = df_ct_pivot['recurrent']

        # Perform paired t-test
        t_pvalue = stats.ttest_rel(primary_counts, recurrent_counts)[1]

        # Perform Wilcoxon signed-rank test
        try:
            w_pvalue = stats.wilcoxon(primary_counts, recurrent_counts)[1]
        except ValueError as e:
            w_pvalue = None  # Handle case where all differences are zero

        # Calculate median fold change
        fold_change = np.median(recurrent_counts / primary_counts)

        # add mutation type
        df_ct_pivot['mutation'] = mutation

        # append to results
        stats_df.append([mutation, t_pvalue, w_pvalue, fold_change])
        res_df.append(df_ct_pivot)

    # create results df
    stats_df = pd.DataFrame(stats_df, columns = ['mutation', 't_pvalue', 'w_pvalue', 'fold_change'])
    res_df = pd.concat(res_df)

    # melt results df
    res_df = res_df.melt(id_vars='mutation', var_name='tumor_type', value_name='mutation_count')

    # add type of counts
    res_df['type_of_counts'] = type_of_counts
    stats_df['type_of_counts'] = type_of_counts

    res_df['mutation_count_type'] = res_df['mutation'] + res_df['type_of_counts']
    stats_df['mutation_count_type'] = stats_df['mutation'] + stats_df['type_of_counts']

    return res_df, stats_df


def signif(p, n_sig = 2):
    # return significant figures
    return str(round(p, n_sig - int(np.floor(np.log10(abs(p)))) - 1))


def calculate_c_t_mutations(mutation_summary_file):
    # load df
    df = pd.read_csv(mutation_summary_file, sep='\t')

    # remove hypoermutated sample
    df = df[~df['sample'].str.contains('RLGS8')]

    # create a new df for total mutations per sample
    mutations_per_sample = df.groupby('sample')['count'].sum().reset_index()

    # set df index
    df.index = df['sample']
    mutations_per_sample.index = mutations_per_sample['sample']

    # for both total number of mutations and fraction of mutations, calculate stats
    res_df, stats_df = calculate_stats(df, 'total')

    # calculate fraction of mutations per sample
    df['count'] = df['count'] / mutations_per_sample['count']
    res_df_fraction, stats_df_fraction = calculate_stats(df, 'fraction')

    # combine res and stats dfs
    res_df = pd.concat([res_df, res_df_fraction])
    stats_df = pd.concat([stats_df, stats_df_fraction])

    # add a column to the res_df that is the mutation plus stats_df w_pvalue for that mutation type
    res_df = res_df.merge(stats_df[['mutation_count_type', 'w_pvalue']], on='mutation_count_type')
    res_df['mutation_pval'] = res_df['mutation'] + ', p=' + res_df['w_pvalue'].apply(lambda x: str(signif(x)))

    return res_df, stats_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load data
    res_df, stats_df = calculate_c_t_mutations(mutation_summary_file)

    # save to files
    stats_df.to_csv(nucleotide_paired_mutation_stats_file, sep='\t', index=False)
    res_df.to_csv(nucleotide_paired_mutation_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["mutation_summary_file=", "nucleotide_paired_mutation_file=", "nucleotide_paired_mutation_stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--mutation_summary_file"):
            mutation_summary_file = str(arg)

        if opt in ("--nucleotide_paired_mutation_file"):
            nucleotide_paired_mutation_file = str(arg)
        if opt in ("--nucleotide_paired_mutation_stats_file"):
            nucleotide_paired_mutation_stats_file = str(arg)
            

    main()





