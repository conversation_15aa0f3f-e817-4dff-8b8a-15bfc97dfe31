# Alec <PERSON>i - <EMAIL>

library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)
library(tidytext)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}





paired_dotplot = function(input_df){

    
# Step 1: Reorder patients by mutation burden within each source
input_df <- input_df %>%
  mutate(patient = reorder_within(patient, mutations_per_mb, source, fun = max, .desc = TRUE)) %>%
  
  # Step 2: Reverse the factor order
  mutate(patient = fct_rev(patient))
    
# plot
p = ggplot(input_df, aes(x = patient, y = log10(mutations_per_mb), fill = tumor_type)) + plot_theme() +
geom_point(pch=21, size=3) +

# Dashed horizontal line at y = 10
geom_hline(yintercept = log10(10), linetype = "dashed", color = "black", linewidth = 1) + 

# Set colors for the points
scale_fill_manual(values = c("primary" = "#df7b20", "recurrent" = "#229a32")) +

# Facet by the source column
facet_grid(~source, scales = "free_x", space = 'free_x') +

# Use reordered x-axis
scale_x_reordered() +

ggtitle('') +
xlab('') + ylab('Mutation Burden (Mutations / Mb, log10)')

print(p)

return()
}






pdf(opt$figure_file, width=16)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')

# calculate mutations per mb
input_df$mutations_per_mb = as.numeric(input_df$mutation_count) / 3200


# plot all tumor types
paired_dotplot(input_df)


dev.off()


print(opt$figure_file)




