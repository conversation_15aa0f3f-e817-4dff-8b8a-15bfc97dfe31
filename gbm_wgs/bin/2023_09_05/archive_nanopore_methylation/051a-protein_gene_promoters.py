# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def calculate_promoters(protein_coding_genes_gff_file, upstream_length=2000, downstream_length=500):
    # read gff file
    df = pd.read_csv(protein_coding_genes_gff_file, sep='\t', header=None)

    # add column names
    column_names = ['seqname', 'source', 'type', 'start', 'end', 'score', 'strand', 'phase', 'attributes']
    df.columns = column_names

    # add columns
    df['chrom'] = 'chr' + df['seqname'].str.split(".").str[0].str.split("_").str[1].str.replace(r'^0+', '', regex=True)
    df['gene'] = df['attributes'].str.extract(r'gene=([^;]+)')

    # calculate promoter regions
    df['promoter_start'] = df['start'] - upstream_length
    df['promoter_end'] = df['start'] + downstream_length

    # make sure there is no negative value
    mask = df['promoter_start'] < 0
    df.loc[mask,'promoter_start'] = 0

    # filter columns
    df = df[['chrom', 'promoter_start', 'promoter_end', 'gene']]
    df.columns = ['chrom', 'start', 'end', 'gene']

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # calculate promoter regions from gff
    promoter_df = calculate_promoters(protein_coding_genes_gff_file)

    # save to file
    promoter_df.to_csv(protein_coding_promoter_bed_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["protein_coding_genes_gff_file=", "protein_coding_promoter_bed_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--protein_coding_genes_gff_file"):
            protein_coding_genes_gff_file = str(arg)

        if opt in ("--protein_coding_promoter_bed_file"):
            protein_coding_promoter_bed_file = str(arg)
            
    main()




