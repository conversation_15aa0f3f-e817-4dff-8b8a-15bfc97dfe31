# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import umap
from sklearn.decomposition import PCA

from sklearn.preprocessing import StandardScaler
import subprocess

help_message = '''
Failed
'''


def run_umap(scaled_data, df):
    # 2. Apply UMAP for dimensionality reduction
    umap_reducer = umap.UMAP(n_components=2, random_state=42)
    umap_data = umap_reducer.fit_transform(scaled_data)

    # 3. Create a new df with the UMAP data
    umap_df = pd.DataFrame(umap_data, columns=['dim1', 'dim2'])

    # add description
    umap_df['source'] = 'umap'

    # add primary or recurrent status
    umap_df['sample'] = df.index
    umap_df['status'] = umap_df['sample'].apply(lambda x: x.split('_')[1])

    # add patient id 
    umap_df['patient_id'] = umap_df['sample'].str.split('_').str[0]

    return umap_df


def run_pca(scaled_data, df):
    # 2. Apply PCA for dimensionality reduction
    pca = PCA(n_components=2)
    pca_data = pca.fit_transform(scaled_data)

    # 3. Create a new df with the PCA data
    pca_df = pd.DataFrame(pca_data, columns=['dim1', 'dim2'])
    
    # add description
    pca_df['source'] = 'pca'

    # add primary or recurrent status
    pca_df['sample'] = df.index
    pca_df['status'] = pca_df['sample'].apply(lambda x: x.split('_')[1])

    # add patient id 
    pca_df['patient_id'] = pca_df['sample'].str.split('_').str[0]

    return pca_df


def cluster_mean_promoter_methylation(mean_methylation_file):
    # load raw methylation df
    df = pd.read_csv(mean_methylation_file, sep='\t')

    # pivot the df to have the samples as rows and gene mean_mehtylation as columns
    df = df.pivot(index='sample', columns='chrom_bin', values='percent_modified')

    # median impute by columns
    df = df.fillna(df.median())

    # 1. Standardize the data
    scaler = StandardScaler()
    scaled_data = scaler.fit_transform(df)

    # apply UMAP for dimensionality reduction
    res_df = run_umap(scaled_data, df)

    # apply PCA for dimensionality reduction
    pca_df = run_pca(scaled_data, df)

    # combine the results
    res_df = pd.concat([res_df, pca_df])

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load raw methylation df
    umap_df = cluster_mean_promoter_methylation(mean_methylation_file)

    # save results file
    umap_df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["mean_methylation_file=", "cgc_file=", "r_script=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--mean_methylation_file"):
            mean_methylation_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)


    main()




