# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''



def calculate_overlap(start1, end1, start2, end2):
    """Calculate the maximum size of the promoter regions."""
    # Determine the start and end of the overlap
    start_overlap = min(start1, start2)
    end_overlap = max(end1, end2)
    
    # Calculate overlap length
    overlap = max(0, end_overlap - start_overlap)

    # Determine min start and max end
    min_start = min(start1, start2)
    max_end = max(end1, end2)
    
    return overlap, min_start, max_end


def process_promoter_overlaps(intersected_bed_file):
    # Read the intersected bed file
    df = pd.read_csv(intersected_bed_file, sep='\t', header=None)
    df.columns = ['chr', 'start', 'end', 'type', 'gene', 'attributes', 'chr2', 'start2', 'end2', 'type2', 'gene2', 'attributes2']

    # Apply the function to each row in the DataFrame
    df[['overlap', 'min_start', 'max_end']] = df.apply(
        lambda row: pd.Series(calculate_overlap(row['start'], row['end'], row['start2'], row['end2'])),
        axis=1
    )

    # subset to essential columns
    df = df[['chr', 'min_start', 'max_end', 'gene', 'gene2', 'overlap', 'attributes', 'attributes2']]
    df.columns = ['chr', 'start', 'end', 'gene', 'gene2', 'overlap', 'attributes_1', 'attributes_2']

    # remove duplicate paired genes
    # Create a new column with normalized pairs (sets)
    paired_series = df.apply(lambda row: frozenset([row['gene'], row['gene2']]), axis=1)
    
    # Find duplicated pairs
    duplicates = paired_series.duplicated(keep='first')

    # Filter DataFrame to keep only rows with unique pairs
    df = df[~duplicates]

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # process the promoter overlaps
    df = process_promoter_overlaps(intersected_bed_file)

    # save to file
    df.to_csv(common_promoter_file, sep='\t', index=False, header=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["intersected_bed_file=", "common_promoter_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--intersected_bed_file"):
            intersected_bed_file = str(arg)

        if opt in ("--common_promoter_file"):
            common_promoter_file = str(arg)
            
    main()




