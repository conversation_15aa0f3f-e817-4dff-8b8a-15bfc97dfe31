library(optparse)
library(forcats)
library(ggplot2)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




histogram = function(stats_df){

# all combined
p = ggplot(stats_df, aes(x = t_pval)) + plot_theme() +
geom_histogram() +

coord_cartesian(xlim = c(0, 1)) +  
    
ggtitle('Paired t-test P-value') +
xlab("P-values") + ylab('N genes')

print(p)

    
# all combined
p = ggplot(stats_df, aes(x = t_fdr)) + plot_theme() +
geom_histogram() +

coord_cartesian(xlim = c(0, 1)) +  

ggtitle('Paired t-test FDR') +
xlab("FDR") + ylab('N genes')

print(p)



# all combined
p = ggplot(stats_df, aes(x = u_pval)) + plot_theme() +
geom_histogram() +

coord_cartesian(xlim = c(0, 1)) +  

ggtitle('U-test P-value') +
xlab("P-values") + ylab('N genes')

print(p)

# all combined
p = ggplot(stats_df, aes(x = u_fdr)) + plot_theme() +
geom_histogram() +

coord_cartesian(xlim = c(0, 1)) +  

ggtitle('U-test FDR') +
xlab("FDR") + ylab('N genes')

print(p)


}






boxplot = function(gene, raw_input_df, raw_stats_df){

# subset to gene
input_df = raw_input_df[raw_input_df$gene == gene,]
stats_df = raw_stats_df[raw_stats_df$gene == gene,]

new_title = paste0(gene, ": T-test fdr=", signif(sub_stats_df$t_fdr[1], 3), "U-test fdr=", signif(sub_stats_df$u_fdr[1], 3))

# all combined
p = ggplot(input_df, aes(x = tumor, y = mean_methylation)) + plot_theme() +
geom_boxplot() +
geom_jitter(size = 3, alpha = 0.6) + 

ggtitle(new_title) +
xlab("") + ylab('Mean methylation') +

theme(plot.title = element_text(size = 16))


print(p)
}





barplot = function(gene, raw_input_df, raw_stats_df){

# subset to gene
input_df = raw_input_df[raw_input_df$gene == gene,]
stats_df = raw_stats_df[raw_stats_df$gene == gene,]

new_title = paste0(gene, ": T-test fdr=", signif(sub_stats_df$t_fdr[1], 3), "U-test fdr=", signif(sub_stats_df$u_fdr[1], 3))

# all combined
p = ggplot(input_df, aes(x = sample_id, y = mean_methylation, fill = tumor)) + plot_theme() +
geom_bar(stat = "identity", position = position_dodge(width = 0.5)) +

ggtitle(new_title) +
xlab("") + ylab('Mean methylation') +

theme(plot.title = element_text(size = 16))



print(p)
}



# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))



pdf(opt$figure_file)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')


histogram(stats_df)


# # significant genes
# sig_genes = stats_df$gene[stats_df$t_sig < 0.05 | stats_df$u_sig < 0.05]
# print(head(stats_df))
# print(length(sig_genes))

# # sort by significance
# stats_df$gene = fct_reorder(stats_df$gene, stats_df$t_sig, .desc=TRUE)

# # subset to only significant genes
# sub_stats_df = stats_df[stats_df$gene %in% sig_genes,]


# # factor levels
# input_df$tumor = factor(input_df$tumor, levels = c("primary", "recurrent"))

# # order samples
# sorted_vector <- unique(input_df$sample_id)[order(sapply(unique(input_df$sample_id), extract_numeric))]
# input_df$sample_id = factor(input_df$sample_id, levels = sorted_vector)



# genes_oi = levels(sub_stats_df$gene)[1:50]

# # plot
# lapply(genes_oi, boxplot, input_df, stats_df)
# lapply(genes_oi, barplot, input_df, stats_df)


dev.off()


print(opt$figure_file)






