library(optparse)
library(forcats)
library(ggplot2)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}





# histogram for all samples combined
histogram = function(input_df){

# plot
p = ggplot(input_df, aes(x = mean_methylation)) + plot_theme() +
geom_histogram(bins = 100) +

# geom_density(color = "black", linewidth = 1) + 

ggtitle('Combined mean promoter methylation') +
xlab('Mean promoter methylation') + ylab('Number of genes (protein-coding)')


print(p)

return()
}


# plot histogram for each samples
tumor_class_histogram = function(class, input_df){

sub_df = input_df[input_df$tumor == class,]
    
# plot
p = ggplot(sub_df, aes(x = mean_methylation)) + plot_theme() +
geom_histogram(bins = 100) +

ggtitle(paste0(class, ' tumors')) +
xlab('Mean promoter methylation') + ylab('Number of genes (protein-coding)')


print(p)

return()
}



# plot boxplots for each sample
combined_boxplots = function(input_df, stats_df){

pval = signif(stats_df$fdr[stats_df$sample == 'combined'], 3)

# plot
p = ggplot(input_df, aes(x = tumor, y = mean_methylation)) + plot_theme() +
geom_boxplot() +

ggtitle(paste0("All patients, FDR=", pval)) +
xlab('') + ylab('Mean promoter methylation')


print(p)

return()
}


# plot boxplots for each sample
individual_boxplots = function(sample, input_df, stats_df){

sub_df = input_df[input_df$sample_id == sample,]
pval = signif(stats_df$fdr[stats_df$sample == sample], 3)

# plot
p = ggplot(sub_df, aes(x = tumor, y = mean_methylation)) + plot_theme() +
geom_boxplot() +

ggtitle(paste0(sample, ", FDR=", pval)) +
xlab('') + ylab('Mean promoter methylation')


print(p)

return()
}


# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))



pdf(opt$figure_file)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')


print(head(input_df))

# order samples
sorted_vector <- unique(input_df$sample_id)[order(sapply(unique(input_df$sample_id), extract_numeric))]
input_df$sample_id = factor(input_df$sample_id, levels = sorted_vector)

print(head(stats_df))


# plot all samples together
histogram(input_df)

# plot primary and recurrent separately
lapply(unique(input_df$tumor), tumor_class_histogram, input_df)

# plot boxplots of combined
combined_boxplots(input_df, stats_df)

# plot each sample individually
lapply(levels(input_df$sample_id), individual_boxplots, input_df, stats_df)


dev.off()


print(opt$figure_file)






