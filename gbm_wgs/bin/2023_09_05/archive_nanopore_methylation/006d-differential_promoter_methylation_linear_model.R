# Alec <PERSON>
library(optparse)
library(parallel)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file"),
    make_option(c("-d","--threads"), type="character", default=NULL,
            help="",
            dest="threads")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



# Function to run the linear regression
run_lm <- function(gene, df) {
    data <- df[df$gene == gene, ]
    model <- lm(methylation ~ patient, data = data)

    coef_table <- coef(summary(model))
    this_pval <- coef_table["(Intercept)", "Pr(>|t|)"]
    this_coef <- coef_table["(Intercept)", "Estimate"]

    return(list(gene = gene, pval = this_pval, coef = this_coef))
}

# Load data
input_df <- read.csv(opt$figure_data_file, sep = '\t')

# Seed
set.seed(42)

# mclapply
results <- mclapply(unique(input_df$gene), function(i) {
    run_lm(i, input_df)
}, mc.cores = as.numeric(opt$threads))

# Convert results to a dataframe
results_df <- do.call(rbind, lapply(results, as.data.frame))

# Optional: Rename columns if needed
colnames(results_df) <- c('gene', 'p_value', 'coefficient')

# Save the results
write.table(results_df, file = opt$figure_stats_file, sep = '\t', row.names = FALSE, quote = FALSE)


