# Load necessary libraries
library(optparse)
library(forcats)
library(ggplot2)
library(dplyr)
library(reshape2)
library(ggrastr)

# Options list for parser options
option_list <- list(
    make_option(c("-a", "--figure_data_file"), type = "character", default = NULL, help = "", dest = "figure_data_file"),
    make_option(c("-b", "--figure_stats_file"), type = "character", default = NULL, help = "", dest = "figure_stats_file"),
    make_option(c("-c", "--figure_file"), type = "character", default = NULL, help = "", dest = "figure_file")
)

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list = option_list)

# Parse the arguments
opt <- parse_args(parser)

# Define plot theme
plot_theme <- function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12, angle = 90, hjust = 1, vjust = 0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



# Define the heatmap function
heatmap <- function(input_df) {
    p = ggplot(input_df, aes(x = bin, y = sample)) + plot_theme() +
        rasterize(geom_tile(aes(fill = percent_modified)), dpi = 350) +
        scale_fill_gradient2(low = "white", mid = "#F4E555", high = "#4E269F", midpoint = 50, limits = c(0, 100)) +
        facet_grid(. ~ chrom, scales = "free_x", space = "free_x") +
        ggtitle('') +
        xlab("Bases (x10 kbp)") + ylab('Sample') +
        guides(fill = guide_colourbar(ticks.colour = "black", frame.colour = 'black'))

    print(p)
    return()
}


# Open PDF device
pdf(opt$figure_file, width = 25, height = 10)

# Load data frames
input_df = read.csv(opt$figure_data_file, sep = '\t')
stats_df = read.csv(opt$figure_stats_file, sep = '\t')

# Sort factors
input_df$chrom <- factor(input_df$chrom, levels = c('chr1', 'chr2', 'chr3', 'chr4', 'chr5', 'chr6', 'chr7', 'chr8', 'chr9', 'chr10', 'chr11', 'chr12', 'chr13', 'chr14', 'chr15', 'chr16', 'chr17', 'chr18', 'chr19', 'chr20', 'chr21', 'chr22', 'chrX'))

# # Cluster the data frame
# input_df = cluster_df(input_df)

# Set sample factor levels
input_df$sample <- factor(input_df$sample, levels = c('RLGS2_primary', 'RLGS1_primary', 'RLGS2_recurrent', 'RLGS4_primary', 'RLGS5_primary', 'RLGS12_recurrent', 'RLGS12_primary', 'RLGS11_recurrent', 'RLGS6_recurrent', 'RLGS6_primary', 'RLGS3_primary', 'RLGS11_primary', 'RLGS4_recurrent', 'RLGS1_recurrent', 'RLGS9_primary', 'RLGS8_recurrent', 'RLGS8_primary', 'RLGS9_recurrent', 'RLGS5_recurrent', 'RLGS3_recurrent'))

# Generate the plot
heatmap(input_df)

# Close PDF device
dev.off()

print(opt$figure_file)





