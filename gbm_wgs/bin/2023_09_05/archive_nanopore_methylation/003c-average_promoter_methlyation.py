# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from concurrent.futures import ProcessPoolExecutor

help_message = '''
Failed
'''

# Function to calculate average methylation for each gene
def calculate_avg_methylation(args):
    gene_list, df = args

    res_df = []

    for gene in gene_list:
        # subset to gene
        mask = df['gene'] == gene

        # ensure each base is counted only once
        mask = np.logical_and(mask, ~df['start2'].duplicated())

        # calculate mean methylation
        avg_methylation = df.loc[mask, 'methylation'].mean()
        res_df.append([gene, avg_methylation])

    # convert to df
    res_df = pd.DataFrame(res_df, columns=['gene', 'average_methylation'])

    print(res_df.head())

    return res_df


def average_methylation_per_gene_promoter(file, threads):
    # load df
    df = pd.read_csv(file, sep='\t', header=None)
    df.columns = ['chrom', 'start', 'stop', 'gene', 'chr2', 'start2', 'stop2', 'methylation']

    # # subset to cols_oi
    # cols_oi = ['chrom', 'start', 'stop', 'gene', 'methylation']
    # df = df.loc[:,cols_oi]

    res_df = []

    # split genes into chunks
    genes = df['gene'].unique()
    gene_chunks = np.array_split(genes, threads)

    # define args
    args = [(chunk, df) for chunk in gene_chunks]

    with ProcessPoolExecutor(max_workers=threads) as executor:
        # generate wilcoxon rank-sum p-vals
        res_df = pd.concat(list(executor.map(calculate_avg_methylation, args)))

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # calculate promoter regions from gff
    res_df = average_methylation_per_gene_promoter(intersected_bed_file, threads)

    # write to file
    res_df.to_csv(methylation_by_gene_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["intersected_bed_file=", "methylation_by_gene_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--intersected_bed_file"):
            intersected_bed_file = str(arg)

        if opt in ("--methylation_by_gene_file"):
            methylation_by_gene_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()




