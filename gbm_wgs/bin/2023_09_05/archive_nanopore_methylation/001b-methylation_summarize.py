# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import subprocess, glob

help_message = '''
Failed
'''


def merge_dfs(methylation_files):
    # list of dfs
    res_df = []

    for file_path in methylation_files:
        df = pd.read_csv(file_path, sep='\t')
        res_df.append(df)

    # Step 5: Merge all DataFrames into a single DataFrame
    res_df = pd.concat(res_df, ignore_index=True)

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # list files
    methylation_regex = os.path.join(methylation_res_dir, '*5mC_binned_200kbp.tsv')
    methylation_files = glob.glob(methylation_regex)

    # merge dfs
    methylation_df = merge_dfs(methylation_files)

    # save to files
    methylation_df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_stats_file', figure_stats_file, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["methylation_res_dir=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--methylation_res_dir"):
            methylation_res_dir = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)

    main()




