# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''

def load_and_sort_dmr(dmr_file, methylation_res_dir):
    # load dmr file
    df = pd.read_csv(dmr_file, sep='\t')

    # for each sample sort and save it to a new file
    for sample in df['sample'].unique():
        # subset to sample
        sub_df = df[df['sample'] == sample]

        # reorder columns
        new_col_order = ['dmr_chr', 'start', 'end', 'length', 'differential_methylation', 'methylation_class', 'sample', 'chr', 'promoter_start', 'promoter_end', 'gene']
        sub_df = sub_df[new_col_order]

        # rename some columns
        sub_df = sub_df.rename(columns={'chr':'prom_chr', 'dmr_chr': 'chr'})

        # sort by chrom and start
        sub_df = sub_df.sort_values(by=['chr', 'start'])

        # save to file
        outfile = os.path.join(methylation_res_dir, f'{sample}-dmr_sorted.bed')
        sub_df.to_csv(outfile, sep='\t', index=False, header=False)


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load and sort dmr file
    load_and_sort_dmr(dmr_file, methylation_res_dir)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["dmr_file=", "methylation_res_dir="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--dmr_file"):
            dmr_file = str(arg)
            
        if opt in ("--methylation_res_dir"):
            methylation_res_dir = str(arg)

    main()




