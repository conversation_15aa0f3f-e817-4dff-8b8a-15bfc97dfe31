# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def subset_to_mgmt_promoter(gene_bed_file, genes_of_interest = ['MGMT'], promoter_start = -2000, promoter_end = 500):
    # load gene bed file
    df = pd.read_csv(gene_bed_file, sep='\t', header=None)

    # add column names
    df.columns = ['chr', 'start', 'end', 'gene', 'type', 'info']

    # subset to mgmt
    df = df[df['gene'].isin(genes_of_interest)]

    # add 'chr' back
    df['chr'] = 'chr' + df['chr'].astype('str')

    # modify start and end according to promoter
    df['end'] = df['start'] + promoter_end
    df['start'] = df['start'] + promoter_start

    print(df['start'])
    print(df['end'])
    print(df['end'] - df['start'])

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process cnvkit file
    df = subset_to_mgmt_promoter(gene_bed_file)

    # save to files
    df.to_csv(mgmt_protein_bed_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["gene_bed_file=", "mgmt_protein_bed_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--gene_bed_file"):
            gene_bed_file = str(arg)

        if opt in ("--mgmt_protein_bed_file"):
            mgmt_protein_bed_file = str(arg)

    main()


