# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob, re
from scipy.cluster.hierarchy import dendrogram, linkage, fcluster
import matplotlib.pyplot as plt

help_message = '''
Failed
'''


def merge_dfs(methylation_files):
    # list of dfs
    res_df = []

    for file in methylation_files:
        df = pd.read_csv(file, sep='\t')

        # add sample info
        sample = file.split("/")[-1].split("-")[0]
        
        if "tumor" in sample:
            sample = re.sub("tumor", "recurrent", sample)
        else:
            sample = re.sub("normal", "primary", sample)
            
        df['sample'] = sample

        # subset to main chromosomes
        # mask = np.isin(df['chrom'], ['chr1'])
        mask = np.isin(df['chrom'], chromosomes)
        df = df[mask]
        
        res_df.append(df)

    # merge all DataFrames into a single DataFrame
    res_df = pd.concat(res_df, ignore_index=True)

    # replace spaces with underscores
    res_df.columns = res_df.columns.str.replace(' ', '_')

    # add column combining the chromosome and bin
    res_df['chrom_bin'] = res_df['chrom'] + "_" + res_df['bin'].astype('str')

    return res_df


def get_sample_order(linked, labels):
    # Extract cluster labels based on hierarchical clustering
    # Specify the number of clusters or a threshold for clustering
    cluster_labels = fcluster(linked, t=np.inf, criterion='distance')
    
    # Create a DataFrame to sort samples
    df = pd.DataFrame({'sample': labels, 'cluster': cluster_labels})
    
    # Sort DataFrame by cluster labels and distance from dendrogram
    sorted_df = df.sort_values(by='cluster').reset_index(drop=True)
    
    # Return the sorted sample labels
    return sorted_df['sample'].values

def plot_dendrogram(linked, pivot_df, output_file):
    # Dendrogram
    plt.figure(figsize=(15, 10))
    dendro = dendrogram(linked, orientation='top', labels=pivot_df.index.to_list(), distance_sort='descending', show_leaf_counts=True)
    plt.title('Hierarchical Clustering Dendrogram')
    plt.xlabel('Sample')
    plt.ylabel('Distance')

    # Save to PDF
    plt.savefig(output_file, format='pdf')


def hierarchical_clustering(df, figure_file, n_top_variable_regions=2000):
    # 1. Compute Variability
    variability = df.groupby('chrom_bin')['percent_modified'].std().reset_index()
    variability.columns = ['chrom_bin', 'std_dev']

    # 2. Select Top 2000 Most Variable chrom_bins
    top_2000_bins = variability.nlargest(n_top_variable_regions, 'std_dev')['chrom_bin']

    # 3. Prepare Data for Clustering
    filtered_df = df[df['chrom_bin'].isin(top_2000_bins)]
    pivot_df = filtered_df.pivot_table(index='sample', columns='chrom_bin', values='percent_modified')

    pivot_df = pivot_df.fillna(50)

    # 4. Perform Hierarchical Clustering
    # Linkage matrix
    linked = linkage(pivot_df, method='ward')

    ordered_samples = get_sample_order(linked, pivot_df.index.to_list())

    # create a df with the ordered samples
    # stats_df = pd.DataFrame([ordered_samples, list(range(len(ordered_samples)))], columns=['sample', 'order'])
    stats_df = pd.DataFrame({'sample': ordered_samples, 'order': range(len(ordered_samples))})
    
    # create pdf of dendrogram
    plot_dendrogram(linked, pivot_df, figure_file)

    return stats_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # dmr files
    methl_regex = os.path.join(methylation_res_dir, '*normal-5mC_binned_10kbp.tsv')
    methyl_files = glob.glob(methl_regex)
    methl_regex = os.path.join(methylation_res_dir, '*tumor-5mC_binned_10kbp.tsv')
    methyl_files.extend(glob.glob(methl_regex))


    # load DMRs
    df = merge_dfs(methyl_files)

    # hierarchical clustering
    stats_df = hierarchical_clustering(df, figure_file)

    # save to files
    df.to_csv(figure_data_file, sep='\t', index=False)
    stats_df.to_csv(figure_stats_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # NOTE dendrogram Rscript required
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/ggdendro/bin/Rscript"

    chromosomes = ['chr1', 'chr2', 'chr3', 'chr4', 'chr5', 'chr6', 'chr7', 'chr8', 'chr9', 'chr10', 'chr11', 'chr12', 'chr13', 'chr14', 'chr15', 'chr16', 'chr17', 'chr18', 'chr19', 'chr20', 'chr21', 'chr22', 'chrX']

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["methylation_res_dir=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--methylation_res_dir"):
            methylation_res_dir = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)

    main()




