library(optparse)
library(forcats)
library(ggplot2)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




dotplot = function(input_df){

color_vals = list(True = 'black', False='grey')

# all combined
p = ggplot(input_df, aes(x = sample, y = test, size = log_pvalue, fill = double_to_single_ratio, color = pval_significant)) + plot_theme() +
geom_point(pch=21) +

ggtitle('Enrichment of DMRs in double- vs. single-promoters') +
xlab("") + ylab('') + 

scale_fill_gradient(low = "white", high = "firebrick1") +
scale_color_manual(values = color_vals) +

guides(size = guide_legend(title = "P-value (log10)"), fill = guide_legend(title = "N. double / N. single promoters")) 

print(p)
    
}



pdf(opt$figure_file, width=12, height=7)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')

# refactor sample
input_df$sample = fct_reorder(input_df$sample, input_df$log_pvalue, .desc = TRUE)

# create plot
dotplot(input_df)

dev.off()


print(opt$figure_file)






