# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

import subprocess

help_message = '''
Failed
'''


def load_df_for_ap(rnaseq_methylation_merged_file):
    # load dfs
    df = pd.read_csv(rnaseq_methylation_merged_file, sep='\t')

    # set index
    df.index = df['gene'].to_numpy()

    # separate into meth and rna dfs
    meth_df = df[df['type'] == 'methylation']
    rna_df = df[df['type'] == 'rna_seq']

    # # filter rna_df p-values by fc_cutoff
    # rna_df.loc[rna_df['logFC'] < np.log2(rna_fc_cutoff),'p_value'] = 1

    # create p-value and logFC dfs
    pval_df = meth_df.loc[:,['p_value']]
    pval_df.columns = ['methlyation']
    pval_df['rna_seq'] = rna_df['p_value']

    fc_df = meth_df.loc[:,['logFC']]
    fc_df.columns = ['methlyation']
    fc_df['rna_seq'] = rna_df['logFC']

    return pval_df, fc_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load dfs
    pval_df, fc_df = load_df_for_ap(rnaseq_methylation_merged_file)

    # save to files
    pval_df.to_csv(pval_file, sep='\t')
    fc_df.to_csv(fc_file, sep='\t')

    # run R script
    cline = [rscript, r_script, '--pval_file', pval_file, '--fc_file', fc_file, '--gmt_file', gmt_file, '--cytoscape_file_prefix', cytoscape_file_prefix, '--output_file', output_file, '--max_min_genes', max_min_genes]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # Rscript 
    rscript = '/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r_dev/bin/Rscript'

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["rnaseq_methylation_merged_file=", "r_script=", "pval_file=", "fc_file=", "gmt_file=", "output_file=", "cytoscape_file_prefix=", "max_min_genes="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--rnaseq_methylation_merged_file"):
            rnaseq_methylation_merged_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--pval_file"):
            pval_file = str(arg)
        if opt in ("--fc_file"):
            fc_file = str(arg)
        if opt in ("--gmt_file"):
            gmt_file = str(arg)

        if opt in ("--output_file"):
            output_file = str(arg)
        if opt in ("--cytoscape_file_prefix"):
            cytoscape_file_prefix = str(arg)
        if opt in ("--max_min_genes"):
            max_min_genes = str(arg)

            
    main()





