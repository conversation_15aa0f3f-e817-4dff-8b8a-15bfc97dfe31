# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

import subprocess

help_message = '''
Failed
'''


def load_df_for_ap(methylation_file):
    # load dfs
    df = pd.read_csv(methylation_file, sep='\t')

    # set index
    df.index = df['gene'].to_numpy()

    # subset to p-value from t-test
    df = df[['t_pval']]
    df.columns = ['methylation']

    return df

def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load dfs
    pval_df = load_df_for_ap(methylation_file)

    # save to files
    pval_df.to_csv(pval_file, sep='\t')

    # run R script
    cline = [rscript, r_script, '--pval_file', pval_file, '--gmt_file', gmt_file, '--cytoscape_file_prefix', cytoscape_file_prefix, '--output_file', output_file, '--max_min_genes', max_min_genes]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # Rscript 
    rscript = '/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r_dev/bin/Rscript'

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["methylation_file=", "r_script=", "pval_file=", "fc_file=", "gmt_file=", "output_file=", "cytoscape_file_prefix=", "max_min_genes="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--methylation_file"):
            methylation_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--pval_file"):
            pval_file = str(arg)
        if opt in ("--gmt_file"):
            gmt_file = str(arg)

        if opt in ("--output_file"):
            output_file = str(arg)
        if opt in ("--cytoscape_file_prefix"):
            cytoscape_file_prefix = str(arg)
        if opt in ("--max_min_genes"):
            max_min_genes = str(arg)

            
    main()





