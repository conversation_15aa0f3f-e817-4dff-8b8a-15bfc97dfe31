library(optparse)
library(forcats)
library(ggplot2)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}






# histogram = function(input_df){

# # all combined
# p = ggplot(input_df, aes(x = length, fill = sample)) + plot_theme() +
# geom_histogram() +

# ggtitle('') +
# xlab("DMR size") + ylab('Number of DMRs')


# print(p)

# return()
# }




boxplot = function(input_df){

input_df$sample = fct_reorder(as.factor(input_df$sample), input_df$length, median, .desc=TRUE)

# all combined
p = ggplot(input_df, aes(x = sample, y = length)) + plot_theme() +
geom_boxplot() +

ggtitle('') +
xlab("") + ylab('DMR size')


print(p)

return()
}

boxplot_log = function(input_df){

input_df$sample = fct_reorder(as.factor(input_df$sample), input_df$length, median, .desc=TRUE)

# all combined
p = ggplot(input_df, aes(x = sample, y = log10(length))) + plot_theme() +
geom_boxplot() +

ggtitle('') +
xlab("") + ylab('DMR size (log10)')


print(p)

return()
}



barplot = function(input_df){

input_df$sample = fct_reorder(as.factor(input_df$sample), input_df$dmr_count, sum, .desc=TRUE)

# all combined
p = ggplot(input_df, aes(x = sample, y = dmr_count, fill = class)) + plot_theme() +
geom_bar(stat = "identity", position = position_dodge(width = 0.5)) +  # position_dodge for side-by-side bars

ggtitle('') +
xlab("Sample") + ylab('Number of DMRs')


print(p)

return()
}


plot_per_sample = function(sample, input_df){

sub_df = input_df[input_df$sample == sample,]

test_result <- wilcox.test(length ~ class, data = sub_df)
median_diff <- median(sub_df[sub_df$class == "hyper", "length"]) - 
               median(sub_df[sub_df$class == "hypo", "length"])

# all combined
p = ggplot(sub_df, aes(x = class, y = length)) + plot_theme() +
# geom_histogram() +
geom_boxplot() +

ggtitle(paste0(sample, " - Recurrent vs. Primary")) +
xlab("") + ylab('DMR length') +
    
    geom_text(aes(x = 2, y = max(sub_df$length), 

    label = paste("p-value =", signif(test_result$p.value, 4),
    "\nMedian diff (hyper - hypo) =", signif(median_diff, 3))),

    hjust = 1, vjust = 1, size = 6)  # Add p-value to the plot


print(p)

return()
}




pdf(opt$figure_file)

# load df
stats_df = read.csv(opt$figure_stats_file, sep='\t')
input_df = read.csv(opt$figure_data_file, sep='\t')


# plot
# histogram(input_df)
boxplot(input_df)
boxplot_log(input_df)
barplot(stats_df)


# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# order samples
sorted_vector <- unique(input_df$sample)[order(sapply(unique(input_df$sample), extract_numeric))]
input_df$sample = factor(input_df$sample, levels = sorted_vector)

# order methylation levels
input_df$class = factor(input_df$class, levels = c('hypo', 'hyper'))

# histogram per sample
lapply(levels(input_df$sample), plot_per_sample, input_df)


dev.off()


print(opt$figure_file)






