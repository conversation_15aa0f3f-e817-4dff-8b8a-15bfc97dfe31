# <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob, re

help_message = '''
Failed
'''


def load_and_combine_files(results_dir):
    # list of files
    file_regex = os.path.join(results_dir, '*-mean_promoter_methylation.tsv')
    files = glob.glob(file_regex)

    # Initialize an empty list to store the DataFrames
    res_df = []

    # Iterate through each file in the directory
    for file in files:
        # Load the file as a DataFrame
        df = pd.read_csv(file, sep='\t')
        df.index = df['gene'].to_numpy()

        # drop gene column
        df.drop(columns=['gene'], inplace=True)

        # rename column
        sample = file.split('/')[-1].split('-')[0]
        sample = re.sub('tumor', 'recurrent', sample)
        sample = re.sub('normal', 'primary', sample)
        df.columns = [sample]
        
        # Append the DataFrame to the list
        res_df.append(df)

    # Combine the DataFrames into a single DataFrame
    return pd.concat(res_df, axis=1)


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load and combine files
    res_df = load_and_combine_files(results_dir)

    # write to file
    res_df.to_csv(combined_promoter_methylation_file, sep='\t')
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "combined_promoter_methylation_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--combined_promoter_methylation_file"):
            combined_promoter_methylation_file = str(arg)
            
    main()




