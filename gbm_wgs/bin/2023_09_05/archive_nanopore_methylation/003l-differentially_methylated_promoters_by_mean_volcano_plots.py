# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

import subprocess

help_message = '''
Failed
'''


def load_df(methylation_file):
    # load dfs
    meth_df = pd.read_csv(methylation_file, sep='\t')

    # calculate logFC
    meth_df['logFC'] = np.log2(meth_df['mean_fc'])

    # remove t_sig and u_sig
    meth_df = meth_df.drop(columns=['t_sig', 'u_sig'])

    # melt the df according to gene, mean_fc, logFC, and cgc_gene
    meth_df = meth_df.melt(id_vars=['gene', 'mean_fc', 'logFC', 'cgc_gene'], var_name='test', value_name='p_value')

    return meth_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load dfs
    df = load_df(methylation_file)

    # write to file
    df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # Rscript 
    rscript = '/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript'

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["methylation_file=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--methylation_file"):
            methylation_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)


            
    main()




