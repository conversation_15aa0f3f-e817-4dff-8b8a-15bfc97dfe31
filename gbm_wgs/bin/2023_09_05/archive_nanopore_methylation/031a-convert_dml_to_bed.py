# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def load_dml_file(file, chromosomes_list, pval_limit = 1e-128):
    # for each file, load as df and subset to relevant columns
    df = pd.read_csv(file, sep='\t', dtype={'fdr': 'float64', 'pval': 'float64'})

    # for pvals and fdrs less than 1e-64, set to 1e-64
    df['fdr'] = df['fdr'].apply(lambda x: pval_limit if x < pval_limit else x)
    df['pval'] = df['pval'].apply(lambda x: pval_limit if x < pval_limit else x)
    
    # drop columns
    df = df.drop(columns=['phi1', 'phi2', 'stat', 'diff.se'])

    # subset to chromosomes
    df = df[df['chr'].isin(chromosomes_list)]

    # rename columns
    df = df.rename(columns={'chr': 'chrom', 'pos': 'start'})

    # add end column and sort columns to start with chrom, start, end then rest
    df['end'] = df['start']
    df = df[['chrom', 'start', 'end'] + [col for col in df.columns if col not in ['chrom', 'start', 'end']]]

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load and sort dmr file
    df = load_dml_file(dml_file, chromosomes)

    # save to file
    df.to_csv(dml_bed_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    chromosomes = ['chr1', 'chr2', 'chr3', 'chr4', 'chr5', 'chr6', 'chr7', 'chr8', 'chr9', 'chr10', 'chr11', 'chr12', 'chr13', 'chr14', 'chr15', 'chr16', 'chr17', 'chr18', 'chr19', 'chr20', 'chr21', 'chr22', 'chrX', 'chrY']

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["dml_file=", "dml_bed_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--dml_file"):
            dml_file = str(arg)
            
        if opt in ("--dml_bed_file"):
            dml_bed_file = str(arg)

    main()




