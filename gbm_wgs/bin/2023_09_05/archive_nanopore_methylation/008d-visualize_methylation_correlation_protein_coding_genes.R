# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)
library(ggrastr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




density_plot = function(df){

# density polygons
p = ggplot(df, aes(x = primary, y = recurrent)) + plot_theme() +
stat_density_2d(aes(fill = after_stat(level)), geom = "polygon", color='black', 
        contour = TRUE, bins = 50) + # Increased bins for more levels  
  
facet_wrap(~ patient, nrow = 2) +  # Facet by patient into 2 rows and 6 columns

scale_fill_gradientn(colours = c("#1d00bf", "yellow"), 
        name = "Density Level") +

ggtitle('') +
xlab('Primary methylation rate') + ylab('Recurrent methylation rate') +
theme(axis.text.x = element_text(angle = 0))

print(p)

    
# contours
p = ggplot(df, aes(x = primary, y = recurrent)) + plot_theme() +
stat_density_2d(aes(color = after_stat(level)), geom = "contour", bins = 50) +

facet_wrap(~ patient, nrow = 2) +  # Facet by patient into 2 rows and 6 columns

scale_color_gradientn(colours = c("#1d00bf", "yellow"), 
name = "Density Level") +
  
ggtitle('') +
xlab('Primary methylation rate') + ylab('Recurrent methylation rate') + 
theme(axis.text.x = element_text(angle = 0))

print(p)



# dotplots
p = ggplot(df, aes(x = primary, y = recurrent)) + plot_theme() +
rasterize(geom_point(alpha = 0.01), dpi=350)  +

facet_wrap(~ patient, nrow = 2) +  # Facet by patient into 2 rows and 6 columns

ggtitle('') +
xlab('Primary methylation rate') + ylab('Recurrent methylation rate') + 
theme(axis.text.x = element_text(angle = 0))

print(p)

    
return()

}


sort_patients = function(df){
# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# order samples
sorted_vector <- unique(df$patient)[order(sapply(unique(df$patient), extract_numeric))]
df$patient = factor(df$patient, levels = sorted_vector)

return(df)
}


pdf(opt$figure_file, width=22, height=10)

# load df
df = read.csv(opt$figure_data_file, sep='\t')

# sort patients
df = sort_patients(df)

# apply to all patients
density_plot(df)


dev.off()


print(opt$figure_file)




