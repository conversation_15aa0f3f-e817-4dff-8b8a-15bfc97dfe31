# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from sklearn.mixture import GaussianMixture
from sklearn.metrics import silhouette_score

from concurrent.futures import ProcessPoolExecutor, as_completed

help_message = '''
Failed
'''


def evaluate_gmm_iteration(X, n_components, iteration):
    # Initialize GMM with the current number of components
    gmm = GaussianMixture(n_components=n_components, random_state=iteration)
    
    # Fit the GMM
    gmm.fit(X)
    
    # Predict the cluster labels
    labels = gmm.predict(X)
    
    # Compute metrics
    bic = gmm.bic(X)
    aic = gmm.aic(X)
    sil_score = silhouette_score(X, labels, metric='euclidean')
    
    return {
        'n_components': n_components,
        'iteration': iteration + 1,
        'bic': bic,
        'aic': aic,
        'silhouette_score': sil_score,
        'labels': labels
    }


def evaluate_gmm(X, cpg_site, threads, max_components=10, iterations=10):
    results = []
    best_bic = np.inf
    best_labels = None

    # Create a list of tasks for parallel processing
    tasks = []
    for n_components in range(2, max_components + 1):
        for i in range(iterations):
            tasks.append((X, n_components, i))

    # Use ProcessPoolExecutor to parallelize the GMM fitting
    with ProcessPoolExecutor(max_workers=threads) as executor:
        futures = [executor.submit(evaluate_gmm_iteration, *task) for task in tasks]
        
        for future in as_completed(futures):
            result = future.result()
            results.append(result)
            
            # Track the best model
            if result['bic'] < best_bic:
                best_bic = result['bic']
                best_labels = result['labels']

    # Convert results to DataFrame
    results_df = pd.DataFrame(results)
    
    # Rank rows by bic
    results_df = results_df.sort_values(by='bic')

    # Create a DataFrame with the cpg_site and the labels
    cpg_site_df = pd.DataFrame({'cpg': cpg_site, 'labels': best_labels})

    return results_df, cpg_site_df


def perform_gmm_fitting(methylation_file, threads):
    # load df
    df = pd.read_csv(methylation_file, sep='\t')

    # separate into primary and recurrent dfs
    primary_df = df.loc[:,['cpg', 'primary']]
    recurrent_df = df.loc[:,['cpg', 'recurrent']]

    # seed and randomly sample 10000 rows
    np.random.seed(1234)
    primary_df = primary_df.sample(10000)
    recurrent_df = recurrent_df.sample(10000)

    # evalute gmm for primary
    primary_res, primary_cpg_site_classes = evaluate_gmm(primary_df[['primary']], primary_df['cpg'].to_numpy(), threads)

    # evalute gmm for recurrent
    recurrent_res, recurrent_cpg_site_classes = evaluate_gmm(recurrent_df[['recurrent']], recurrent_df['cpg'].to_numpy(), threads)
    # recurrent_res, recurrent_cpg_site_classes = evaluate_gmm(df.loc[:,['recurrent']], df['cpg'].to_numpy(), threads)

    # combine the results
    primary_res['type'] = 'primary'
    recurrent_res['type'] = 'recurrent'

    df = pd.concat([primary_res, recurrent_res])

    # combine the cpg_site_classes
    primary_cpg_site_classes['type'] = 'primary'
    recurrent_cpg_site_classes['type'] = 'recurrent'

    cpg_df = pd.concat([primary_cpg_site_classes, recurrent_cpg_site_classes])

    return df, cpg_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # combine the methylation files
    df, cpg_df = perform_gmm_fitting(methylation_file, threads)

    # save to files
    df.to_csv(figure_data_file, sep='\t', index=False)
    cpg_df.to_csv(cpg_clusters_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["methylation_file=", "figure_data_file=", "cpg_clusters_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--methylation_file"):
            methylation_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--cpg_clusters_file"):
            cpg_clusters_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)
            
    main()


