# <PERSON>
library(optparse)
library(ActivePathways)

# options list for parser options
option_list <- list(
    make_option(c("-a","--pval_file"), type="character", default=NULL,
            help="",
            dest="pval_file"),
    make_option(c("-f","--fc_file"), type="character", default=NULL,
            help="",
            dest="fc_file"),
    make_option(c("-b","--gmt_file"), type="character", default=NULL,
            help="",
            dest="gmt_file"),
    make_option(c("-c","--cytoscape_file_prefix"), type="character", default=NULL,
            help="",
            dest="cytoscape_file_prefix"),
    make_option(c("-d","--output_file"), type="character", default=NULL,
            help="",
            dest="output_file"),
    make_option(c("-e","--max_min_genes"), type="character", default=NULL,
            help="",
            dest="max_min_genes")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

# load dfs
pval_df = read.csv(opt$pval_file, sep='\t', row.names=1)
pvals = as.matrix(pval_df)
colnames(pvals) = colnames(pval_df)

fc_df = read.csv(opt$fc_file, sep='\t', row.names=1)
fcs = as.matrix(fc_df)
colnames(fcs) = colnames(fc_df)

# set NAs to 1 and define colors
pvals[is.na(pvals)] = 1
fcs[is.na(fcs)] = 0


# minimum and maximum number of genes
gene_limits = strsplit(opt$max_min_genes, ",")[[1]]
min_genes = as.numeric(gene_limits[2])
max_genes = as.numeric(gene_limits[1])


# run ActivePathways
# enriched_pathways = ActivePathways(pvals, opt$gmt_file, scores_direction=fcs, constraints_vector=c(-1,1), merge_method='DPM', cytoscape_file_tag=opt$cytoscape_file_prefix, geneset_filter = c(min_genes, max_genes), background=rownames(pvals))
enriched_pathways = ActivePathways(pvals, opt$gmt_file, scores_direction=fcs, constraints_vector=c(-1,1), merge_method='DPM', cytoscape_file_tag=opt$cytoscape_file_prefix, geneset_filter = c(min_genes, max_genes))

# export enriched pathways
export_as_CSV(enriched_pathways, opt$output_file)

print("ActivePathways Complete")



