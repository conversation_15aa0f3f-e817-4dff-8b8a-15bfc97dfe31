# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob, subprocess

help_message = '''
Failed
'''

def load_res_dfs(methylation_res_dir, file_regex, cgc_file):
    # list files 
    files = glob.glob(os.path.join(methylation_res_dir, file_regex))

    # load each file and add sample and tumor details
    dfs = []

    for file in files:
        # load file
        df = pd.read_csv(file, sep='\t', header=None)
        df.columns = ['dmr_chr', 'start', 'end', 'length', 'differential_methylation', 'methylation_class', 'sample', 'chr', 'promoter_start', 'promoter_end', 'gene', 'chr', 'cpg_start', 'cpg_stop', 'mean_methylation']

        # add tumor details
        df['tumor'] = file.split('/')[-1].split('_')[1].split("-")[0]

        dfs.append(df)

    # concatenate all dfs
    df = pd.concat(dfs)

    # add cgc information
    cgc_df = pd.read_csv(cgc_file, sep='\t')

    df['cgc_gene'] = df['gene'].isin(cgc_df['GENE_SYMBOL'])

    # subset to cgc genes only
    df = df[df['cgc_gene']]

    # rename to primary and recurrent
    df.loc[df['tumor'] == 'normal','tumor'] = 'primary'
    df.loc[df['tumor'] == 'tumor','tumor'] = 'recurrent'
    
    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load and sort dmr file
    df = load_res_dfs(methylation_res_dir, file_regex, cgc_file)

    # save results file
    df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    # file regex 
    file_regex = '*dmr_raw_methylation.tsv'

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["methylation_res_dir=", "cgc_file=", "r_script=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--methylation_res_dir"):
            methylation_res_dir = str(arg)
        if opt in ("--cgc_file"):
            cgc_file = str(arg)
            
        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)



    main()




