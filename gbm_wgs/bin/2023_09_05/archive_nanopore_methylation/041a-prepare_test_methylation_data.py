# <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def load_methyl_bed(file, chromosomes):
    # columns
    columns = ['chrom', 'start', 'end', 'modified base code and motif', 'score', 'strand', 'start position', 'end position', 'color', 'Nvalid_cov', 'percent modified', 'Nmod', 'Ncanonical', 'Nother_mod', 'Ndelete', 'Nfail', 'Ndiff', 'Nnocall']

    # for each file, load as df and subset to relevant columns
    df = pd.read_csv(file, sep='\t', header=None, compression='gzip')
    
    # Split the last column based on spaces
    split_columns = df.iloc[:, -1].str.split(expand=True)
    
    # Assign the split columns back to the original DataFrame
    df = pd.concat([df.iloc[:, :-1], split_columns], axis=1)
    df.columns = columns

    # subset to specific chromosomes
    df = df[df['chrom'].isin(chromosomes)]
    
    # subset to columns of interest
    df = df[['chrom', 'start', 'end', 'percent modified', 'Nmod', 'score']]

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load and sort dmr file
    df = load_methyl_bed(methyl_file, chromosomes)

    # save to file
    df.to_csv(methyl_outfile, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    chromosomes = ['chr1', 'chr2', 'chr3', 'chr4', 'chr5', 'chr6', 'chr7', 'chr8', 'chr9', 'chr10', 'chr11', 'chr12', 'chr13', 'chr14', 'chr15', 'chr16', 'chr17', 'chr18', 'chr19', 'chr20', 'chr21', 'chr22', 'chrX']

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["methyl_file=", "methyl_outfile="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--methyl_file"):
            methyl_file = str(arg)
            
        if opt in ("--methyl_outfile"):
            methyl_outfile = str(arg)

    main()




