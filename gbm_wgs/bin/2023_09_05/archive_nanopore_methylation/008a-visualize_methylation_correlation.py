# <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import re, subprocess

help_message = '''
Failed
'''

def load_df(file):
    # columns
    columns = ['chrom', 'start', 'end', 'modified base code and motif', 'score', 'strand', 'start position', 'end position', 'color', 'Nvalid_cov', 'percent modified', 'Nmod', 'Ncanonical', 'Nother_mod', 'Ndelete', 'Nfail', 'Ndiff', 'Nnocall']

    # for each file, load as df and subset to relevant columns
    df = pd.read_csv(file, sep='\t', header=None, compression='gzip')

    # Split the last column based on spaces
    split_columns = df.iloc[:, -1].str.split(expand=True)

    # Assign the split columns back to the original DataFrame
    df = pd.concat([df.iloc[:, :-1], split_columns], axis=1)
    df.columns = columns

    # set index as chrom and start
    df.index = df['chrom'].astype('str') + '-' + df['start'].astype('str')

    # subset to relevant columns
    df = df[['percent modified']]

    # rename columns
    sample = file.split("/")[-1].split(".")[1]
    sample = re.sub(r'-primary_', '_', sample)
    
    # rename "tumor" with "recurrent" and "normal" with "primary"
    sample = re.sub(r'tumor', 'recurrent', sample)
    sample = re.sub(r'normal', 'primary', sample)

    df.columns = [sample]

    return df


def separate_primary_recurrent(df):
    # Extract patient IDs from the column names
    primary_cols = [col for col in df.columns if 'primary' in col]
    recurrent_cols = [col for col in df.columns if 'recurrent' in col]

    # Extract patient IDs
    patient_ids = [col.split('_')[0] for col in primary_cols]

    # Prepare the result DataFrame
    result_df = pd.DataFrame({
        'primary': df[primary_cols].values.transpose().flatten(),
        'recurrent': df[recurrent_cols].values.transpose().flatten(),
        'patient_id': [pid for pid in patient_ids for _ in range(len(df))]
    })

    # Handle cpg_site for the DataFrame
    result_df['cpg_site'] = np.tile(df.index.to_numpy(), len(primary_cols))

    return result_df


def combined_methylation_files(primary_methylation_file, recurrent_methylation_file, nsites = 1000000):
    # Load the primary and recurrent methylation files
    primary_df = load_df(primary_methylation_file)
    recurrent_df = load_df(recurrent_methylation_file)

    # combine
    res_df = pd.concat([primary_df, recurrent_df], axis=1)

    # separate into primary and recurrent data
    res_df = separate_primary_recurrent(res_df)

    # remove rows with NA in either primary or recurrent
    res_df = res_df.dropna()

    # use only chromosomes
    res_df = res_df[res_df['cpg_site'].str.contains('chr')]

    # seed and randomly sample n rows
    np.random.seed(1234)
    res_df = res_df.sample(nsites)

    return res_df



def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # combine the methylation files
    df = combined_methylation_files(primary_methylation_file, recurrent_methylation_file)

    # save to file
    df.to_csv(figure_data_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["primary_methylation_file=", "recurrent_methylation_file=", "figure_data_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--primary_methylation_file"):
            primary_methylation_file = str(arg)
        if opt in ("--recurrent_methylation_file"):
            recurrent_methylation_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
            

    main()


