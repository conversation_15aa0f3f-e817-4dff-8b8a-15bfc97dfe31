# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

import subprocess
from statsmodels.stats import multitest

help_message = '''
Failed
'''

def load_rnaseq_df(rnaseq_file, fc_cutoff = 0.5):
    # load df
    df = pd.read_csv(rnaseq_file, sep='\t', index_col=0)

    # subset to deseq2_paired
    df = df[df['condition'] == 'deseq2_paired']

    # index is gene
    df.index = df['gene_name'].to_numpy()

    # subset to pvalue and log2FC columns
    df = df.loc[:,['log2FoldChange', 'pvalue', 'gene_name']]

    # rename columns
    df.columns = ['logFC', 'p_value', 'gene']

    # add fdr column
    df['fdr'] = multitest.fdrcorrection(df['p_value'])[1]

    # add type column
    df['type'] = 'rna_seq'

    df.loc[df['logFC'] < np.log2(fc_cutoff),'p_value'] = 1

    print(df)

    return df


def load_df_for_ap(methylation_file, rnaseq_file):
    # load dfs
    meth_df = pd.read_csv(methylation_file, sep='\t')
    meth_df.index = meth_df['gene'].to_numpy()
    rna_df = load_rnaseq_df(rnaseq_file)

    # subset to common genes
    common_genes = np.intersect1d(meth_df.index.astype("<U64"), rna_df.index.astype("<U64"))
    meth_df = meth_df.loc[common_genes,:]
    rna_df = rna_df.loc[common_genes,:]

    # rename columns and fdr and type columns
    meth_df = meth_df.rename(columns = {'log2_mean_fc':'logFC', 't_pval':'p_value', 't_fdr':'fdr'})
    meth_df['type'] = 'methylation'

    # subset to pvalue and log2FC columns
    meth_df = meth_df.loc[:,['logFC', 'p_value', 'fdr', 'type', 'gene']]
    rna_df = rna_df.loc[:,['logFC', 'p_value', 'fdr', 'type', 'gene']]

    # combine dfs
    df = pd.concat([meth_df, rna_df])

    # add gene column
    df['gene'] = df.index.to_numpy()

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load dfs
    df = load_df_for_ap(methylation_file, rna_seq_file)

    # write to file
    df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # Rscript 
    rscript = '/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript'

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["methylation_file=", "rna_seq_file=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--methylation_file"):
            methylation_file = str(arg)
        if opt in ("--rna_seq_file"):
            rna_seq_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)


            
    main()




