# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob
from concurrent.futures import ProcessPoolExecutor

import scipy.stats as stats

help_message = '''
Failed
'''


def fishers_method(p_values, scores_direction, expected_direction = None):
    if np.all(scores_direction != 0):
        # apply directionality penalty where applicable
        d_mask = expected_direction != 0
        directionality = expected_direction[d_mask] * scores_direction[d_mask] / np.abs(scores_direction[d_mask])
        p_values_directional = p_values[d_mask]

        chisq_directional = np.abs(-2 * np.sum(np.log(p_values_directional) * directionality))

        # calculate for non-directionality p-values
        if np.any(~d_mask):
            p_values_nondirectional = p_values[~d_mask]
            chisq_directional = np.concatenate([np.abs(-2 * sum(np.log(p_values_nondirectional))), chisq_directional])

    else:
        chisq_directional = -2 * np.log(p_values)

    return np.sum(chisq_directional)


def merge_pval_fishers_method(gene_df, pval='fdr'):
    # merge p-values using fishers method
    p_values = gene_df[pval].to_numpy()
    scores_direction = gene_df['diff'].to_numpy()
    expected_direction = np.repeat(1, len(p_values))

    # apply fishers method
    chisq = fishers_method(p_values, scores_direction, expected_direction)

    # calculate p-value
    pval = 1 - stats.chi2.cdf(chisq, 2 * len(p_values))

    # calculate mean difference
    mean_diff = np.mean(gene_df['diff'])

    # define the gene
    gene = gene_df['gene'].iloc[0]

    return [pval, gene, mean_diff]


def calculate_gene_pvalues(args):
    # Calculate p-values per gene
    df, genes = args
    p_val_df = []

    for gene in genes:
        # Subset to gene
        gene_df = df[df['gene'] == gene].copy()

        # merge p-values using fishers method
        p_val_df.append(merge_pval_fishers_method(gene_df))

    # convert to df
    p_val_df = pd.DataFrame(p_val_df, columns=['pval', 'gene', 'mean_diff'])

    return p_val_df


def summarize_dml_files(file, threads):
    # results df
    res_df = []

    # load df
    df = pd.read_csv(file, sep='\t', header=None)

    # add column names
    df.columns = ['chromosome', 'start', 'stop', 'gene', 'chr2', 'cpg_site', 'cpg_stop', 'mu1', 'mu2', 'diff', 'pval', 'fdr']

    # split genes into number of threads
    genes_lists = np.array_split(df['gene'].unique(), threads)

    # list args
    args = [(df[df['gene'].isin(genes)],genes) for genes in genes_lists]

    # calculate p-values per gene using multi-threads
    with ProcessPoolExecutor(max_workers=threads) as executor:
        pval_df = pd.concat(list(executor.map(calculate_gene_pvalues, args)))

    # get sample name
    sample = file.split('/')[-1].split('_')[0]

    # add sample column
    pval_df['sample'] = sample

    # add to results dfs
    res_df.append(pval_df)

    # concat res_df
    res_df = pd.concat(res_df)

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and sort dml files
    df = summarize_dml_files(raw_promoter_pval_file, threads)

    # save to files
    df.to_csv(promoter_pval_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["raw_promoter_pval_file=", "promoter_pval_file=", "figure_stats_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--raw_promoter_pval_file"):
            raw_promoter_pval_file = str(arg)

        if opt in ("--promoter_pval_file"):
            promoter_pval_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()




