# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import scipy.stats as stats
import subprocess, re

help_message = '''
Failed
'''


def process_methylation_per_gene(raw_df):
    # process each sample independently
    final_df = []

    for sample in raw_df['sample'].unique():
        input_df = raw_df[raw_df['sample'] == sample]

        # the number of times a gene appears
        value_counts = input_df.index.value_counts()

        # Filter strings that appear more than once
        duplicate_genes = value_counts[value_counts > 1].index.tolist()
        single_genes = value_counts[value_counts == 1].index.tolist()

        # create empty df to store results
        res_df = input_df.loc[single_genes,:]

        # process each sample independently
        for gene in duplicate_genes:
            # if all classes are the same, keep the first
            if len(np.unique(input_df.loc[gene,'methylation_class'])) == 1:
                res_df = res_df.append(input_df.loc[gene,:].iloc[0,:])

            # if there are two classes, keep the one with the highest differential methylation
            else:
                res_df = res_df.append(input_df.loc[gene,:].sort_values('differential_methylation', ascending=False).iloc[0,:])

        final_df.append(res_df)

    return pd.concat(final_df)


def process_methylation_file(dmr_promoter_results_file, cgc_file):
    # load methylation by gene file
    dmr_methylation_df = pd.read_csv(dmr_promoter_results_file, sep='\t')

    # process methylation by gene file
    dmr_methylation_df = process_methylation_per_gene(dmr_methylation_df)

    # classify genes as cancer genes or not
    cgc_df = pd.read_csv(cgc_file, sep='\t')

    dmr_methylation_df['cgc_gene'] = dmr_methylation_df['gene'].isin(cgc_df['GENE_SYMBOL'])

    return dmr_methylation_df


def summarize_variants(df):
    # summarize number of mutations in each gene
    res_df = df.groupby(['gene', 'cgc_gene'])['sample'].nunique().reset_index()

    res_df = res_df.sort_values('sample', ascending=False)

    # add the fraction of patients with a mutation in each gene
    res_df['patient_fraction'] = res_df['sample'] / df['sample'].nunique()

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load methylation file
    df = process_methylation_file(dmr_promoter_results_file, cgc_file)

    # summarize data
    summary_df = summarize_variants(df)

    # save to files
    df.to_csv(figure_data_file, sep='\t', index=False)
    summary_df.to_csv(figure_stats_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["dmr_promoter_results_file=", "cgc_file=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--dmr_promoter_results_file"):
            dmr_promoter_results_file = str(arg)
        if opt in ("--cgc_file"):
            cgc_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)

    main()


