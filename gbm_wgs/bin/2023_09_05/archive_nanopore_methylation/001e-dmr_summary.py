# <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import subprocess

help_message = '''
Failed
'''


def summarize_variants(methylation_stats_file):
    # load df
    methylation_df = pd.read_csv(methylation_stats_file, sep='\t')

    # create a new df summarizing the number of DMRs for each sample
    dmr_df = methylation_df.groupby(['sample', 'class']).size().reset_index(name='dmr_count')

    print(dmr_df.head())

    return dmr_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load and summarize variants
    dmr_df = summarize_variants(methylation_stats_file)

    # save to files
    dmr_df.to_csv(figure_stats_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', methylation_stats_file, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["methylation_stats_file=", "r_script=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--methylation_stats_file"):
            methylation_stats_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)

    main()




