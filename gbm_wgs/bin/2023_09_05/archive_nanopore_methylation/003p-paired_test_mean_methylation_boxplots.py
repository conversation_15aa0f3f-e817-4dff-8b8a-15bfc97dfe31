# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import subprocess

help_message = '''
Failed
'''

def calculate_methylation_difference(file):
    # Read in the data
    df = pd.read_csv(file, sep='\t')

    # Create a MultiIndex from gene, cpg_start, and patient
    df.set_index(['gene', 'sample_id'], inplace=True)

    # sort by sample_id then gene
    df.sort_index(inplace=True)
    
    # Separate into primary and recurrent
    primary = df[df['tumor'] == 'primary'].copy()
    recurrent = df[df['tumor'] == 'recurrent'].copy()

    # Ensure indices are the same
    if not primary.index.equals(recurrent.index):
        raise ValueError("Primary and recurrent indices do not match.")

    # Align the indices and add methylation column of recurrent to primary
    primary = primary.join(recurrent['mean_methylation'], rsuffix='_recurrent')

    # Calculate the methylation difference
    primary['methylation_difference'] = np.where(
        primary['mean_methylation'] > primary['mean_methylation_recurrent'],
        'Decrease',
        'Increase'
    )

    # Reset index to prepare for rejoining with the original df
    primary.reset_index(inplace=True)

    # Add the calculated methylation difference back to the original df
    df = df.reset_index()
    df = df.merge(primary[['gene', 'sample_id', 'methylation_difference']], 
                  on=['gene', 'sample_id'], 
                  how='left')

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process methylation to get difference
    df = calculate_methylation_difference(mean_promoter_methylation_file)

    # write to file
    df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["mean_promoter_methylation_file=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--mean_promoter_methylation_file"):
            mean_promoter_methylation_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)


    main()




