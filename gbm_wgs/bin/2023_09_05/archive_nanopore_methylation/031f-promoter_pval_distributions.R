# <PERSON>
library(optparse)
library(ggplot2)
library(ggrepel)
library(ggrastr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}






histogram = function(sample, input_df){

# plot
p = ggplot(input_df, aes(x = fdr)) + plot_theme() +
geom_histogram(bins = 100) +

ggtitle(sample) +
xlab('FDR') + ylab('Number of CpGs')

print(p)


# plot
p = ggplot(input_df, aes(x = pval)) + plot_theme() +
geom_histogram(bins = 100) +

ggtitle(sample) +
xlab('Pval') + ylab('Number of CpGs')

print(p)


return()
}




pdf(opt$figure_file)

input_df = read.csv(opt$figure_data_file, sep='\t', header=FALSE)

# add header
colnames(input_df) = c('chromosome', 'start', 'stop', 'gene', 'chr2', 'cpg_site', 'cpg_stop', 'mu1', 'mu2', 'diff', 'pval', 'fdr')

# extract the sample name
sample = strsplit(basename(opt$figure_data_file), "_")[[1]][1]

# make a histogram 
histogram(sample, input_df)


dev.off()



print(opt$figure_file)


