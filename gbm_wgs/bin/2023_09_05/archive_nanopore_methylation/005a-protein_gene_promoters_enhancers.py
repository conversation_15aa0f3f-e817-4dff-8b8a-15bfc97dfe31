# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def process_gene_promoters(df, upstream_length=2000, downstream_length=200):
    # filter protein-coding genes
    mask = df['type'] != 'enhancer'
    df = df[mask]
    
    # add gene details
    df['gene'] = df['attributes'].str.extract(r'gene=([^;]+)')

    # calculate promoter regions
    df['promoter_start'] = df['start'] - upstream_length
    df['promoter_end'] = df['start'] + downstream_length

    # make sure there is no negative value
    mask = df['promoter_start'] < 0
    df.loc[mask,'promoter_start'] = 0

    return df


def process_enhancers(df):
    # filter enhancers
    mask = df['type'] == 'enhancer'
    df = df[mask]

    # add enhancer details
    df['gene'] = 'enhancer'

    # add pseudo gene details
    df['promoter_start'] = df['start']
    df['promoter_end'] = df['start']

    return df


def calculate_promoters(protein_coding_genes_gff_file):
    # read gff file
    df = pd.read_csv(protein_coding_genes_gff_file, sep='\t', header=None)

    # add column names
    column_names = ['seqname', 'source', 'type', 'start', 'end', 'score', 'strand', 'phase', 'attributes']
    df.columns = column_names

    # add chromosome
    df['chr'] = 'chr' + df['seqname'].str.split(".").str[0].str.split("_").str[1].str.replace(r'^0+', '', regex=True)

    # replace chr23 with chrX and chr24 with chrY
    mask = df['chr'] == 'chr23'
    df.loc[mask, 'chr'] = 'chrX'
    mask = df['chr'] == 'chr24'
    df.loc[mask, 'chr'] = 'chrY'

    # analyze protein-coding genes
    pc_df = process_gene_promoters(df)

    # analyze enhancers
    enh_df = process_enhancers(df)

    # combine dataframes
    df = pd.concat([pc_df, enh_df])

    # sort values
    df = df.sort_values(['chr', 'promoter_start'])

    # filter columns
    df = df[['chr', 'promoter_start', 'promoter_end', 'type', 'gene', 'attributes']]
    df.columns = ['chr', 'start', 'end', 'type', 'gene', 'attributes']

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # calculate promoter regions from gff
    promoter_df = calculate_promoters(protein_coding_genes_gff_file)

    print(promoter_df.shape)

    # save to file
    promoter_df.to_csv(protein_coding_promoter_bed_file, sep='\t', index=False, header=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["protein_coding_genes_gff_file=", "protein_coding_promoter_bed_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--protein_coding_genes_gff_file"):
            protein_coding_genes_gff_file = str(arg)

        if opt in ("--protein_coding_promoter_bed_file"):
            protein_coding_promoter_bed_file = str(arg)
            
    main()




