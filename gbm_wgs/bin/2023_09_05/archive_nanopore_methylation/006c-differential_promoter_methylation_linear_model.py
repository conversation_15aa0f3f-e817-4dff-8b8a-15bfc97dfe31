# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import subprocess


help_message = '''
Failed
'''


def run_lm_test(file, methylation_patient_csv, figure_stats_file, r_script, figure_data_file, threads):
    # load df
    df = pd.read_csv(file, sep='\t', index_col=0)
    
    # sort columns by sample
    df = df.sort_index(axis=1)

    # # group by gene and calculate average
    # df['gene'] = df.index.str.split('-').str[1]
    # df = df.groupby('gene').mean()
    
    # separate into primary and recurrent samples
    primary = df.loc[:, df.columns.str.contains('normal')]
    recurrent = df.loc[:, df.columns.str.contains('tumor')]
    
    # rename columns
    primary.columns = [col.split('_')[0] for col in primary.columns]
    recurrent.columns = [col.split('_')[0] for col in recurrent.columns]
    
    # create new df of difference in gene methylation between primary and recurrent samples
    df = recurrent - primary
    
    # save memory
    del recurrent
    del primary

    # # melt df
    # df = df.reset_index().melt(id_vars='gene', var_name='patient', value_name='methylation')
    
    # melt df
    df = df.reset_index().melt(id_vars='index', var_name='patient', value_name='methylation')
    
    # reset index
    df['cpg_site'] = df['index'].str.split("-").str[0]
    df.index = df['index'].str.split("-").str[1].to_numpy()
    df['gene'] = df.index.to_numpy()
    df = df.drop('index', axis=1)

    # subset to only include patients in methylation_patient_csv
    patients = methylation_patient_csv.split(',')
    df = df.loc[df['patient'].isin(patients)]

    # save to file
    df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_stats_file', figure_stats_file, '--threads', str(threads)]

    print(" ".join(cline))
    subprocess.run(cline)


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # process methylation by gene file
    run_lm_test(methylation_by_gene_promoter_file, methylation_patient_csv, figure_stats_file, r_script, figure_data_file, threads)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["methylation_by_gene_promoter_file=", "methylation_patient_csv=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--methylation_by_gene_promoter_file"):
            methylation_by_gene_promoter_file = str(arg)
        if opt in ("--methylation_patient_csv"):
            methylation_patient_csv = str(arg)
            
        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)

        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()


