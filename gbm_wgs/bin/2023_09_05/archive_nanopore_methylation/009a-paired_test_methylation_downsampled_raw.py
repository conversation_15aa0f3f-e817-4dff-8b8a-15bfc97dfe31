# Alec <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob, re

import scipy.stats as stats

from concurrent.futures import ProcessPoolExecutor
from statsmodels.stats import multitest


help_message = '''
Failed
'''


def differential_methylation_stats(args):
    genes, df, n_cpg_sample = args

    # seed the number of cpg sites to sample
    np.random.seed(1234)

    # results list
    res = []
    
    # # separate primary and recurrent samples
    # cols_oi = list(df.columns[df.columns.str.contains('primary')])
    # cols_oi.append('cpg')
    # primary = df.loc[:,cols_oi]
    # recurrent = df.loc[:,df.columns.str.contains('recurrent')]

    # for each gene, run paired t-test and U-test
    for gene in genes:
        sub_df = df.loc[gene,:]
        # sub_primary = primary.loc[gene,:]
        # sub_recurrent = recurrent.loc[gene,:]

        # sample cpg sites
        index_selection = np.random.choice(list(range(sub_df.index.size)), n_cpg_sample, replace=False)

        # subset to sampled cpg sites
        sub_df = sub_df.iloc[index_selection,:]
        # sub_primary = sub_primary.iloc[index_selection,:]
        # sub_recurrent = sub_recurrent.iloc[index_selection,:].to_numpy().flatten()

        # which sites
        sites = sub_df['cpg'].to_numpy()
        # sub_primary = sub_primary.iloc[:,0].to_numpy().flatten()

        # separate into primary and recurrent
        sub_primary = sub_df['primary'].to_numpy()
        sub_recurrent = sub_df['recurrent'].to_numpy()

        # run paired t-test
        # t_pval = stats.ttest_rel(primary[primary['gene'] == gene]['mean_methylation'], recurrent[recurrent['gene'] == gene]['mean_methylation'])[1]
        t_pval = stats.ttest_rel(sub_primary, sub_recurrent)[1]

        # run U-test
        u_pval = stats.mannwhitneyu(sub_primary, sub_recurrent)[1]

        # calculate fc
        fc = np.log2(np.mean(sub_recurrent) / np.max([np.mean(sub_primary), 0.1]))
        
        # append results
        res.append([gene, t_pval, u_pval, fc, ",".join(sites)])

    return pd.DataFrame(res, columns=['gene', 't_pval', 'u_pval', 'log2_mean_fc', 'cpg_sites'])


def load_df(file):
    # load methylation file
    df = pd.read_csv(file, sep='\t', header=None)

    df.columns = ['chr1', 'start1', 'stop1', 'gene', 'chr', 'start', 'stop', 'methylation']

    # set index as gene and start
    df.index = df['start'].astype(str).to_numpy() + '-' + df['gene'].to_numpy()

    # subset to methylation column and rename 
    df = df[['methylation']]

    new_name = file.split('/')[-1].split('_')[1].split("-")[0]
    df.columns = [tumor_name_dict[new_name]]

    return df


def filter_df(df):
    # filter df for na and excessive 0's
    df = df.dropna()
    df = df.loc[~(df == 0).all(axis=1),:]

    return df


def differential_promoter_methylation_analysis(regex, threads, p_cutoff = 0.05, n_cpg_sample = 10):
    # find methylation files
    files = glob.glob(regex)

    # load each file
    df1 = load_df(files[0])
    df2 = load_df(files[1])

    # merge dataframes
    df = pd.concat([df1, df2], axis=1)

    # filter df for na and excessive 0's
    df = filter_df(df)

    # set gene and cpg site, then index
    df['gene'] = df.index.str.split('-').str[1]
    df['cpg'] = df.index.str.split('-').str[0]
    df.index = df['gene'].to_numpy()

    # subset to genes with at least 5 cpg sites
    df = df.groupby('gene').filter(lambda x: x.index.size > n_cpg_sample)

    # split genes into equal length lists
    genes = np.array_split(df['gene'].unique(), threads)

    # args for parallel processing
    args = [(gene, df.loc[gene,:], n_cpg_sample) for gene in genes]

    # for each gene, run differential methylation analysis between primary and recurrent samples
    with ProcessPoolExecutor(max_workers=threads) as executor:
        res_df = list(executor.map(differential_methylation_stats, args))

    # fdr correct
    res_df = pd.concat(res_df)
    res_df['t_fdr'] = multitest.fdrcorrection(res_df['t_pval'])[1]
    res_df['u_fdr'] = multitest.fdrcorrection(res_df['u_pval'])[1]

    # classify genes as significant or not
    res_df['t_sig'] = res_df['t_fdr'] < p_cutoff
    res_df['u_sig'] = res_df['u_fdr'] < p_cutoff

    # add patient column
    patient = files[0].split('/')[-1].split('_')[0]
    res_df['patient'] = patient

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # define regex for methylation files
    regex = f'{res_dir}/analysis_nanopore/methylation/{sample}_*-promoters.bed'

    # load methylation file
    df = differential_promoter_methylation_analysis(regex, threads)

    # print summary details
    n_sig = df[df['t_sig'] | df['u_sig']].shape[0]
    print(f"Number of significant genes: {n_sig}")

    # save to file
    df.to_csv(figure_data_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    tumor_name_dict = {'tumor':'recurrent', 'normal':'primary'}

    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["res_dir=", "sample=", "figure_data_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--res_dir"):
            res_dir = str(arg)
        if opt in ("--sample"):
            sample = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()


