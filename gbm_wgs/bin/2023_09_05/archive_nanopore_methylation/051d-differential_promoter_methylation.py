# Alec <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from scipy.stats import fisher_exact
from concurrent.futures import ProcessPoolExecutor


help_message = '''
Failed
'''


def prioritize_features(df, pval_cutoff = 0.05):
    # subset to significant p-values
    df = df[df['p_value'] < pval_cutoff]

    res = []

    # iterate through genes
    for gene in df['gene'].unique():
        gene_df = df[df['gene'] == gene]

        for sample_pair in gene_df['sample'].unique():
            sample_pair_df = gene_df[gene_df['sample'] == sample_pair]

            # get the difference of the sample pair
            diff = sample_pair_df['difference'].sum()

            # get the sign of the difference
            sign = np.sign(diff)

            # get the minimum p-value associated with the difference of the same sign
            pvals = sample_pair_df[sample_pair_df['difference'] * sign > 0]['p_value']
            min_pval = pvals.min()

            res.append([gene, diff, sign, min_pval, sample_pair])

    res = pd.DataFrame(res, columns=['gene', 'sum_diff', 'sign', 'min_pval', 'sample'])

    return res


def differential_promoter_methylation_testing(args):
    input_df, sample_code = args

    stats_df = []

    for gene in input_df['gene'].unique():
        df = input_df[input_df['gene'] == gene]

        # calculate difference, log2 fold change, and p-value for each position
        primary_df = df[df['tumor_type'] == 'primary']
        recurrent_df = df[df['tumor_type'] == 'recurrent']

        # calculate percent methylation difference for each position between methylated and unmethylated
        for index, row in primary_df.iterrows():
            start = row['start']

            try:
                unmethyl_row = recurrent_df[recurrent_df['start'] == start].iloc[0].squeeze()

                # calculate p-value using fishers exact test
                primary_n_methylated = row['n_methylated']
                primary_n_unmethylated = row['n_unmethylated']
                recurrent_n_methylated = unmethyl_row['n_methylated']
                recurrent_n_unmethylated = unmethyl_row['n_unmethylated']
    
                # calculate p-value
                p_value = fisher_exact([[primary_n_methylated, primary_n_unmethylated], [recurrent_n_methylated, recurrent_n_unmethylated]])[1]

                # calculate difference in percent methylation
                percent_methylated = row['percent_methylated']
                unmethylated_percent_methylated = unmethyl_row['percent_methylated']

                difference = percent_methylated - unmethylated_percent_methylated

                # calculate log2 fold change
                log2_fc = np.log2((percent_methylated + 1) / (unmethylated_percent_methylated + 1))
    
                stats_df.append([gene, start, p_value, difference, log2_fc])
            
            except:
                pass

    stats_df = pd.DataFrame(stats_df, columns=['gene', 'start', 'p_value', 'difference', 'log2_fc'])

    # add sample code
    stats_df['sample_code'] = sample_code

    # prioritize p-values
    res_df = prioritize_features(stats_df)

    return res_df, stats_df


def differential_methylation(promoter_methylation_combined_file, threads):
    # load df
    input_df = pd.read_csv(promoter_methylation_combined_file, sep='\t')

    # process each sample_code
    args = [(input_df[input_df['sample_code'] == sample_code], sample_code) for sample_code in input_df['sample_code'].unique()]

    res_df = []
    res_stats_df = []

    # calculate p-values per gene using multi-threads
    with ProcessPoolExecutor(max_workers=threads) as executor:
        results = executor.map(differential_promoter_methylation_testing, args)

        for res in results:
            df, stats_df = res
            res_df.append(df)
            res_stats_df.append(stats_df)

    # concatenate
    res_df = pd.concat(res_df)
    res_stats_df = pd.concat(res_stats_df)

    return res_df, res_stats_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load files
    df = differential_methylation(promoter_methylation_combined_file, threads)

    # perform feature prioritization
    df, stats_df = prioritize_features(df)

    # save to files
    df.to_csv(promoter_methlyation_results_file, sep='\t', index=False)
    stats_df.to_csv(promoter_prioritized_stats_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # default threads
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["promoter_methylation_combined_file=", "promoter_methlyation_results_file=", "promoter_prioritized_stats_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--promoter_methylation_combined_file"):
            promoter_methylation_combined_file = str(arg)

        if opt in ("--promoter_methlyation_results_file"):
            promoter_methlyation_results_file = str(arg)
        if opt in ("--promoter_prioritized_stats_file"):
            promoter_prioritized_stats_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()




