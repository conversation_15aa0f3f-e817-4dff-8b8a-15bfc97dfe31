# <PERSON>
library(optparse)
library(ggplot2)
library(ggrastr)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




density_plot = function(input_df){

# subset to sample
patient_id = unique(input_df$patient_id)[1]
    
# density polygons
p = ggplot(df, aes(x = primary, y = recurrent)) + plot_theme() +
  stat_density_2d(aes(fill = after_stat(level)), geom = "polygon", color='black', 
                  contour = TRUE, bins = 50) + # Increased bins for more levels  scale_fill_gradientn(colours = c("#1d00bf", "yellow"), 
  
    scale_fill_gradientn(colours = c("#1d00bf", "yellow"), 
        name = "Density Level") +
    
ggtitle(patient_id) +
xlab('Primary methylation rate') + ylab('Recurrent methylation rate') +
theme(axis.text.x = element_text(angle = 0))

print(p)

    
# contours
p = ggplot(df, aes(x = primary, y = recurrent)) + plot_theme() +
  stat_density_2d(aes(color = after_stat(level)), geom = "contour", bins = 50) +
  scale_color_gradientn(colours = c("#1d00bf", "yellow"), 
                        name = "Density Level") +
  
ggtitle(patient_id) +
xlab('Primary methylation rate') + ylab('Recurrent methylation rate') + 
theme(axis.text.x = element_text(angle = 0))

print(p)


# dotplots
p = ggplot(df, aes(x = primary, y = recurrent)) + plot_theme() +
rasterize(geom_point(alpha = 0.01), dpi=350)  +
  
ggtitle(patient_id) +
xlab('Primary methylation rate') + ylab('Recurrent methylation rate') + 
theme(axis.text.x = element_text(angle = 0))

print(p)


return()

}




pdf(opt$figure_file)

# load df
df = read.csv(opt$figure_data_file, sep='\t')


# apply to all patients
density_plot(df)


dev.off()


print(opt$figure_file)




