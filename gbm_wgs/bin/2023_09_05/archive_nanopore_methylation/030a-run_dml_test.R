# Adopted from epi2me wf-somatic_variation v1.1.0

library(optparse)
library(DSS)
require(bsseq)
require(data.table)

# Disable scientific notation
options(scipen=999)

# options list for parser options
option_list <- list(
    make_option(c("-a","--tumor_bed_file"), type="character", default=NULL,
            help="",
            dest="tumor_bed_file"),
    make_option(c("-b","--normal_bed_file"), type="character", default=NULL,
            help="",
            dest="normal_bed_file"),
    make_option(c("-c","--dml_results_file"), type="character", default=NULL,
            help="",
            dest="dml_results_file"),
    make_option(c("-d","--threads"), type="character", default=NULL,
            help="",
            dest="threads")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)



# Import data and create input structure
BSobj <- makeBSseqData( 
    list(
        fread(opt$tumor_bed_file, sep = '\t', header = T),
        fread(opt$normal_bed_file, sep = '\t', header = T)
    ), c("Tumor", "Normal")
)

# DML testing
dmlTest = DMLtest(BSobj, 
    group1=c("Tumor"), 
    group2=c("Normal"),
    equal.disp = FALSE,
    smoothing=TRUE,
    smoothing.span=500,
    ncores=opt$threads)

# Write output files
write.table(dmlTest, opt$dml_results_file, sep='\t', quote=F, col.names=T, row.names=F)


