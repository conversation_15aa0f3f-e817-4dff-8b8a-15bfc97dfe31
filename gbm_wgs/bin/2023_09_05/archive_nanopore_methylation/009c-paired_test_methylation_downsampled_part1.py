# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob


help_message = '''
Failed
'''


def sample_indices_by_category(df, category_col, n_samples):
    # Group by the category column
    grouped = df.groupby(category_col).apply(lambda x: x.sample(n=min(n_samples, len(x))).index)
    
    # Flatten the result and convert to a list
    sampled_indices = [index for sublist in grouped for index in sublist]
    
    return sampled_indices


def load_df(file):
    # load methylation file
    df = pd.read_csv(file, sep='\t', header=None)

    df.columns = ['chr1', 'start1', 'stop1', 'gene', 'chr', 'start', 'stop', 'methylation']

    # set index as gene and start
    df.index = df['start'].astype(str).to_numpy() + '-' + df['gene'].to_numpy()

    # subset to methylation column and rename 
    df = df[['methylation']]

    new_name = file.split('/')[-1].split('_')[1].split("-")[0]
    df.columns = [tumor_name_dict[new_name]]

    return df


def filter_df(df):
    # filter df for na and excessive 0's
    df = df.dropna()
    df = df.loc[~(df == 0).all(axis=1),:]

    return df


def differential_promoter_methylation_analysis(regex, promoter_all_cpg_file, n_cpg_sample = 10):
    # find methylation files
    files = glob.glob(regex)

    # load each file
    df1 = load_df(files[0])
    df2 = load_df(files[1])

    # merge dataframes
    df = pd.concat([df1, df2], axis=1)

    # filter df for na and excessive 0's
    df = filter_df(df)

    # set gene and cpg site, then index
    df['gene'] = df.index.str.split('-').str[1]
    df['cpg'] = df.index.str.split('-').str[0]

    # save to file
    df.to_csv(promoter_all_cpg_file, sep='\t', index=False)

    # subset to genes with at least n cpg sites
    df = df.groupby('gene').filter(lambda x: x.index.size >= n_cpg_sample)

    # reset index
    df.index = df.reset_index().index

    # sample n_cpg_sample cpg sites for each gene
    sampled_indices = sample_indices_by_category(df, 'gene', n_cpg_sample)

    # subset to sampled indices
    df = df.loc[sampled_indices,:]

    # add patient column
    patient = files[0].split('/')[-1].split('_')[0]
    df['patient'] = patient

    print(df.head())
    print(df.shape)
    print(len(df['gene'].unique()))

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # define regex for methylation files
    regex = f'{res_dir}/analysis_nanopore/methylation/{sample}_*-promoters.bed'

    # load methylation file
    df = differential_promoter_methylation_analysis(regex, promoter_all_cpg_file)

    # save to file
    df.to_csv(figure_data_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    tumor_name_dict = {'tumor':'recurrent', 'normal':'primary'}

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["res_dir=", "sample=", "promoter_all_cpg_file=", "figure_data_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--res_dir"):
            res_dir = str(arg)
        if opt in ("--sample"):
            sample = str(arg)

        if opt in ("--promoter_all_cpg_file"):
            promoter_all_cpg_file = str(arg)
        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)

    main()


