library(optparse)
library(forcats)
library(ggplot2)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}






boxplot = function(input_df, stats_df){

input_df$sample_type = factor(input_df$sample_type, levels = c("primary", "recurrent", "all"))

sub_stats_df = stats_df[stats_df$test_type == 'mannwhitney',]
p_value = signif(sub_stats_df$p_value[1], 3)

# all combined
p = ggplot(input_df, aes(x = sample_type, y = correlation)) + plot_theme() +
geom_boxplot() +

ggtitle(paste0('Mann-whitney U test p = ', p_value)) +
xlab("") + ylab('Spearman rho')


print(p)
}



barplot = function(input_df, stats_df){

input_df$sample_type = factor(input_df$sample_type, levels = c("primary", "recurrent", "all"))

sub_stats_df = stats_df[stats_df$test_type == 't_test',]
p_value = signif(sub_stats_df$p_value[1], 3)

# all combined
p = ggplot(input_df, aes(x = sample_number, y = correlation, fill = sample_type)) + plot_theme() +
geom_bar(stat = "identity", position = position_dodge(width = 0.5)) +

ggtitle(paste0('Paired t-test p = ', p_value)) +
xlab("Sample") + ylab('Spearman rho')


print(p)
}





pdf(opt$figure_file)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')


# plot
boxplot(input_df, stats_df)
barplot(input_df, stats_df)


dev.off()


print(opt$figure_file)






