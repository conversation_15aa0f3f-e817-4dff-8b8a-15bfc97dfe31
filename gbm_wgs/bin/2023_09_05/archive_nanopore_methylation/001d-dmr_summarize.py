# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''


def load_dmr_file(dmr_bed_file, addition_to_visualize = 500000):
    # sample name
    sample = dmr_bed_file.split('/')[-1].split('-')[0]

    # load df
    dmr_df = pd.read_csv(dmr_bed_file, sep='\t')

    # classify based on whether the DMR is hypermethylated or hypomethylated
    dmr_df['class'] = np.where(dmr_df['diff.Methy'] > 0, 'hyper', 'hypo')

    # # shift start and add to end of DMR
    # dmr_df['start'] = dmr_df['start'] - (addition_to_visualize / 2)
    # dmr_df['end'] = dmr_df['end'] + (addition_to_visualize / 2)

    # # add length
    # dmr_df['length'] = dmr_df['end'] - dmr_df['start']
    
    # add sample name
    dmr_df['sample'] = sample

    # reorder columns
    dmr_df = dmr_df[['chr', 'start', 'end', 'length', 'diff.Methy', 'class', 'sample']]

    return dmr_df


def merge_dfs(methylation_files):
    # list of dfs
    res_df = []

    for file_path in methylation_files:
        df = load_dmr_file(file_path)
        res_df.append(df)

    # merge all DataFrames into a single DataFrame
    res_df = pd.concat(res_df, ignore_index=True)

    return res_df



def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # dmr files
    dmr_regex = os.path.join(methylation_res_dir, '*/*primary/mod/5mC/DMR/*5mC.dmr.tsv')
    dmr_files = glob.glob(dmr_regex)

    # load DMRs
    dmr_df = merge_dfs(dmr_files)

    # save to files
    dmr_df.to_csv(dmr_results_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["methylation_res_dir=", "dmr_results_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--methylation_res_dir"):
            methylation_res_dir = str(arg)

        if opt in ("--dmr_results_file"):
            dmr_results_file = str(arg)
            
    main()




