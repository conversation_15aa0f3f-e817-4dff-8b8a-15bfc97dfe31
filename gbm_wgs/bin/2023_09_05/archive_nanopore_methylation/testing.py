# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from concurrent.futures import ProcessPoolExecutor

from scipy import stats
from statsmodels.stats import multitest

import statsmodels.api as sm


help_message = '''
Failed
'''

def differential_methylation_lm_stats(args):
    genes, df = args

    # for each gene, run differential methylation analysis using linear models
    res = []

    for gene in genes:
        # subset to gene and patient names
        sub_df = df.loc[gene,:]

        # define dependent and independent variables
        y = sub_df['methylation']

        # one hot encode cpg_site and patient
        x0 = sm.add_constant(pd.get_dummies(sub_df['cpg_site'], columns = ['cpg_site']))
        x1 = pd.get_dummies(sub_df['patient'], columns = ['patient'])
        x1 = pd.concat([x0, x1], axis=1)

        # fit linear models
        model0 = sm.OLS(y, x0).fit()
        model1 = sm.OLS(y, x1).fit()

        # compare models
        f_test = model1.compare_f_test(model0)

        # get the number of cpg sites
        n_cpg = sub_df.index.size

        # append results
        res.append([gene, f_test[1], n_cpg])
    
    # create df
    res_df = pd.DataFrame(res, columns=['gene', 'p_value', 'n_cpg_sites'])

    return res_df


def run_lm_test(file, threads):
    # load df
    df = pd.read_csv(file, sep='\t', index_col=0)
    
    # sort columns by sample
    df = df.sort_index(axis=1)
    
    # separate into primary and recurrent samples
    primary = df.loc[:, df.columns.str.contains('normal')]
    recurrent = df.loc[:, df.columns.str.contains('tumor')]
    
    # rename columns
    primary.columns = [col.split('_')[0] for col in primary.columns]
    recurrent.columns = [col.split('_')[0] for col in recurrent.columns]
    
    # create new df of difference in gene methylation between primary and recurrent samples
    df = recurrent - primary
    
    # save memory
    del recurrent
    del primary
    
    # melt df
    df = df.reset_index().melt(id_vars='index', var_name='patient', value_name='methylation')
    
    # reset index
    df['cpg_site'] = df['index'].str.split("-").str[0]
    df.index = df['index'].str.split("-").str[1].to_numpy()
    df['gene'] = df.index.to_numpy()
    df = df.drop('index', axis=1)    

    # split genes into equal length lists
    genes = np.array_split(df['gene'].unique(), threads)

    # args for parallel processing
    args = [(gene, df.loc[gene,]) for gene in genes]

    # for each gene, run differential methylation analysis using linear models
    with ProcessPoolExecutor(max_workers=threads) as executor:
        res_df = list(executor.map(differential_methylation_lm_stats, args))

    # Concatenate all DataFrames
    res_df = pd.concat(res_df)

    # multi-test correction
    res_df['fdr'] = multitest.fdrcorrection(res_df['p_value'])[1]

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # process methylation by gene file
    res_df = run_lm_test(methylation_by_gene_promoter_file, threads)

    # save to file
    res_df.to_csv(figure_stats_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["methylation_by_gene_promoter_file=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--methylation_by_gene_promoter_file"):
            methylation_by_gene_promoter_file = str(arg)
            
        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()


