# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import scipy.stats as stats
import subprocess, re

help_message = '''
Failed
'''


def create_gene_translation_dict(gene_translation_file):
    # load gene translation file
    gene_translation_df = pd.read_csv(gene_translation_file, sep='\t')
    
    # create dictionary
    gene_translation_dict = dict(zip(gene_translation_df['Gene stable ID'], gene_translation_df['Gene name']))
    
    return gene_translation_dict


def load_gene_expression_file(gene_expression_file, gene_translation_file):
    # load gene expression file
    gene_expression_df = pd.read_csv(gene_expression_file, sep='\t', index_col=0)
    
    # load gene translation dict
    gene_translation_dict = create_gene_translation_dict(gene_translation_file)


    # translate gene expression file index
    gene_expression_df.index = gene_expression_df.index.str.split(".").str[0].map(gene_translation_dict)

    # drop rows with missing gene names
    mask = gene_expression_df.index.astype("<U64") != 'nan'
    gene_expression_df = gene_expression_df[mask]

    # remove duplicates
    gene_expression_df = gene_expression_df[~gene_expression_df.index.duplicated(keep='first')]

    # rename columns
    gene_expression_df.columns = gene_expression_df.columns.str.split("_").str[0]
    
    return gene_expression_df


def process_methylation_per_gene(input_df):
    # the number of times a gene appears
    value_counts = input_df.index.value_counts()

    # Filter strings that appear more than once
    duplicate_genes = value_counts[value_counts > 1].index.tolist()
    single_genes = value_counts[value_counts == 1].index.tolist()

    # create empty df to store results
    res_df = input_df.loc[single_genes,:]

    # process each sample independently
    for gene in duplicate_genes:
        # if all classes are the same, keep the first
        if len(np.unique(input_df.loc[gene,'methylation_class'])) == 1:
            res_df = pd.concat([res_df, input_df.loc[gene,:].iloc[[0],:]])

        # if there are two classes, keep the one with the highest differential methylation
        else:
            res_df = pd.concat([res_df, input_df.loc[gene,:].sort_values('differential_methylation', ascending=False).iloc[[0],:]])

    return res_df


def correlate_expression_with_methylation(intersected_bed_file, gene_expression_df, dmr_promoter_results_file):
    # load methylation by gene file
    dmr_methylation_df = pd.read_csv(intersected_bed_file, sep='\t', header=None)
    dmr_methylation_df.columns = ['chr', 'promoter_start', 'promoter_end', 'gene', 'dmr_chr', 'start', 'end', 'length', 'differential_methylation', 'methylation_class', 'sample']

    dmr_methylation_df.to_csv(dmr_promoter_results_file, sep='\t', index=False)
    
    # create empty df to store results
    res_df = []

    # get common samples
    samples = dmr_methylation_df['sample'].unique()

    # remove RLGS2 and RLGS7 because the RNA extraction failed for either primary or recurrent of these patients
    samples = [sample for sample in samples if not re.search('RLGS2|RLGS7', sample)]

    # process each sample independently
    for sample in samples:
        # sample methylation and gene df
        methyl_sample_df = dmr_methylation_df[dmr_methylation_df['sample'] == sample]
        methyl_sample_df.index = methyl_sample_df['gene'].to_numpy()

        mask = gene_expression_df.columns.str.split("-").str[0] == sample
        expression_sample_df = gene_expression_df.loc[:,mask]

        # process methylation df
        methyl_sample_df = process_methylation_per_gene(methyl_sample_df)

        # process hyper and hypo separately
        for methylation_class in ['hyper', 'hypo']:
            # subset methylation df to methylation class
            methyl_sample_df_class = methyl_sample_df[methyl_sample_df['methylation_class'] == methylation_class]

            # subset to common genes
            common_genes = np.intersect1d(expression_sample_df.index.astype("<U64"), methyl_sample_df_class.index.astype("<U64"))

            if len(common_genes) > 0:
                sub_expression_sample_df = expression_sample_df.loc[common_genes,:]
                methyl_sample_df_class = methyl_sample_df_class.loc[common_genes,:]
    
                # rename expression columns
                sub_expression_sample_df.columns = sub_expression_sample_df.columns.str.split("_").str[0].str.split("-").str[1]
    
                # subtract primary from recurrent
                sub_expression_sample_df['difference'] = sub_expression_sample_df['recurrent'] - sub_expression_sample_df['primary']
    
                # remove genes with missing values
                common_genes = common_genes[~sub_expression_sample_df.loc[common_genes,'difference'].isna()]
    
                sub_expression_sample_df = sub_expression_sample_df.loc[common_genes,:]
                methyl_sample_df_class = methyl_sample_df_class.loc[common_genes,:]
                
                # calculate spearman correlation
                r, p = stats.spearmanr(sub_expression_sample_df['difference'], methyl_sample_df_class['differential_methylation'])
    
                # add the number of genes per measurement
                n_genes = len(common_genes)
    
                # add to df
                res_df.append([sample, methylation_class, r, p, n_genes])

    # create df
    res_df = pd.DataFrame(res_df, columns=['sample', 'methylation_class', 'correlation', 'p_value', 'n_genes'])

    print(res_df)

    return res_df



def subset_df(df):
    # Group by sample and class, count n_genes
    grouped = df.groupby(['sample', 'methylation_class'])['n_genes'].sum().reset_index()
    
    # Filter for samples with >10 n_genes for both classes
    filtered_samples = grouped.groupby('sample').filter(lambda x: (x['n_genes'] > 10).all())
    
    # List of samples with >10 n_genes for both hyper and hypo classes
    samples_list = filtered_samples['sample'].unique()

    return df[np.isin(df['sample'], samples_list)]


def calculate_stats(input_df):
    # create empty df to store results
    stats_df = []

    # subset df to samples with sufficient genes
    df = subset_df(input_df)
    
    # run mann whitney u test
    hypomethylation_r_values = df[df['methylation_class'] == 'hypo']['correlation']
    hypermethylation_r_values = df[df['methylation_class'] == 'hyper']['correlation']

    test_stat, p = stats.mannwhitneyu(hypomethylation_r_values, hypermethylation_r_values)

    stats_df.append([test_stat,p,'mannwhitney'])

    # Perform t-test assuming equal variances
    test_stat, p = stats.ttest_rel(hypomethylation_r_values, hypermethylation_r_values)

    stats_df.append([test_stat,p,'t_test'])

    # create df
    stats_df = pd.DataFrame(stats_df, columns=['statistic', 'p_value', 'test_type'])

    return stats_df



def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load gene expression file and translate to gene names
    gene_expression_df = load_gene_expression_file(gene_expression_file, gene_translation_file)

    # load methylation by gene file and find correlation
    res_df = correlate_expression_with_methylation(intersected_bed_file, gene_expression_df, dmr_promoter_results_file)

    # run stats on primary vs recurrent
    stats_df = calculate_stats(res_df)

    # write to file
    res_df.to_csv(figure_data_file, sep='\t', index=False)
    stats_df.to_csv(figure_stats_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["gene_expression_file=", "gene_translation_file=", "intersected_bed_file=", "r_script=", "dmr_promoter_results_file=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--gene_expression_file"):
            gene_expression_file = str(arg)
        if opt in ("--gene_translation_file"):
            gene_translation_file = str(arg)
        if opt in ("--intersected_bed_file"):
            intersected_bed_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--dmr_promoter_results_file"):
            dmr_promoter_results_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)

    main()


