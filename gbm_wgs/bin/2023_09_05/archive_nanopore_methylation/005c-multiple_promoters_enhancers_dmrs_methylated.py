# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from scipy import stats
import subprocess
from statsmodels.stats import multitest


help_message = '''
Failed
'''

def fishers_exact_test_combined(intersect_df, double_promoter_df, single_promoter_dmr_df, promoter_df):
    # calculate the number of overlaps between dmr and promoter regions
    n_dmr_promoter = len(np.unique(np.concatenate([intersect_df['gene'], intersect_df['gene2']])))

    # calculate the number of double promoters that do not overlap with dmr regions
    n_no_dmr_promoter = np.sum(~np.isin(np.unique(np.concatenate([double_promoter_df['gene'], double_promoter_df['gene2']])), np.concatenate([intersect_df['gene'], intersect_df['gene2']])))

    # calculate the number of single promoter dmr regions that do not overlap with double promoters
    n_dmr_no_promoter = np.sum(~np.isin(single_promoter_dmr_df['gene'], np.concatenate([intersect_df['gene'], intersect_df['gene2']])))

    # calculate the number of promoters that do not overlap
    n_no_dmr_no_promoter = promoter_df['gene'].unique().shape[0] - n_dmr_promoter - n_no_dmr_promoter - n_dmr_no_promoter

    # create the contingency table
    contingency_table = np.array([[n_dmr_promoter, n_no_dmr_promoter], [n_dmr_no_promoter, n_no_dmr_no_promoter]])

    # run fisher's exact test
    oddsratio, pvalue = stats.fisher_exact(contingency_table)

    # calculate the ratio of double to single promoters that have dmrs
    double_to_single_ratio = n_dmr_promoter / n_dmr_no_promoter

    return pvalue, double_to_single_ratio


def fishers_exact_test(args):
    sample, intersect_df, double_promoter_df, single_promoter_dmr_df, promoter_df = args

    # subset to the sample
    intersect_df = intersect_df[intersect_df['sample'] == sample]
    single_promoter_dmr_df = single_promoter_dmr_df[single_promoter_dmr_df['sample'] == sample]

    # calculate the number of overlaps between dmr and promoter regions
    n_dmr_promoter = len(np.unique(np.concatenate([intersect_df['gene'], intersect_df['gene2']])))

    # calculate the number of double promoters that do not overlap with dmr regions
    n_no_dmr_promoter = np.sum(~np.isin(np.concatenate([double_promoter_df['gene'], double_promoter_df['gene2']]), np.concatenate([intersect_df['gene'], intersect_df['gene2']])))

    # calculate the number of single promoter dmr regions that do not overlap with double promoters
    n_dmr_no_promoter = np.sum(~np.isin(single_promoter_dmr_df['gene'], np.concatenate([intersect_df['gene'], intersect_df['gene2']])))

    # calculate the number of promoters that do not overlap
    n_no_dmr_no_promoter = promoter_df['gene'].unique().shape[0] - n_dmr_promoter - n_no_dmr_promoter - n_dmr_no_promoter

    # create the contingency table
    contingency_table = np.array([[n_dmr_promoter, n_no_dmr_promoter], [n_dmr_no_promoter, n_no_dmr_no_promoter]])

    # run fisher's exact test
    oddsratio, pvalue = stats.fisher_exact(contingency_table)

    # calculate the ratio of double to single promoters that have dmrs
    double_to_single_ratio = n_dmr_promoter / n_dmr_no_promoter

    return sample, pvalue, double_to_single_ratio


def analyze_overlaps(dmr_double_promoter_file, single_promoter_file, double_promoter_file, promoter_file):
    # Read the intersected bed file
    double_dmr_df = pd.read_csv(dmr_double_promoter_file, sep='\t', header=None)
    double_dmr_df.columns = ['chr', 'start', 'end', 'gene', 'gene2', 'overlap', 'attributes_1', 'attributes_2', 'chr_dmr', 'start_dmr', 'end_dmr', 'dmr_length', 'methylation_diff', 'methyl_class', 'sample']

    # load single and double promoter and dmr dfs
    single_dmr_df = pd.read_csv(single_promoter_file, sep='\t')

    # double promoter df
    double_promoter_df = pd.read_csv(double_promoter_file, sep='\t', header=None)
    double_promoter_df.columns = ['chr', 'start', 'end', 'gene', 'gene2', 'overlap', 'attributes_1', 'attributes_2']

    # load dmr df
    promoter_df = pd.read_csv(promoter_file, sep='\t', compression='gzip')

    # run fisher's exact test for all samples: dmr region vs promoter region
    pval, double_to_single_ratio = fishers_exact_test_combined(double_dmr_df, double_promoter_df, single_dmr_df, promoter_df)

    # run fisher's exact test for each sample individually
    args = [(sample, double_dmr_df, double_promoter_df, single_dmr_df, promoter_df) for sample in double_dmr_df['sample'].unique()]

    # run fisher's exact test for each sample
    res_df = pd.DataFrame([fishers_exact_test(arg) for arg in args], columns=['sample', 'pvalue', 'double_to_single_ratio'])

    # add the combined pvalue to the df
    res_df.loc[res_df.shape[0]] = ['combined', pval, double_to_single_ratio]

    # fdr
    res_df['fdr'] = multitest.fdrcorrection(res_df['pvalue'])[1]
    
    # log transform the pvalues
    res_df['log_pvalue'] = -np.log10(res_df['pvalue'])
    res_df['log_fdr'] = -np.log10(res_df['fdr'])

    # add significance column
    res_df['pval_significant'] = res_df['pvalue'] < 0.05
    res_df['fdr_significant'] = res_df['fdr'] < 0.05

    # add pseudo y-val
    res_df['test'] = 'fishers_exact'

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # process the promoter overlaps
    df = analyze_overlaps(intersected_bed_file, single_promoter_file, double_promoter_file, promoter_file)

    # save to file
    df.to_csv(figure_data_file, sep='\t', index=False)
    
    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["intersected_bed_file=", "single_promoter_file=", "double_promoter_file=", "promoter_file=", "r_script=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--intersected_bed_file"):
            intersected_bed_file = str(arg)
        if opt in ("--single_promoter_file"):
            single_promoter_file = str(arg)
        if opt in ("--double_promoter_file"):
            double_promoter_file = str(arg)
        if opt in ("--promoter_file"):
            promoter_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)

            
    main()




