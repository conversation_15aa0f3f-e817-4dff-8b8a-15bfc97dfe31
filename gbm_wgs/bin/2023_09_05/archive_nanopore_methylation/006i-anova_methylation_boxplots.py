# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob, subprocess

help_message = '''
Failed
'''

def calculate_methylation_difference(df):
    # Create a MultiIndex from gene, cpg_start, and patient
    df.set_index(['gene', 'cpg_start', 'patient'], inplace=True)

    # Separate into primary and recurrent
    primary = df[df['tumor'] == 'primary'].copy()
    recurrent = df[df['tumor'] == 'recurrent'].copy()

    # Ensure indices are the same
    if not primary.index.equals(recurrent.index):
        raise ValueError("Primary and recurrent indices do not match.")

    # Align the indices and add methylation column of recurrent to primary
    primary = primary.join(recurrent['methylation'], rsuffix='_recurrent')

    # Calculate the methylation difference
    primary['methylation_difference'] = np.where(
        primary['methylation'] > primary['methylation_recurrent'],
        'Decrease',
        'Increase'
    )

    # Reset index to prepare for rejoining with the original df
    primary.reset_index(inplace=True)

    # Add the calculated methylation difference back to the original df
    df = df.reset_index()
    df = df.merge(primary[['gene', 'cpg_start', 'patient', 'methylation_difference']], 
                  on=['gene', 'cpg_start', 'patient'], 
                  how='left')

    return df


def add_mean_methylation_difference(df, mean_methylation_file):
    # load mean methylation df
    mean_df = pd.read_csv(mean_methylation_file, sep='\t')

    # calculate difference for each patient
    primary = mean_df[mean_df['tumor'] == 'primary'].copy()
    recurrent = mean_df[mean_df['tumor'] == 'recurrent'].copy()

    # Ensure indices are the same
    primary.index = primary['sample_id'] + "_" + primary['gene']
    recurrent.index = recurrent['sample_id'] + "_" + recurrent['gene']

    # subtract the primary from the recurrent
    recurrent['difference'] = np.round(recurrent['mean_methylation'] - primary['mean_methylation'], 3)

    # create a new column with the mean methylation difference
    recurrent['sample_gene'] = recurrent['sample'] + "_" + recurrent['gene']
    methylation_mapping = recurrent.set_index('sample_gene')['difference'].to_dict()

    # create a new column with the mean methylation difference
    df['sample_gene'] = df['patient'] + "_" + df['gene']

    # map the mean methylation difference
    df['mean_methylation_difference'] = df['sample_gene'].map(methylation_mapping)

    print(df)

    # add a column being the patient and mean methylation difference
    df['patient_mean_methylation_difference'] = df['patient'] + "_" + df['mean_methylation_difference'].astype(str)

    return df


def load_raw_df(file, mean_methylation_file, methylation_patient_csv):
    # load df
    df = pd.read_csv(file, sep='\t', index_col=0)

    # subset to patients in methylation_patient_csv
    patients = methylation_patient_csv.split(",")
    df = df.loc[:,np.isin(df.columns.str.split("_").str[0], patients)]
    
    # add details
    df['gene'] = df.index.str.split("-").str[1]
    df['cpg_start'] = df.index.str.split("-").str[0]
    
    # melt df
    df = df.melt(id_vars=['gene', 'cpg_start'], var_name='patient_tumor', value_name='methylation')

    # replace "normal" with "primary" and "tumor" with "recurrent"
    df['patient_tumor'] = df['patient_tumor'].str.replace("normal", "primary")
    df['patient_tumor'] = df['patient_tumor'].str.replace("tumor", "recurrent")
    
    # rename columns
    df['patient'] = df['patient_tumor'].str.split("_").str[0]
    df['tumor'] = df['patient_tumor'].str.split("_").str[1]

    # calculate methylation difference
    df = calculate_methylation_difference(df)
    
    # add mean methylation difference
    df = add_mean_methylation_difference(df, mean_methylation_file)

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load raw methylation df
    meth_df = load_raw_df(raw_methylation_file, mean_methylation_file, methylation_patient_csv)

    # save results file
    meth_df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["raw_methylation_file=", "mean_methylation_file=", "methylation_patient_csv=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--raw_methylation_file"):
            raw_methylation_file = str(arg)
        if opt in ("--mean_methylation_file"):
            mean_methylation_file = str(arg)
        if opt in ("--methylation_patient_csv"):
            methylation_patient_csv = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)


    main()




