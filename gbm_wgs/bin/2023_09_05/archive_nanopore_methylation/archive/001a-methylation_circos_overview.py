# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import re, glob, subprocess
from concurrent.futures import ProcessPoolExecutor

help_message = '''
Failed
'''


def multi_thread_analyze_methylation(args):
    primary_file, interval_size, window_size = args

    # columns
    columns = ['chrom', 'start', 'end', 'modified base code and motif', 'score', 'strand', 'start position', 'end position', 'color', 'Nvalid_cov', 'percent modified', 'Nmod', 'Ncanonical', 'Nother_mod', 'Ndelete', 'Nfail', 'Ndiff', 'Nnocall']
    res_df = []

    # sample name 
    sample = primary_file.split('/')[-1].split('.')[1].split("_")[0]
    recurrent_file = re.sub("_normal", "_tumor", primary_file)

    # for each file, load as df and subset to relevant columns
    primary_df = pd.read_csv(primary_file, sep='\t', header=None, compression='gzip', names=columns)
    primary_df = primary_df[cols_oi]

    print(primary_df.head())

    recurrent_df = pd.read_csv(recurrent_file, sep='\t', header=None, compression='gzip', names=columns)

    print(recurrent_df.head())

    # subset to relevant columns
    cols_oi = ['chrom', 'start', 'end', 'percent modified']

    recurrent_df = recurrent_df[cols_oi]

    # subtract primary from recurrent
    recurrent_df['percent modified'] = recurrent_df['percent modified'] - primary_df['percent modified']

    # Iterate over each chromosome in primary_df
    for chrom in recurrent_df['chrom'].unique():
        # Subset to data for the current chromosome in recurrent_df
        chrom_df = recurrent_df.loc[recurrent_df['chrom'] == chrom, ['start position', 'percent modified']]
        
        # Calculate average percent modified over intervals of size interval_size
        chrom_df = chrom_df.groupby(chrom_df['start position'] // interval_size)['percent modified'].mean().reset_index()
        
        # Add chromosome information to the result DataFrame
        chrom_df['chrom'] = chrom
        chrom_df.columns = ['start', 'percent modified', 'chrom']

        # add end position for bed file consistency
        chrom_df['end'] = chrom_df['start']

        # Calculate rolling average over window_size
        chrom_df['rolling_avg'] = chrom_df['percent modified'].rolling(window=window_size, min_periods=1).mean()

        # add sample name
        chrom_df['sample'] = sample

        # reorder columns
        chrom_df = chrom_df[['sample', 'chrom', 'start', 'end', 'percent modified', 'rolling_avg']]
        
        # Append the results for the current chromosome to res_df
        res_df.append(chrom_df)

    # Concatenate all results into a single DataFrame
    res_df = pd.concat(res_df)

    return res_df


def calculate_average_methylation(primary_bed_files, interval_size=200000, window_size=10, threads=1):
    # multi-thread args
    args = [(file, interval_size, window_size) for file in primary_bed_files]

    print(args)

    # multi-thread process files
    with ProcessPoolExecutor(max_workers=threads) as executor:
        res_df = pd.concat(list(executor.map(multi_thread_analyze_methylation, args)))

    return res_df


def load_dmr_file(dmr_bed_files, addition_to_visualize = 500000):
    # results df
    res_df = []

    # iterate through each DMR file
    for file in dmr_bed_files:
        # sample name
        sample = file.split('/')[-1].split('.')[1].split("_")[0]

        # load df
        dmr_df = pd.read_csv(file, sep='\t')

        # classify based on whether the DMR is hypermethylated or hypomethylated
        dmr_df['class'] = np.where(dmr_df['diff.Methy'] > 0, 'hyper', 'hypo')

        # shift start and add to end of DMR
        dmr_df['start'] = dmr_df['start'] - (addition_to_visualize / 2)
        dmr_df['end'] = dmr_df['end'] + (addition_to_visualize / 2)

        # add sample name
        dmr_df['sample'] = sample

        # reorder columns
        dmr_df = dmr_df[['sample', 'chrom', 'start', 'end', 'diff.Methy', 'class']]

        # Append the results for the current DMR file to res_df
        res_df.append(dmr_df)

    # Concatenate all results into a single DataFrame
    res_df = pd.concat(res_df)

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # list of primary and recurrent methylation bed files
    primary_methylation_bed_file_regex = res_dir + "/*/*/mod/5mC/bedMethyl/*normal*"
    primary_bed_files = glob.glob(primary_methylation_bed_file_regex)

    primary_bed_files = primary_bed_files[:1]

    # calculate average difference between recurrent and primary methylation over 200,000 bp window
    methylation_df = calculate_average_methylation(primary_bed_files, threads=threads)

    # load DMRs
    dmr_bed_file_regex = res_dir + "/*/*/mod/5mC/DMR/*.5mC.dmr.tsv"
    dmr_bed_files = glob.glob(dmr_bed_file_regex)

    dmr_df = load_dmr_file(dmr_bed_files)

    # save to files
    methylation_df.to_csv(figure_data_file, sep='\t', index=False)
    dmr_df.to_csv(figure_stats_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_stats_file', figure_stats_file, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    # subprocess.run(cline)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["res_dir=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--res_dir"):
            res_dir = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
        if opt in ("--threads"):
            threads = str(arg)
            
    main()




