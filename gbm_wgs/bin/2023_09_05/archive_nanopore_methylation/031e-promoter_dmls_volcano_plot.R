# <PERSON>
library(optparse)
library(ggplot2)
library(ggrepel)
library(ggrastr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}






# plot features
axis_size = 10
title_size = 12

pval_cutoff = 0.05
logfc_cutoff = 0.1



volcano_plot = function(sample, input_df){

input_df = input_df[input_df$sample == sample,]


# separate into significant and non-significant genes
nonsig_df = input_df[input_df$pval >= pval_cutoff,]
sig_df = input_df[input_df$pval < pval_cutoff,]


# separate into genes past mean_diff logfc_cutoff and -logfc_cutoff
sigpval_df = sig_df[sig_df$mean_diff <= logfc_cutoff & sig_df$mean_diff >= -logfc_cutoff,]
sig_df = sig_df[sig_df$mean_diff >= logfc_cutoff | sig_df$mean_diff <= -logfc_cutoff,]

# separate by up and down
sig_df_up = sig_df[sig_df$mean_diff >= logfc_cutoff,]
sig_df_down = sig_df[sig_df$mean_diff <= -logfc_cutoff,]


# Calculate the counts
num_red_points = nrow(sig_df_up)
num_blue_points = nrow(sig_df_down)
num_orange_points = nrow(sigpval_df)



# create plot
p = ggplot(input_df, aes(x=mean_diff, y=log10_pval)) + theme_bw() +

geom_vline(xintercept = -logfc_cutoff, linetype = "dashed", color = "grey") +
geom_vline(xintercept = logfc_cutoff, linetype = "dashed", color = "grey") +

geom_hline(yintercept = -log10(pval_cutoff), linetype = "dashed", color = "grey") +

geom_point(data = sig_df_up, fill = 'firebrick', size=4, pch=21, alpha=0.8) + 
geom_point(data = sig_df_down, fill = 'dodgerblue', size=4, pch=21, alpha=0.8) + 

# geom_point(data = sig_df, fill = '#f5b75c', size=4, pch=21, alpha=0.8) + 
geom_point(data = sigpval_df, fill = '#f5b75c', size=4, pch=21, alpha=0.8) + 
rasterize(geom_point(data = nonsig_df, size=4, pch=21, alpha=0.2, fill='grey'), dpi=350) + 


ggtitle(paste(sample, " - ", " Red:", num_red_points, "Blue:", num_blue_points, "Orange:", num_orange_points)) +
ylab('Padj (-log10)') + xlab('Fold change (log2)') +

theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), 
      axis.line = element_line(colour = "black"), 
      plot.title = element_text(size=title_size),
      axis.text=element_text(size=axis_size, color= 'black'), 
      axis.text.x = element_text(angle=0),
      axis.title=element_text(size=title_size*0.8),
     legend.position='none')


# genes requiring a label
p = p +
geom_label_repel(data=sig_df, aes(label=gene), show.legend=FALSE,
                seed              = 1234,
				size				= 3,
				force             = 0.5,
                max.overlaps      = 10,
				nudge_x           = 0.01,
				hjust             = 0,
				segment.size      = 0.2,
                color = 'black'
)

print(p)

return()

}



sort_df = function(input_df){
    # Function to extract numeric parts from each element
    extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))
    
    # Extract and sort by numeric parts
    sorted_vector <- unique(input_df$sample)[order(sapply(unique(input_df$sample), extract_numeric))]

    # order samples
    input_df$sample = factor(input_df$sample, levels = sorted_vector)
    
    return(input_df)
}


pdf(opt$figure_file)

input_df = read.csv(opt$figure_data_file, sep='\t')

# convert pval to -log10
input_df$log10_pval = -log10(input_df$pval)

# sort samples
input_df = sort_df(input_df)

lapply(levels(input_df$sample)[1], volcano_plot, input_df)

# # volcano_plot(unique(input_df$sample)[1], input_df)
# lapply(levels(input_df$sample), volcano_plot, input_df)

dev.off()


print(opt$figure_file)





