library(optparse)
library(forcats)
library(ggplot2)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}





boxplot = function(gene, input_df, sample){

# subset to gene
input_df = input_df[input_df$gene == gene,]

new_title = paste0(sample, "- ", gene)

# Assuming input_df is your original dataframe and it's already grouped by some identifier for each pair
input_df <- input_df %>% 
  arrange(cpg_start, tumor) %>%  # Ensure data is ordered first by pair ID, then by tumor stage if not already
  mutate(methylation = if_else(lead(mean_methylation) > mean_methylation, "Increase", "Decrease"),
         methylation = if_else(is.na(methylation), "No Change", methylation))  # Handle last group if it doesn't have a pair

    
# all combined
p = ggplot(input_df, aes(x = tumor, y = mean_methylation)) + plot_theme() +
geom_boxplot(outlier.shape = NA) +
geom_point(size = 3, alpha = 0.6) +
geom_line(aes(group = cpg_start, color = methylation), alpha = 0.5) +

scale_color_manual(values = c("Increase" = "blue", "Decrease" = "red")) +
    
# geom_jitter(size = 3, alpha = 0.6) + 
# geom_line(aes(group = cpg_start), position = position_dodge(width = 0.75), alpha = 0.5) +  # Add this line to draw connecting lines

ggtitle(new_title) +
xlab("") + ylab('Mean methylation (%)') +
ylim(0,100)

theme(plot.title = element_text(size = 16))

print(p)
return()
}


# wrapper to create boxplots
create_boxplot = function(sample, input_df){

# subset to sample
sub_df = input_df[input_df$sample == sample,]

print(sample)

# create boxplots
lapply(unique(sub_df$gene), boxplot, sub_df, sample)

return()
}



pdf(opt$figure_file)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')

# sort factors
input_df$tumor = factor(input_df$tumor, levels = c('primary', 'recurrent'))

# create boxplots for each sample
lapply(unique(input_df$sample), create_boxplot, input_df)


dev.off()


print(opt$figure_file)






