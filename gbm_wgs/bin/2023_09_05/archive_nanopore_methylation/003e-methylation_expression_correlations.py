# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import scipy.stats as stats
import subprocess, re

help_message = '''
Failed
'''


def create_gene_translation_dict(gene_translation_file):
    # load gene translation file
    gene_translation_df = pd.read_csv(gene_translation_file, sep='\t')
    
    # create dictionary
    gene_translation_dict = dict(zip(gene_translation_df['Gene stable ID'], gene_translation_df['Gene name']))
    
    return gene_translation_dict


def load_gene_expression_file(gene_expression_file, gene_translation_file):
    # load gene expression file
    gene_expression_df = pd.read_csv(gene_expression_file, sep='\t', index_col=0)
    
    # load gene translation dict
    gene_translation_dict = create_gene_translation_dict(gene_translation_file)


    # translate gene expression file index
    gene_expression_df.index = gene_expression_df.index.str.split(".").str[0].map(gene_translation_dict)

    # drop rows with missing gene names
    mask = gene_expression_df.index.astype("<U64") != 'nan'
    gene_expression_df = gene_expression_df[mask]

    # remove duplicates
    gene_expression_df = gene_expression_df[~gene_expression_df.index.duplicated(keep='first')]

    # rename columns
    gene_expression_df.columns = gene_expression_df.columns.str.split("_").str[0]
    
    return gene_expression_df


def correlate_expression_with_methylation(combined_promoter_methylation_file, gene_expression_df):
    # load methylation by gene file
    methylation_by_gene_df = pd.read_csv(combined_promoter_methylation_file, sep='\t', index_col=0)

    # # rename columns
    # methylation_by_gene_df.columns = methylation_by_gene_df.columns.str.replace('_tumor', '-recurrent')
    # methylation_by_gene_df.columns = methylation_by_gene_df.columns.str.replace('_normal', '-primary')

    
    # subset to common genes (row names)
    common_genes = np.intersect1d(gene_expression_df.index.astype("<U64"), methylation_by_gene_df.index.astype("<U64"))
    gene_expression_df = gene_expression_df.loc[common_genes,:]
    methylation_by_gene_df = methylation_by_gene_df.loc[common_genes,:]


    # sort the columns to match
    gene_expression_df = gene_expression_df.sort_index(axis=1)
    methylation_by_gene_df = methylation_by_gene_df.sort_index(axis=1)

    # create empty df to store results
    res_df = []

    # get common samples
    samples = np.intersect1d(methylation_by_gene_df.columns, gene_expression_df.columns)

    # remove RLGS2
    samples = [sample for sample in samples if not re.search('RLGS2', sample)]

    # calculate correlation by column
    for col in samples:
        # remove missing values at gene level
        gene_expression_values = gene_expression_df[col]
        methylation_values = methylation_by_gene_df[col]

        # the samples need to match
        mask = np.logical_and(~np.isnan(gene_expression_values), ~np.isnan(methylation_values))
        gene_expression_values = gene_expression_values[mask]
        methylation_values = methylation_values[mask]

        
        # calculate spearman correlation
        r, p = stats.spearmanr(gene_expression_values, methylation_values)

        # add to df
        res_df.append([col, r, p])

    # create df
    res_df = pd.DataFrame(res_df, columns=['sample', 'correlation', 'p_value'])

    print(res_df)

    return res_df


def calculate_stats(df):
    # classify by primary and recurrent
    df['sample_type'] = df['sample'].str.split("-").str[1]

    # add just sample number
    df['sample_number'] = df['sample'].str.split("-").str[0]

    stats_df = []
    
    # run mann whitney u test
    primary_r_values = df[df['sample_type'] == 'primary']['correlation']
    recurrent_r_values = df[df['sample_type'] == 'recurrent']['correlation']

    test_stat, p = stats.mannwhitneyu(primary_r_values, recurrent_r_values)

    stats_df.append([test_stat,p,'mannwhitney'])

    # Perform t-test assuming equal variances
    test_stat, p = stats.ttest_rel(primary_r_values, recurrent_r_values)

    stats_df.append([test_stat,p,'t_test'])

    # create df
    stats_df = pd.DataFrame(stats_df, columns=['statistic', 'p_value', 'test_type'])

    print(stats_df)

    return df, stats_df




def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load gene expression file and translate to gene names
    gene_expression_df = load_gene_expression_file(gene_expression_file, gene_translation_file)

    # load methylation by gene file and find correlation
    res_df = correlate_expression_with_methylation(combined_promoter_methylation_file, gene_expression_df)

    # run stats on primary vs recurrent
    res_df, stats_df = calculate_stats(res_df)

    # write to file
    res_df.to_csv(figure_data_file, sep='\t', index=False)
    stats_df.to_csv(figure_stats_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["gene_expression_file=", "gene_translation_file=", "combined_promoter_methylation_file=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--gene_expression_file"):
            gene_expression_file = str(arg)
        if opt in ("--gene_translation_file"):
            gene_translation_file = str(arg)
        if opt in ("--combined_promoter_methylation_file"):
            combined_promoter_methylation_file = str(arg)
            
        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)

    main()


