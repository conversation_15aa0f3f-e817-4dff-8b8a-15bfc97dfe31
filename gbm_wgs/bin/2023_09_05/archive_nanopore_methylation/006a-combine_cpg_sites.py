# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''

def methylation_of_gene_promoters(files):
    res_df = []

    # For each file, calculate the methylation of gene promoters and append to res_df
    for file in files:
        print(file)
        # Load df
        df = pd.read_csv(file, sep='\t', header=None)
        df.columns = ['chrom', 'start1', 'stop1', 'gene', 'chr2', 'start', 'stop', 'methylation']
        df.index = df['start'].astype('str') + '-' + df['gene'].astype('str')

        # Subset to relevant columns
        cols_oi = ['methylation']
        df = df[cols_oi]

        # Rename columns
        sample_name = file.split('/')[-1].split('-')[0]
        df.columns = [sample_name]

        # Append to res_df
        res_df.append(df)

    # Concatenate all DataFrames, merging on 'gene' and index
    res_df = pd.concat(res_df, axis=1, join='outer')

    # Fill NaN values with mean of column
    res_df = res_df.fillna(res_df.mean())
    
    return res_df
    

def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # calculate promoter regions from gff
    res_df = methylation_of_gene_promoters(intersected_bed_files_csv.split(","))

    # write to file
    res_df.to_csv(methylation_by_gene_promoter_file, sep='\t')
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["intersected_bed_files_csv=", "methylation_by_gene_promoter_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--intersected_bed_files_csv"):
            intersected_bed_files_csv = str(arg)

        if opt in ("--methylation_by_gene_promoter_file"):
            methylation_by_gene_promoter_file = str(arg)

    main()




