# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def load_and_sort_df(file, chromosomes):
    # read df
    df = pd.read_csv(file, sep='\t')

    # remove non-conventional chromosomes
    df = df[df['chr'].isin(chromosomes)]

    # sort df
    df = df.sort_values(by=['chr', 'start', 'end'])

    # rename chr column
    df = df.rename(columns = {'chr':'chrom'})

    return df
    

def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load and sort file
    df = load_and_sort_df(figure_stats_file, chromosomes)

    # save file
    df.to_csv(dmr_bed_file, sep='\t', index=False, compression='gzip')

    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    # chromosome list
    chromosomes = [str(i) for i in range(1, 23)] + ['X', 'Y']
    chromosomes = [f'chr{i}' for i in chromosomes]

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["figure_stats_file=", "dmr_bed_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)

        if opt in ("--dmr_bed_file"):
            dmr_bed_file = str(arg)

    main()


