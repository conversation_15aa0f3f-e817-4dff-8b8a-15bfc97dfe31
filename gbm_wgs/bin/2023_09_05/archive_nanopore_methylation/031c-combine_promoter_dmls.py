# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob, subprocess


help_message = '''
Failed
'''


def summarize_dml(df, pval_cutoff = 0.01, diff_cutoff = 0.1):
    # filter dmls
    df = df[(df['pval'] < pval_cutoff) & (np.abs(df['mean_diff']) > diff_cutoff)]

    # group by sample and count
    summary_df = df.groupby('sample').size().reset_index(name='counts')

    return summary_df


def merge_files(files):
    # results df
    res_df = []

    # load and combine each file
    for file in files:
        df = pd.read_csv(file, sep='\t')
        res_df.append(df)

    # combine all files
    res_df = pd.concat(res_df)

    # summarize number of significant promoters
    summary_df = summarize_dml(res_df)

    return res_df, summary_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # define files from regex
    regex = f'{results_dir}/analysis_nanopore/dml_methylation/*_dmls_final.tsv'
    files = glob.glob(regex)

    # load and sort dml files
    df, summary_df = merge_files(files)

    # save to files
    df.to_csv(figure_data_file, sep='\t', index=False)
    summary_df.to_csv(figure_stats_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)

    main()




