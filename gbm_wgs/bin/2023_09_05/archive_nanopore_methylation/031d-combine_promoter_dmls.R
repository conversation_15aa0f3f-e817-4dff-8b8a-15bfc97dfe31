library(optparse)
library(forcats)
library(ggplot2)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}





barplot = function(input_df){


# all combined
p = ggplot(input_df, aes(x = sample, y = counts)) + plot_theme() +
geom_bar(stat = "identity") +

ggtitle('') +
xlab('') + ylab('Number of differentially methylated genes')


print(p)
}





pdf(opt$figure_file)

# load dfs
summary_df = read.csv(opt$figure_stats_file, sep='\t')


# sort samples by number of unique genes
summary_df$sample = fct_reorder(summary_df$sample, summary_df$counts, .desc = TRUE)


# plot
barplot(summary_df)


dev.off()


print(opt$figure_file)






