# Alec <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from scipy import stats
from concurrent.futures import ProcessPoolExecutor


help_message = '''
Failed
'''


def differential_promoter_methylation_beta_value(args, min_samples=3):
    df, gene_list = args

    stats_df = []

    # calculate difference, log2 fold change, and p-value for each position
    main_primary_df = df[df['tumor_type'] == 'primary']
    main_recurrent_df = df[df['tumor_type'] == 'recurrent']

    # iterate through each gene
    for gene in gene_list:
        # subset to gene
        primary_df = main_primary_df[main_primary_df['gene'] == gene]
        recurrent_df = main_recurrent_df[main_recurrent_df['gene'] == gene]

        # for each unique start position test for differential methylation
        for position in primary_df['start'].unique():
            # subset to position
            primary_sub_df = primary_df[primary_df['start'] == position]
            recurrent_sub_df = recurrent_df[recurrent_df['start'] == position]

            # subset to percent methylation and match samples by sample code if possible, otherwise remove the sample code from both arrays
            primary_percent_methylation = []
            recurrent_percent_methylation = []

            # for each sample code, match the percent methylation value between the primary and recurrent samples using the sample code
            for sample_code in primary_sub_df['sample_code'].unique():
                primary_sample_percent_methylation = primary_sub_df.loc[primary_sub_df['sample_code'] == sample_code, 'percent_methylated'].values
                recurrent_sample_percent_methylation = recurrent_sub_df.loc[recurrent_sub_df['sample_code'] == sample_code, 'percent_methylated'].values

                # make sure they both have values
                if len(primary_sample_percent_methylation) != 0 and len(recurrent_sample_percent_methylation) != 0:
                    primary_percent_methylation.append(primary_sample_percent_methylation[0])
                    recurrent_percent_methylation.append(recurrent_sample_percent_methylation[0])

            # calculate p-value if there are at least n samples
            if len(primary_percent_methylation) >= min_samples:
                # paired t-test, unpaired t-test, and u-test
                paired_ttest_p_value = stats.ttest_rel(primary_percent_methylation, recurrent_percent_methylation)[1]
                unpaired_ttest_p_value = stats.ttest_ind(primary_percent_methylation, recurrent_percent_methylation)[1]
                
                paired_ttest_p_value_log = stats.ttest_rel(np.log10(primary_percent_methylation), np.log10(recurrent_percent_methylation))[1]
                unpaired_ttest_p_value_log = stats.ttest_ind(np.log10(primary_percent_methylation), np.log10(recurrent_percent_methylation))[1]
                
                u_test_p_value = stats.mannwhitneyu(primary_percent_methylation, recurrent_percent_methylation)[1]
                try:
                    wilxocon_p_value = stats.wilcoxon(primary_percent_methylation, recurrent_percent_methylation)[1]
                except:
                    wilxocon_p_value = 1

                # calculate difference in percent methylation
                difference = np.mean(primary_percent_methylation) - np.mean(recurrent_percent_methylation)
                median_difference = np.median(primary_percent_methylation) - np.median(recurrent_percent_methylation)

                # calculate log2 fold change
                log2_fc = np.log2((np.mean(primary_percent_methylation) + 0.01) / (np.mean(recurrent_percent_methylation) + 0.01))
                median_log2fc = np.log2((np.median(primary_percent_methylation) + 0.01) / (np.median(recurrent_percent_methylation) + 0.01))

                stats_df.append([position, paired_ttest_p_value, unpaired_ttest_p_value, paired_ttest_p_value_log, unpaired_ttest_p_value_log, wilxocon_p_value, u_test_p_value, difference, median_difference, log2_fc, median_log2fc, gene])                

    stats_df = pd.DataFrame(stats_df, columns=['position', 'paired_ttest_p_value', 'unpaired_ttest_p_value', 'paired_log_ttest_p_value', 'unpaired_log_ttest_p_value', 'wilcoxon_p_value', 'u_test_p_value', 'difference', 'median_difference', 'log2_fc', 'median_log2fc', 'gene'])

    # fill na with 1
    stats_df = stats_df.fillna(1)

    return stats_df


def add_tss(df, protein_coding_genes_gff_file):
    # load gff
    gff_df = pd.read_csv(protein_coding_genes_gff_file, sep='\t', header=None)

    # add column names
    column_names = ['seqname', 'source', 'type', 'start', 'end', 'score', 'strand', 'phase', 'attributes']
    gff_df.columns = column_names

    gff_df['gene'] = gff_df['attributes'].str.extract(r'gene=([^;]+)')

    # add tss and subset to gene and tss
    gff_df['tss'] = gff_df['start']
    gff_df = gff_df[['gene', 'tss']]

    # merge
    df = pd.merge(df, gff_df, on='gene')
    
    return df


def differential_methylation(promoter_methylation_combined_file, protein_coding_genes_gff_file, threads):
    # load df
    input_df = pd.read_csv(promoter_methylation_combined_file, sep='\t')

    # separate gene lists based on number of threads
    gene_lists = np.array_split(input_df['gene'].unique(), threads)

    # process each gene list
    args = [(input_df[input_df['gene'].isin(gene_list)], gene_list) for gene_list in gene_lists]

    # calculate p-values per gene using multi-threads
    with ProcessPoolExecutor(max_workers=threads) as executor:
        df = list(executor.map(differential_promoter_methylation_beta_value, args))

    # concatenate
    df = pd.concat(df, ignore_index=True)

    # add tss
    df = add_tss(df, protein_coding_genes_gff_file)

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # process file for differential methylation
    df = differential_methylation(promoter_methylation_combined_file, protein_coding_genes_gff_file, threads)

    # save to files
    df.to_csv(promoter_methlyation_results_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # default threads
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["promoter_methylation_combined_file=", "protein_coding_genes_gff_file=", "promoter_methlyation_results_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--promoter_methylation_combined_file"):
            promoter_methylation_combined_file = str(arg)
        if opt in ("--protein_coding_genes_gff_file"):
            protein_coding_genes_gff_file = str(arg)

        if opt in ("--promoter_methlyation_results_file"):
            promoter_methlyation_results_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()




