# Alec <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob, re

import scipy.stats as stats

from concurrent.futures import ProcessPoolExecutor
from statsmodels.stats import multitest


help_message = '''
Failed
'''


def differential_methylation_stats(args):
    genes, df = args

    # results list
    res = []
    
    # for each gene, run paired t-test and U-test
    for gene in genes:
        sub_df = df.loc[gene,:]

        # separate into primary and recurrent
        sub_primary = sub_df['primary'].to_numpy()
        sub_recurrent = sub_df['recurrent'].to_numpy()

        # run paired t-test
        t_pval = stats.ttest_rel(sub_primary, sub_recurrent)[1]

        # run U-test
        u_pval = stats.mannwhitneyu(sub_primary, sub_recurrent)[1]

        # calculate fc
        fc = np.log2(np.mean(sub_recurrent) / np.max([np.mean(sub_primary), 0.1]))
        
        # append results
        res.append([gene, t_pval, u_pval, fc])

    return pd.DataFrame(res, columns=['gene', 't_pval', 'u_pval', 'log2_mean_fc'])


def differential_promoter_methylation_analysis(regex, threads, p_cutoff = 0.05):
    # find methylation files
    files = glob.glob(regex)

    # load and combine all files
    df = pd.concat([pd.read_csv(file, sep='\t') for file in files])

    # split genes into equal length lists
    genes = np.array_split(df['gene'].unique(), threads)

    # set index
    df.index = df['gene'].to_numpy()

    # args for parallel processing
    args = [(gene, df.loc[gene,:]) for gene in genes]

    # for each gene, run differential methylation analysis between primary and recurrent samples
    with ProcessPoolExecutor(max_workers=threads) as executor:
        res_df = list(executor.map(differential_methylation_stats, args))

    # combine results
    res_df = pd.concat(res_df)

    # fdr correct
    res_df['t_fdr'] = multitest.fdrcorrection(res_df['t_pval'])[1]
    res_df['u_fdr'] = multitest.fdrcorrection(res_df['u_pval'])[1]

    # classify genes as significant or not
    res_df['t_sig'] = res_df['t_fdr'] < p_cutoff
    res_df['u_sig'] = res_df['u_fdr'] < p_cutoff

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # define regex for methylation files
    regex = f'{res_dir}/analysis_nanopore/methylation/009-paired_test_methylation_downsampled_part1_*.tsv'

    # load methylation file
    df = differential_promoter_methylation_analysis(regex, threads)

    # print summary details
    print(f"Number of significant genes: {np.sum(df['t_sig'])}")

    # save to file
    df.to_csv(figure_data_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["res_dir=", "figure_data_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--res_dir"):
            res_dir = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()


