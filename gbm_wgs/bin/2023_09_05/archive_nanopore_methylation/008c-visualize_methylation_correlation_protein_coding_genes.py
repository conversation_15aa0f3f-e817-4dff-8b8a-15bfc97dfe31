# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import subprocess

help_message = '''
Failed
'''


def melt_df(file):
    # Load the DataFrame
    df = pd.read_csv(file, sep='\t', index_col=0)

    # separate into normal and tumor dfs
    normal_df = df[[col for col in df.columns if 'normal' in col]]
    tumor_df = df[[col for col in df.columns if 'tumor' in col]]

    # rename each df column to remove "normal" or "tumor"
    normal_df.columns = normal_df.columns.str.split("_").str[0]
    tumor_df.columns = tumor_df.columns.str.split("_").str[0]

    # melt the dfs
    normal_df = normal_df.melt(var_name='patient', value_name='primary')
    tumor_df = tumor_df.melt(var_name='patient', value_name='recurrent')

    # merge the dfs
    normal_df['recurrent'] = tumor_df['recurrent'].to_numpy()

    return normal_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load methylation file
    df = melt_df(methylation_by_gene_promoter_file)

    # save to file
    df.to_csv(figure_data_file, sep='\t', index=False)

    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["methylation_by_gene_promoter_file=", "r_script=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--methylation_by_gene_promoter_file"):
            methylation_by_gene_promoter_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)

    main()


