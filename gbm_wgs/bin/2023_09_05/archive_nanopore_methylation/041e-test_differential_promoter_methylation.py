# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import itertools
from scipy.stats import fisher_exact

help_message = '''
Failed
'''


def differential_promoter_methylation_testing(mgmt_promoter_methylation_combined_file, methylated_samples, unmethylated_samples):
    # load df
    input_df = pd.read_csv(mgmt_promoter_methylation_combined_file, sep='\t')

    # create combinations
    combinations = list(itertools.product(methylated_samples, unmethylated_samples))

    stats_df = []

    # iterate through sample pairs
    for samples in combinations:
        # subset to samples
        df = input_df[input_df['sample'].isin(samples)]

        for gene in df['gene'].unique():
            df = df[df['gene'] == gene]

            # calculate difference, log2 fold change, and p-value for each position
            methylated_df = df[df['methylation_classification'] == 'methylated']
            unmethylated_df = df[df['methylation_classification'] == 'unmethylated']

            # calculate percent methylation difference for each position between methylated and unmethylated
            for index, row in methylated_df.iterrows():
                gene = row['gene']
                start = row['start']

                try:
                    unmethyl_row = unmethylated_df[unmethylated_df['start'] == start].iloc[0].squeeze()

                    # calculate p-value using fishers exact test
                    methyl_n_methylated = row['n_methylated']
                    methyl_n_unmethylated = row['n_unmethylated']
                    unmethyl_n_methylated = unmethyl_row['n_methylated']
                    unmethyl_n_unmethylated = unmethyl_row['n_unmethylated']
        
                    # calculate p-value
                    p_value = fisher_exact([[methyl_n_methylated, methyl_n_unmethylated], [unmethyl_n_methylated, unmethyl_n_unmethylated]])[1]

                    # calculate difference in percent methylation
                    percent_methylated = row['percent_methylated']
                    unmethylated_percent_methylated = unmethyl_row['percent_methylated']

                    difference = percent_methylated - unmethylated_percent_methylated

                    # create a unique name of the sample pair
                    sample = "_".join(samples)
        
                    stats_df.append([gene, start, sample, p_value, difference])
                
                except:
                    pass

    stats_df = pd.DataFrame(stats_df, columns=['gene', 'start', 'sample', 'p_value', 'difference'])

    return stats_df


def prioritize_features(df, purity_ploidy_file, pval_cutoff = 0.05):
    # subset to significant p-values
    df = df[df['p_value'] < pval_cutoff]

    # load purity and ploidy file
    purity_ploidy_df = pd.read_csv(purity_ploidy_file, sep='\t')

    res = []

    # iterate through genes
    for gene in df['gene'].unique():
        gene_df = df[df['gene'] == gene]

        for sample_pair in gene_df['sample'].unique():
            sample_pair_df = gene_df[gene_df['sample'] == sample_pair]

            # calculate minimum purity for each sample pair
            purities = purity_ploidy_df[purity_ploidy_df['sample'].isin(sample_pair.split('_'))]['purity']
            purity_minimum = purities.min()

            # get the difference of the sample pair
            diff = sample_pair_df['difference'].sum()

            # get the sign of the difference
            sign = np.sign(diff)

            # get the minimum p-value associated with the difference of the same sign
            pvals = sample_pair_df[sample_pair_df['difference'] * sign > 0]['p_value']
            min_pval = pvals.min()

            res.append([gene, diff, sign, min_pval, sample_pair, purity_minimum, ",".join(purities.astype('str')), ",".join(pvals.astype('str'))])

    res = pd.DataFrame(res, columns=['gene', 'sum_diff', 'sign', 'min_pval', 'sample', 'purity_minimum', 'purity_values', 'pvals'])

    return res


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load files
    df = differential_promoter_methylation_testing(mgmt_promoter_methylation_combined_file, methylated_samples, unmethylated_samples)

    # perform feature prioritization
    stats_df = prioritize_features(df, purity_ploidy_file)

    # save to files
    df.to_csv(mgmt_promoter_methylation_stats_file, sep='\t', index=False)
    stats_df.to_csv(prioritized_stats_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')




if __name__ == "__main__":
    # define methylated and unmethylated samples
    methylated_samples = ['RLGS5-primary', 'RLGS5-recurrent', 'RLGS8-primary', 'RLGS11-primary']
    unmethylated_samples = ['RLGS2-primary', 'RLGS7-recurrent', 'RLGS9-primary', 'RLGS12-recurrent']

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["mgmt_promoter_methylation_combined_file=", "purity_ploidy_file=", "prioritized_stats_file=", "mgmt_promoter_methylation_stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--mgmt_promoter_methylation_combined_file"):
            mgmt_promoter_methylation_combined_file = str(arg)
        if opt in ("--purity_ploidy_file"):
            purity_ploidy_file = str(arg)

        if opt in ("--prioritized_stats_file"):
            prioritized_stats_file = str(arg)
        if opt in ("--mgmt_promoter_methylation_stats_file"):
            mgmt_promoter_methylation_stats_file = str(arg)

    main()




