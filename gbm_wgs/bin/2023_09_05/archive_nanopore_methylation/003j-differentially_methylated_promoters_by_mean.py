# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import scipy.stats as stats
import subprocess

from concurrent.futures import ProcessPoolExecutor
from statsmodels.stats import multitest


help_message = '''
Failed
'''


def melt_methylation_file(file):
    # load df
    df = pd.read_csv(file, sep='\t', index_col=0)
    
    # filter out rows that have missing values
    df = df.dropna()

    # set gene name
    df['gene'] = df.index
    
    # melt df
    df = pd.melt(df, id_vars=['gene'], var_name='sample', value_name='mean_methylation')

    # add sample name
    df['sample_id'] = df['sample'].str.split('_').str[0]
    df['tumor'] = df['sample'].str.split('_').str[1]

    return df


def differential_methylation_stats(args):
    genes, df = args

    # results list
    res = []

    # separate primary and recurrent samples
    primary = df[df['tumor'] == 'primary']
    recurrent = df[df['tumor'] == 'recurrent']

    # for each gene, run paired t-test and U-test
    for gene in genes:
        # run paired t-test
        t_stat, t_pval = stats.ttest_rel(primary[primary['gene'] == gene]['mean_methylation'], recurrent[recurrent['gene'] == gene]['mean_methylation'])

        # run U-test
        u_stat, u_pval = stats.mannwhitneyu(primary[primary['gene'] == gene]['mean_methylation'], recurrent[recurrent['gene'] == gene]['mean_methylation'])

        # calculate fc
        fc = np.mean(recurrent[recurrent['gene'] == gene]['mean_methylation']) / np.mean(primary[primary['gene'] == gene]['mean_methylation'])

        # append results
        res.append([gene, t_pval, u_pval, fc])

    return pd.DataFrame(res, columns=['gene', 't_pval', 'u_pval', 'mean_fc'])


def differential_promoter_methylation_analysis(file, methylation_patient_csv, threads, p_cutoff = 0.05):
    # load methylation file
    df = melt_methylation_file(file)

    # subset to only patients in methylation_patient_csv
    patients = methylation_patient_csv.split(",")
    df = df[df['sample_id'].isin(patients)]

    # split genes into equal length lists
    genes = np.array_split(df['gene'].unique(), threads)
    
    # args for parallel processing
    args = [(gene, df) for gene in genes]

    # for each gene, run differential methylation analysis between primary and recurrent samples
    with ProcessPoolExecutor(max_workers=threads) as executor:
        res_df = list(executor.map(differential_methylation_stats, args))

    # fdr correct
    res_df = pd.concat(res_df)
    res_df['t_fdr'] = multitest.fdrcorrection(res_df['t_pval'])[1]
    res_df['u_fdr'] = multitest.fdrcorrection(res_df['u_pval'])[1]

    # classify genes as significant or not
    res_df['t_sig'] = res_df['t_fdr'] < p_cutoff
    res_df['u_sig'] = res_df['u_fdr'] < p_cutoff

    return df, res_df


def summarize_cancer_genes(df, cgc_file):
    # load cgc file
    cgc_df = pd.read_csv(cgc_file, sep='\t')

    # get cancer genes in dfs
    df['cgc_gene'] = df['gene'].isin(cgc_df['GENE_SYMBOL'])

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load methylation file
    df, res_df = differential_promoter_methylation_analysis(combined_promoter_methylation_file, methylation_patient_csv, threads)

    # add cgc genes
    df = summarize_cancer_genes(df, cgc_file)
    res_df = summarize_cancer_genes(res_df, cgc_file)

    # print summary details
    n_sig = res_df[res_df['t_sig'] | res_df['u_sig']].shape[0]
    print(f"Number of significant genes: {n_sig}")

    # save to files
    df.to_csv(figure_data_file, sep='\t', index=False)
    res_df.to_csv(figure_stats_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["combined_promoter_methylation_file=", "cgc_file=", "methylation_patient_csv=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--combined_promoter_methylation_file"):
            combined_promoter_methylation_file = str(arg)
        if opt in ("--cgc_file"):
            cgc_file = str(arg)
        if opt in ("--methylation_patient_csv"):
            methylation_patient_csv = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()


