# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob, re
import itertools

help_message = '''
Failed
'''


def add_gene_loci(res, gene_bed_file):
    # load gene file and subset to MGMT
    gene_df = pd.read_csv(gene_bed_file, sep='\t', header=None)
    gene_df.columns = ['chr', 'start', 'end', 'gene', 'type', 'info']

    # add methylation classification and sample name
    gene_df['tumor_type'] = 'gene'

    # subset to columns of interest
    gene_df = gene_df[['start', 'gene', 'tumor_type']]

    # set end position to be the max end position of res for each gene
    gene_df['end'] = max(res['end'])

    # add to res
    res = pd.concat([res, gene_df])

    return res


def merge_files(files, gene_bed_file):
    res = []

    for file in files:
        df = pd.read_csv(file, sep='\t', header=None)

        # add column names
        column_names = ['chr', 'start2', 'end2', 'gene', 'chr2', 'start', 'end', 'percent_methylated', 'n_methylated', 'n_bases']
        df.columns = column_names

        # add n_unmethylated
        df['n_unmethylated'] = df['n_bases'] - df['n_methylated']

        # subset to columns of interest
        df = df[['chr', 'start', 'end', 'gene', 'percent_methylated', 'n_methylated', 'n_unmethylated']]

        # add sample name
        sample = file.split('/')[-1].split('_')[0]

        # replace normal with primary and tumor with recurrent
        sample = re.sub(r'normal', 'primary', sample)
        sample = re.sub(r'tumor', 'recurrent', sample)
        df['sample'] = sample

        # add sample code
        df['sample_code'] = sample.split("-")[0]

        # add tumor_type
        df['tumor_type'] = sample.split("-")[1]

        res.append(df)

    # concatenate
    res = pd.concat(res)


    # add mgmt loci
    res = add_gene_loci(res, gene_bed_file)

    return res


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # define files
    file_regex = f'{results_dir}/analysis_nanopore/methylation/*_promoters_methylation.bed'
    files = glob.glob(file_regex)

    # load files
    df = merge_files(files, gene_bed_file)

    # save to file
    df.to_csv(combined_methylation_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "gene_bed_file=", "combined_methylation_file=", "figure_stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)
        if opt in ("--gene_bed_file"):
            gene_bed_file = str(arg)

        if opt in ("--combined_methylation_file"):
            combined_methylation_file = str(arg)

    main()




