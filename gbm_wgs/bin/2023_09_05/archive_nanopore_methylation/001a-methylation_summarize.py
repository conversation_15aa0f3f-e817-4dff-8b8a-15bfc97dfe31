# Alec <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

help_message = '''
Failed
'''


def load_methyl_bed(file):
    # columns
    columns = ['chrom', 'start', 'end', 'modified base code and motif', 'score', 'strand', 'start position', 'end position', 'color', 'Nvalid_cov', 'percent modified', 'Nmod', 'Ncanonical', 'Nother_mod', 'Ndelete', 'Nfail', 'Ndiff', 'Nnocall']

    # subset to relevant columns
    cols_oi = ['chrom', 'start', 'end', 'percent modified']

    # for each file, load as df and subset to relevant columns
    df = pd.read_csv(file, sep='\t', header=None, compression='gzip')
    
    # Split the last column based on spaces
    split_columns = df.iloc[:, -1].str.split(expand=True)
    
    # Assign the split columns back to the original DataFrame
    df = pd.concat([df.iloc[:, :-1], split_columns], axis=1)
    df.columns = columns
    
    df = df[cols_oi]

    return df


def calculate_average_methylation(primary_methylation_bed_file, recurrent_methylation_bed_file, interval_size=200000, window_size=10):
    # Initialize an empty list to store results
    res_df = []
    
    # Extract sample name from the file path
    sample = primary_methylation_bed_file.split('/')[-1].split('.')[1].split("_")[0]

    # Load primary and recurrent methylation data
    primary_df = load_methyl_bed(primary_methylation_bed_file)
    recurrent_df = load_methyl_bed(recurrent_methylation_bed_file)

    # Subtract primary from recurrent methylation
    recurrent_df['percent modified'] = recurrent_df['percent modified'].astype('float') - primary_df['percent modified'].astype('float')

    # Delete primary_df to save memory
    del primary_df

    # Iterate over each chromosome in recurrent_df
    for chrom in recurrent_df['chrom'].unique():
        # Subset data for the current chromosome
        chrom_df = recurrent_df.loc[recurrent_df['chrom'] == chrom, ['start', 'percent modified']]
        
        # Calculate start and end positions in intervals of interval_size
        chrom_df['interval_start'] = (chrom_df['start'] // interval_size) * interval_size + 1

        # Calculate average percent modified over intervals
        chrom_df = chrom_df.groupby('interval_start')['percent modified'].mean().reset_index()
        chrom_df['interval_end'] = chrom_df['interval_start'] + interval_size - 1

        # Add chromosome information
        chrom_df['chrom'] = chrom
        
        # Calculate rolling average over window_size
        chrom_df['rolling_avg'] = chrom_df['percent modified'].rolling(window=window_size, min_periods=1).mean()
        
        # Add sample name
        chrom_df['sample'] = sample

        # add bin number
        chrom_df['bin'] = list(range(chrom_df.shape[0]))
        
        # Reorder columns
        chrom_df = chrom_df[['sample', 'chrom', 'interval_start', 'interval_end', 'percent modified', 'rolling_avg', 'bin']]

        print(chrom_df)

        # Append results for the current chromosome to res_df
        res_df.append(chrom_df)

    # Concatenate all results into a single DataFrame
    res_df = pd.concat(res_df)

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    

    # calculate average difference between recurrent and primary methylation over 200,000 bp window
    methylation_df = calculate_average_methylation(primary_methylation_bed_file, recurrent_methylation_bed_file, interval_size = window_size)

    # save to files
    methylation_df.to_csv(methlyation_results_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["primary_methylation_bed_file=", "recurrent_methylation_bed_file=", "window_size=", "methlyation_results_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--primary_methylation_bed_file"):
            primary_methylation_bed_file = str(arg)
        if opt in ("--recurrent_methylation_bed_file"):
            recurrent_methylation_bed_file = str(arg)

        if opt in ("--window_size"):
            window_size = int(arg) * 1000
        
        if opt in ("--methlyation_results_file"):
            methlyation_results_file = str(arg)

    main()




