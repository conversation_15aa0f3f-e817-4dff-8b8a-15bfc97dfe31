library(optparse)
library(forcats)
library(ggplot2)
library(dplyr)
library(reshape2)
library(ggdendro)
# library(gplots)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



heatmap = function(input_df){

# all combined
p = ggplot(input_df, aes(x = gene, y = sample)) + plot_theme() +
geom_tile(aes(fill = mean_methylation)) +
scale_fill_gradient2(low = "white", mid = "#F4E555", high = "#4E269F", midpoint=50, limits = c(0,100), name = 'Mean methylation') +

ggtitle('') +
xlab("Gene promoters (chronological)") + ylab('Sample') +
guides(fill = guide_colourbar(ticks.colour = "black", frame.colour = 'black')) + 

theme(axis.text.x = element_blank())


print(p)

return()
}



# Function to create and print dendrogram with sample names
create_dendrogram <- function(hr) {
  dendro <- as.dendrogram(hr)
  dendro_data <- ggdendro::dendro_data(dendro)
  
  # Extract segment and label data
  dendro_segments <- ggdendro::segment(dendro_data)
  dendro_labels <- ggdendro::label(dendro_data)
  
  # Create the dendrogram plot
  p <- ggplot() + 
    geom_segment(data = dendro_segments, 
                 aes(x = x, y = y, xend = xend, yend = yend)) +
    geom_text(data = dendro_labels, 
              aes(x = x, y = y, label = label), 
              hjust = 1, size = 3) +  # Adjust `hjust` and `size` as needed
    coord_flip() + 
    theme_minimal() + 
    scale_y_reverse() +  # Reverse the y-axis to align with the heatmap
    ggtitle('Dendrogram')
  
  print(p)
}






# correlation function
corr.dist = function(x) as.dist(1-cor(t(x)))






# load df
input_df = read.csv(opt$figure_data_file, sep='\t')

# Pivot the data to a wide format for clustering
data_wide <- dcast(input_df, sample ~ gene, value.var = "mean_methylation")

# Remove the 'sample' column for clustering
data_matrix <- as.matrix(data_wide[,-1])
rownames(data_matrix) <- data_wide$sample

# # cleanup matrix
# cols_with_na <- which(colSums(is.na(data_matrix)) > 0)
# data_matrix_clean <- data_matrix[, -cols_with_na]

# subsample and perform hierarchical clustering
hr <- hclust(corr.dist(data_matrix))
# hr <- hclust(dist(data_matrix))

# Order samples based on clustering
ordered_samples <- rownames(data_matrix)[hr$order]



# Reorder the input_df based on hierarchical clustering
input_df$sample <- factor(input_df$sample, levels = ordered_samples)


png(gsub("pdf", "png", opt$figure_file), width=25, height=10, units='in', res=300)

# plot
heatmap(input_df)

dev.off()


pdf(gsub("png", "pdf", opt$figure_file))

# create dendrogram
create_dendrogram(hr)

dev.off()


print(opt$figure_file)


