# <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''

def add_null_rows(df):
    # Create a new DataFrame for combinations
    primary_values = np.arange(0, 101)  # Primary values from 0 to 100
    recurrent_values = np.arange(0, 101)  # Recurrent values from 0 to 100

    # Prepare a list to store new rows
    new_rows = []

    # Loop through each unique patient_id in the original DataFrame
    for patient in df['patient_id'].unique():
        # Create combinations
        for primary in primary_values:
            for recurrent in recurrent_values:
                new_rows.append({
                    'primary': primary,
                    'recurrent': recurrent,
                    'patient_id': patient,
                    'cpg_site': 'null'  # Assigning 'null' for cpg_site
                })

    # Create the new DataFrame
    new_df = pd.DataFrame(new_rows)

    # Concatenate the original DataFrame with the new combinations
    result_df = pd.concat([df, new_df], ignore_index=True)

    return result_df


def combined_dfs(regex):
    # find methylation files
    files = glob.glob(regex)

    # load and combine all files
    res_df = []
    corr_df = []

    for file in files:
        # load data
        df = pd.read_csv(file, sep='\t')
        sample_name = file.split('/')[-1].split('_')[-1].split('.')[0]

        # calculate correlation between primary and recurrent columns
        correlation = np.round(df['primary'].corr(df['recurrent'], method='spearman'), 3)

        # add null rows to df for visualization
        df = add_null_rows(df)
        
        # add correlation column
        df['patient_correlation'] = df['patient_id'] + " " + str(correlation)

        # append to res_df
        res_df.append(df)

        # append to corr_df
        corr_df.append([correlation, sample_name, f'{df["patient_id"].unique()[0]} {correlation}'])

    # combine the dataframes
    res_df = pd.concat(res_df)

    # combine the correlation
    corr_df = pd.DataFrame(corr_df, columns=['correlation', 'patient', 'patient_correlation'])

    return res_df, corr_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # define regex for methylation files
    regex = f'{res_dir}/analysis_nanopore/_figure_data/008-visualize_methylation_correlation_RLGS*.tsv'

    # load methylation file
    df, corr_df = combined_dfs(regex)

    # save to files
    df.to_csv(figure_data_file, sep='\t', index=False)
    corr_df.to_csv(figure_stats_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["res_dir=", "figure_data_file=", "figure_stats_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--res_dir"):
            res_dir = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()


