library(optparse)
library(forcats)
library(ggplot2)
library(dplyr)
library(tidyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




create_boxplot = function(gene, input_df, stats_df){

# subset to gene
input_df = input_df[input_df$gene == gene,]
stats_df = stats_df[stats_df$gene == gene,]

    
p = ggplot(input_df, aes(x = tumor, y = methylation)) + plot_theme() +
geom_boxplot(outlier.shape = NA) +
geom_point(size = 3, alpha = 0.6) +
geom_line(aes(group = cpg_start, color = methylation_difference), alpha = 0.25) +

scale_color_manual(values = c("Increase" = "blue", "Decrease" = "red")) +

facet_wrap(~ patient, scales = "free_x", nrow = 1) +  # Facet along x-axis by 'patient' in a single row

ggtitle(paste(gene, "p=", signif(stats_df$t_pval, 3), "mean log2FC=", signif(log2(stats_df$mean_fc), 3))) +
xlab("") + ylab('Mean methylation (%)') +
ylim(0, 100) +

theme(plot.title = element_text(size = 16))



print(p)
return()
}





pdf(opt$figure_file, width=20)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')

# subset to common genes
input_df = input_df[input_df$gene %in% stats_df$gene,]
stats_df = stats_df[stats_df$gene %in% input_df$gene,]

# sort factors
# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# order patients
sorted_vector <- unique(input_df$patient)[order(sapply(unique(input_df$patient), extract_numeric))]
input_df$patient = factor(input_df$patient, levels = sorted_vector)

# sort by p-value
stats_df$gene = fct_reorder(factor(stats_df$gene), stats_df$t_pval, .desc = FALSE)


# create boxplots for each sample
# lapply(stats_df$gene[1:10], create_boxplot, input_df, stats_df)
lapply(c(head(levels(stats_df$gene),10),tail(levels(stats_df$gene),10)), create_boxplot, input_df, stats_df)


dev.off()


print(opt$figure_file)






