# Alec <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import scipy.stats as stats
from statsmodels.stats import multitest
import subprocess

help_message = '''
Failed
'''


def melt_methylation_file(combined_promoter_methylation_file):
    # load df
    df = pd.read_csv(combined_promoter_methylation_file, sep='\t', index_col=0)

    # median impute
    median_values = df.median()
    df = df.fillna(median_values)
    
    # set gene name
    df['gene'] = df.index
    
    # melt df
    df = pd.melt(df, id_vars=['gene'], var_name='sample', value_name='mean_methylation')

    # # remove na
    # df = df[~df['mean_methylation'].isna()]

    # add sample name
    df['sample_id'] = df['sample'].str.split('_').str[0]
    df['tumor'] = df['sample'].str.split('_').str[1]

    return df


def compare_primary_recurrent_methylation(df):
    # mann-whitney u test between primary and recurrent combined
    primary = df[df['sample'].str.contains('primary')]
    recurrent = df[df['sample'].str.contains('recurrent')]

    test_stat, p = stats.mannwhitneyu(primary['mean_methylation'], recurrent['mean_methylation'])

    # calculate fold change
    fold_change = np.mean(recurrent['mean_methylation']) / np.mean(primary['mean_methylation'])

    # create list for results
    res_df = [[test_stat, p, fold_change, 'combined']]

    # for each sample, compare primary and recurrent
    for sample in df['sample_id'].unique():
        # subset to sample
        sub_df = df[df['sample_id'] == sample]
        
        # mann-whitney u test between primary and recurrent combined
        primary = sub_df[sub_df['sample'].str.contains('primary')]
        recurrent = sub_df[sub_df['sample'].str.contains('recurrent')]

        test_stat, p = stats.mannwhitneyu(primary['mean_methylation'], recurrent['mean_methylation'])

        # calculate fold change
        fold_change = np.mean(recurrent['mean_methylation']) / np.mean(primary['mean_methylation'])

        # add to results
        res_df.append([test_stat, p, fold_change, sample])

    # create df
    res_df = pd.DataFrame(res_df, columns=['test_stat', 'p_value', 'fold_change', 'sample'])

    # fdr correction
    res_df['fdr'] = multitest.multipletests(res_df['p_value'], method='fdr_bh')[1]
    
    return res_df



def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # process methylation by gene file
    df = melt_methylation_file(combined_promoter_methylation_file)

    # compare primary and recurrent methylation
    res_df = compare_primary_recurrent_methylation(df)

    # save to files
    df.to_csv(figure_data_file, sep='\t', index=False)
    res_df.to_csv(figure_stats_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["combined_promoter_methylation_file=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--combined_promoter_methylation_file"):
            combined_promoter_methylation_file = str(arg)
            
        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)

    main()


