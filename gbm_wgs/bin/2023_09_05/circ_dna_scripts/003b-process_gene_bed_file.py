# <PERSON>cheli
import argparse
import pandas as pd

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--gene_bed_file", required=True)
    parser.add_argument("--gene_bed_file_processed", required=True)
    return parser.parse_args()


def main():
    args = parse_arguments()

    # load df 
    df = pd.read_csv(args.gene_bed_file, sep='\t', header=None)

    # add chr to first column
    df[0] = 'chr' + df[0].astype('str')

    # save results
    df.to_csv(args.gene_bed_file_processed, sep='\t', index=False, header=False)


if __name__ == "__main__":
    main()
