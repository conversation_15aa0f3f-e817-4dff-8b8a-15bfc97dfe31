# <PERSON>cheli
import argparse
import pandas as pd
import numpy as np

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--combined_counts", required=True)  # Combined counts file from 004a
    parser.add_argument("--output_file", required=True)  # Output file path
    return parser.parse_args()


def calculate_cohort_fractions(combined_counts_df):
    """Calculate fraction of cohort with ecDNAs for each method"""
    
    # Pivot the data to have methods as columns
    pivot_df = combined_counts_df.pivot(index='sample_name', columns='method', values='count').fillna(0)
    
    # Calculate total number of samples
    total_samples = len(pivot_df)
    
    results = []
    
    for method in pivot_df.columns:
        # Count samples with at least one ecDNA (count > 0)
        samples_with_ecdna = (pivot_df[method] > 0).sum()
        
        # Calculate fraction
        fraction_with_ecdna = samples_with_ecdna / total_samples
        
        results.append({
            'method': method,
            'total_samples': total_samples,
            'samples_with_ecdna': samples_with_ecdna,
            'samples_without_ecdna': total_samples - samples_with_ecdna,
            'fraction_with_ecdna': fraction_with_ecdna,
            'percentage_with_ecdna': fraction_with_ecdna * 100
        })
    
    return pd.DataFrame(results)


def calculate_method_overlap(combined_counts_df):
    """Calculate overlap between methods (samples detected by both, only one, etc.)"""
    
    # Pivot the data to have methods as columns
    pivot_df = combined_counts_df.pivot(index='sample_name', columns='method', values='count').fillna(0)
    
    # Convert to binary (has ecDNA or not)
    binary_df = (pivot_df > 0).astype(int)
    
    # Calculate overlap statistics
    if 'short_reads' in binary_df.columns and 'long_reads' in binary_df.columns:
        both_methods = ((binary_df['short_reads'] == 1) & (binary_df['long_reads'] == 1)).sum()
        only_short_reads = ((binary_df['short_reads'] == 1) & (binary_df['long_reads'] == 0)).sum()
        only_long_reads = ((binary_df['short_reads'] == 0) & (binary_df['long_reads'] == 1)).sum()
        neither_method = ((binary_df['short_reads'] == 0) & (binary_df['long_reads'] == 0)).sum()
        
        total_samples = len(binary_df)
        
        overlap_stats = {
            'total_samples': total_samples,
            'both_methods': both_methods,
            'only_short_reads': only_short_reads,
            'only_long_reads': only_long_reads,
            'neither_method': neither_method,
            'fraction_both': both_methods / total_samples,
            'fraction_only_short': only_short_reads / total_samples,
            'fraction_only_long': only_long_reads / total_samples,
            'fraction_neither': neither_method / total_samples,
            'percentage_both': (both_methods / total_samples) * 100,
            'percentage_only_short': (only_short_reads / total_samples) * 100,
            'percentage_only_long': (only_long_reads / total_samples) * 100,
            'percentage_neither': (neither_method / total_samples) * 100
        }
        
        return pd.DataFrame([overlap_stats])
    else:
        return pd.DataFrame()


def create_summary_statistics(combined_counts_df):
    """Create summary statistics for the comparison"""
    
    # Basic statistics per method
    summary_stats = combined_counts_df.groupby('method').agg({
        'count': ['mean', 'median', 'std', 'min', 'max', 'sum']
    }).round(3)
    
    # Flatten column names
    summary_stats.columns = ['_'.join(col).strip() for col in summary_stats.columns]
    summary_stats = summary_stats.reset_index()
    
    return summary_stats


def main():
    """Main function to calculate cohort fractions"""
    args = parse_arguments()
    
    # Load combined counts data
    combined_counts_df = pd.read_csv(args.combined_counts, sep='\t')
    
    # Calculate cohort fractions
    cohort_fractions = calculate_cohort_fractions(combined_counts_df)
    
    # Calculate method overlap
    overlap_stats = calculate_method_overlap(combined_counts_df)
    
    # Create summary statistics
    summary_stats = create_summary_statistics(combined_counts_df)
    
    # Combine all results into a comprehensive output
    # Save main results
    cohort_fractions.to_csv(args.output_file, sep='\t', index=False)
    
    # Save additional analysis files
    base_path = args.output_file.replace('.tsv', '')
    
    if not overlap_stats.empty:
        overlap_stats.to_csv(f"{base_path}_overlap.tsv", sep='\t', index=False)
    
    summary_stats.to_csv(f"{base_path}_summary.tsv", sep='\t', index=False)


if __name__ == "__main__":
    main()
