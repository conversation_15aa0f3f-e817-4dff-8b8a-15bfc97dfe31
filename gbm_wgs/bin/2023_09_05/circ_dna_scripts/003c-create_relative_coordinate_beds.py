# <PERSON> Bahcheli
import argparse
import pandas as pd
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--intersection_bed", required=True)  # Intersection BED file
    parser.add_argument("--ecdna_bed", required=True)  # ecDNA regions BED file
    parser.add_argument("--output_file", required=True)  # Output summary file
    return parser.parse_args()


def genomic_to_relative_coordinate(genomic_pos, ecdna_segments):
    """Convert genomic coordinate to relative coordinate excluding gaps"""
    relative_pos = 0

    for _, segment in ecdna_segments.iterrows():
        segment_start = segment['start']
        segment_end = segment['end']

        if genomic_pos < segment_start:
            # Position is before this segment, shouldn't happen for intersected features
            break
        elif genomic_pos <= segment_end:
            # Position is within this segment
            relative_pos += genomic_pos - segment_start
            break
        else:
            # Position is after this segment, add full segment length and continue
            relative_pos += segment_end - segment_start

    return relative_pos


def create_relative_coordinates_by_circ_id(intersected_file, ecdna_file):
    if os.stat(intersected_file).st_size == 0 or os.stat(ecdna_file).st_size == 0:
        return None

    # load dfs 
    ecdna_df = pd.read_csv(ecdna_file, sep='\t')
    intersected_df = pd.read_csv(intersected_file, sep='\t', header=None)
    intersected_df.columns = ['exon_chrom', 'exon_start', 'exon_end', 'exon_name', 'exon_score', 'exon_strand', 'ecdna_chrom', 'ecdna_start', 'ecdna_end', 'circ_id', 'fragment_id', 'ecdna_strand', 'coverage', 'estimated_proportions']
    
    bed_files_data = {}
    
    # group by circ_id
    grouped = intersected_df.groupby('circ_id')
    
    for circ_id, group in grouped:
        # get ecDNA region info for this circ_id, sorted by start position
        ecdna_region = ecdna_df[ecdna_df['circ_id'] == circ_id].sort_values('start')

        if ecdna_region.empty:
            continue

        chrom = ecdna_region['#chr'].iloc[0] if '#chr' in ecdna_region.columns else ecdna_region['chr'].iloc[0]

        # calculate total ecDNA length (excluding gaps)
        total_ecdna_length = sum(ecdna_region['end'] - ecdna_region['start'])

        # create relative coordinates for exons
        bed_records = []
        for _, row in group.iterrows():
            # convert genomic coordinates to relative coordinates
            relative_start = genomic_to_relative_coordinate(row['exon_start'], ecdna_region)
            relative_end = genomic_to_relative_coordinate(row['exon_end'], ecdna_region)

            # validate that coordinates are within expected range
            if relative_start >= total_ecdna_length or relative_end > total_ecdna_length:
                print(f"Warning: Relative coordinates ({relative_start}-{relative_end}) exceed total ecDNA length ({total_ecdna_length}) for circ_id {circ_id}")

            # create BED record with relative coordinates
            bed_record = {
                'chrom': chrom,
                'start': relative_start,
                'end': relative_end,
                'name': row['exon_name'],
                'score': row['exon_score'],
                'strand': row['exon_strand'],
                'circ_id': circ_id
            }

            bed_records.append(bed_record)

        # create dataframe for this circ_id
        bed_df = pd.DataFrame(bed_records)
        bed_df = bed_df.sort_values(['start'])

        bed_files_data[circ_id] = bed_df
    
    return bed_files_data


# def write_bed_files_by_circ_id(bed_files_data, output_prefix):
#     """Write separate BED files for each circ_id"""
#     output_files = []
    
#     for circ_id, bed_df in bed_files_data.items():
#         # create output filename
#         output_file = f"{output_prefix}_circ_{circ_id}.bed"
        
#         # write BED file
#         if not bed_df.empty:
#             bed_df.to_csv(output_file, sep='\t', index=False, header=False)
        
#         output_files.append(output_file)
    
#     return output_files


def write_combined_file(bed_files_data, output_file):
    if bed_files_data == None:
        with open(output_file, "w") as outfile:
            pass
    else:
        # combine all dataframes
        combined_df = pd.concat(bed_files_data.values(), ignore_index=True)
        combined_df = combined_df.sort_values(['chrom', 'start'])
    
        # write combined BED file
        combined_df.to_csv(output_file, sep='\t', index=False)


def main():
    """Main function to create relative coordinate BED files from intersection results"""
    args = parse_arguments()

    # create relative coordinates by circ_id
    bed_files_data = create_relative_coordinates_by_circ_id(args.intersection_bed, args.ecdna_bed)

    # write combined file
    write_combined_file(bed_files_data, args.output_file)

    # # determine output prefix (remove .txt extension if present)
    # output_prefix = args.output_file.replace('.txt', '').replace('_summary', '')

    # # write BED files for each circ_id
    # output_files = write_bed_files_by_circ_id(bed_files_data, output_prefix)

    # # create a summary file listing all output files
    # with open(args.output_file, 'w') as f:
    #     for output_file in output_files:
    #         f.write(f"{output_file}\n")


if __name__ == "__main__":
    main()
