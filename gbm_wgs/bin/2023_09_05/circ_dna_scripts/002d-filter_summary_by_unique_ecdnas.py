#!/usr/bin/env python3
"""
Script to filter summary.txt based on unique circ_ids from filtered ecDNA BED file
Author: <PERSON>
Date: 2023-09-05
"""

import pandas as pd
import argparse
import os


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--filtered_bed", required=True)  # Filtered ecDNA BED file
    parser.add_argument("--original_summary", required=True)  # Original summary.txt file
    parser.add_argument("--output_summary", required=True)  # Output filtered summary file
    return parser.parse_args()


def get_unique_circ_ids(filtered_bed_file):
    """Extract unique circ_ids from filtered BED file"""
    # Read the filtered BED file
    bed_df = pd.read_csv(filtered_bed_file, sep='\t')
    
    # Get unique circ_ids
    unique_circ_ids = set(bed_df['circ_id'].unique())
    
    return unique_circ_ids


def filter_summary_file(original_summary_file, output_summary_file, unique_circ_ids):
    """Filter summary.txt file to keep only unique circ_ids"""
    # Read the original summary file
    summary_df = pd.read_csv(original_summary_file, sep='\t')
    
    # Filter to keep only unique circ_ids
    filtered_summary = summary_df[summary_df['circ_id'].isin(unique_circ_ids)]
    
    # Create output directory if needed
    os.makedirs(os.path.dirname(output_summary_file), exist_ok=True)
    
    # Save filtered summary
    filtered_summary.to_csv(output_summary_file, sep='\t', index=False)
    
    return len(summary_df), len(filtered_summary)


def main():
    """Main function to filter summary file based on unique ecDNAs"""
    args = parse_arguments()
    
    # check if summary file is empty
    if os.path.getsize(args.filtered_bed) == 0:
        # copy summary file to output without filtering
        with open(args.original_summary, 'r') as f:
            with open(args.output_summary, 'w') as f_out:
                f_out.write(f.read())
        return

    # Get unique circ_ids from filtered BED file
    unique_circ_ids = get_unique_circ_ids(args.filtered_bed)
    
    # Filter summary file
    filter_summary_file(args.original_summary, args.output_summary, unique_circ_ids)
    

if __name__ == "__main__":
    main()
