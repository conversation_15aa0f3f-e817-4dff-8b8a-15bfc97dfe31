# <PERSON> Bahcheli
import argparse
import pandas as pd
import os
import subprocess
import tempfile
import glob

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--sample_name", required=True)  # Sample name
    parser.add_argument("--amplicon_dir", required=True)  # Directory containing amplicon BED files
    parser.add_argument("--enhancer_bed", required=True)  # Enhancer BED file
    parser.add_argument("--bedtools_path", required=True)  # Path to bedtools
    parser.add_argument("--output_file", required=True)  # Output intersection file
    return parser.parse_args()

def run_bedtools_intersect(bedtools_path, bed_a, bed_b):
    """Run bedtools intersect and return results"""
    
    cmd = [bedtools_path, 'intersect', '-a', bed_a, '-b', bed_b, '-wa', '-wb']
    result = subprocess.run(cmd, capture_output=True, text=True, check=True)

    return result.stdout

def process_amplicon_intersections(sample_name, amplicon_dir, enhancer_bed, bedtools_path, output_file):
    """Process all amplicon files for a sample and intersect with enhancers"""
    
    # Find all amplicon BED files for this sample
    amplicon_pattern = os.path.join(amplicon_dir, f"{sample_name}_amplicon*_ecDNA_*_intervals.bed")
    amplicon_files = glob.glob(amplicon_pattern)
    
    print(f"Found {len(amplicon_files)} amplicon files for sample {sample_name}")
    
    # Process each amplicon file
    for amplicon_file in amplicon_files:
        if os.path.exists(amplicon_file) and os.path.getsize(amplicon_file) > 0:            
            # Extract amplicon identifier from filename
            amplicon_name = os.path.basename(amplicon_file).replace('_intervals.bed', '')
            
            # Read amplicon BED file
            try:
                amplicon_df = pd.read_csv(amplicon_file, sep='\t', header=None, names=['chr', 'start', 'end'])
                
                # Add unique amplicon IDs for each region
                amplicon_df = amplicon_df.reset_index()
                amplicon_df['amplicon_id'] = amplicon_df['index'].apply(lambda i: f"{amplicon_name}_{i+1}")
                
                # Create temporary file with amplicon IDs
                with tempfile.NamedTemporaryFile(mode='w', suffix='.bed', delete=False) as temp_amplicon:
                    amplicon_df[['chr', 'start', 'end', 'amplicon_id']].to_csv(
                        temp_amplicon.name, sep='\t', header=False, index=False
                    )
                    temp_amplicon_path = temp_amplicon.name
                
                try:
                    # Intersect with enhancers
                    intersection_output = run_bedtools_intersect(bedtools_path, temp_amplicon_path, enhancer_bed)
                    
                    # Append to output file
                    if intersection_output:
                        with open(output_file, 'a') as f:
                            f.write(intersection_output)
                        print(f"  Found {len(intersection_output.strip().split(chr(10)))} intersections")
                    else:
                        print(f"  No intersections found")
                
                finally:
                    # Clean up temporary file
                    if os.path.exists(temp_amplicon_path):
                        os.unlink(temp_amplicon_path)
                        
            except Exception as e:
                print(f"Error processing {amplicon_file}: {e}")
                continue
        else:
            print(f"Skipping empty or missing file: {amplicon_file}")
    
    # Ensure output file exists (create empty file if no intersections)
    if not os.path.exists(output_file):
        with open(output_file, 'w') as f:
            pass

def main():
    """Main function to process amplicon-enhancer intersections"""
    args = parse_arguments()
            
    # Process intersections
    process_amplicon_intersections(
        args.sample_name, 
        args.amplicon_dir, 
        args.enhancer_bed, 
        args.bedtools_path, 
        args.output_file
    )
    
    print("Processing complete!")

if __name__ == "__main__":
    main()
