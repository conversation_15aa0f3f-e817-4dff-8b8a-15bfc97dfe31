# <PERSON>cheli
import argparse
import pandas as pd
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--combined_amplicon_results", required=True)  # Combined amplicon results file
    parser.add_argument("--enhancer_intersections_dir", required=True)  # Directory containing enhancer intersection files
    parser.add_argument("--cgc_file", required=True)  # Cancer Gene Census file
    parser.add_argument("--sample_list", required=True)  # Comma-separated list of samples
    parser.add_argument("--output_file", required=True)  # Output file
    return parser.parse_args()


def load_cgc_genes(cgc_file):
    """Load Cancer Gene Census genes"""
    cgc_df = pd.read_csv(cgc_file, sep='\t')
    canonical_genes = set(cgc_df['GENE_SYMBOL'].dropna().unique())
    return canonical_genes


def process_sample_enhancer_intersections(sample_name, enhancer_intersections_dir):
    """Process enhancer intersections for a single sample"""
    enhancer_intersection_file = os.path.join(enhancer_intersections_dir, sample_name, "amplicon_enhancer_intersections.bed")

    if not os.path.exists(enhancer_intersection_file) or os.path.getsize(enhancer_intersection_file) == 0:
        return pd.DataFrame()

    # Read enhancer intersection file (bedtools intersect -wa -wb output)
    # Format: amplicon_chr, amplicon_start, amplicon_end, amplicon_id, enhancer_chr, enhancer_start, enhancer_end, enhancer_name, enhancer_score, enhancer_strand
    df = pd.read_csv(enhancer_intersection_file, sep='\t', header=None)
    df.columns = ['amplicon_chr', 'amplicon_start', 'amplicon_end', 'amplicon_id', 'enhancer_chr', 'enhancer_start', 'enhancer_end', 'enhancer_name', 'enhancer_score', 'enhancer_strand']
    df['sample_name'] = sample_name
    df['type'] = 'enhancer'

    # Create a dummy gene_name column for consistency
    df['gene_name'] = 'glioblastoma_enhancer'

    # Extract circ_id from amplicon_id (e.g., RLGS2-primary_amplicon1_ecDNA_1_1 -> amplicon1_ecDNA_1)
    df['circ_id'] = df['amplicon_id'].str.replace(f'{sample_name}_', '').str.replace('_\d+$', '', regex=True)

    return df


def process_amplicon_results(combined_amplicon_results):
    """Process combined amplicon results to create gene intersections"""
    amplicon_df = pd.read_csv(combined_amplicon_results, sep='\t')

    # Create a DataFrame that mimics gene intersections
    # Each row represents a gene found in an amplicon
    results = []

    for _, row in amplicon_df.iterrows():
        sample_name = row['sample_name']
        # Create a unique circ_id for each amplicon (using row index as amplicon number)
        circ_id = f"amplicon_{row.name + 1}"

        # Process oncogenes if they exist
        if 'canonical_oncogenes' in row and pd.notna(row['canonical_oncogenes']) and row['canonical_oncogenes'] != 'None':
            oncogenes = [og.strip() for og in row['canonical_oncogenes'].split(',')]
            for oncogene in oncogenes:
                results.append({
                    'sample_name': sample_name,
                    'circ_id': circ_id,
                    'gene_name': oncogene,
                    'type': 'gene'
                })

        # Process other genes if they exist
        if 'other_genes' in row and pd.notna(row['other_genes']) and row['other_genes'] != 'None':
            other_genes = [og.strip() for og in row['other_genes'].split(',')]
            for gene in other_genes:
                results.append({
                    'sample_name': sample_name,
                    'circ_id': circ_id,
                    'gene_name': gene,
                    'type': 'gene'
                })

    return pd.DataFrame(results)


def analyze_oncogenes_per_amplicon(intersections_df, canonical_genes):
    """Analyze canonical oncogenes and enhancers per amplicon (circ_id)"""
    results = []

    # Group by sample and circ_id
    grouped = intersections_df.groupby(['sample_name', 'circ_id'])

    for (sample_name, circ_id), group in grouped:
        # Get unique genes in this amplicon
        genes_in_amplicon = set(group[group['type'] == 'gene']['gene_name'].dropna().unique())

        # Find canonical oncogenes
        oncogenes_in_amplicon = genes_in_amplicon.intersection(canonical_genes)

        # Count enhancers
        enhancer_count = len(group[group['type'] == 'enhancer'])

        # Count other genes (non-oncogenes)
        other_genes_count = len(genes_in_amplicon) - len(oncogenes_in_amplicon)

        # Create result record
        result = {
            'sample_name': sample_name,
            'circ_id': circ_id,
            'total_genes': len(genes_in_amplicon),
            'canonical_oncogenes_count': len(oncogenes_in_amplicon),
            'canonical_oncogenes': ','.join(sorted(oncogenes_in_amplicon)) if oncogenes_in_amplicon else 'None',
            'enhancer_count': enhancer_count,
            'other_genes_count': other_genes_count,
            'all_genes': ','.join(sorted(genes_in_amplicon)) if genes_in_amplicon else 'None'
        }

        results.append(result)

    return pd.DataFrame(results)


def count_features(group):
    """Count amplicons with oncogenes, enhancers, and other genes per sample"""
    oncogene_mask = group['canonical_oncogenes_count'] > 0
    oncogenes = len(group[oncogene_mask])

    remaining = group[~oncogene_mask]
    enhancers = len(remaining[remaining['enhancer_count'] > 0])

    remaining = remaining[remaining['enhancer_count'] == 0]
    other_genes = len(remaining[remaining['other_genes_count'] > 0])

    remaining = remaining[remaining['other_genes_count'] == 0]
    without_genes = len(remaining)

    return pd.Series({
        'with_oncogenes': oncogenes,
        'with_enhancers': enhancers,
        'with_other_genes': other_genes,
        'without_genes': without_genes
    })


def main():
    """Main function to analyze canonical oncogenes and enhancers in amplicons"""
    args = parse_arguments()
    sample_list = args.sample_list.split(',')

    # Load canonical genes
    canonical_genes = load_cgc_genes(args.cgc_file)

    # Process amplicon results to get gene intersections
    amplicon_intersections = process_amplicon_results(args.combined_amplicon_results)

    # Process all samples for enhancer intersections
    all_enhancer_intersections = []
    for sample in sample_list:
        enhancer_df = process_sample_enhancer_intersections(sample.strip(), args.enhancer_intersections_dir)
        if not enhancer_df.empty:
            all_enhancer_intersections.append(enhancer_df)

    # Combine all intersections
    all_intersections = []
    if not amplicon_intersections.empty:
        all_intersections.append(amplicon_intersections)
    if all_enhancer_intersections:
        combined_enhancer_intersections = pd.concat(all_enhancer_intersections, ignore_index=True)
        all_intersections.append(combined_enhancer_intersections)

    # Combine all intersections
    combined_intersections = pd.concat(all_intersections, ignore_index=True)

    # Analyze oncogenes per amplicon
    df = analyze_oncogenes_per_amplicon(combined_intersections, canonical_genes)

    # Save results
    df.to_csv(args.output_file, sep='\t', index=False)

    print(f"Processed {len(sample_list)} samples")


if __name__ == "__main__":
    main()

