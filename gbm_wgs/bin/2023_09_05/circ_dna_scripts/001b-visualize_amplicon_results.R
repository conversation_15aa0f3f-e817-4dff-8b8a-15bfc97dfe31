#!/usr/bin/env Rscript

# Load required packages while suppressing startup messages
suppressPackageStartupMessages({
  library(ggplot2)      # For creating plots and themes
  library(dplyr)        # For data manipulation
  library(tidyr)        # For data tidying
  library(readr)        # For reading TSV files
  library(RColorBrewer) # For color palettes
  library(optparse)     # For parsing command line options
})

# Define command line options
option_list <- list(
  make_option(c("-i", "--input_file"), type="character", help="Path to input TSV file"),
  make_option(c("-c", "--counts_file"), type="character", help="Path to counts TSV file"),
  make_option(c("-o", "--figure_file"), type="character", help="Prefix for output files")
)

# Parse command line arguments
opt_parser <- OptionParser(option_list=option_list)
opt <- parse_args(opt_parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



create_plots = function(input_df, counts_df) {

# Process the data:
# - Extract patient ID from before the hyphen in sample_name
# - Extract tumor type from after the hyphen in sample_name
# - Convert size from base pairs to megabases
data <- input_df %>%
  mutate(
    patient = gsub("-.*", "", sample_name),
    tumor_type = gsub(".*-", "", sample_name),
    size_mb = size_bp / 1e6
  )

# Create count plot of number of amplicons per sample (including zero counts)
p <- ggplot(counts_df, aes(x = sample_name, y = n_amplicons)) + plot_theme() +
geom_bar(stat = "identity", color = "black") +

geom_text(aes(label = n_amplicons), vjust = -0.5) +
labs(title = "Number of Amplicons per Sample", x = "Sample", y = "Count") +

theme(axis.text.x = element_text(angle = 90, hjust = 1, vjust = 0.5))

print(p)



# Create plot of canonical oncogenes per sample-amplicon
oncogene_data <- input_df %>%
  mutate(
    oncogene_count = ifelse(!is.na(canonical_oncogenes), 
                           lengths(strsplit(canonical_oncogenes, ",")), 
                           0),
    sample_label = paste0(sample_name, "_", sapply(strsplit(amplicon_name, "_"), `[`, 1))  # Add first part before "_"
  ) %>%
  group_by(sample_label, amplicon_name) %>%
  summarise(
    oncogene_count = sum(oncogene_count),
    oncogene_names = paste(na.omit(canonical_oncogenes), collapse=", "),
    .groups = 'drop'
  )

p <- ggplot(oncogene_data, aes(x = sample_label, y = oncogene_count)) + plot_theme() +
geom_bar(stat = "identity") +
geom_text(aes(label = oncogene_names), vjust = -0.5, size = 2) +  # Oncogene labels above bars

labs(title = "Canonical Oncogenes per Sample-Amplicon", x = "Sample", y = "Count of Oncogenes") +
theme(axis.text.x = element_text(angle = 45, hjust = 1, vjust = 1)) 

print(p)

}


# Save plots to PDF
pdf(opt$figure_file)

# Read the input data
input_df <- read_tsv(opt$input_file, show_col_types = FALSE)
counts_df <- read_tsv(opt$counts_file, show_col_types = FALSE)

# create plots
create_plots(input_df, counts_df)

dev.off()
