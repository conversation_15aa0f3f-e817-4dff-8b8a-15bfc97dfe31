# Alec Bahcheli
import argparse
import pandas as pd
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--amplicon_counts", required=True)
    parser.add_argument("--decoil_counts", required=True)
    parser.add_argument("--output_file", required=True)
    return parser.parse_args()


def combine_dfs(amplicon_counts, decoil_counts):
    # load counts files
    amplicon_counts = pd.read_csv(amplicon_counts, sep='\t')
    decoil_counts = pd.read_csv(decoil_counts, sep='\t')
    
    # merge on sample_name
    merged_counts = pd.merge(amplicon_counts, decoil_counts, on='sample_name', how='outer')

    # rename columns
    merged_counts = merged_counts.rename(columns={'n_amplicons': 'amplicon_architect', 'n_ecdnas': 'decoil'})

    # melt
    merged_counts = merged_counts.melt(id_vars='sample_name', var_name='method', value_name='count')

    # rename methods
    merged_counts['method'] = merged_counts['method'].replace({'amplicon_architect': 'short_reads', 'decoil': 'long_reads'})
    
    return merged_counts


def main():
    """Main function to combine method counts"""
    args = parse_arguments()

    # combine dfs
    merged_counts = combine_dfs(args.amplicon_counts, args.decoil_counts)
    
    # save results
    merged_counts.to_csv(args.output_file, sep='\t', index=False)

if __name__ == "__main__":
    main()
