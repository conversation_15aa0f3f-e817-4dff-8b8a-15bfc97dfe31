# <PERSON>
library(optparse)
library(ggplot2)
library(dplyr)
library(tidyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--cohort_fraction"), type="character", default=NULL,
            help="Cohort fraction file",
            dest="cohort_fraction"),
    make_option(c("-b","--figure_file"), type="character", default=NULL,
            help="Output figure file",
            dest="figure_file")
)

parser <- OptionParser(usage = "%prog -a cohort_fraction.tsv -b figure.pdf", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}

create_fraction_bar_plot = function(fraction_df) {
    
    # Define colors consistent with other plots
    method_colors <- c("short_reads" = "#229a32", "long_reads" = "#df7b20")
    
    # Create bar plot showing fraction with ecDNAs
    p1 = ggplot(fraction_df, aes(x = method, y = percentage_with_ecdna, fill = method)) +
        geom_bar(stat = "identity", color = 'black', width = 0.6) +
        geom_text(aes(label = paste0(round(percentage_with_ecdna, 1), "%\n(", samples_with_ecdna, "/", total_samples, ")")), 
                  vjust = -0.5, size = 5) +
        scale_fill_manual(values = method_colors, name = "Method") +
        scale_x_discrete(labels = c("short_reads" = "Short Reads\n(AmpliconSuite)", 
                                   "long_reads" = "Long Reads\n(DeCoil)")) +
        labs(title = "Fraction of Cohort with ecDNAs by Detection Method", 
             y = "Percentage of Samples with ecDNAs (%)", 
             x = "Detection Method") +
        ylim(0, max(fraction_df$percentage_with_ecdna) * 1.2) +
        plot_theme() +
        theme(legend.position = "none")
    
    print(p1)
    
    return()
}

create_sample_counts_plot = function(fraction_df) {
    
    # Reshape data for stacked bar plot
    plot_data <- fraction_df %>%
        select(method, samples_with_ecdna, samples_without_ecdna) %>%
        pivot_longer(cols = c(samples_with_ecdna, samples_without_ecdna), 
                     names_to = "ecdna_status", values_to = "count")
    
    # Define colors for ecDNA status
    status_colors <- c("samples_with_ecdna" = "#2E8B57", "samples_without_ecdna" = "#D3D3D3")
    
    p2 = ggplot(plot_data, aes(x = method, y = count, fill = ecdna_status)) +
        geom_bar(stat = "identity", color = 'black') +
        geom_text(aes(label = count), position = position_stack(vjust = 0.5), size = 4) +
        scale_fill_manual(values = status_colors, 
                         name = "ecDNA Status",
                         labels = c("samples_with_ecdna" = "With ecDNAs", 
                                   "samples_without_ecdna" = "Without ecDNAs")) +
        scale_x_discrete(labels = c("short_reads" = "Short Reads\n(AmpliconSuite)", 
                                   "long_reads" = "Long Reads\n(DeCoil)")) +
        labs(title = "Sample Distribution by ecDNA Detection Status", 
             y = "Number of Samples", 
             x = "Detection Method") +
        plot_theme()
    
    print(p2)
    
    return()
}

create_overlap_plot = function(base_path) {
    
    # Try to load overlap data
    overlap_file <- paste0(base_path, "_overlap.tsv")
    
    if(file.exists(overlap_file)) {
        overlap_df <- read.csv(overlap_file, sep='\t')
        
        # Reshape for plotting
        overlap_plot_data <- data.frame(
            category = c("Both Methods", "Short Reads Only", "Long Reads Only", "Neither Method"),
            count = c(overlap_df$both_methods, overlap_df$only_short_reads, 
                     overlap_df$only_long_reads, overlap_df$neither_method),
            percentage = c(overlap_df$percentage_both, overlap_df$percentage_only_short,
                          overlap_df$percentage_only_long, overlap_df$percentage_neither)
        )
        
        # Define colors for overlap categories
        overlap_colors <- c("Both Methods" = "#8B0000", "Short Reads Only" = "#229a32", 
                           "Long Reads Only" = "#df7b20", "Neither Method" = "#D3D3D3")
        
        p3 = ggplot(overlap_plot_data, aes(x = category, y = percentage, fill = category)) +
            geom_bar(stat = "identity", color = 'black') +
            geom_text(aes(label = paste0(round(percentage, 1), "%\n(", count, ")")), 
                      vjust = -0.5, size = 4) +
            scale_fill_manual(values = overlap_colors, name = "Detection Category") +
            labs(title = "Method Overlap: Samples Detected by Each Method", 
                 y = "Percentage of Samples (%)", 
                 x = "Detection Category") +
            ylim(0, max(overlap_plot_data$percentage) * 1.2) +
            plot_theme() +
            theme(legend.position = "none",
                  axis.text.x = element_text(angle = 45, hjust = 1))
        
        print(p3)
    }
    
    return()
}


pdf(opt$figure_file, height=7, width=5)

# Load cohort fraction data
fraction_df <- read.csv(opt$cohort_fraction, sep='\t')

# Reorder methods for consistent display
fraction_df$method <- factor(fraction_df$method, levels = c('short_reads', 'long_reads'))

# Create plots
create_fraction_bar_plot(fraction_df)
create_sample_counts_plot(fraction_df)

# Create overlap plot if data exists
base_path <- gsub('.tsv$', '', opt$cohort_fraction)
create_overlap_plot(base_path)

dev.off()

print(opt$figure_file)

