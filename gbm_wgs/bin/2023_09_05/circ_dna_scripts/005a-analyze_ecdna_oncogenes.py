# <PERSON>i
import argparse
import pandas as pd
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--gene_intersections_dir", required=True)  # Directory containing gene intersection files
    parser.add_argument("--enhancer_intersections_dir", required=True)  # Directory containing enhancer intersection files
    parser.add_argument("--cgc_file", required=True)  # Cancer Gene Census file
    parser.add_argument("--sample_list", required=True)  # Comma-separated list of samples
    parser.add_argument("--output_file", required=True)  # Output file
    return parser.parse_args()


def load_cgc_genes(cgc_file):
    """Load Cancer Gene Census genes"""
    cgc_df = pd.read_csv(cgc_file, sep='\t')
    # get unique gene symbols
    canonical_genes = set(cgc_df['GENE_SYMBOL'].dropna().unique())
    return canonical_genes


def process_sample_intersections(sample_name, gene_intersections_dir, enhancer_intersections_dir):
    """Process gene and enhancer intersections for a single sample"""
    gene_intersection_file = os.path.join(gene_intersections_dir, f"{sample_name}_gene_relative_coordinates.tsv")
    enhancer_intersection_file = os.path.join(enhancer_intersections_dir, sample_name, "enhancer_relative_coordinates.tsv")
    
    df = []

    if os.path.exists(gene_intersection_file) and os.path.getsize(gene_intersection_file) != 0:
        tmp_df = pd.read_csv(gene_intersection_file, sep='\t')
        tmp_df['type'] = 'gene'
        df.append(tmp_df)

    if os.path.exists(enhancer_intersection_file) and os.path.getsize(enhancer_intersection_file) != 0:
        tmp_df = pd.read_csv(enhancer_intersection_file, sep='\t')
        tmp_df['type'] = 'enhancer'
        df.append(tmp_df)
    
    # Combine DataFrames and add sample name
    if len(df) == 0:
        return pd.DataFrame()
    
    df = pd.concat(df, ignore_index=True)
    df['sample_name'] = sample_name
    df = df.rename(columns = {'name':'gene_name'})
    
    return df


def analyze_oncogenes_per_ecdna(intersections_df, canonical_genes):
    """Analyze canonical oncogenes and enhancers per ecDNA (circ_id)"""
    results = []
    
    # group by sample and circ_id
    grouped = intersections_df.groupby(['sample_name', 'circ_id'])
    
    for (sample_name, circ_id), group in grouped:
        # get unique genes in this ecDNA
        genes_in_ecdna = set(group['gene_name'].unique())
        
        # find canonical oncogenes
        oncogenes_in_ecdna = genes_in_ecdna.intersection(canonical_genes)

        # create result record
        result = {
            'sample_name': sample_name,
            'circ_id': circ_id,
            'total_genes': len(genes_in_ecdna),
            'canonical_oncogenes_count': len(oncogenes_in_ecdna),
            'canonical_oncogenes': ','.join(sorted(oncogenes_in_ecdna)) if oncogenes_in_ecdna else 'None',
            'enhancer_count': len(group[group['type'] == 'enhancer']),
            'other_genes_count': len(group[group['type'] == 'gene']) - len(oncogenes_in_ecdna),
            'all_genes': ','.join(sorted(genes_in_ecdna)) if genes_in_ecdna else 'None'
        }

        results.append(result)

    return pd.DataFrame(results)


def main():
    """Main function to analyze canonical oncogenes and enhancers in ecDNA"""
    args = parse_arguments()
    sample_list = args.sample_list.split(',')

    # load canonical genes
    canonical_genes = load_cgc_genes(args.cgc_file)

    # process all samples for gene intersections
    all_intersections = []
    for sample in sample_list:
        sample_df = process_sample_intersections(sample.strip(), args.gene_intersections_dir, args.enhancer_intersections_dir)
        if not sample_df.empty:
            all_intersections.append(sample_df)

    # combine all gene intersections
    combined_intersections = pd.concat(all_intersections, ignore_index=True)

    # analyze oncogenes and enhancers per ecDNA
    oncogene_analysis = analyze_oncogenes_per_ecdna(combined_intersections, canonical_genes)

    # add patient and tumor type columns
    oncogene_analysis['patient'] = oncogene_analysis['sample_name'].str.split('-').str[0]
    oncogene_analysis['tumor_type'] = oncogene_analysis['sample_name'].str.split('-').str[1]

    # sort results
    oncogene_analysis = oncogene_analysis.sort_values(['sample_name', 'canonical_oncogenes_count'], ascending=[True, False])

    # save results
    oncogene_analysis.to_csv(args.output_file, sep='\t', index=False)


if __name__ == "__main__":
    main()
