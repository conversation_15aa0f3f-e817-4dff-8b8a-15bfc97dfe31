# <PERSON> Ba<PERSON>cheli
import argparse
import pandas as pd
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--gene_intersections", required=True)  
    parser.add_argument("--enhancer_intersections", required=True)  
    parser.add_argument("--cgc_file", required=True)  # Cancer Gene Census file
    parser.add_argument("--output_file", required=True)  # Output file
    return parser.parse_args()


def load_cgc_genes(cgc_file):
    """Load Cancer Gene Census genes"""
    cgc_df = pd.read_csv(cgc_file, sep='\t')
    # get unique gene symbols
    canonical_genes = set(cgc_df['GENE_SYMBOL'].dropna().unique())
    return canonical_genes


def combine_intersections(gene_intersections, enhancer_intersections):
    """Process gene and enhancer intersections for a single sample"""
    df = []

    tmp_df = pd.read_csv(gene_intersections, sep='\t')
    tmp_df['type'] = 'gene'
    df.append(tmp_df)

    tmp_df = pd.read_csv(enhancer_intersections, sep='\t')
    tmp_df['type'] = 'enhancer'
    df.append(tmp_df)
    
    df = pd.concat(df, ignore_index=True)
    df = df.rename(columns = {'name':'gene_name'})
    
    return df


def analyze_oncogenes_per_ecdna(intersections_df, canonical_genes):
    """Analyze canonical oncogenes and enhancers per ecDNA (circ_id)"""
    results = []
    
    # group by sample and circ_id
    grouped = intersections_df.groupby(['circ_id'])
    
    for circ_id, group in grouped:
        circ_id = circ_id[0]
        sample = circ_id.split('_')[0]
        
        # get unique genes in this ecDNA
        genes_in_ecdna = set(group['gene_name'].unique())

        # remove "glioblastoma_enhancer" from genes_in_ecdna
        if "glioblastoma_enhancer" in genes_in_ecdna:
            genes_in_ecdna.remove("glioblastoma_enhancer")
        
        # find canonical oncogenes
        oncogenes_in_ecdna = genes_in_ecdna.intersection(canonical_genes)

        # create result record
        result = {
            'sample_name': sample,
            'circ_id': circ_id,
            'total_genes': len(genes_in_ecdna),
            'canonical_oncogenes_count': len(oncogenes_in_ecdna),
            'canonical_oncogenes': ','.join(sorted(oncogenes_in_ecdna)) if oncogenes_in_ecdna else 'None',
            'enhancer_count': len(group[group['type'] == 'enhancer']),
            'other_genes_count': len(group[group['type'] == 'gene']) - len(oncogenes_in_ecdna),
            'all_genes': ','.join(sorted(genes_in_ecdna)) if genes_in_ecdna else 'None'
        }

        results.append(result)

    return pd.DataFrame(results)


def main():
    """Main function to analyze canonical oncogenes and enhancers in ecDNA"""
    args = parse_arguments()

    # load canonical genes
    canonical_genes = load_cgc_genes(args.cgc_file)

    combined_intersections = combine_intersections(args.gene_intersections, args.enhancer_intersections)

    # analyze oncogenes and enhancers per ecDNA
    oncogene_analysis = analyze_oncogenes_per_ecdna(combined_intersections, canonical_genes)

    # add patient and tumor type columns
    oncogene_analysis['patient'] = oncogene_analysis['sample_name'].str.split('-').str[0]
    oncogene_analysis['tumor_type'] = oncogene_analysis['sample_name'].str.split('-').str[1]

    # save results
    oncogene_analysis.to_csv(args.output_file, sep='\t', index=False)


if __name__ == "__main__":
    main()
