# <PERSON>
library(optparse)
library(ggplot2)
library(dplyr)
library(tidyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--combined_counts"), type="character", default=NULL,
            help="Combined counts file",
            dest="combined_counts"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="Output figure file",
            dest="figure_file")
)

parser <- OptionParser(usage = "%prog -a combined_counts.tsv -b oncogene_counts.tsv -c figure.pdf", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}

create_comparison_plots = function(input_df) {

# Define colors
method_colors <- c("short_reads" = "#229a32", "long_reads" = "#df7b20")

p = ggplot(input_df, aes(x = sample_name, y = count, fill = method)) +
geom_bar(stat = "identity", position = position_dodge(width = 0.8), width = 0.8, color='black') +
geom_text(aes(label = count), position = position_dodge(width = 0.8), vjust = -0.5) +

scale_fill_manual(values = method_colors, name = "Method") +

labs(title = "ecDNA Detection Method Comparison", y = "Number of ecDNAs", x = "") +

plot_theme() +
theme(axis.text.x = element_text(angle = 90, hjust = 1, vjust = 0.5))

print(p)

return()
}



reorder_samples = function(df){
# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# order samples
sorted_vector <- unique(df$sample_name)[order(sapply(unique(df$sample_name), extract_numeric))]
df$sample_name = factor(df$sample_name, levels = sorted_vector)

return(df)
}



pdf(opt$figure_file, width=15)

# load df
counts_df = read.csv(opt$combined_counts, sep='\t')

# reorder samples
counts_df = reorder_samples(counts_df)

# reorder methods
counts_df$method = factor(counts_df$method, levels = c('short_reads', 'long_reads'))

# plot
create_comparison_plots(counts_df)

dev.off()

print(opt$figure_file)
