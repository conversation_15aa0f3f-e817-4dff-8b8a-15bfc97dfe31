# Alec Bahcheli
import argparse
import pandas as pd
import os
import glob

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--results_dir", required=True)  # Directory containing amplicon BED files
    parser.add_argument("--output_file", required=True)  # Output intersection file
    return parser.parse_args()


def combine_ecdna_bed_files(results_dir):
    # Find all amplicon BED files for this sample
    amplicon_pattern = os.path.join(results_dir, "circ_dna/*/ampliconsuite/ampliconclassifier/amplicon_information/*amplicon*_ecDNA_*_intervals.bed")
    amplicon_files = glob.glob(amplicon_pattern)
    
    all_ecdna_dfs = []

    # load and combine files
    for amplicon_file in amplicon_files:
        df = pd.read_csv(amplicon_file, sep='\t', header=None)
        df.columns = ['#chr', 'start', 'end']

        # add sample name and amplicon
        sample = amplicon_file.split('/')[-1].split('_')[0]
        circ_id = amplicon_file.split('/')[-1].split('_')[1]
        df['circ_id'] = sample + "_" + circ_id
        df['sample'] = sample

        all_ecdna_dfs.append(df)

    # combine all dfs
    df = pd.concat(all_ecdna_dfs, ignore_index=True)

    # for consistency, add other details
    df[['fragment_id', 'strand', 'coverage', 'estimated_proportions']] = ['unknown', '+', 'unknown', 'unknown']

    # sort columns
    df = df[['#chr', 'start', 'end', 'circ_id', 'fragment_id', 'strand', 'coverage', 'estimated_proportions', 'sample']]

    return df


def main():
    """Main function to process amplicon-enhancer intersections"""
    args = parse_arguments()
            
    # Process intersections
    df = combine_ecdna_bed_files(args.results_dir)

    # save results
    df.to_csv(args.output_file, sep='\t', index=False)
    
    print("Processing complete!")

if __name__ == "__main__":
    main()
