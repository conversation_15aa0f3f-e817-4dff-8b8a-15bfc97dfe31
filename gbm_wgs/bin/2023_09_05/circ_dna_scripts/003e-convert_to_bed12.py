# Alec Bahcheli
import argparse
import pandas as pd
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--exon_coords_summary", required=False)  # Summary file listing exon relative coordinate BED files
    parser.add_argument("--gene_coords_summary", required=False)  # Summary file listing gene relative coordinate BED files
    parser.add_argument("--enhancer_coords_summary", required=False)  # Summary file listing enhancer relative coordinate BED files
    parser.add_argument("--sample_name", required=False)  # Summary file listing gene relative coordinate BED files
    parser.add_argument("--output_dir", required=True)  # Output directory for BED12 files
    return parser.parse_args()


def convert_relative_bed_to_bed12(relative_df, feature_type="exon", default_color="0,255,0"):
    """Convert relative coordinate BED to BED12 format following complete BED12 specification"""
    if relative_df.empty:
        return pd.DataFrame()

    bed12_records = []

    # group by gene name (without exon number)
    relative_df['gene_name'] = relative_df['name'].str.split('_').str[0]
    grouped = relative_df.groupby('gene_name')

    for gene_name, group in grouped:
        chrom = group.iloc[0]['chrom']
        strand = group.iloc[0]['strand']

        # sort by start position to ensure proper order
        group = group.sort_values('start')

        # calculate block information according to BED12 spec
        chrom_start = group['start'].min()
        chrom_end = group['end'].max()

        # block starts must be relative to chromStart (column 2)
        block_starts = (group['start'] - chrom_start).tolist()
        block_sizes = (group['end'] - group['start']).tolist()
        block_count = len(group)

        # validate BED12 format requirements
        # first exon start must be 0
        assert block_starts[0] == 0, f"First block start must be 0, got {block_starts[0]}"

        # last exon's start + size must equal chromEnd - chromStart
        last_block_end = block_starts[-1] + block_sizes[-1]
        expected_span = chrom_end - chrom_start
        assert last_block_end == expected_span, f"Last block end {last_block_end} != span {expected_span}"

        # create BED12 record following complete specification
        bed12_record = {
            'chrom': chrom,                                    # Column 1: seqid
            'chromStart': chrom_start,                         # Column 2: start (0-based)
            'chromEnd': chrom_end,                             # Column 3: end (not included)
            'name': f"{gene_name}_{feature_type}",             # Column 4: feature name
            'score': 1000,                                     # Column 5: score (0-1000)
            'strand': strand,                                  # Column 6: directionality
            'thickStart': chrom_start,                         # Column 7: thick start
            'thickEnd': chrom_end,                             # Column 8: thick end
            'itemRgb': default_color,                          # Column 9: color (R,G,B)
            'blockCount': block_count,                         # Column 10: exon count
            'blockSizes': ','.join(str(size) for size in block_sizes) + ',',    # Column 11: exon sizes
            'blockStarts': ','.join(str(start) for start in block_starts) + ',' # Column 12: exon starts
        }

        bed12_records.append(bed12_record)

    # create dataframe
    bed12_df = pd.DataFrame(bed12_records)

    # sort by chromosome and start position
    bed12_df = bed12_df.sort_values(['chrom', 'chromStart'])

    return bed12_df


def write_bed12_file(bed12_df, output_file):
    """Write BED12 dataframe to file with track header"""
    with open(output_file, 'w') as f:
        # write track header
        f.write("track itemRgb=On\n")

        # write BED12 records
        if not bed12_df.empty:
            bed12_df.to_csv(f, sep='\t', index=False, header=False)


def load_dfs_and_circ_ids(exon_coords_summary, gene_coords_summary, enhancer_coords_summary):
    # collect all circ_ids from both exon and gene files
    all_circ_ids = set()

    # process exon files
    exon_data = {}

    # if the file is not empty
    if os.path.getsize(exon_coords_summary) > 0:
        # load df
        exon_df = pd.read_csv(exon_coords_summary, sep='\t')
        # uniq circ_ids
        circ_ids = exon_df['circ_id'].unique()
        for circ_id in circ_ids:
            # subset to this circ_id
            circ_df = exon_df[exon_df['circ_id'] == circ_id]
            if not circ_df.empty:
                exon_data[circ_id] = circ_df
                all_circ_ids.add(circ_id)

    # process gene files
    gene_data = {}

    # if the file is not empty
    if os.path.getsize(gene_coords_summary) > 0:
        # load df
        gene_df = pd.read_csv(gene_coords_summary, sep='\t')
        # uniq circ_ids
        circ_ids = gene_df['circ_id'].unique()
        for circ_id in circ_ids:
            # subset to this circ_id
            circ_df = gene_df[gene_df['circ_id'] == circ_id]
            if not circ_df.empty:
                gene_data[circ_id] = circ_df
                all_circ_ids.add(circ_id)

    # process enhancer gene files 
    enhancer_data = {}

    # if the file is not empty
    if os.path.getsize(enhancer_coords_summary) > 0:
        # load df
        enhancer_df = pd.read_csv(enhancer_coords_summary, sep='\t')
        # uniq circ_ids
        circ_ids = enhancer_df['circ_id'].unique()
        for circ_id in circ_ids:
            # subset to this circ_id
            circ_df = enhancer_df[enhancer_df['circ_id'] == circ_id]
            if not circ_df.empty:
                enhancer_data[circ_id] = circ_df
                all_circ_ids.add(circ_id)

    return exon_data, gene_data, enhancer_data, all_circ_ids


def process_circ_ids(all_circ_ids, exon_data, gene_data, enhancer_data, sample_name, output_dir):
    # create combined BED12 files for each circ_id
    for circ_id in all_circ_ids:
        combined_bed12_records = []

        # add gene records (blue color)
        if circ_id in gene_data:
            gene_bed12_df = convert_relative_bed_to_bed12(gene_data[circ_id], "gene", "0,0,255")
            combined_bed12_records.append(gene_bed12_df)

        # add exon records (green color)
        if circ_id in exon_data:
            exon_bed12_df = convert_relative_bed_to_bed12(exon_data[circ_id], "exon", "0,255,0")
            combined_bed12_records.append(exon_bed12_df)

        # add enhancer records (yellow color)
        if circ_id in enhancer_data:
            enhancer_bed12_df = convert_relative_bed_to_bed12(enhancer_data[circ_id], "enhancer", "255,255,0")
            combined_bed12_records.append(enhancer_bed12_df)

        # combine all records
        if combined_bed12_records:
            combined_df = pd.concat(combined_bed12_records, ignore_index=True)
            combined_df = combined_df.sort_values(['chrom', 'chromStart'])

            # create output filename
            output_file = os.path.join(output_dir, f"{sample_name}_circ_{circ_id}_features.bed")

            # write BED12 file
            write_bed12_file(combined_df, output_file)

    # write file to indicate completion
    with open(os.path.join(output_dir, f"{sample_name}_complete.txt"), 'w') as f:
        f.write("Complete")


def main():
    """Main function to convert relative coordinate BED files to BED12 format"""
    args = parse_arguments()

    # load dataframes and circ_ids
    exon_data, gene_data, enhancer_data, all_circ_ids = load_dfs_and_circ_ids(args.exon_coords_summary, args.gene_coords_summary, args.enhancer_coords_summary)

    # process and combine exons and genes into annotation file
    process_circ_ids(all_circ_ids, exon_data, gene_data, enhancer_data, args.sample_name, args.output_dir)


if __name__ == "__main__":
    main()
