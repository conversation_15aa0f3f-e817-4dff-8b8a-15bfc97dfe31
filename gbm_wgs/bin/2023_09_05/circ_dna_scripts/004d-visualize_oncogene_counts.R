# <PERSON>
library(optparse)
library(ggplot2)
library(dplyr)
library(tidyr)

# options list for parser options
option_list <- list(
    make_option(c("-b","--combined_oncogene_counts"), type="character", default=NULL,
            help="Combined oncogene counts file",
            dest="combined_oncogene_counts"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="Output figure file",
            dest="figure_file")
)

parser <- OptionParser(usage = "%prog -a combined_counts.tsv -b combined_oncogene_counts.tsv -c figure.pdf", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}

create_comparison_plots = function(oncogene_df) {

# Define colors
oncogene_colors <- c("has_oncogenes" = "#b22222", "only_enhancers" = "#e1a61e", "has_other_genes" = "#1e90ff", "no_genes_or_enhancers" = "#a3a3a3")

# perform for all samples combined, comparing between SR and LR
sub_df = oncogene_df[oncogene_df$sample_name == 'total',]
    
p = ggplot(sub_df, aes(x = method, y = count, fill = oncogene_status)) +
geom_bar(stat = "identity", color='black') +
geom_text(aes(label = count), position = position_stack(vjust = 0.5)) +

scale_fill_manual(values = oncogene_colors, name = "Oncogene Status") +

labs(title = "ecDNA Oncogene Content by Method", y = "Number of ecDNAs", x = "Method") +
plot_theme()

print(p)


# perform for each sample LR only
sub_df = oncogene_df[oncogene_df$sample_name != 'total' & oncogene_df$method == 'long_reads',]

p = ggplot(sub_df, aes(x = sample_name, y = count, fill = oncogene_status)) +
geom_bar(stat = "identity", color='black') +
geom_text(aes(label = count), position = position_stack(vjust = 0.5)) +

scale_fill_manual(values = oncogene_colors, name = "Oncogene Status") +

labs(title = "ecDNA Oncogene Content Decoil", y = "Number of ecDNAs", x = "Method") +
plot_theme() +
theme(axis.text.x = element_text(angle = 90, hjust = 1, vjust = 0.5))

print(p)

return()
}


reorder_samples = function(df){
# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# order samples
sorted_vector <- unique(df$sample_name)[order(sapply(unique(df$sample_name), extract_numeric))]
df$sample_name = factor(df$sample_name, levels = sorted_vector)

return(df)
}


pdf(opt$figure_file)

# load df
oncogene_df = read.csv(opt$combined_oncogene_counts, sep='\t')

# sort samples
oncogene_df = reorder_samples(oncogene_df)

# refactor
oncogene_df$method = factor(oncogene_df$method, levels = c('short_reads', 'long_reads'))

# plot
create_comparison_plots(oncogene_df)

dev.off()

print(opt$figure_file)
