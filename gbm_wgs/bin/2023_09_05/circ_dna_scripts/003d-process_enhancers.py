# <PERSON> Bahcheli
import argparse
import pandas as pd


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--gff_file", required=True)  # Input GFF file
    parser.add_argument("--enhancer_bed_file", required=True)  # Output BED file
    return parser.parse_args()


def extract_enhancers_from_gff(gff_file):
    """Extract enhancers from GFF file and convert to BED format"""
    # read gff file
    df = pd.read_csv(gff_file, sep='\t', header=None, comment='#')

    # add column names
    column_names = ['seqname', 'source', 'type', 'start', 'end', 'score', 'strand', 'phase', 'attributes']
    df.columns = column_names

    # subset to BestRefSeq
    df = df[df['source'] == 'BestRefSeq']

    # filter for enhancer features
    enhancers_df = df[df['type'] == 'enhancer']
    
    if enhancers_df.empty:
        print("No enhancer features found in GFF file")
        # create empty BED file with proper header
        empty_bed = pd.DataFrame(columns=['#chr', 'start', 'end', 'name', 'score', 'strand'])
        return empty_bed

    # extract enhancer information
    enhancers_df = enhancers_df.copy()
    
    # extract enhancer ID from attributes
    enhancers_df['enhancer_id'] = enhancers_df['attributes'].str.extract(r'ID=([^;]+)')
    
    # if no ID, use generic naming
    enhancers_df.loc[enhancers_df['enhancer_id'].isna(), 'enhancer_id'] = 'enhancer_' + enhancers_df.loc[enhancers_df['enhancer_id'].isna()].index.astype(str)

    # convert NCBI chromosome names to standard format
    def convert_ncbi_to_chr(seqname):
        ncbi_to_chr = {
            'NC_000001.11': 'chr1', 'NC_000002.12': 'chr2', 'NC_000003.12': 'chr3',
            'NC_000004.12': 'chr4', 'NC_000005.10': 'chr5', 'NC_000006.12': 'chr6',
            'NC_000007.14': 'chr7', 'NC_000008.11': 'chr8', 'NC_000009.12': 'chr9',
            'NC_000010.11': 'chr10', 'NC_000011.10': 'chr11', 'NC_000012.12': 'chr12',
            'NC_000013.11': 'chr13', 'NC_000014.9': 'chr14', 'NC_000015.10': 'chr15',
            'NC_000016.10': 'chr16', 'NC_000017.11': 'chr17', 'NC_000018.10': 'chr18',
            'NC_000019.10': 'chr19', 'NC_000020.11': 'chr20', 'NC_000021.9': 'chr21',
            'NC_000022.11': 'chr22', 'NC_000023.11': 'chrX', 'NC_000024.10': 'chrY',
            'NC_012920.1': 'chrM'
        }
        return ncbi_to_chr.get(seqname, seqname)

    # convert to BED format (0-based coordinates)
    bed_df = pd.DataFrame({
        '#chr': enhancers_df['seqname'].apply(convert_ncbi_to_chr),
        'start': enhancers_df['start'] - 1,  # convert to 0-based
        'end': enhancers_df['end'],
        'name': enhancers_df['enhancer_id'],
        'score': enhancers_df['score'].fillna(0),
        'strand': enhancers_df['strand']
    })

    # filter for standard chromosomes only
    standard_chroms = [f'chr{i}' for i in range(1, 23)] + ['chrX', 'chrY', 'chrM']
    bed_df = bed_df[bed_df['#chr'].isin(standard_chroms)]

    return bed_df


def main():
    """Main function to extract enhancers from GFF"""
    args = parse_arguments()
    
    print(f"Processing GFF file: {args.gff_file}")
    
    # extract enhancers
    enhancer_bed = extract_enhancers_from_gff(args.gff_file)
    
    print(f"Found {len(enhancer_bed)} enhancer regions")
    
    # save to BED file
    enhancer_bed.to_csv(args.enhancer_bed_file, sep='\t', index=False)
    
    print(f"Enhancer BED file saved to: {args.enhancer_bed_file}")


if __name__ == "__main__":
    main()
