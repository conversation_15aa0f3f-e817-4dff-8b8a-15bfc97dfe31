# <PERSON>cheli
import argparse
import pandas as pd
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--ecdna_oncogene_analysis", required=True)  # ecDNA oncogene analysis file
    parser.add_argument("--output_file", required=True)  # Output file
    return parser.parse_args()

def analyze_oncogenes_per_sample(oncogene_file):
    """Analyze oncogenes per sample"""
    # load oncogene analysis
    oncogene_df = pd.read_csv(oncogene_file, sep='\t')

    results = []

    # group by sample
    for sample_name, sample_group in oncogene_df.groupby('sample_name'):
        # get all oncogenes in this sample (across all ecDNAs)
        all_oncogenes = []
        for _, row in sample_group.iterrows():
            if row['canonical_oncogenes'] != 'None' and pd.notna(row['canonical_oncogenes']):
                oncogenes = row['canonical_oncogenes'].split(',')
                all_oncogenes.extend([og.strip() for og in oncogenes])

        # get unique oncogenes
        unique_oncogenes = list(set(all_oncogenes))

        # classify ecDNAs by priority: oncogenes > enhancers > other
        ecdnas_with_oncogenes = len(sample_group[sample_group['canonical_oncogenes_count'] > 0])
        ecdnas_with_enhancers = len(sample_group[(sample_group['canonical_oncogenes_count'] == 0) &
                                                (sample_group['enhancer_count'] > 0)])
        ecdnas_with_other = len(sample_group[(sample_group['canonical_oncogenes_count'] == 0) &
                                           (sample_group['enhancer_count'] == 0)])

        # create result record
        result = {
            'sample_name': sample_name,
            'total_ecdnas': len(sample_group),
            'ecdnas_with_oncogenes': ecdnas_with_oncogenes,
            'ecdnas_with_enhancers': ecdnas_with_enhancers,
            'ecdnas_with_other': ecdnas_with_other,
            'total_oncogenes_count': len(unique_oncogenes),
            'total_enhancers_count': sample_group['enhancer_count'].sum(),
            'unique_oncogenes': ','.join(sorted(unique_oncogenes)) if unique_oncogenes else 'None',
            'patient': sample_group['patient'].iloc[0],
            'tumor_type': sample_group['tumor_type'].iloc[0]
        }

        results.append(result)
    
    return pd.DataFrame(results)

def main():
    """Main function to analyze oncogenes per sample"""
    args = parse_arguments()
    
    # analyze oncogenes per sample
    sample_analysis = analyze_oncogenes_per_sample(args.ecdna_oncogene_analysis)
    
    # sort results
    sample_analysis = sample_analysis.sort_values(['sample_name'], ascending=[True])
    
    # save results
    sample_analysis.to_csv(args.output_file, sep='\t', index=False)

if __name__ == "__main__":
    main()
