# <PERSON>
library(optparse)
library(ggplot2)
library(dplyr)
library(tidyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--sample_oncogene_analysis"), type="character", default=NULL,
            help="Sample oncogene analysis file",
            dest="sample_oncogene_analysis"),
    make_option(c("-b","--figure_file"), type="character", default=NULL,
            help="Output figure file",
            dest="figure_file")
)

parser <- OptionParser(usage = "%prog -a sample_oncogenes.tsv -b figure.pdf", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}

create_sample_oncogene_plots = function(input_df) {

# Define colors for classification
classification_colors <- c(
    "Oncogenes" = "#E31A1C",      # Red
    "Enhancers" = "#FF7F00",      # Orange
    "Other" = "#1F78B4"           # Blue
)

# 1. Stacked bar plot showing ecDNA classification per sample
plot_data <- input_df %>%
    select(sample_name, ecdnas_with_oncogenes, ecdnas_with_enhancers, ecdnas_with_other) %>%
    pivot_longer(cols = c(ecdnas_with_oncogenes, ecdnas_with_enhancers, ecdnas_with_other),
                 names_to = "classification", values_to = "count") %>%
    mutate(classification = case_when(
        classification == "ecdnas_with_oncogenes" ~ "Oncogenes",
        classification == "ecdnas_with_enhancers" ~ "Enhancers",
        classification == "ecdnas_with_other" ~ "Other",
        TRUE ~ classification
    )) %>%
    mutate(classification = factor(classification, levels = c("Oncogenes", "Enhancers", "Other")))

p1 <- ggplot(plot_data, aes(x = sample_name, y = count, fill = classification)) +
geom_bar(stat = "identity", color='black') +
geom_text(aes(label = ifelse(count > 0, count, "")),
          position = position_stack(vjust = 0.5), size = 3) +

scale_fill_manual(values = classification_colors, name = "Classification") +

labs(title = "ecDNA Classification per Sample",
     subtitle = "Priority: Oncogenes > Enhancers > Other",
     y = "Number of ecDNAs", x = "Sample") +

plot_theme() +
theme(axis.text.x = element_text(angle = 90, hjust = 1, vjust = 0.5))

print(p1)

# 2. Simple oncogene count per sample
p2 <- ggplot(input_df, aes(x = sample_name, y = total_oncogenes_count)) +
geom_bar(stat = "identity", fill = "#E31A1C", color='black') +
geom_text(aes(label = total_oncogenes_count), vjust = -0.5, size = 3) +

labs(title = "Unique Oncogenes per Sample", y = "Number of Unique Oncogenes", x = "Sample") +

plot_theme() +
theme(axis.text.x = element_text(angle = 90, hjust = 1, vjust = 0.5))

print(p2)

return()
}


sort_factors = function(df){
    # Function to extract numeric parts from each element
    extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

    # Extract and sort by numeric parts
    sorted_vector <- unique(df$sample_name)[order(sapply(unique(df$sample_name), extract_numeric))]
    df$sample_name = factor(df$sample_name, levels = sorted_vector)
    
    return(df)
}


pdf(opt$figure_file, width=15)

# load df
sample_df = read.csv(opt$sample_oncogene_analysis, sep='\t')

# sort factors
sample_df = sort_factors(sample_df)

# plot
create_sample_oncogene_plots(sample_df)

dev.off()

print(opt$figure_file)
