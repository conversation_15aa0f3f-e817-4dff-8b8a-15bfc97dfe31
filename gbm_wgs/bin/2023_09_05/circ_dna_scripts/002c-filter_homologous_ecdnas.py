#!/usr/bin/env python3
"""
Script to filter homologous ecDNAs based on sequence overlap
Author: <PERSON>
Date: 2023-09-05
"""

import pandas as pd
import argparse
import os


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--input_bed", required=True)  # Input ecDNA BED file
    parser.add_argument("--intersect_file", required=True)  # Bedtools intersect output file
    parser.add_argument("--output_bed", required=True)  # Output filtered BED file
    parser.add_argument("--overlap_threshold", type=float, default=0.9)  # Overlap threshold (90%)
    return parser.parse_args()


def create_ecdna_regions_bed(input_bed):
    """Create a BED file with merged regions for each ecDNA"""
    # Read the input BED file
    df = pd.read_csv(input_bed, sep='\t')
    
    # Group by circ_id and create merged regions
    ecdna_regions = []
    for circ_id, group in df.groupby('circ_id'):
        # Get the overall span for this ecDNA
        min_start = group['start'].min()
        max_end = group['end'].max()
        chrom = group['#chr'].iloc[0]  # Assume all fragments are on same chromosome
        
        ecdna_regions.append({
            'chrom': chrom,
            'start': min_start,
            'end': max_end,
            'circ_id': circ_id,
            'size': max_end - min_start
        })
    
    return pd.DataFrame(ecdna_regions)


def identify_homologous_ecdnas(intersect_file, overlap_threshold):
    """Identify homologous ecDNAs based on overlap threshold"""
    # Read bedtools intersect output
    # Columns: chrA, startA, endA, circ_idA, sizeA, chrB, startB, endB, circ_idB, sizeB, overlap
    intersect_df = pd.read_csv(intersect_file, sep='\t', header=None)
    intersect_df.columns = ['chrA', 'startA', 'endA', 'circ_idA', 'fragment_idA', 'strandA', 'coverageA', 'estimated_proportionsA', 
    'chrB', 'startB', 'endB', 'circ_idB', 'fragment_idB', 'strandB', 'coverageB', 'estimated_proportionsB', 'overlap']

    # add size
    intersect_df['sizeA'] = intersect_df['endA'] - intersect_df['startA']
    intersect_df['sizeB'] = intersect_df['endB'] - intersect_df['startB']
    
    # Remove self-intersections
    intersect_df = intersect_df[intersect_df['circ_idA'] != intersect_df['circ_idB']]
    
    # Calculate overlap percentage for both ecDNAs
    intersect_df['overlap_pct_A'] = intersect_df['overlap'] / intersect_df['sizeA']
    intersect_df['overlap_pct_B'] = intersect_df['overlap'] / intersect_df['sizeB']
    
    # Find pairs where either ecDNA has >threshold overlap
    homologous_pairs = intersect_df[
        (intersect_df['overlap_pct_A'] >= overlap_threshold) | 
        (intersect_df['overlap_pct_B'] >= overlap_threshold)
    ]
    
    # Create sets of homologous ecDNAs
    homologous_groups = []
    processed_ids = set()
    
    for _, row in homologous_pairs.iterrows():
        circ_id_a = row['circ_idA']
        circ_id_b = row['circ_idB']
        
        if circ_id_a not in processed_ids and circ_id_b not in processed_ids:
            # Start new group
            group = {circ_id_a, circ_id_b}
            homologous_groups.append(group)
            processed_ids.update(group)
        else:
            # Add to existing group
            for group in homologous_groups:
                if circ_id_a in group or circ_id_b in group:
                    group.update({circ_id_a, circ_id_b})
                    processed_ids.update({circ_id_a, circ_id_b})
                    break
    
    return homologous_groups


def select_representative_ecdnas(homologous_groups):
    """Select representative ecDNA from each homologous group (smallest circ_id)"""
    to_remove = set()
    
    for group in homologous_groups:
        # Sort by circ_id and keep the smallest (first)
        sorted_group = sorted(group)
        representative = sorted_group[0]
        
        # Mark others for removal
        to_remove.update(sorted_group[1:])
    
    return to_remove


def filter_original_bed(input_bed, output_bed, circ_ids_to_remove):
    """Filter the original BED file to remove homologous ecDNAs"""
    df = pd.read_csv(input_bed, sep='\t')
    
    # Remove rows with circ_ids in the removal set
    filtered_df = df[~df['circ_id'].isin(circ_ids_to_remove)]
    
    # Save filtered results
    filtered_df.to_csv(output_bed, sep='\t', index=False)


def main():
    """Main function to filter homologous ecDNAs"""
    args = parse_arguments()
    
    # check size of input bed file
    if os.stat(args.input_bed).st_size == 0:
        # create empty output file
        with open(args.output_bed, 'w') as f:
            pass
        return

    elif os.stat(args.intersect_file).st_size == 0:
        # copy input bed file to output
        with open(args.output_bed, 'w') as f:
            with open(args.input_bed, 'r') as g:
                f.write(g.read())
        return

    # Identify homologous ecDNAs from bedtools intersect output
    homologous_groups = identify_homologous_ecdnas(args.intersect_file, args.overlap_threshold)
    
    # Select representative ecDNAs
    circ_ids_to_remove = select_representative_ecdnas(homologous_groups)
    
    # Filter original BED file
    filter_original_bed(args.input_bed, args.output_bed, circ_ids_to_remove)


if __name__ == "__main__":
    main()
