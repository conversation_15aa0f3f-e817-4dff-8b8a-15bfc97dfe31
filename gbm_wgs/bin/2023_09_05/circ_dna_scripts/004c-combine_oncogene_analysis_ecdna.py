# <PERSON> Bahcheli
import argparse
import pandas as pd
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--amplicon_results", required=True)
    parser.add_argument("--decoil_oncogene_analysis", required=True)
    parser.add_argument("--output_file", required=True)
    return parser.parse_args()


# count decoil ecDNAs with oncogenes, enhancers, and other genes per sample
def count_features(group):
    oncogene_mask = group['canonical_oncogenes_count'] > 0
    oncogenes = len(group[oncogene_mask])

    remaining = group[~oncogene_mask]
    other_genes = len(remaining[remaining['other_genes_count'] > 0])

    remaining = remaining[remaining['other_genes_count'] == 0]
    enhancers = len(remaining[remaining['enhancer_count'] > 0])
    
    remaining = remaining[remaining['enhancer_count'] == 0]
    without_genes = len(remaining)
    
    return pd.Series({
        'has_oncogenes': oncogenes,
        'only_enhancers': enhancers,
        'has_other_genes': other_genes,
        'no_genes_or_enhancers': without_genes
    })


def combine_dfs(amplicon_results, decoil_oncogene_analysis):
    # load amplicon results
    amplicon_df = pd.read_csv(amplicon_results, sep='\t')

    # count amplicons with oncogenes, enhancers, and other genes per sample
    amplicon_summary = amplicon_df.groupby('sample_name').apply(count_features).reset_index()
    amplicon_summary['method'] = 'AmpliconSuite'
    
    # load decoil oncogene analysis
    decoil_df = pd.read_csv(decoil_oncogene_analysis, sep='\t')
    
    # count decoil ecDNAs with oncogenes, enhancers, and other genes per sample    
    decoil_summary = decoil_df.groupby('sample_name').apply(count_features).reset_index()
    decoil_summary['method'] = 'DeCoil'
    
    # combine results
    combined_df = pd.concat([amplicon_summary, decoil_summary], ignore_index=True)
    
    # melt
    combined_df = combined_df.melt(id_vars=['sample_name', 'method'], var_name='oncogene_status', value_name='count')

    # remove total_ecdnas
    combined_df = combined_df[combined_df['oncogene_status'] != 'total_ecdnas']
    
    # append a single row that summarizes the total number of ecDNAs for each method
    last_row = combined_df.groupby(['method', 'oncogene_status'])['count'].sum().reset_index()
    last_row['sample_name'] = 'total'

    combined_df = pd.concat([combined_df, last_row], ignore_index=True)

    # rename methods
    combined_df['method'] = combined_df['method'].replace({'AmpliconSuite': 'short_reads', 'DeCoil': 'long_reads'})

    return combined_df


def main():
    """Main function to combine oncogene counts"""
    args = parse_arguments()
    
    # combine dfs
    combined_df = combine_dfs(args.amplicon_results, args.decoil_oncogene_analysis)

    # save results
    combined_df.to_csv(args.output_file, sep='\t', index=False)

if __name__ == "__main__":
    main()
