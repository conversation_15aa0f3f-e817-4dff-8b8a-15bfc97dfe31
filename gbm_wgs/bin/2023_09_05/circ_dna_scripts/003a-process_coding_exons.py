# <PERSON> Ba<PERSON>cheli
import argparse
import pandas as pd


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--protein_coding_genes_gff_file", required=True)  # Input GFF file
    parser.add_argument("--protein_coding_bed_file", required=True)  # Output BED file
    return parser.parse_args()


def extract_exons_from_gff(protein_coding_genes_gff_file):
    """Extract exons from GFF file and convert to BED format"""
    # read gff file
    df = pd.read_csv(protein_coding_genes_gff_file, sep='\t', header=None, comment='#')

    # add column names
    column_names = ['seqname', 'source', 'type', 'start', 'end', 'score', 'strand', 'phase', 'attributes']
    df.columns = column_names

    # first get all protein-coding gene names
    protein_coding_genes = df[df['attributes'].str.contains('gene_biotype=protein_coding', na=False)]
    protein_coding_gene_names = protein_coding_genes['attributes'].str.extract(r'gene=([^;]+)')[0].dropna().unique()
    
    # filter for exons only from BestRefSeq and ensembl
    exons_df = df[df['type'] == 'exon']
    exons_df = exons_df[exons_df['source'] == 'BestRefSeq']
    exons_df = exons_df[exons_df['attributes'].str.contains('Dbxref=Ensembl')]

    # extract gene names from exon attributes
    exons_df['gene'] = exons_df['attributes'].str.extract(r'gene=([^;]+)')[0]

    # subset to only exons from protein-coding genes
    exons_df = exons_df[exons_df['gene'].isin(protein_coding_gene_names)]

    # extract exon number
    exons_df['exon_number'] = exons_df['attributes'].str.extract(r'ID=([^;]+)')[0].str.split('-').str[-1]

    # add exon_number to gene
    exons_df['gene'] = exons_df['gene'] + '_' + exons_df['exon_number']

    # add chromosome with chr prefix
    exons_df['chr'] = 'chr' + exons_df['seqname'].str.split(".").str[0].str.split("_").str[1].str.replace(r'^0+', '', regex=True)

    # replace chr23 with chrX and chr24 with chrY
    mask = exons_df['chr'] == 'chr23'
    exons_df.loc[mask, 'chr'] = 'chrX'
    mask = exons_df['chr'] == 'chr24'
    exons_df.loc[mask, 'chr'] = 'chrY'

    # subset to chromosomes 1-22 and x and y
    valid_chrs = ['chr' + str(i) for i in range(1, 23)] + ['chrX', 'chrY']
    exons_df = exons_df[exons_df['chr'].isin(valid_chrs)]

    # sort values
    exons_df = exons_df.sort_values(['chr', 'start'])

    # remove rows with duplicated values in all columns
    exons_df = exons_df.drop_duplicates()

    # filter columns for BED format
    exons_df = exons_df[['chr', 'start', 'end', 'gene', 'type', 'strand']]

    return exons_df


def main():
    """Main function to process GFF file and create exon BED file"""
    args = parse_arguments()

    # extract exons from gff
    exon_df = extract_exons_from_gff(args.protein_coding_genes_gff_file)

    # save to file
    exon_df.to_csv(args.protein_coding_bed_file, sep='\t', index=False, header=False)


if __name__ == "__main__":
    main()

