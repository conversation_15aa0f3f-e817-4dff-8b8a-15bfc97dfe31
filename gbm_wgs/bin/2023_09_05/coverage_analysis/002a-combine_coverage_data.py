# <PERSON> Bahcheli
import argparse
import pandas as pd
import os
import glob


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--coverage_dir", required=True)  # Directory containing coverage ratio files
    parser.add_argument("--output_file", required=True)  # Output combined file
    return parser.parse_args()


def load_coverage_files(coverage_dir):
    """Load all coverage ratio files from directory"""
    # Find all coverage ratio files
    pattern = os.path.join(coverage_dir, '*_coverage_ratios.tsv')
    coverage_files = glob.glob(pattern)
    
    all_data = []
    
    for file_path in coverage_files:
        df = pd.read_csv(file_path, sep='\t')
        
        all_data.append(df)

    # Combine all data
    combined_df = pd.concat(all_data, ignore_index=True)
    
    return combined_df


def filter_standard_chromosomes(df):
    """Filter to standard chromosomes and sort properly"""
    # Define standard chromosomes
    standard_chrs = [f"chr{i}" for i in range(1, 23)] + ["chrX", "chrY"]
    
    # Filter to standard chromosomes
    df = df[df['chr'].isin(standard_chrs)].copy()
    
    # Create chromosome order for sorting
    chr_order = {f"chr{i}": i for i in range(1, 23)}
    chr_order["chrX"] = 23
    chr_order["chrY"] = 24
    
    df['chr_order'] = df['chr'].map(chr_order)
    
    # Sort by chromosome and position
    df = df.sort_values(['chr_order', 'start']).drop('chr_order', axis=1)
    
    return df


def main():
    """Main function to combine coverage data"""
    args = parse_arguments()
    
    # Load all coverage files
    combined_df = load_coverage_files(args.coverage_dir)
    
    # Filter to standard chromosomes
    combined_df = filter_standard_chromosomes(combined_df)
    
    # Save combined data
    combined_df.to_csv(args.output_file, sep='\t', index=False)


if __name__ == "__main__":
    main()
