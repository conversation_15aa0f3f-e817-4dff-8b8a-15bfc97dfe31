# <PERSON>
# Test script for combined coverage visualization
import subprocess
import os
import pandas as pd

def create_test_coverage_data():
    """Create test coverage data files"""
    print("Creating test coverage data...")
    
    # Create test directory
    test_dir = "/Users/<USER>/Desktop/tmp/coverage_analysis"
    os.makedirs(test_dir, exist_ok=True)
    
    # Sample data structure based on existing format
    import numpy as np
    
    # Create test data for multiple samples
    samples = [
        ("RLGS1", "primary"),
        ("RLGS1", "recurrent"), 
        ("RLGS2", "primary"),
        ("RLGS3", "primary")
    ]
    
    for patient, tumor_type in samples:
        sample_name = f"{patient}-{tumor_type}"
        
        # Create realistic coverage data
        data = []
        
        for chr_num in range(1, 23):  # chr1-22
            chr_name = f"chr{chr_num}"
            
            # Generate coverage data for this chromosome
            for start in range(0, 10000000, 500):  # 10Mb of data per chromosome
                end = start + 500
                
                # Simulate log2 ratios with some amplifications
                base_ratio = np.random.normal(0, 0.3)  # Most regions near 0
                
                # Add some amplifications
                if np.random.random() < 0.02:  # 2% chance of amplification
                    base_ratio += np.random.uniform(1, 4)
                
                # Smooth the ratio (simplified)
                smooth_ratio = base_ratio + np.random.normal(0, 0.1)
                
                data.append({
                    'chr': chr_name,
                    'start': start,
                    'end': end,
                    'log2_ratio': base_ratio,
                    'log2_ratio_smooth': smooth_ratio,
                    'sample_name': sample_name,
                    'patient': patient,
                    'tumor_type': tumor_type
                })
        
        # Add chrX and chrY
        for chr_name in ['chrX', 'chrY']:
            for start in range(0, 5000000, 500):  # 5Mb for sex chromosomes
                end = start + 500
                base_ratio = np.random.normal(0, 0.2)
                smooth_ratio = base_ratio + np.random.normal(0, 0.1)
                
                data.append({
                    'chr': chr_name,
                    'start': start,
                    'end': end,
                    'log2_ratio': base_ratio,
                    'log2_ratio_smooth': smooth_ratio,
                    'sample_name': sample_name,
                    'patient': patient,
                    'tumor_type': tumor_type
                })
        
        # Save to file
        df = pd.DataFrame(data)
        output_file = os.path.join(test_dir, f"{sample_name}_coverage_ratios.tsv")
        df.to_csv(output_file, sep='\t', index=False)
        print(f"Created: {output_file} ({len(df)} regions)")
    
    return test_dir


def test_combine_coverage():
    """Test combining coverage data"""
    print("Testing coverage data combination...")
    
    test_dir = create_test_coverage_data()
    
    cmd = [
        "/Users/<USER>/opt/miniconda3/envs/py_dev/bin/python",
        "/Users/<USER>/Library/CloudStorage/Dropbox/phd/reimand_lab_ab/gbm_wgs/bin/2023_09_05/coverage_analysis/002a-combine_coverage_data.py",
        "--coverage_dir", test_dir,
        "--output_file", "/Users/<USER>/Desktop/tmp/combined_coverage_test.tsv"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("Coverage combination: SUCCESS")
        print("STDOUT:", result.stdout)
        
        # Check output files
        if os.path.exists("/Users/<USER>/Desktop/tmp/combined_coverage_test.tsv"):
            df = pd.read_csv("/Users/<USER>/Desktop/tmp/combined_coverage_test.tsv", sep='\t')
            print(f"Combined data shape: {df.shape}")
            print(f"Samples: {sorted(df['sample_name'].unique())}")
            print(f"Patients: {sorted(df['patient'].unique())}")
            print(f"Tumor types: {sorted(df['tumor_type'].unique())}")
            
    else:
        print("Coverage combination: FAILED")
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)


def test_visualization():
    """Test visualization"""
    print("Testing coverage visualization...")
    
    cmd = [
        "/Users/<USER>/opt/miniconda3/envs/r4_env/bin/Rscript",
        "/Users/<USER>/Library/CloudStorage/Dropbox/phd/reimand_lab_ab/gbm_wgs/bin/2023_09_05/coverage_analysis/002b-visualize_coverage_lines.R",
        "--combined_coverage", "/Users/<USER>/Desktop/tmp/combined_coverage_test.tsv",
        "--chr_mids", "/Users/<USER>/Desktop/tmp/combined_coverage_test_chr_mids.tsv",
        "--figure_file", "/Users/<USER>/Desktop/tmp/combined_coverage_lines_test.pdf"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("Coverage visualization: SUCCESS")
        print(f"Output saved to: /Users/<USER>/Desktop/tmp/combined_coverage_lines_test.pdf")
    else:
        print("Coverage visualization: FAILED")
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)


def main():
    """Run all tests"""
    print("Starting combined coverage visualization tests...")
    print("=" * 60)
    
    test_combine_coverage()
    print()
    
    test_visualization()
    print()
    
    print("=" * 60)
    print("Testing complete!")
    
    # Show final results
    print("\nFinal output files:")
    import glob
    files = glob.glob("/Users/<USER>/Desktop/tmp/*coverage*")
    for f in sorted(files):
        if os.path.isfile(f):
            size = os.path.getsize(f)
            print(f"  - {os.path.basename(f)} ({size:,} bytes)")


if __name__ == "__main__":
    main()
