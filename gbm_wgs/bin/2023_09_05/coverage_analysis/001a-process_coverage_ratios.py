# Alec Bahcheli
import argparse
import pandas as pd
import numpy as np
import gzip

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--tumor_coverage", required=True)  # Tumor coverage BED file
    parser.add_argument("--normal_coverage", required=True)  # Normal coverage BED file
    parser.add_argument("--output_file", required=True)  # Output file path
    parser.add_argument("--sample_name", required=True)  # Sample name
    return parser.parse_args()


def load_coverage_file(file_path):
    """Load mosdepth coverage BED file"""
    # Check if file is gzipped
    if file_path.endswith('.gz'):
        df = pd.read_csv(file_path, sep='\t', compression='gzip', header=None)
    else:
        df = pd.read_csv(file_path, sep='\t', header=None)
    
    # Standard mosdepth regions.bed format: chr, start, end, coverage
    df.columns = ['chr', 'start', 'end', 'coverage']
    
    return df


def bin_coverage_to_5kb(df):
    """Bin coverage data into 5kb bins by averaging - optimized version"""

    bin_size = 5000  # 5kb bins
    binned_results = []

    for chr_name in df['chr'].unique():
        chr_df = df[df['chr'] == chr_name].copy()
        chr_df = chr_df.sort_values('start')

        if len(chr_df) == 0:
            continue

        # Create bin assignments for each region
        chr_df['bin_start'] = (chr_df['start'] // bin_size) * bin_size
        chr_df['bin_end'] = chr_df['bin_start'] + bin_size

        # Group by bins and calculate averages
        binned_chr = chr_df.groupby(['bin_start', 'bin_end']).agg({
            'tumor_coverage': 'mean',
            'normal_coverage': 'mean',
            'log2_ratio': 'mean',
            'sample_name': 'first',
            'patient': 'first',
            'tumor_type': 'first'
        }).reset_index()

        # Add chromosome and window size info
        binned_chr['chr'] = chr_name
        binned_chr['start'] = binned_chr['bin_start']
        binned_chr['end'] = binned_chr['bin_end']
        binned_chr['window_size'] = bin_size

        # Select final columns
        binned_chr = binned_chr[['chr', 'start', 'end', 'tumor_coverage', 'normal_coverage',
                                'log2_ratio', 'sample_name', 'patient', 'tumor_type', 'window_size']]

        binned_results.append(binned_chr)

    return pd.concat(binned_results, ignore_index=True) if binned_results else pd.DataFrame()


def calculate_coverage_ratios(tumor_df, normal_df, sample_name):
    """Calculate log2(tumor/normal) coverage ratios with 5kb binning"""

    # Ensure both dataframes have the same regions
    if len(tumor_df) != len(normal_df):
        raise ValueError("Tumor and normal coverage files have different numbers of regions")

    # Check that genomic coordinates match
    coords_match = (
        (tumor_df['chr'] == normal_df['chr']).all() and
        (tumor_df['start'] == normal_df['start']).all() and
        (tumor_df['end'] == normal_df['end']).all()
    )

    if not coords_match:
        raise ValueError("Genomic coordinates do not match between tumor and normal files")

    # Create result dataframe
    result_df = tumor_df[['chr', 'start', 'end']].copy()

    # Calculate log2 ratio with pseudocount to avoid division by zero
    pseudocount = 1
    result_df['tumor_coverage'] = tumor_df['coverage']
    result_df['normal_coverage'] = normal_df['coverage']
    result_df['log2_ratio'] = np.log2((tumor_df['coverage'] + pseudocount) / (normal_df['coverage'] + pseudocount))

    # Add sample information
    result_df['sample_name'] = sample_name
    result_df['patient'] = sample_name.split('-')[0]
    result_df['tumor_type'] = sample_name.split('-')[1]

    # Calculate window size (assuming uniform windows from mosdepth)
    result_df['window_size'] = result_df['end'] - result_df['start']

    # Filter out regions with very low coverage in both samples (likely unmappable regions)
    min_coverage_threshold = 5
    result_df = result_df[
        (result_df['tumor_coverage'] >= min_coverage_threshold) |
        (result_df['normal_coverage'] >= min_coverage_threshold)
    ]

    # Bin coverage into 5kb bins
    binned_df = bin_coverage_to_5kb(result_df)

    # Calculate rolling averages for smoother visualization on binned data
    # Use a smaller window since we now have 5kb bins
    rolling_window = 5  # 5 bins = 25kb window

    # Calculate rolling averages by chromosome
    binned_df = binned_df.sort_values(['chr', 'start'])
    binned_df['log2_ratio_smooth'] = binned_df.groupby('chr')['log2_ratio'].transform(
        lambda x: x.rolling(window=rolling_window, center=True, min_periods=1).mean()
    )

    return binned_df


def main():
    """Main function to process coverage files and calculate ratios"""
    args = parse_arguments()
    
    # Load coverage files
    tumor_df = load_coverage_file(args.tumor_coverage)
    normal_df = load_coverage_file(args.normal_coverage)
    
    # Calculate coverage ratios
    result_df = calculate_coverage_ratios(tumor_df, normal_df, args.sample_name)
    
    # Save results
    result_df.to_csv(args.output_file, sep='\t', index=False)


if __name__ == "__main__":
    main()
