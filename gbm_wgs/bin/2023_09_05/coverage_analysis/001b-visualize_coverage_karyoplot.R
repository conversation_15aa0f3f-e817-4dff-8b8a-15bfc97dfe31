# <PERSON>
library(optparse)
library(karyoploteR)
library(GenomicRanges)
library(dplyr)
library(ggplot2)

# options list for parser options
option_list <- list(
    make_option(c("-a","--sample_coverage"), type="character", default=NULL,
            help="Sample coverage ratios TSV file",
            dest="sample_coverage"),
    make_option(c("-b","--figure_file"), type="character", default=NULL,
            help="Output figure PDF file",
            dest="figure_file")
)

parser <- OptionParser(usage = "%prog -a sample_coverage.tsv -b output.pdf", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}

prepare_genomic_ranges = function(coverage_df) {
    # Convert to GRanges object for karyoploteR
    gr <- GRanges(
        seqnames = coverage_df$chr,
        ranges = IRanges(start = coverage_df$start, end = coverage_df$end),
        log2_ratio = coverage_df$log2_ratio,
        log2_ratio_smooth = coverage_df$log2_ratio_smooth,
        sample_name = coverage_df$sample_name,
        patient = coverage_df$patient,
        tumor_type = coverage_df$tumor_type
    )

    return(gr)
}

create_karyoplot = function(coverage_gr) {
  # Get sample name
  sample_name <- unique(coverage_gr$sample_name)[1]

  # Sort to avoid tiny drawing gaps at tile boundaries
  coverage_gr <- sort(coverage_gr)

  # === Layout: ideograms on top, single data panel below
  # r0/r1 define the vertical slot for the data within the plotting area [0..1]
  # Keep it close to the ideograms but not overlapping
  data_r0 <- (-0.5)
  data_r1 <- 2

  kp <- plotKaryotype(
    genome = "hg38",
    main   = paste(""),
    plot.type = 2  # ideograms + 1 data panel
  )

  # Shared y-limits for exact overplot
  ymin <- -2
  ymax <- 6

  # Threshold (example: log2(6) ~ 2.585)
  high_amp_threshold <- log2(6)

  # Split regions
  high_amp_regions <- coverage_gr[coverage_gr$log2_ratio_smooth >  high_amp_threshold]
  normal_regions   <- coverage_gr[coverage_gr$log2_ratio_smooth <= high_amp_threshold]

  # 1) Draw the full track in grey (baseline)
  if (length(coverage_gr) > 0) {
    kpLines(kp,
            data = coverage_gr,
            y    = coverage_gr$log2_ratio_smooth,
            col  = "grey60",
            lwd  = 2,
            ymin = ymin, ymax = ymax,
            r0   = data_r0, r1 = data_r1,
            data.panel = 1)
  }

  # 2) Overplot only the high-amp segments in red, same y & panel -> perfect overlap
  if (length(high_amp_regions) > 0) {
    kpLines(kp,
            data = high_amp_regions,
            y    = high_amp_regions$log2_ratio_smooth,
            col  = "firebrick",
            lwd  = 2,
            ymin = ymin, ymax = ymax,
            r0   = data_r0, r1 = data_r1,
            data.panel = 1)
  }

    return()
}


pdf(opt$figure_file, width = 20, height = 12)

# Load data
coverage_df <- read.csv(opt$sample_coverage, sep='\t')

# Create genomic ranges object
coverage_gr <- prepare_genomic_ranges(coverage_df)

# Create single karyoplot
create_karyoplot(coverage_gr)

dev.off()
