# <PERSON>
library(optparse)
library(ggplot2)
library(dplyr)
library(RColorBrewer)
library(viridis)

# options list for parser options
option_list <- list(
    make_option(c("-a","--combined_coverage"), type="character", default=NULL,
            help="Combined coverage data TSV file",
            dest="combined_coverage"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="Output figure PDF file",
            dest="figure_file")
)

parser <- OptionParser(usage = "%prog -a combined_coverage.tsv -c output.pdf", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}

prepare_coverage_data = function(coverage_df) {
    # Extract patient and tumor type information
    coverage_df$patient = as.factor(coverage_df$patient)
    coverage_df$tumor_type = as.factor(coverage_df$tumor_type)
    
    # Create line type based on tumor type
    coverage_df$line_type = ifelse(coverage_df$tumor_type == "primary", "solid", "dashed")
    
    # Ensure chromosome factor levels are in correct order
    chr_levels = c(paste0("chr", 1:22), "chrX", "chrY")
    coverage_df$chr = factor(coverage_df$chr, levels = chr_levels)
    
    return(coverage_df)
}

create_coverage_line_plot = function(coverage_df) {
    
    # Prepare data
    coverage_df = prepare_coverage_data(coverage_df)
    
    # Get unique patients for color assignment
    patients = unique(coverage_df$patient)
    n_patients = length(patients)
    
    # Use a color palette that works well with transparency
    if (n_patients <= 8) {
        colors = brewer.pal(max(3, n_patients), "Set2")
    } else if (n_patients <= 12) {
        colors = brewer.pal(n_patients, "Set3")
    } else {
        colors = rainbow(n_patients, alpha = 0.8)
    }
    
    names(colors) = patients
    
    # Create the main plot
    p = ggplot(coverage_df, aes(x = start, y = log2_ratio_smooth)) +
        geom_line(aes(color = patient, linetype = tumor_type), 
                 alpha = 0.2, size = 0.5) +
        
        # Facet by chromosome
        facet_wrap(~ chr, scales = "free_x", ncol = 6, strip.position = "bottom") +
        
        # Color and line type scales
        scale_color_manual(values = colors, name = "Patient") +
        scale_linetype_manual(values = c("primary" = "solid", "recurrent" = "dashed"),
                             name = "Tumor Type") +
        
        # Y-axis limits and labels
        ylim(-2, 10) +
        ggtitle('Log2(Tumor/Normal) Coverage Ratios by Chromosome') +
        xlab('Genomic Position') + ylab('Log2(Tumor/Normal)') +
        
        # Add horizontal line at y=0
        geom_hline(yintercept = 0, color = "black", linetype = "dotted", alpha = 0.5) +
        
        # Add horizontal line at high amplification threshold (log2(6) ~ 2.58)
        geom_hline(yintercept = log2(6), color = "red", linetype = "dotted", alpha = 0.3) +
        
        plot_theme() +
        theme(
            axis.text.x = element_blank(),  # Remove x-axis text since it's not meaningful in faceted view
            axis.ticks.x = element_blank(),
            legend.position = "bottom",
            legend.box = "horizontal",
            strip.text = element_text(size = 12)
        )
    
    print(p)

    return()
}

pdf(opt$figure_file, width = 24, height = 16)

# Load data
coverage_df <- read.csv(opt$combined_coverage, sep='\t')

# Create main coverage line plot
create_coverage_line_plot(coverage_df)

dev.off()
