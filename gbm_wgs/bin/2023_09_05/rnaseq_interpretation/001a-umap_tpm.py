# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import umap
from sklearn.decomposition import PCA

from sklearn.preprocessing import StandardScaler


help_message = '''
Failed
'''


def run_umap(scaled_data, df):
    # 2. Apply UMAP for dimensionality reduction
    umap_reducer = umap.UMAP(n_components=2, random_state=42)
    umap_data = umap_reducer.fit_transform(scaled_data)

    # 3. Create a new df with the UMAP data
    umap_df = pd.DataFrame(umap_data, columns=['dim1', 'dim2'])

    # add description
    umap_df['source'] = 'umap'

    # add sample
    umap_df['sample'] = df.index

    return umap_df


def run_pca(scaled_data, df):
    # 2. Apply PCA for dimensionality reduction
    pca = PCA(n_components=2)
    pca_data = pca.fit_transform(scaled_data)

    # 3. Create a new df with the PCA data
    pca_df = pd.DataFrame(pca_data, columns=['dim1', 'dim2'])
    
    # add description
    pca_df['source'] = 'pca'

    # add sample
    pca_df['sample'] = df.index

    return pca_df


def umap_tpm(tpm_file):
    # load raw methylation df
    df = pd.read_csv(tpm_file, sep='\t')

    # set index and drop gene_name and gene_id
    df.index = df['gene_name'].to_numpy()
    df.drop(['gene_name', 'gene_id'], axis=1, inplace=True)

    # log transform
    df = np.log10(df + 1)

    # fill na with 0
    df.fillna(0, inplace=True)

    # transpose so that the samples are the rows
    df = df.transpose()

    # 1. Standardize the data
    scaler = StandardScaler()
    scaled_data = scaler.fit_transform(df)

    # apply UMAP for dimensionality reduction
    res_df = run_umap(scaled_data, df)

    # apply PCA for dimensionality reduction
    pca_df = run_pca(scaled_data, df)

    # combine the results
    res_df = pd.concat([res_df, pca_df])

    # add primary or recurrent status
    res_df['tumor_type'] = res_df['sample'].apply(lambda x: x.split('_')[1])

    # add patient id 
    res_df['patient_id'] = res_df['sample'].str.split('_').str[0]

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load raw methylation df
    umap_df = umap_tpm(tpm_file)

    # save results file
    umap_df.to_csv(figure_data_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["tpm_file=", "figure_data_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--tpm_file"):
            tpm_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)


    main()




