# <PERSON> (<EMAIL>)

library(optparse)
library(ssgsea.GBM.classification)

# options list for parser options
option_list <- list(
    make_option(c("-a","--tpm_file"), type="character", default=NULL,
            help="",
            dest="tpm_file"),
    make_option(c("-b","--gct_file"), type="character", default=NULL,
            help="",
            dest="gct_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




gct_path <- opt$gct_file


#Run Qianghu's transcriptional classifier
runSsGSEAwithPermutation(gct_path,100)


print("DONE")

