# Alec <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




violin_plot = function(input_df){

  # Create the violin plot
  p = ggplot(input_df, aes(x = data_source, y = log10_pvalue)) + plot_theme() +
    geom_violin(trim = TRUE) +  # Add violin plot with trimming
    ggtitle('All P-values') +
    xlab("") + 
    ylab('Pvalue (-log10)')

  print(p)

  return()
}


boxplot = function(input_df) {

  # Calculate the number of counts for each data_source
  count_data <- input_df %>%
    group_by(data_source) %>%
    summarise(count = n()) %>%
    summarise(title = paste(data_source, "(", count, ")", collapse = ", "))

  # Dynamically generate the title
  dynamic_title <- paste("Significant P-values (Counts: ", count_data$title, ")", sep = "")

  # Create the boxplot
  p = ggplot(input_df, aes(x = data_source, y = log10_pvalue)) + plot_theme() +
    geom_boxplot() +
    ggtitle(dynamic_title) +
    xlab("") + ylab('Pvalue (-log10)') + 

    # reduce title size
    theme(plot.title = element_text(size = 8))

  print(p)

  return()
}




pdf(opt$figure_file)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')

violin_plot(input_df)

# repeat for only significant p-values
input_df = input_df[input_df$pvalue < 0.05,]

boxplot(input_df)

dev.off()


print(opt$figure_file)





