# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''

def process_dfs(dgea_file, gene_expression_file, single_samples, data_type_dgea = 'gene_counts-deseq2_paired', min_fdr = 0.05, min_log2fc = 1):
    # load dfs
    data_df = pd.read_csv(gene_expression_file, sep="\t")
    stats_df = pd.read_csv(dgea_file, sep="\t")

    # set index and drop gene_name column
    data_df.index = data_df['gene_id'].to_numpy()
    data_df.drop(columns='gene_name', inplace=True)

    stats_df.index = stats_df['gene'].to_numpy()

    # remove samples without pairs
    data_df = data_df.loc[:, ~data_df.columns.str.contains("|".join(single_samples))]

    # subset to dgea_method
    stats_df = stats_df[stats_df['data_type_dgea'] == data_type_dgea]

    # subset to significant genes and fc cutoff
    sig_mask = (stats_df['fdr'] < min_fdr) & (abs(stats_df['logFC']) > min_log2fc)
    sig_genes = stats_df['gene'].to_numpy()[sig_mask]

    data_df = data_df.loc[sig_genes,:]
    stats_df = stats_df.loc[sig_genes,:]

    # melt the data_df
    data_df = data_df.melt(id_vars = ['gene_id'], var_name='sample', value_name='gene_counts')

    # add patient and tumor columns
    data_df['patient'] = data_df['sample'].str.split("_").str[0]
    data_df['tumor'] = data_df['sample'].str.split("_").str[1]

    # rename gene column
    data_df = data_df.rename(columns={'gene_id':'gene'})

    return data_df, stats_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load data and stats files
    data_df, stats_df = process_dfs(dgea_file, gene_expression_file, single_samples)

    # save data and stats files
    data_df.to_csv(figure_data_file, sep="\t", index=False)
    stats_df.to_csv(figure_stats_file, sep="\t", index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # samples without pairs
    single_samples = ['RLGS2', 'RLGS7']

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["dgea_file=", "gene_expression_file=", "figure_data_file=", "figure_stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)

    for opt, arg in opts:
        if opt in ("--dgea_file"):
            dgea_file = str(arg)
        if opt in ("--gene_expression_file"):
            gene_expression_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
            
    main()






