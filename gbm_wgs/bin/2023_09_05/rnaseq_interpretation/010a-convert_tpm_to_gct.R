# <PERSON> (<EMAIL>)
library(optparse)

# options list for parser options
option_list <- list(
    make_option(c("-a","--tpm_file"), type="character", default=NULL,
            help="",
            dest="tpm_file"),
    make_option(c("-b","--gct_file"), type="character", default=NULL,
            help="",
            dest="gct_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




matrix_path <- opt$tpm_file
gct_out <- opt$gct_file

#Create gct file of gene expression matrix
data <- read.delim(matrix_path)

# drop the first column
data <- data[,-1]

Description <- rep(NA,nrow(data))
data2 <- cbind(data[,1],Description,data[,2:ncol(data)])
colnames(data2)[1] <- "NAME"
write.table(data2, gct_out, sep="\t", quote=FALSE, row.names=FALSE)

conIn <- file(gct_out, "r")
rawfile = readLines(conIn)
close(conIn)

mytext <- c("#1.2", paste(nrow(data2),"\t",(ncol(data)-1),sep=""),rawfile)
conOut = file(gct_out, "w")
writeLines(mytext, conOut)
close(conOut)

print("DONE")


