# <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob
import statsmodels.stats.multitest as multitest

help_message = '''
Failed
'''


def process_edger(df):
    # rename columns
    df = df.rename(columns={'PValue': 'p_value', 'logFC': 'log_fc', 'Gene': 'gene'})


def load_and_combine_dfs(files):
    # load and combined dfs
    res_df = []

    for file in files:
        df = pd.read_csv(file, sep='\t')

        # add data type column
        df['data_type'] = file.split('/')[-1].split('-')[0]

        # add dgea column
        df['dgea'] = file.split('/')[-1].split('-')[1].split('.')[0]

        # add columns of data type and dgea
        df['data_type_dgea'] = df['data_type'] + '-' + df['dgea']

        # rename columns
        df = df.rename(columns={'PValue': 'p_value', 'log2FoldChange': 'logFC', 'pvalue': 'p_value', 'gene_name':'gene'})
        
        # subset to columns of interest
        df = df.loc[:, ['gene', 'p_value', 'logFC', 'fdr', 'data_type', 'dgea', 'data_type_dgea']]

        # recalculate the fdr
        df['fdr'] = multitest.fdrcorrection(df['p_value'])[1]

        # add logfdr
        df['log_fdr'] = -np.log10(df['fdr'])

        res_df.append(df)

    # combine dfs
    df = pd.concat(res_df)

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # list files based on regex
    file_regex = f'{results_dir}/analysis_rna/dgea/*paired.tsv'
    files = glob.glob(file_regex)

    # load and combined dfs
    df = load_and_combine_dfs(files)

    # save combined dataframe
    df.to_csv(figure_data_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "figure_data_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)

    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
            
    main()






