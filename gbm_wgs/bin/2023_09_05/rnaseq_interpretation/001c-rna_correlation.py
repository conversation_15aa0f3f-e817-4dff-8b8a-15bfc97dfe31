# Alec <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import subprocess

help_message = '''
Failed
'''


def reshape_expression_data(df):
    # add gene column
    df['gene'] = df.index.to_numpy()

    # Melt the DataFrame to long format
    melted_df = df.melt(var_name='patient_type', value_name='expression', id_vars=['gene'])
    
    # Extract patient name and tumor type
    melted_df['patient'] = melted_df['patient_type'].str.split('_').str[0]
    melted_df['tumor'] = melted_df['patient_type'].str.split("_").str[1]
    
    # create primary and recurrent dfs
    primary_df = melted_df.loc[melted_df['tumor'].str.contains("primary"), :]
    recurrent_df = melted_df.loc[melted_df['tumor'].str.contains("recurrent"), :]

    # sort by patient and gene
    primary_df = primary_df.sort_values(by=['patient', 'gene'])
    recurrent_df = recurrent_df.sort_values(by=['patient', 'gene'])

    # add recurrent expression to primary df
    primary_df['recurrent'] = recurrent_df['expression'].to_numpy()

    # rename column
    primary_df = primary_df.rename(columns={'expression': 'primary'})

    return primary_df


def load_and_process_file(tpm_file, single_sample_patients):
    # Load the TPM file
    df = pd.read_csv(tpm_file, sep='\t')

    # set index and drop gene_name and gene_id
    df.index = df['gene_name'].to_numpy()
    df.drop(['gene_name', 'gene_id'], axis=1, inplace=True)

    # remove patients with only one measurement
    mask = ~df.columns.str.contains('|'.join(single_sample_patients))
    df = df.loc[:, mask]

    # log10 transform the data
    df = np.log10(df + 1)
    
    # Melt the DataFrame
    melted_df = reshape_expression_data(df)

    corr_df = []

    # calculate correlation between primary and recurrent genes per patient
    for patient in melted_df['patient'].unique():
        # subset to patient
        mask = melted_df['patient'] == patient
        patient_df = melted_df.loc[mask, :]

        # calculate correlation
        correlation = np.round(patient_df['primary'].corr(patient_df['recurrent']), 3)

        # append to list
        corr_df.append([patient, correlation, f'{patient} {correlation}'])

        # add correlation value to df
        melted_df.loc[mask, 'correlation'] = correlation

    # create correlation DataFrame
    corr_df = pd.DataFrame(corr_df, columns=['patient', 'correlation', 'patient_correlation'])
    
    # create patient_correlation columns in melted_df
    melted_df['patient_correlation'] = melted_df['patient'] + ' ' + melted_df['correlation'].astype(str)

    return melted_df, corr_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and process file
    df, stats_df = load_and_process_file(tpm_file, single_sample_patients)

    # save to file
    df.to_csv(figure_data_file, sep='\t', index=False)
    stats_df.to_csv(figure_stats_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    # patients with only one measurement
    single_sample_patients = ['RLGS2', 'RLGS7']
    

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["tpm_file=", "figure_stats_file=", "figure_data_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--tpm_file"):
            tpm_file = str(arg)

        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)

    main()


