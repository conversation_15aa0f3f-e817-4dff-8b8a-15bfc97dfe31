# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import re

help_message = '''
Failed
'''


# Function to parse GFF3 file and create a dictionary mapping transcript_id to gene_name
def parse_gff3(file_path, gene_type='ID'):
    transcript_to_gene = {}  # Initialize an empty dictionary to store transcript_id to gene_name mapping

    # record protein-coding genes
    protein_coding_genes = []

    with open(file_path, 'r') as f:  # Open the GFF3 file for reading
        for line in f:  # Iterate over each line in the file
            if line.startswith('#'):  # Skip comment lines
                continue
            # record protein-coding genes
            if re.search('protein_coding', line):
                match = re.search(r'gene_name=([^;]+)', line)
                protein_coding_genes.append(match.group(1))

            fields = line.strip().split('\t')  # Split the line into fields using tab delimiter

            attributes = fields[8]  # Extract the attributes field
            attributes_dict = dict(pair.split('=') for pair in attributes.split(';')) # Convert attributes field to a dictionary
            
            if gene_type in attributes_dict and 'gene_name' in attributes_dict:  # Check if both transcript_id and gene_name are present
                transcript_id = attributes_dict[gene_type]  # Extract transcript_id

                gene_name = attributes_dict['gene_name']  # Extract gene_name

                transcript_to_gene[transcript_id] = gene_name  # Add transcript_id to gene_name mapping to the dictionary

    return transcript_to_gene, protein_coding_genes


def load_and_process_file(tpm_file, gff_file):
    # Load the TPM file
    df = pd.read_csv(tpm_file, sep='\t', index_col=0)

    # Load the GFF3 file and parse it to create a dictionary mapping transcript_id to gene_name
    transcript_to_gene, protein_coding_gene_list = parse_gff3(gff_file)

    # print(transcript_to_gene)

    # Create a new column 'gene_name' in the TPM dataframe by mapping transcript_id to gene_name
    # df['gene_name'] = df.index.map(transcript_to_gene).fillna(df.index.to_numpy())
    # df['gene_name'] = df.index.map(transcript_to_gene).fillna(df.index)

    # Map the transcript IDs to gene names
    mapped_names = df.index.map(transcript_to_gene)
    
    # Replace NaN values (where mapping failed) with the original index
    df['gene_name'] = mapped_names.where(mapped_names.notna(), df.index)

    # replace index with gene_name
    df.index = df['gene_name'].to_numpy()
    df = df.drop('gene_name', axis=1)

    # subset to only protein coding genes
    mask = df.index.isin(protein_coding_gene_list)
    protein_df = df[mask]

    return df, protein_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and process file
    df, protein_df = load_and_process_file(tpm_file, gff_file)

    # save to files
    df.to_csv(gene_name_tpm_file, sep='\t')
    protein_df.to_csv(protein_tpm_file, sep='\t')
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"
    

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["tpm_file=", "gff_file=", "gene_name_tpm_file=", "protein_tpm_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--tpm_file"):
            tpm_file = str(arg)
        if opt in ("--gff_file"):
            gff_file = str(arg)

        if opt in ("--gene_name_tpm_file"):
            gene_name_tpm_file = str(arg)
        if opt in ("--protein_tpm_file"):
            protein_tpm_file = str(arg)

    main()


