# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

help_message = '''
Failed
'''


def load_and_subset(counts_file, differential_methylation_file):
    # load files
    counts_df = pd.read_csv(counts_file, sep="\t")
    dm_df = pd.read_csv(differential_methylation_file, sep="\t")

    # set index
    counts_df.index = counts_df['gene_id'].to_numpy()

    # common genes
    common_genes = np.intersect1d(counts_df['gene_id'].to_numpy(), dm_df['gene'].to_numpy())

    # subset to genes in dm_df
    counts_df = counts_df.loc[common_genes,:]

    return counts_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and subset file
    df = load_and_subset(counts_file, differential_methylation_file)

    # save file
    df.to_csv(pcg_counts_file, sep="\t", index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["counts_file=", "differential_methylation_file=", "pcg_counts_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--counts_file"):
            counts_file = str(arg)
        if opt in ("--differential_methylation_file"):
            differential_methylation_file = str(arg)

        if opt in ("--pcg_counts_file"):
            pcg_counts_file = str(arg)

    main()


