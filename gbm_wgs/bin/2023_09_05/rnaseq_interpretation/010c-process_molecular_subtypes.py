# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import subprocess

help_message = '''
Failed
'''


def process_classification_file(classification_file, significant_threshold=0.05):
    # read file
    df = pd.read_csv(classification_file, sep='\t')

    # drop columns
    df = df.drop(columns=['Proneural', 'Classical', 'Mesenchymal'])

    # set sample as index
    df['sample'] = df.index.to_numpy()

    # melt
    df = df.melt(id_vars=['sample'], var_name='subtype', value_name='p_value')

    # remove characters following "_" in subtype
    df['subtype'] = df['subtype'].str.split('_').str[0]

    # add column 'significant'
    df['significant'] = 'No'
    df.loc[df['p_value'] < significant_threshold, 'significant'] = 'Yes'

    # log10 transform p_value
    df['log10_pval'] = -np.log10(df['p_value'])

    # create a new df with the most significant subtype for each sample
    subtype_df = df.sort_values(by=['sample', 'p_value'])
    subtype_df = subtype_df.groupby('sample').first().reset_index()

    return subtype_df, df



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process cnvkit file
    df, stats_df = process_classification_file(classification_file)

    # save to files
    df.to_csv(subtype_file, sep='\t', index=False)
    stats_df.to_csv(figure_stats_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["classification_file=", "r_script=", "subtype_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--classification_file"):
            classification_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--subtype_file"):
            subtype_file = str(arg)

        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()


