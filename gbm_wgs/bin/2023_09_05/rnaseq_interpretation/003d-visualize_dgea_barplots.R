# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




pval_cutoff = 0.05



barplot = function(gene, input_df, stats_df){

# susbet to gene of interest
input_df = input_df[input_df$gene == gene,]
stats_df = stats_df[stats_df$gene == gene,]
    
# set title
title = paste0(unique(stats_df$gene)[1], ", p=", signif(stats_df$p_value, 3), ', fdr=', signif(stats_df$fdr, 3))
    
# create plot
p = ggplot(input_df, aes(x=patient, y=gene_counts, fill = tumor)) + plot_theme() +
geom_bar(stat="identity", position="dodge") +

ggtitle(title) + ylab('CPM') + xlab('') +

theme(axis.text.x = element_text(angle=90))


print(p)

return()

}




sort_df = function(input_df){
    # Function to extract numeric parts from each element
    extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))
    
    # Extract and sort by numeric parts
    sorted_vector <- unique(input_df$patient)[order(sapply(unique(input_df$patient), extract_numeric))]

    # order samples
    input_df$patient = factor(input_df$patient, levels = sorted_vector)
    
    return(input_df)
}



pdf(opt$figure_file)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')

# sort samples
input_df = sort_df(input_df)

# sort genes
stats_df$gene = fct_reorder(stats_df$gene, stats_df$fdr, .desc=FALSE)
input_df$gene = factor(input_df$gene, levels = intersect(levels(stats_df$gene), input_df$gene))

lapply(levels(input_df$gene), barplot, input_df, stats_df)


dev.off()






