# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)
library(ggrepel)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




dotplot = function(input_df){

# -log10 transform
input_df$browns_pvalues = -log10(input_df$browns_pvalues)
input_df$dpm_pvalues = -log10(input_df$dpm_pvalues)

# log10 cutoff for P-value is 0.05
pval_cutoff = 1.301

# Assign colors based on the conditions
input_df$color = ifelse(input_df$browns_pvalues <= pval_cutoff, "gray", 
                        ifelse(input_df$dpm_pvalues > pval_cutoff, "#1F449C", "#F05039"))

# Count the number of blue and orange points
num_blue = sum(input_df$color == "#1F449C")
num_orange = sum(input_df$color == "#F05039")
num_grey = sum(input_df$color == "gray")

# label df is blue or organe points that are in the genes_oi vector
label_df = input_df %>% filter(gene %in% genes_oi) %>% filter(color == "#1F449C" | color == "#F05039")

# add the top 10 genes by dpm_values to the label_df
label_df = rbind(label_df, input_df %>% arrange(desc(dpm_pvalues)) %>% head(10))


# create plot
p = ggplot(input_df, aes(x = browns_pvalues, y = dpm_pvalues)) + 
geom_point(size = 2.4, shape = 19, aes(color = color)) +

labs(title=paste0('Blue: ', num_blue, ', Orange: ', num_orange, ', Grey: ', num_grey), x ="Brown's P (-log10)", y = "DPM P (-log10)") +

geom_hline(yintercept=pval_cutoff, linetype='dashed', col = 'black', linewidth = 0.5)+
geom_vline(xintercept = pval_cutoff, linetype = "dashed", col = "black", linewidth = 0.5) + 

geom_abline(linewidth = 0.5, slope=1, intercept = 0) +
scale_color_identity() +

ylim(0,7.5) + xlim(0,7.5) +

coord_cartesian() +

theme(plot.title = element_text(size=23,hjust = 0.5),
    axis.title.x = element_text(size=18,margin = unit(c(2, 0, 0, 0), "mm")),
    axis.title.y = element_text(size=18,margin = unit(c(0,4,0,0), "mm")),
    axis.text = element_text(size = 16),
    panel.background = element_blank(),
    axis.line = element_line(colour = "black")) +


geom_label_repel(data=label_df, aes(label=gene), color = 'black',
        seed              = 1234,
        size				= 3,
        force             = 1,
        nudge_y           = 0.5,
        hjust             = 0,
        segment.size      = 0.2,
        max.overlaps = 30
               )

print(p)

}




genes_oi = c('TP53', 'IDH1', 'PTEN', 'ATRX', 'EGFR', 'NF1', 'TERT', 'PIK3CA', 'CIC', 'RB1', 'PIK3R1', 'FUBP1', 'CDKN2A', 'EGFR', 'PDGFRA', 'CDK4', 'CDK6', 'PTEN', 'MDM2', 'MET', 'CCND2', 'MYCN', 'MDM4', 'ATRX')



pdf(opt$figure_file, width=8, height=7)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')

dotplot(input_df)

dev.off()


print(opt$figure_file)





