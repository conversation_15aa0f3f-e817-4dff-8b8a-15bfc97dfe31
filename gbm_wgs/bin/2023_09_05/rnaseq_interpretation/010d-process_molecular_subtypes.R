# <PERSON> (<EMAIL>)

library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}


subtype_colors = c('Proneural' = '#7452a1', 'Classical' = '#9bccea', 'Mesenchymal' = '#8bbf54')


barplot = function(df){

    
# plot
p = ggplot(df, aes(x = sample, y = log10_pval, fill = subtype)) + plot_theme() +
geom_bar(stat = 'identity', color='black', position='dodge') +

scale_fill_manual(values = subtype_colors) +

# add horizontal line at y=-log10(0.05)
geom_hline(yintercept = -log10(0.05), linetype = 'dashed', color = 'red') +

ggtitle('') +
xlab('') + ylab('P-value (-log10)')
# theme(axis.text.x = element_text(angle = 0))

print(p)

return()
}




sort_df = function(input_df){
    # Function to extract numeric parts from each element
    extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))
    
    # Extract and sort by numeric parts
    sorted_vector <- unique(input_df$sample)[order(sapply(unique(input_df$sample), extract_numeric))]

    # order samples
    input_df$sample = factor(input_df$sample, levels = sorted_vector)
    
    return(input_df)
}


pdf(opt$figure_file, width = 15, height = 7)

# load df
df = read.csv(opt$figure_stats_file, sep='\t')

# sort by sample number
df = sort_df(df)

# apply to all patients
barplot(df)


dev.off()


print(opt$figure_file)




