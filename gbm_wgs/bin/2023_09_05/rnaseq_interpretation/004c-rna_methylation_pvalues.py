# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def load_and_process_df(pvalue_file):
    # load data
    df = pd.read_csv(pvalue_file, sep='\t', index_col=0)

    # add gene column
    df['gene'] = df.index

    # melt
    df = pd.melt(df, id_vars=['gene'], var_name='data_source', value_name='pvalue')

    # add -log10(pvalue)
    df['log10_pvalue'] = -np.log10(df['pvalue'])

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process and combine all dfs
    res_df = load_and_process_df(pvalue_file)

    # save to files
    res_df.to_csv(figure_data_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["pvalue_file=", "figure_data_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--pvalue_file"):
            pvalue_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        

    main()




