# Alec Bahcheli

import sys, getopt, time, os, subprocess

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import math, glob


help_message = '''
Failed
'''


def create_workflow_file(wf_execution_file, processing_dir):
    # define the sample
    sample = "-".join(wf_execution_file.split("/")[-1].split("-")[-2:])
    print(wf_execution_file)

    # select raw data folder
    input_dir = sample_dict[sample]

    print(sample, input_dir)
    
    # get runtype
    runtype = wf_execution_file.split("/")[-1].split("_")[0]

    
    to_write = f"""#!/bin/bash
#$ -P reimandlab
#$ -N {runtype}_wf-basecalling_{sample}
#$ -l h_vmem=10G,h_rt=21:0:0:0
#$ -q all.q
#$ -o /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/nanopore/
#$ -e /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/nanopore/

source /u/abahcheli/.profile
source /u/abahcheli/.bashrc
source /etc/profile.d/uge_settings.sh

source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh

mamba activate nextflow

cd {processing_dir}

export NXF_SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export NXF_OFFLINE='true'
export NXF_HOME="{processing_dir}/.nextflow"

nextflow run /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/nextflow_pipelines_offline/epi2me-labs-wf-basecalling_v1.1.7/v1_1_7 \
--input {input_dir} \
--sample_name {sample} \
--output_bam True \
--dorado_ext fast5 \
--basecaller_cfg dna_r9.4.1_e8_sup@v3.3 --remora_cfg dna_r9.4.1_e8_sup@v3.3_5mCG_5hmCG@v0 \
--out_dir {processing_dir} \
-work-dir {processing_dir}/work \
-profile singularity -c /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/epi2me_nextflow/basecalling/nextflow.config -resume \
-process.maxDumpTasks 200

"""

    # create wf_execution_file
    with open(wf_execution_file, 'w') as f:
        f.write(to_write)




def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process each directory to link fast5s
    create_workflow_file(wf_execution_file, processing_dir)
    

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # dictionary for converting names
    sample_dict = {'RLGS2-blood': '/.mounts/labs/ont/PC24B242/230530_RLGS_0002_01_ligationSeq_run1', 'RLGS11-primary': '/.mounts/labs/ont/PC24B242/230626_RLGS_0011_02_ligationSeq_run1', 'RLGS6-recurrent': '/.mounts/labs/ont/PC24B242/230613_RLGS_0006_03_ligationSeq_run1', 'RLGS3-blood': '/.mounts/labs/ont/PC24B242/230607_RLGS_0003_01_ligationSeq_run1', 'RLGS11-recurrent': '/.mounts/labs/ont/PC24B242/230626_RLGS_0011_03_ligationSeq_run1', 'RLGS4-blood': '/.mounts/labs/ont/PC24B242/230607_RLGS_0004_01_ligationSeq_run1', 'RLGS10-recurrent': '/.mounts/labs/ont/PC24B242/230116_RLGS_0010_T2_run1', 'RLGS3-recurrent': '/.mounts/labs/ont/PC24B242/230607_RLGS_0003_03_ligationSeq_run1', 'RLGS7-primary': '/.mounts/labs/ont/PC24B242/230620_RLGS_0007_02_ligationSeq_run1', 'RLGS5-recurrent': '/.mounts/labs/ont/PC24B242/230613_RLGS_0005_03_ligationSeq_run1', 'RLGS8-recurrent': '/.mounts/labs/ont/PC24B242/230620_RLGS_0008_03_ligationSeq_run1', 'RLGS9-recurrent': '/.mounts/labs/ont/PC24B242/230626_RLGS_0009_03_ligationSeq_run1', 'RLGS6-primary': '/.mounts/labs/ont/PC24B242/230613_RLGS_0006_02_ligationSeq_run1', 'RLGS1-recurrent': '/.mounts/labs/ont/PC24B242/230530_RLGS_0001_03_ligationSeq_run1', 'RLGS4-primary': '/.mounts/labs/ont/PC24B242/230607_RLGS_0004_02_ligationSeq_run1', 'RLGS6-blood': '/.mounts/labs/ont/PC24B242/230613_RLGS_0006_01_ligationSeq_run1', 'RLGS5-primary': '/.mounts/labs/ont/PC24B242/230613_RLGS_0005_02_ligationSeq_run1', 'RLGS11-blood': '/.mounts/labs/ont/PC24B242/230626_RLGS_0011_01_ligationSeq_run1', 'RLGS2-primary': '/.mounts/labs/ont/PC24B242/230530_RLGS_0002_02_ligationSeq_run1', 'RLGS3-primary': '/.mounts/labs/ont/PC24B242/230607_RLGS_0003_02_ligationSeq_run1', 'RLGS8-blood': '/.mounts/labs/ont/PC24B242/230620_RLGS_0008_01_ligationSeq_run1', 'RLGS4-recurrent': '/.mounts/labs/ont/PC24B242/230607_RLGS_0004_03_ligationSeq_run1', 'RLGS7-recurrent': '/.mounts/labs/ont/PC24B242/230620_RLGS_0007_03_ligationSeq_run1', 'RLGS12-primary': '/.mounts/labs/ont/PC24B242/230707_RLGS_0012_02_ligationSeq_run1', 'RLGS12-blood': '/.mounts/labs/ont/PC24B242/230707_RLGS_0012_01_ligationSeq_run1', 'RLGS8-primary': '/.mounts/labs/ont/PC24B242/230620_RLGS_0008_02_ligationSeq_run1', 'RLGS1-blood': '/.mounts/labs/ont/PC24B242/230530_RLGS_0001_01_ligationSeq_run1', 'RLGS9-primary': '/.mounts/labs/ont/PC24B242/230626_RLGS_0009_02_ligationSeq_run1', 'RLGS2-recurrent': '/.mounts/labs/ont/PC24B242/230530_RLGS_0002_03_ligationSeq_run1', 'RLGS10-primary': '/.mounts/labs/ont/PC24B242/230116_RLGS_0010_T1_run1', 'RLGS9-blood': '/.mounts/labs/ont/PC24B242/230626_RLGS_0009_01_ligationSeq_run1', 'RLGS7-blood': '/.mounts/labs/ont/PC24B242/230620_RLGS_0007_01_ligationSeq_run1', 'RLGS1-primary': '/.mounts/labs/ont/PC24B242/230530_RLGS_0001_02_ligationSeq_run1', 'RLGS5-blood': '/.mounts/labs/ont/PC24B242/230613_RLGS_0005_01_ligationSeq_run1', 'RLGS12-recurrent': '/.mounts/labs/ont/PC24B242/230707_RLGS_0012_03_ligationSeq_run1', 'RLGS10-blood': '/.mounts/labs/ont/PC24B242/230116_RLGS_0010_R_run1'}

    # sample_dict = {'/.mounts/labs/ont/PC24B242/230530_RLGS_0002_01_ligationSeq_run1': 'RLGS1_ligationSeq', '/.mounts/labs/ont/PC24B242/230626_RLGS_0011_02_ligationSeq_run1': 'RLGS2_ligationSeq', '/.mounts/labs/ont/PC24B242/230613_RLGS_0006_03_ligationSeq_run1': 'RLGS3_ligationSeq', '/.mounts/labs/ont/PC24B242/230607_RLGS_0003_01_ligationSeq_run1': 'RLGS1_ligationSeq', '/.mounts/labs/ont/PC24B242/230626_RLGS_0011_03_ligationSeq_run1': 'RLGS3_ligationSeq', '/.mounts/labs/ont/PC24B242/230607_RLGS_0004_01_ligationSeq_run1': 'RLGS1_ligationSeq', '/.mounts/labs/ont/PC24B242/230116_RLGS_0010_T2_run1': 'RLGST2_run1', '/.mounts/labs/ont/PC24B242/230607_RLGS_0003_03_ligationSeq_run1': 'RLGS3_ligationSeq', '/.mounts/labs/ont/PC24B242/230620_RLGS_0007_02_ligationSeq_run1': 'RLGS2_ligationSeq', '/.mounts/labs/ont/PC24B242/230613_RLGS_0005_03_ligationSeq_run1': 'RLGS3_ligationSeq', '/.mounts/labs/ont/PC24B242/230620_RLGS_0008_03_ligationSeq_run1': 'RLGS3_ligationSeq', '/.mounts/labs/ont/PC24B242/230626_RLGS_0009_03_ligationSeq_run1': 'RLGS3_ligationSeq', '/.mounts/labs/ont/PC24B242/230613_RLGS_0006_02_ligationSeq_run1': 'RLGS2_ligationSeq', '/.mounts/labs/ont/PC24B242/230530_RLGS_0001_03_ligationSeq_run1': 'RLGS3_ligationSeq', '/.mounts/labs/ont/PC24B242/230607_RLGS_0004_02_ligationSeq_run1': 'RLGS2_ligationSeq', '/.mounts/labs/ont/PC24B242/230613_RLGS_0006_01_ligationSeq_run1': 'RLGS1_ligationSeq', '/.mounts/labs/ont/PC24B242/230613_RLGS_0005_02_ligationSeq_run1': 'RLGS2_ligationSeq', '/.mounts/labs/ont/PC24B242/230626_RLGS_0011_01_ligationSeq_run1': 'RLGS1_ligationSeq', '/.mounts/labs/ont/PC24B242/230530_RLGS_0002_02_ligationSeq_run1': 'RLGS2_ligationSeq', '/.mounts/labs/ont/PC24B242/230607_RLGS_0003_02_ligationSeq_run1': 'RLGS2_ligationSeq', '/.mounts/labs/ont/PC24B242/230620_RLGS_0008_01_ligationSeq_run1': 'RLGS1_ligationSeq', '/.mounts/labs/ont/PC24B242/230607_RLGS_0004_03_ligationSeq_run1': 'RLGS3_ligationSeq', '/.mounts/labs/ont/PC24B242/230620_RLGS_0007_03_ligationSeq_run1': 'RLGS3_ligationSeq', '/.mounts/labs/ont/PC24B242/230707_RLGS_0012_02_ligationSeq_run1': 'RLGS2_ligationSeq', '/.mounts/labs/ont/PC24B242/230707_RLGS_0012_01_ligationSeq_run1': 'RLGS1_ligationSeq', '/.mounts/labs/ont/PC24B242/230620_RLGS_0008_02_ligationSeq_run1': 'RLGS2_ligationSeq', '/.mounts/labs/ont/PC24B242/230530_RLGS_0001_01_ligationSeq_run1': 'RLGS1_ligationSeq', '/.mounts/labs/ont/PC24B242/230626_RLGS_0009_02_ligationSeq_run1': 'RLGS2_ligationSeq', '/.mounts/labs/ont/PC24B242/230530_RLGS_0002_03_ligationSeq_run1': 'RLGS3_ligationSeq', '/.mounts/labs/ont/PC24B242/230116_RLGS_0010_T1_run1': 'RLGST1_run1', '/.mounts/labs/ont/PC24B242/230626_RLGS_0009_01_ligationSeq_run1': 'RLGS1_ligationSeq', '/.mounts/labs/ont/PC24B242/230620_RLGS_0007_01_ligationSeq_run1': 'RLGS1_ligationSeq', '/.mounts/labs/ont/PC24B242/230530_RLGS_0001_02_ligationSeq_run1': 'RLGS2_ligationSeq', '/.mounts/labs/ont/PC24B242/230613_RLGS_0005_01_ligationSeq_run1': 'RLGS1_ligationSeq', '/.mounts/labs/ont/PC24B242/230707_RLGS_0012_03_ligationSeq_run1': 'RLGS3_ligationSeq', '/.mounts/labs/ont/PC24B242/230116_RLGS_0010_R_run1': 'RLGSR_run1'}

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["wf_execution_file=", "processing_dir="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--wf_execution_file"):
            wf_execution_file = str(arg)
            
        if opt in ("--processing_dir"):
            processing_dir = str(arg)
            
    main()





