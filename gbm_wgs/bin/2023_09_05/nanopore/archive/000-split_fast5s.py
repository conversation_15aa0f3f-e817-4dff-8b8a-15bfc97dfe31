# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os, subprocess

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import math, glob


help_message = '''
Failed
'''


def distribute_files(input_dir, output_dir, pod5_dir):
    # Create 20 new directories
    for i in range(20):
        os.makedirs(os.path.join(output_dir, f"{i+1}"), exist_ok=True)
        os.makedirs(os.path.join(pod5_dir, f"{i+1}"), exist_ok=True)
    
    # Get list of files in input directory
    files = os.listdir(input_dir)
    num_files = len(files)
    
    # Calculate approximate number of files per directory
    files_per_directory = math.ceil(num_files / 20)
    
    # Distribute files into directories
    for i, file in enumerate(files):
        dest_dir = os.path.join(output_dir, f"{(i // files_per_directory) + 1}")
        src_file_path = os.path.join(input_dir, file)
        dest_file_path = os.path.join(dest_dir, file)
        try:
            os.symlink(src_file_path, dest_file_path)
        except:
            pass


def process_fast5_dirs(directories, fast5_link_path, pod5_directory):
    # Create a new directory to store the linked fast5s and pod5s
    os.makedirs(fast5_link_path, exist_ok=True)
    os.makedirs(pod5_directory, exist_ok=True)

    # touch null.txt file to indicate that the fast5s have been linked and pod5 dir created
    with open(os.path.join(fast5_link_path, "null.txt"), "w") as f:
        pass
    with open(os.path.join(pod5_directory, "null.txt"), "w") as f:
        pass
    
    # Process each directory
    for directory in directories:
        # Get the sample name
        sample_name = "_".join(directory.split('/')[-3].split("_")[1:-1])
        sample_name = sample_dict.get(sample_name, sample_name)
        
        # Create a new directory for the sample
        sample_dir = os.path.join(fast5_link_path, sample_name)
        os.makedirs(sample_dir, exist_ok=True)

        # create a new directory for the sample in pod5s
        pod5_dir = os.path.join(pod5_directory, sample_name)
        os.makedirs(pod5_dir, exist_ok=True)

        
        # distribute the fast5s
        distribute_files(directory, sample_dir, pod5_dir)



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # Find directories matching the pattern
    directories = glob.glob(f'{production_dir}/*/*/fast5_pass')

    # process each directory to link fast5s
    process_fast5_dirs(directories, fast5_link_path, pod5_directory)
    

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # production_dir = "/.mounts/labs/reimandlab/production"
    fast5_link_path = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/data/raw_data/fast5s"

    # dictionary for converting names
    sample_dict = {'RLGS_0010_R': 'RLGS10-blood', 'RLGS_0010_T1': 'RLGS10-primary', 'RLGS_0010_T2': 'RLGS10-recurrent'}


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["production_dir=", "fast5_link_path=", "pod5_directory="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--production_dir"):
            production_dir = str(arg)
        if opt in ("--production_dir"):
            production_dir = str(arg)
            
        if opt in ("--fast5_link_path"):
            fast5_link_path = str(arg)
        if opt in ("--pod5_directory"):
            pod5_directory = str(arg)
            
    main()





