# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np



help_message = '''
Failed
'''

def create_workflow_file(linked_ubam_file, wf_execution_file):
    # define the sample
    sample = "-".join(linked_ubam_file.split("/")[-1].split("-")[:-1])
    runtype = linked_ubam_file.split("/")[-3]

    # get the sample name and the results directory
    # sample = nanopore_sample_dict[original_sample]
    processing_dir = os.path.join("/".join(linked_ubam_file.split("/")[:-2]), sample)

    to_write = f"""#!/bin/bash
#$ -P reimandlab
#$ -N {runtype}_wf-alignment_{sample}
#$ -l h_vmem=10G,h_rt=1:0:0:0
#$ -q u20build
#$ -o /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/nanopore/
#$ -e /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/nanopore/

source /u/abahcheli/.profile
source /u/abahcheli/.bashrc
source /etc/profile.d/uge_settings.sh

source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh

mamba activate nextflow

cd {processing_dir}

export NXF_SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/

nextflow run epi2me-labs/wf-alignment \
--bam {linked_ubam_file} \
--references /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore/ \
--threads 20 \
--out_dir {processing_dir} \
-profile singularity -c /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/epi2me_nextflow/alignment/nextflow.config -resume

"""

    # create wf_execution_file
    with open(wf_execution_file, 'w') as f:
        f.write(to_write)




def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # create workflow file
    create_workflow_file(linked_ubam_file, wf_execution_file)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["linked_ubam_file=", "wf_execution_file=", "figure_data_file=", "figure_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--linked_ubam_file"):
            linked_ubam_file = str(arg)

        if opt in ("--wf_execution_file"):
            wf_execution_file = str(arg)

    main()


