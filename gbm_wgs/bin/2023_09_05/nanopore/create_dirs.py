import os

nanopore_tumor_sample_csv = "RLGS1-primary,RLGS1-recurrent,RLGS2-primary,RLGS2-recurrent,RLGS3-primary,RLGS3-recurrent,RLGS4-primary,RLGS4-recurrent,RLGS5-primary,RLGS5-recurrent,RLGS6-primary,RLGS6-recurrent,RLGS7-primary,RLGS7-recurrent,RLGS8-primary,RLGS8-recurrent,RLGS9-primary,RLGS9-recurrent,RLGS10-primary,RLGS10-recurrent,RLGS11-primary,RLGS11-recurrent,RLGS12-primary,RLGS12-recurrent"
sample_codes_list = set([sample.split("-")[0] for sample in nanopore_tumor_sample_csv.split(",")])

main_dir = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore_evolution'

dir_list = [os.path.join(main_dir, sample) for sample in sample_codes_list]

for dir in dir_list:
    if not os.path.exists(dir):
        os.makedirs(dir)

