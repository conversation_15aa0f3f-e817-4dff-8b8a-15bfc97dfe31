# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


sample_sex_dict = {'RLGS1': 'XY', 'RLGS2': 'XY', 'RLGS3': 'XX', 'RLGS4': 'XY', 'RLGS5': 'XY', 'RLGS6': 'XY', 'RLGS7': 'XX', 'RLGS8': 'XX', 'RLGS9': 'XY', 'RLGS10': 'XY', 'RLGS11': 'XY', 'RLGS12': 'XY'}


def create_workflow_file(bam_file, processing_dir, genome_fasta, model, wf_execution_file):
    # define the sample
    sample = bam_file.split("/")[-2]
    sample_id = sample.split("-")[0]

    # samples sex
    sex = sample_sex_dict[sample_id]
    
    # removed: --sv --snp --mod --cnv --str --phased \
    # --output_gene_summary \
    # --classify_insert True \
    # added: --bam_min_coverage 0 \
    to_write = f"""#!/bin/bash
#$ -P reimandlab
#$ -N wf-human_variation_{sample}
#$ -l h_vmem=10G,h_rt=7:0:0:0
#$ -q all.q
#$ -o /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/nanopore/
#$ -e /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/nanopore/

source /u/abahcheli/.profile
source /u/abahcheli/.bashrc
source /etc/profile.d/uge_settings.sh

source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh

mamba activate nextflow

cd {processing_dir}

export NXF_SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export NXF_OFFLINE='true'
export NXF_HOME="{processing_dir}/.nextflow"

nextflow run /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/nextflow_pipelines_offline/epi2me-labs-wf-human-variation_v2.3.0/v2_3_0 \
--bam {bam_file} \
--ref {genome_fasta} \
--sv --cnv \
--bam_min_coverage 0 \
--sample_name {sample} --sex {sex} \
--ubam_map_threads 10 \
--override_basecaller_cfg {model} \
--out_dir {processing_dir} \
-work-dir {processing_dir}/work \
-profile singularity \
-c /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/epi2me_nextflow/nextflow.config \
-resume

"""

    # create wf_execution_file
    with open(wf_execution_file, 'w') as f:
        f.write(to_write)



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # create workflow file
    create_workflow_file(bam_file, processing_dir, genome_fasta, model, wf_execution_file)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # model for dorado basecalling
    model = 'dna_r9.4.1_e8_sup@v3.3'
    # --basecaller_cfg dna_r9.4.1_e8_sup@v3.3 --remora_cfg dna_r9.4.1_e8_sup@v3.3_5mCG_5hmCG@v0 
    
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["bam_file=", "genome_fasta=", "processing_dir=", "wf_execution_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--bam_file"):
            bam_file = str(arg)
        if opt in ("--genome_fasta"):
            genome_fasta = str(arg)
        if opt in ("--processing_dir"):
            processing_dir = str(arg)

        if opt in ("--wf_execution_file"):
            wf_execution_file = str(arg)

    main()


