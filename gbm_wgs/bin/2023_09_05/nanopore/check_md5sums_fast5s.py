# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import subprocess

from concurrent.futures import ProcessPoolExecutor

help_message = '''
Failed
'''

def create_df_checksums():
    local = glob.glob("/.mounts/labs/reimandlab/private/users/abahcheli/tmp/local*md5")
    simpson = glob.glob("/.mounts/labs/reimandlab/private/users/abahcheli/tmp/simpson*md5")
    
    def merge_dfs(files):
        # Merges all the dataframes in the list of files
        res_df = []
    
        for file in files:
            df = pd.read_csv(file, delim_whitespace=True, header=None)
            df.columns = ['md5', 'file']
    
            # remove the path from the file name
            df['file'] = df['file'].str.split("/").str[-1]
            
            # add file source to the dataframe
            # df['source'] = file.split("/")[-1].split("_")[0]
            res_df.append(df)
    
        return pd.concat(res_df)
    
    local_df = merge_dfs(local)
    simpson_df = merge_dfs(simpson)
    
    df = pd.merge(local_df, simpson_df, on='file', how='outer', suffixes=('_local_production', '_simpson'))
    # df = df[~df['md5_local_production'].isna()]
    df['checksum_match'] = df['md5_local_production'] == df['md5_simpson']
    
    df.to_csv('/.mounts/labs/reimandlab/private/users/abahcheli/tmp/nanpore_pilot_file_md5s.tsv', sep='\t', index=False)




def compute_checksums(directory):
    # Command to get md5sum of all files in the directory
    cmd = f"find {directory} -type f -exec md5sum {{}} +"
    
    # Run the command
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    # Process the result
    checksums = result.stdout.strip().split('\n')
    
    # Extract file paths and md5 checksums
    file_checksums = [(line.split()[0], line.split()[1]) for line in checksums]

    # list of files in the directory corresponding to the checksums
    files = [line.split()[0] for line in checksums]

    # list of checksums
    checksums = [line.split()[1] for line in checksums]

    # create a df with the files and checksums
    df = pd.DataFrame({'file': files, 'md5_checksum': checksums})
    df['directory'] = directory

    return df
    

def generate_checksums(directories, threads):
    """Generate MD5 checksums for all files in a list of directories and return as a DataFrame."""

    # Use ProcessPoolExecutor to handle directories concurrently
    with ProcessPoolExecutor(max_workers=threads) as executor:
        df = pd.concat(list(executor.map(compute_checksums, directories)))

    return df


# generate checksums for each group of directories
def calculate_checksums_dirs(directories_1, directories_2, threads):
    # generate checksum for first set of dirs 
    df_1 = generate_checksums(directories_1, threads)
    df_2 = generate_checksums(directories_2, threads)

    # rename checksum column of df_2
    df_2.columns = ['directory', 'md5_checksum_simpson_lab', 'file']

    # add checksum column of df_2 to df_1 based on file name
    df = pd.merge(df_1, df_2, on='file', how='left')

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # Example usage
    directories_1 = ['/.mounts/labs/reimandlab/production/230116_RLGS_0010_R_run1/20230116_1521_2C_PAM90818_15eb3a16/fast5_pass', '/.mounts/labs/reimandlab/production/230116_RLGS_0010_T1_run1/20230116_1520_2A_PAM23013_977f38c5/fast5_pass', '/.mounts/labs/reimandlab/production/230116_RLGS_0010_T2_run1/20230116_1522_2E_PAM37215_993e87b0/fast5_pass']
    directories_2 = ['/.mounts/labs/ont/PC24B242/230116_RLGS_0010_R_run1/230116_RLGS_0010_R_run1/20230116_1521_2C_PAM90818_15eb3a16/fast5_pass', '/.mounts/labs/ont/PC24B242/230116_RLGS_0010_T1_run1/230116_RLGS_0010_T1_run1/20230116_1520_2A_PAM23013_977f38c5/fast5_pass', '/.mounts/labs/ont/PC24B242/230116_RLGS_0010_T2_run1/230116_RLGS_0010_T2_run1/20230116_1522_2E_PAM37215_993e87b0/fast5_pass']

    # generate checksums for each group of directories
    df = calculate_checksums_dirs(directories_1, directories_2, threads)

    # save to file
    df.to_csv(outfile, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["null=", "outfile=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--null"):
            null = str(arg)

        if opt in ("--outfile"):
            outfile = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()


