#!/bin/bash
#$ -P reimandlab
#$ -N primary_merge
#$ -l h_vmem=10G,h_rt=1:0:0:0

cd /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore/RLGS10-primary

/.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools merge /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore/RLGS10-primary/RLGS10-primary.pass.bam /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore/RLGS10-primary/RLGS10-primary_p1.pass.bam /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore/RLGS10-primary_p2/RLGS10-primary_p2.pass.bam

echo DONE PASS

/.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools merge /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore/RLGS10-primary/RLGS10-primary.fail.bam /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore/RLGS10-primary/RLGS10-primary_p1.fail.bam /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore/RLGS10-primary_p2/RLGS10-primary_p2.fail.bam

echo DONE FAIL
