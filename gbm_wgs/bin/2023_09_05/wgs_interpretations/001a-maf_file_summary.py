# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os, glob, subprocess

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def open_and_combine_maf_files(directory, genome_size_mb = 2900):
    # Search pattern for files with ".maf" extension
    search_pattern = os.path.join(directory, '*/*.maf')

    # Get list of file paths
    file_paths = glob.glob(search_pattern, recursive=True)

    # List to store DataFrames
    dfs = []

    n_mutations_df = []

    # Iterate over file paths
    for file_path in file_paths:
        # Read MAF file into DataFrame
        df = pd.read_csv(file_path, sep='\t', comment="#", low_memory=False)
        
        # Add a column describing the file
        sample = file_path.split("/")[-1].split("_")[0]
        df['sample'] = sample
        df['type'] = sample.split("-")[1]
        df['sample_number'] = sample.split("-")[0]
        
        # Append DataFrame to list
        dfs.append(df)

        # calculate len of df
        type = sample.split("-")[1]
        n_mutations_df.append([len(df) / genome_size_mb, sample, type])
    
    # Concatenate all DataFrames
    combined_df = pd.concat(dfs, ignore_index=True)

    n_mutations_df = pd.DataFrame(n_mutations_df, columns = ['mutations_per_mb', 'sample', 'type'])
    
    return combined_df, n_mutations_df


def classify_mutation_types(maf_df):
    # if not in the list, then it is a 'other' mutation
    # mutation_types = ['Frame_Shift_Del', 'In_Frame_Del', 'Missense_Mutation', 'In_Frame_Ins', 'Frame_Shift_Ins', 'Nonsense_Mutation']
    mutation_types = ['Frame_Shift_Del', 'In_Frame_Del', 'Missense_Mutation', 'In_Frame_Ins', 'Frame_Shift_Ins', 'Nonsense_Mutation', 'Splice_Site']

    # Create a new column with the mutation type
    maf_df['mutation_type'] = maf_df['Variant_Classification'].apply(lambda x: x if x in mutation_types else 'Other')

    # remove "Other" mutations
    mask = maf_df['mutation_type'].str.contains('Other')
    maf_df = maf_df.loc[np.invert(mask),:]

    return maf_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load dfs and get mutation counts
    maf_df, mut_df = open_and_combine_maf_files(wgs_results_directory)

    # classify mutation types
    maf_df = classify_mutation_types(maf_df)

    # save to files
    maf_df.to_csv(figure_data_file, sep='\t', index=False)
    mut_df.to_csv(figure_stats_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    # regex for files of interest
    file_regex = "*maf"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["wgs_results_directory=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--wgs_results_directory"):
            wgs_results_directory = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()




