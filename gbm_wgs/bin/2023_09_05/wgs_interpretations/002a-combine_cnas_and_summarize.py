# <PERSON>i

import sys, getopt, time, os, glob, subprocess

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''

def load_varscan_cnas(directory):
    # Search pattern for files with ".maf" extension
    search_pattern = os.path.join(directory, '*/*.cnv')

    # Get list of file paths
    file_paths = glob.glob(search_pattern, recursive=True)

    # List to store DataFrames
    dfs = []

    # Iterate over file paths
    for file_path in file_paths:
        # Read MAF file into DataFrame
        df = pd.read_csv(file_path, sep='\t')
        
        # Add a column describing the file
        sample = file_path.split("/")[-2]
        df['sample'] = sample
        df['type'] = sample.split("-")[1]
        df['sample_number'] = sample.split("-")[0]

        # add CNA classification
        df['cna_classification'] = df['seg.mean'].apply(lambda x: 'loss' if x < -0.3 else ('gain' if x > 0.3 else 'normal'))

        # rename cols
        df = df.rename(columns={'chrom': 'chr', 'loc.start': 'start', 'loc.end': 'stop', 'seg.mean':'mean'})
        
        # Append DataFrame to list
        dfs.append(df)
    
    # Concatenate all DataFrames
    combined_df = pd.concat(dfs)

    # add description
    combined_df['software'] = 'varscan2'
    
    return combined_df


def load_hmmcopy_cnas(directory):
    # Search pattern for files with ".maf" extension
    search_pattern = os.path.join(directory, '*/hmmcopy_results.tsv')

    # Get list of file paths
    file_paths = glob.glob(search_pattern, recursive=True)

    # List to store DataFrames
    dfs = []

    # Iterate over file paths
    for file_path in file_paths:
        # Read MAF file into DataFrame
        df = pd.read_csv(file_path, sep='\t')
        
        # Add a column describing the file
        sample = file_path.split("/")[-2]
        df['sample'] = sample
        df['type'] = sample.split("-")[1]
        df['sample_number'] = sample.split("-")[0]

        # add CNA classification
        df['cna_classification'] = df['state'].apply(lambda x: 'loss' if x < 2 else ('gain' if x > 3 else 'normal'))

        # modify chr col
        df['chr'] = 'chr' + df['chr'].astype("str")

        # rename cols
        df = df.rename(columns={'end': 'stop'})
        
        # Append DataFrame to list
        dfs.append(df)
    
    # Concatenate all DataFrames
    combined_df = pd.concat(dfs)
    
    # add description
    combined_df['software'] = 'hmmcopy'

    return combined_df


def load_freec_cnas(directory):
    # Search pattern for files with ".maf" extension
    search_pattern = os.path.join(directory, '*/*bam_CNVs')

    # Get list of file paths
    file_paths = glob.glob(search_pattern, recursive=True)

    # List to store DataFrames
    dfs = []

    # Iterate over file paths
    for file_path in file_paths:
        # Read MAF file into DataFrame
        df = pd.read_csv(file_path, sep='\t', header=None)
        df.columns = ['chr', 'start', 'stop', 'cn', 'cna_classification']
        
        # Add a column describing the file
        sample = file_path.split("/")[-2]
        df['sample'] = sample
        df['type'] = sample.split("-")[1]
        df['sample_number'] = sample.split("-")[0]

        # modify chr col
        df['chr'] = 'chr' + df['chr'].astype("str")
        
        # Append DataFrame to list
        dfs.append(df)
    
    # Concatenate all DataFrames
    combined_df = pd.concat(dfs)
    
    # add description
    combined_df['software'] = 'freec'

    return combined_df


def load_cnvkit_cnas(directory):
    # Search pattern for files with ".maf" extension
    search_pattern = os.path.join(directory, '*/*call.cns')

    # Get list of file paths
    file_paths = glob.glob(search_pattern, recursive=True)

    # List to store DataFrames
    dfs = []

    # Iterate over file paths
    for file_path in file_paths:
        # Read MAF file into DataFrame
        df = pd.read_csv(file_path, sep='\t')
        
        # Add a column describing the file
        sample = file_path.split("/")[-2]
        df['sample'] = sample
        df['type'] = sample.split("-")[1]
        df['sample_number'] = sample.split("-")[0]

        # add CNA classification
        df['cna_classification'] = df['cn'].apply(lambda x: 'loss' if x < 2 else ('gain' if x > 2 else 'normal'))

        # modify col names
        df = df.rename(columns={'chromosome': 'chr', 'end': 'stop', 'log2': 'mean'})

        # drop unnecessary cols
        df = df.drop(columns=['gene', 'depth', 'p_ttest', 'probes', 'weight'])
        
        # Append DataFrame to list
        dfs.append(df)
    
    # Concatenate all DataFrames
    combined_df = pd.concat(dfs)
    
    # add description
    combined_df['software'] = 'cnvkit'

    return combined_df


def load_cnvnator_cnas(directory):
    # Search pattern for files with ".maf" extension
    search_pattern = os.path.join(directory, '*/cnvnator.tsv')

    # Get list of file paths
    file_paths = glob.glob(search_pattern, recursive=True)

    # List to store DataFrames
    dfs = []

    # Iterate over file paths
    for file_path in file_paths:
        # Read MAF file into DataFrame
        df = pd.read_csv(file_path, sep='\t')

        # column names
        df.columns = ['cna_classification', 'coordinates', 'size', 'normalized_RD', 'p-val1', 'p-val2', 'p-val3', 'p-val4', 'q0']

        # split coordinates
        df['chr'] = df['coordinates'].apply(lambda x: x.split(':')[0])
        df['start'] = df['coordinates'].apply(lambda x: x.split(':')[1].split('-')[0])
        df['stop'] = df['coordinates'].apply(lambda x: x.split(':')[1].split('-')[1])

        # drop unnecessary cols
        df = df.drop(columns=['coordinates', 'size', 'normalized_RD', 'p-val1', 'p-val2', 'p-val3', 'p-val4', 'q0'])

        # reclassify cnv_type
        df['cna_classification'] = df['cna_classification'].apply(lambda x: 'loss' if x == 'deletion' else ('gain' if x == 'duplication' else 'normal'))
        
        # Add a column describing the file
        sample = file_path.split("/")[-2]
        df['sample'] = sample
        df['type'] = sample.split("-")[1]
        df['sample_number'] = sample.split("-")[0]
        
        # Append DataFrame to list
        dfs.append(df)
    
    # Concatenate all DataFrames
    combined_df = pd.concat(dfs)
    
    # add description
    combined_df['software'] = 'cnvnator'

    return combined_df


def combine_dfs(directory):
    # load and combine Varscan2 CNAs
    varscan_cna_df = load_varscan_cnas(directory)

    # load hmmcopy CNAs
    hmmcopy_cna_df = load_hmmcopy_cnas(directory)

    # load control-freec CNAs
    freec_cna_df = load_freec_cnas(directory)

    # load cnvkit CNAs
    cnvkit_cna_df = load_cnvkit_cnas(directory)

    # load cnvnator CNAs
    cnvnator_cna_df = load_cnvnator_cnas(directory)


    # combine all files
    combined_df = pd.concat([varscan_cna_df, hmmcopy_cna_df, freec_cna_df, cnvkit_cna_df, cnvnator_cna_df])

    # reorder columns
    combined_df = combined_df.drop(['num.mark','ID', 'state'], axis=1)
    combined_df = combined_df.loc[:,['sample', 'sample_number', 'type', 'software', 'chr', 'start', 'stop', 'cna_classification', 'cn', 'mean', 'median']]

    return combined_df


def summarize_stats(df):    
    # Calculate the length of each segment
    df["length"] = df["stop"].astype('int') - df["start"].astype('int') + 1

    # Group by sample, software, chromosome, and cna_classification to summarize total bases
    summary_df = df.groupby(["sample", "software", "chr", "cna_classification"])["length"].sum().reset_index()

    return summary_df



def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # combine all files
    combined_df = combine_dfs(wgs_results_directory)

    # calculate summary stats
    summary_stats = summarize_stats(combined_df)

    # save files
    combined_df.to_csv(figure_data_file, sep='\t', index=False)
    summary_stats.to_csv(figure_stats_file, sep='\t', index=False)

    print(summary_stats)
    
    # run R script
    cline = [rscript, r_script, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"
    

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["wgs_results_directory=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--wgs_results_directory"):
            wgs_results_directory = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()




