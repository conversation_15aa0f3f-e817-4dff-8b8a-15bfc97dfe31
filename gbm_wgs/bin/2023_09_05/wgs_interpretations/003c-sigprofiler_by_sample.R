# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(viridis) # Ensure this package is installed

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}






# plot venn diagram
barplot = function(input_df){


# my_colors <- c("#E41A1C", "#377EB8", "#4DAF4A", "#984EA3", "#FF7F00", "#FFFF33", 
#              "#A65628", "#F781BF", "#999999", "#8DD3C7", "#BEBADA", "#FB8072", 
#              "#80B1D3", "#FDB462", "#B3DE69", "#FCCDE5", "#D9D9D9", "#BC80BD", 
#              "#CCEBC5", "#FFED6F")

my_colors <- c(
    "#E41A1C", "#377EB8", "#4DAF4A", "#984EA3", "#FF7F00", 
    "#FFFF33", "#A65628", "#F781BF", "#999999", "#8DD3C7", 
    "#BEBADA", "#FB8072", "#80B1D3", "#FDB462", "#B3DE69", 
    "#FCCDE5", "#D9D9D9", "#BC80BD", "#CCEBC5", "#FFED6F", 
    "#FF5733", "#33FF57", "#3357FF"
)
    
p = ggplot(input_df, aes(x = sample_number, y = Fraction, fill = SBS_signature)) + plot_theme() +
geom_bar(stat = "identity") +

ggtitle('') + ylab('Fraction of SNVs') + xlab('') +
scale_fill_manual(values = my_colors)  # Use manual color scale with defined colors
# scale_fill_viridis(discrete = TRUE) # Use viridis for discrete color scale

print(p)

return()
}



pdf(opt$figure_file, width = 7, height = 7)

# load df
input_df = read.csv(opt$figure_stats_file, sep='\t')

# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# order samples
sorted_vector <- unique(input_df$sample_number)[order(sapply(unique(input_df$sample_number), extract_numeric))]
input_df$sample_number = factor(input_df$sample_number, levels = sorted_vector)

# order signatures
input_df$SBS_signature <- fct_reorder(input_df$SBS_signature, input_df$Fraction, .fun = sum, .desc = FALSE)

print(length(unique(input_df$SBS_signature)))

# create plot
barplot(input_df)

dev.off()

print(opt$figure_file)





