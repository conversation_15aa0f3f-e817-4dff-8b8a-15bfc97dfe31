# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os, subprocess

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''

def process_mutation_timing(df):
    # Calculate the number of mutations per cluster
    mutation_counts = df.groupby('cluster_id')['mutation_id'].count() / 1000

    # Calculate mean cellular prevalence for node size
    mean_prevalences = df.groupby('cluster_id')['cellular_prevalence'].mean()

    # Create a DataFrame from mutation counts and mean prevelances
    result_df = pd.DataFrame({'mutation_counts': mutation_counts, 'mean_prevalences': mean_prevalences})

    # Calculate total cellular prevalence
    total_prevalence = result_df['mean_prevalences'].sum()

    # Calculate percentage of total for each cellular prevalence value
    result_df['cellular_prevalence'] = result_df['mean_prevalences'] / total_prevalence

    
    # Reset index and rename columns
    result_df = result_df.reset_index().rename(columns={'cellular_prevalence': 'clonal_prev', 'mutation_counts':'timepoint'})

    # add a clone_id by sorting according to timepoint and ordering
    result_df = result_df.sort_values(by = 'timepoint').reset_index(drop=True)
    result_df['clone_id'] = result_df.index.to_numpy()

    return result_df


def generate_prevalence_df(df):
    res_df = []

    # Iterate through unique timepoints
    for timepoint in df['timepoint'].unique():
        tmp_df = df.copy()
        tmp_df['timepoint'] = timepoint

        # Set clonal_prev to 0 for cluster_id timepoints after the current timepoint
        mask = df['timepoint'] > timepoint
        tmp_df.loc[mask, 'clonal_prev'] = 0
        
        # Calculate relative clonal_prev for each timepoint
        total_clonal_prev = tmp_df[tmp_df['timepoint'] == timepoint]['clonal_prev'].sum()
        tmp_df.loc[tmp_df['timepoint'] == timepoint, 'clonal_prev'] /= total_clonal_prev
        
        res_df.append(tmp_df)

    return pd.concat(res_df).sort_values('timepoint')


def build_tree_edges(df):
    # Sort the DataFrame by cellular_prevalence in descending order
    # sorted_df = df.sort_values(by='clonal_prev', ascending=False)
    sorted_df = df.sort_values(['timepoint', 'clonal_prev'], ascending=[True, False])

    # Get the source cluster_id
    source_cluster = sorted_df.index[0]

    # Initialize lists to store edges
    source_list = []
    target_list = []

    # Iterate through sorted DataFrame to build edges
    for idx, row in sorted_df.iterrows():
        if idx != source_cluster:
            source_list.append(source_cluster)
            target_list.append(idx)

    # Create DataFrame for edges
    edges_df = pd.DataFrame({'source': source_list, 'target': target_list})

    return edges_df



def process_pyclone_dfs(pyclone_dir):
    # list files in pyclone_dir with regex
    pyclone_file_regex = os.path.join(pyclone_dir, '*pyclone_vi.tsv')
    pyclone_files = glob.glob(pyclone_file_regex)

    # for each file, read as df
    res_prev_df = []
    res_edges_df = []

    for file in pyclone_files:
        # load df
        df = pd.read_csv(file, sep='\t')

        # process mutation timing
        df = process_mutation_timing(df)

        # generate edges and prevalence dfs
        prev_df = generate_prevalence_df(df)
        edges_df = build_tree_edges(df)

        # add sample_id
        sample_id = "_".join(file.split('/')[-1].split('_')[:-2])
        prev_df['sample'] = sample_id
        edges_df['sample'] = sample_id

        # append to res_df
        res_prev_df.append(prev_df)
        res_edges_df.append(edges_df)

    # concatenate all dfs
    res_prev_df = pd.concat(res_prev_df)
    res_edges_df = pd.concat(res_edges_df)

    return res_prev_df, res_edges_df



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process summary file
    res_df, stats_df = process_pyclone_dfs(pyclone_dir)

    # save to files
    res_df.to_csv(figure_data_file, sep='\t', index=False)
    stats_df.to_csv(figure_stats_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # TIMESCAPE rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/timescape/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["pyclone_dir=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--pyclone_dir"):
            pyclone_dir = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)

    main()


