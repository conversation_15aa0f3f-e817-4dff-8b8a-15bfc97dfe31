# <PERSON>i

import sys, getopt, time, os, glob, subprocess

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import pyranges as pr


help_message = '''
Failed
'''


def calculate_rolling_average(coverage_files, window_size = 10, max_coverage = 3):
    res_df = []

    # classify samples based on first "-" delimited string
    class_df = pd.DataFrame({'sample': [x.split('/')[-2] for x in coverage_files]})
    class_df['file'] = coverage_files

    # for each file, processing rolling average
    for sample in class_df['sample'].str.split("-").str[0].unique():
        # find files
        files = class_df.loc[class_df['sample'].str.contains(sample), 'file']
        primary_file = files.to_numpy()[files.str.contains('primary')]
        recurrent_file = files.to_numpy()[files.str.contains('recurrent')]
        normal_file = files.to_numpy()[files.str.contains('blood')]


        # load dfs
        primary_df = pd.read_csv(primary_file[0], sep='\t', compression='gzip', header=None)
        primary_df.columns = ['chromosome', 'start', 'stop', 'mean_coverage']

        recurrent_df = pd.read_csv(recurrent_file[0], sep='\t', compression='gzip', header=None)
        recurrent_df.columns = ['chromosome', 'start', 'stop', 'mean_coverage']

        normal_df = pd.read_csv(normal_file[0], sep='\t', compression='gzip', header=None)
        normal_df.columns = ['chromosome', 'start', 'stop', 'mean_coverage']

        # calculate relative coverage, log2
        primary_df['mean_coverage'] = np.log2((primary_df['mean_coverage'].to_numpy() + 1) / (normal_df['mean_coverage'].to_numpy() + 1))
        recurrent_df['mean_coverage'] = np.log2((recurrent_df['mean_coverage'].to_numpy() + 1) / (normal_df['mean_coverage'].to_numpy() + 1))
        
        # calculate rolling averages
        primary_df['rolling_average'] = primary_df['mean_coverage'].rolling(window=window_size, min_periods=1).mean()
        recurrent_df['rolling_average'] = recurrent_df['mean_coverage'].rolling(window=window_size, min_periods=1).mean()

        # set max coverage
        primary_df.loc[primary_df['rolling_average'] > max_coverage, 'rolling_average'] = max_coverage
        recurrent_df.loc[recurrent_df['rolling_average'] > max_coverage, 'rolling_average'] = max_coverage
        
        # add sample names
        primary_df['sample_number'] = sample
        recurrent_df['sample_number'] = sample

        # add type
        primary_df['type'] = 'primary_wgs_seq'
        recurrent_df['type'] = 'recurrent_wgs_seq'

        # add to list
        res_df.extend([primary_df, recurrent_df])

    return pd.concat(res_df)


def calculate_average_coverage(df, interval_size = 200000):
    # Define the original column order
    original_columns = df.columns.copy()

    # Calculate new 'start' and 'stop' values
    df['start'] = df['start'] // interval_size * interval_size
    df['stop'] = ((df['stop'] - 1) // interval_size + 1) * interval_size

    # Group by chromosome, start, and stop, then calculate the mean coverage
    result = df.groupby(['chromosome', 'start', 'stop', 'sample_number', 'type']).agg({'mean_coverage': 'mean', 'rolling_average': 'mean'}).reset_index()

    # Reorder the columns
    result = result[original_columns]

    return result


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # process coverage files
    coverage_files = glob.glob(f'{process_dir}/*/coverage.regions.bed.gz')
    coverage_df = calculate_rolling_average(coverage_files)

    # calculate average over 200,000 bp window
    coverage_df = calculate_average_coverage(coverage_df)

    # load data
    cna_df = pd.read_csv(consensus_cna_file, sep='\t')

    # save to files
    cna_df.to_csv(figure_stats_file, sep='\t', index=False)
    coverage_df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_stats_file', figure_stats_file, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"
    

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["consensus_cna_file=", "process_dir=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--consensus_cna_file"):
            consensus_cna_file = str(arg)
        if opt in ("--process_dir"):
            process_dir = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()




