# Alec <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import gzip

help_message = '''
Failed
'''


def transform_vcf(input_vcf):
    # Open the VCF file and skip the metadata lines, then read the rest with pandas
    with gzip.open(input_vcf, 'rt') as file:
        header = []
        lines = []
        for line in file:
            if line.startswith('##'):
                header.append(line)
            else:
                lines.append(line)

    # Convert the lines to a dataframe
    vcf_data = pd.DataFrame([l.strip().split('\t') for l in lines[1:]], columns=lines[0].strip().split('\t'))


    # empty results df
    res_df = []

    # define the list of columns
    cols_oi = vcf_data.columns[:9]
    my_list = vcf_data.columns[9:]

    # define the sample columns to keep
    main_col1 = vcf_data.columns[9]
    main_col2 = vcf_data.columns[10]

    # iterate through column pairs
    for i in range(0, len(my_list), 2):
        # column pairs
        col1 = my_list[i]
        col2 = my_list[i + 1]

        # rows with information
        mask = vcf_data[col1].str.contains(",")

        # subset to these columns
        cols = list(cols_oi.copy())
        cols.extend([col1, col2])

        # subset to columns and mask, rename columns
        tmp_df = vcf_data.loc[mask,cols].copy()
        tmp_df = tmp_df.rename(columns = {col1:main_col1, col2:main_col2})

        res_df.append(tmp_df)

    return header, pd.concat(res_df)


def write_vcf(header, df, output_vcf):
    # write to file
    with open(output_vcf, 'w') as file:
        # write header
        for line in header:
            file.write(line)
        # write colnames
        file.write('\t'.join(df.columns) + "\n")
        
        # write df
        for index, row in df.iterrows():
            # Convert the row to a string and write it to the file
            line = '\t'.join(map(str, row.values))
            file.write(line + '\n')


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # process the VCF file
    header, df = transform_vcf(input_vcf)

    # write to file
    write_vcf(header, df, output_vcf)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["input_vcf=", "output_vcf="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--input_vcf"):
            input_vcf = str(arg)

        if opt in ("--output_vcf"):
            output_vcf = str(arg)
            
    main()




