# <PERSON>

import sys, getopt, time, os, glob, subprocess

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def process_df(sigprofiler_assignment_file):
    # load df
    df = pd.read_csv(sigprofiler_assignment_file, sep='\t')
    df.index = df['Samples'].to_numpy()
    df = df.iloc[:,1:]

    # Calculate the sum of each row (excluding the 'Samples' column)
    row_sums = df.sum(axis=1)
    
    # Divide each value in the DataFrame (excluding the 'Samples' column) by the sum of its row
    fraction_df = df.div(row_sums, axis=0)
    fraction_df['Samples'] = df.index.to_numpy()
    
    # Melt the DataFrame to reshape it to have three columns: 'Samples', 'SBS signature', and 'Fraction'
    melted_df = pd.melt(fraction_df, id_vars=['Samples'], var_name='SBS_signature', value_name='Fraction')



    # add sample number
    melted_df['sample_number'] = melted_df['Samples'].str.split("_").str[0]

    # remove fractions of less than 0.01
    melted_df = melted_df[melted_df['Fraction'] > 0]
    
    return melted_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load dfs, process, and melt
    df = process_df(sigprofiler_assignment_file)

    # save to files
    df.to_csv(figure_stats_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["sigprofiler_assignment_file=", "r_script=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--sigprofiler_assignment_file"):
            sigprofiler_assignment_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()




