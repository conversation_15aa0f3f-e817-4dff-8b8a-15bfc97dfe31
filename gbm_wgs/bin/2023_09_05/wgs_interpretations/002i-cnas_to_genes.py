# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

import pybedtools

help_message = '''
Failed
'''


def find_intersections(cna_file, gene_file):
    # Load the CNA regions BED file into a DataFrame for easy processing
    cna_df = pd.read_csv(cna_file, sep='\t')

    # Load the genes BED file
    genes_bed = pybedtools.BedTool(gene_file)

    # Initialize a list to store the results
    results = []

    # Process each sample and its CNA classifications
    for sample in cna_df['sample'].unique():
        # Filter the DataFrame for the current sample
        sample_df = cna_df[cna_df['sample'] == sample]
        
        # Convert the sample's DataFrame back to a BedTool object for intersection
        sample_bed = pybedtools.BedTool.from_dataframe(sample_df[['chr', 'start', 'stop', 'sample', 'cna_classification', 'type', 'sample_number', 'length']])

        # Find intersections with genes, retaining information from both
        intersection = sample_bed.intersect(genes_bed, wa=True, wb=True)

        # Iterate over each line in the intersection BedTool object
        for feature in intersection:
            # Each 'feature' is a line from the intersection result, split by tabs
            results.append(str(feature).strip("\n").split('\t'))

    # create combined df
    new_cols = ['chr','start','stop','sample', 'cna_classification', 'type', 'sample_number', 'length']
    new_cols.extend(["chr_gene", "start_gene", "stop_gene", "gene_id", "unknown", "strand", "source", "type", "unknown_2", "details"])
    results = pd.DataFrame(results, columns = new_cols)

    # Define a regular expression pattern to match 'gene_name=' part
    pattern = r'gene_name=([^;]+)'
    
    # Extract 'gene_name=' part using regular expression
    extracted = results['details'].str.extract(pattern, expand=False)
    results['gene'] = extracted

    # drop "normal" regions
    results = results[results['cna_classification'] != "normal"]
    
    return results




def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # find intersections
    cna_genes_df = find_intersections(cna_combined_file, ensembl_gene_bed_file)

    # save to file
    cna_genes_df.to_csv(gene_alterations_by_cna_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["cna_combined_file=", "ensembl_gene_bed_file=", "gene_alterations_by_cna_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--cna_combined_file"):
            cna_combined_file = str(arg)
        if opt in ("--ensembl_gene_bed_file"):
            ensembl_gene_bed_file = str(arg)

        if opt in ("--gene_alterations_by_cna_file"):
            gene_alterations_by_cna_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)
            
    main()




