library(optparse)
library(gplots)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}


# function to sort columns
sort_matrix_columns <- function(mat) {
  # Extract the column names
  col_names <- colnames(mat)
  
  # Separate column names into primary and recurrent
  primary_cols <- col_names[grep("\\.primary", col_names)]
  recurrent_cols <- col_names[grep("\\.recurrent", col_names)]
  
  # Extract the numeric part of the column names
  nums <- unique(as.numeric(gsub("[^0-9]", "", primary_cols)))
  
  # Sort the numeric part
  sorted_nums <- sort(nums)
  
  # Initialize a vector to store sorted column names
  sorted_cols <- character(0)
  
  # Pair primary and recurrent samples for each number
  for (num in sorted_nums) {
    primary_col <- paste0("RLGS", num, ".primary")
    recurrent_col <- paste0("RLGS", num, ".recurrent")
    
    # Append primary and recurrent columns to sorted_cols
    sorted_cols <- c(sorted_cols, primary_col, recurrent_col)
  }
  
  # Filter out columns that do not exist in the matrix
  sorted_cols <- sorted_cols[sorted_cols %in% col_names]
  
  # Reorder the matrix columns
  sorted_mat <- mat[, sorted_cols]
  
  return(sorted_mat)
}





heat_plot = function(df){

# colors
myCol <- colorRampPalette(c('dodgerblue', 'white', 'firebrick'))(50)

df_mat = as.matrix(df)

# add direction 
site_types_cols = rownames(df)

# define distance metric
hr = hclust(dist(df_mat, method = 'euclidean'), method="complete") 
hr2 = hclust(dist(t(df_mat), method = 'euclidean'), method="complete") 

heatmap.2(as.matrix(df), trace = "none", 
Colv = as.dendrogram(hr2),
Rowv = as.dendrogram(hr),
col = myCol,
key=TRUE,
main = "Complete euclidean distance", 
dendrogram = "both",
cexCol=0.6,
cexRow=1,
notecol = "black")

return()
}



heat_plot_unsorted = function(df){

# sort columns
df = sort_matrix_columns(df)

print(head(df))

# Colors
myCol <- colorRampPalette(c('dodgerblue', 'white', 'firebrick'))(50)

df_mat <- as.matrix(df)

# Define distance metric
hr <- hclust(dist(df_mat, method = 'euclidean'), method = "complete")

heatmap.2(as.matrix(df), trace = "none", 
        Rowv = as.dendrogram(hr),
        Colv = NULL,  # Do not reorder columns
        col = myCol,
        key = TRUE,
        main = "Complete euclidean distance", 
        dendrogram = "row",
        cexCol = 0.6,
        cexRow = 1,
        notecol = "black")

return()
}






pdf(opt$figure_file)

# load df
df = read.csv(opt$figure_stats_file, sep='\t')

# set rownames
rownames(df) = df$chr

# remove first 3 columns containing description information
df = df[,3:ncol(df) - 1]

# create plots
heat_plot(df)
heat_plot_unsorted(df)

dev.off()


print(opt$figure_file)






