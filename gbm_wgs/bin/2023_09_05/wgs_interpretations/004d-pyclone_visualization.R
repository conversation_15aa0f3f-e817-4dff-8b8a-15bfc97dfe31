# Alec <PERSON>
library(optparse)
library(timescape)
library(gridExtra)
library(ggplot2)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),            
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}





timescape_plot <- function(sample, data, edges) {

# subset to sample
sub_data = data[data$sample == sample,]
sub_edges = edges[edges$sample == sample,]

# drop samples column
sub_data$sample = NULL
sub_edges$sample = NULL

    
plot = timescape(sub_data, sub_edges)


# timescape(clonal_prev = data,
#           tree_edges = tree_edges,
#           mutations = "NA", 
#           clone_colours = "NA",
#           xaxis_title = "Time Point", 
#           yaxis_title = "Clonal Prevalence",
#           phylogeny_title = "Clonal Phylogeny", 
#           alpha = 50,
#           genotype_position = "stack", 
#           perturbations = "NA", 
#           sort = FALSE,
#           show_warnings = TRUE, 
#           width = 900, 
#           height = NULL)
    
return(plot)
}




pdf(opt$figure_file)
# pdf(opt$figure_file, height = 4 * length(unique(input_df$sample)))


# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')
edges_df = read.csv(opt$figure_stats_file, sep='\t')

# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# Extract and sort by numeric parts
sorted_vector <- unique(input_df$sample)[order(sapply(unique(input_df$sample), extract_numeric))]

# order samples
input_df$sample = factor(input_df$sample, levels = sorted_vector)


# create plots
lapply(levels(input_df$sample), timescape_plot, input_df, edges_df)


# plot_list = lapply(levels(input_df$sample), timescape_plot, input_df, edges_df)
# do.call(grid.arrange, c(lapply(plot_list, ggplotGrob), ncol = 1))  # Adjust the 'ncol' as needed


dev.off()


print(opt$figure_file)






