# <PERSON>i

import sys, getopt, time, os, glob, subprocess

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def open_and_combine_dfs(snv_file, cna_combined_file):
    # Open the MAF file
    maf_df = pd.read_csv(snv_file, sep='\t')

    # subset to columns of interest
    maf_df = maf_df[['Hugo_Symbol', 'Variant_Classification', 'sample', 'type', 'sample_number', 'mutation_type', 'Start_Position']]
    maf_df = maf_df.rename(columns = {'Start_Position':'start'})
    
    # Open the CNA file
    cna_df = pd.read_csv(cna_combined_file, sep='\t')
    cna_df['sample'] = cna_df['sample'].str.split("_").str[0]
    
    # subset to columns of interest and rename columns
    cna_df = cna_df[['gene', 'cna_classification', 'sample', 'type', 'sample_number', 'cna_classification', 'start']]
    cna_df.columns = ['Hugo_Symbol', 'Variant_Classification', 'sample', 'type', 'sample_number', 'mutation_type', 'start']

    # Combine the two dataframes
    mut_df = pd.concat([maf_df, cna_df])

    mut_df['sample'] = mut_df['sample'].str.replace("-", ".")

    return mut_df


def add_empty_genes(mut_df, genes_oi):
    # for each gene, add a row for a new sample
    for gene in genes_oi:
        tmp_df = pd.DataFrame({'Hugo_Symbol': [gene], 'sample': ['null13-sample'], 'mutation_type': ['No Mutation']})
        mut_df = pd.concat([mut_df, tmp_df])

    # make sure each sample is represented as well
    for sample in mut_df['sample'].unique():
        tmp_df = pd.DataFrame({'Hugo_Symbol': ['null_gene'], 'sample': [sample], 'mutation_type': ['No Mutation']})
        mut_df = pd.concat([mut_df, tmp_df])

    return mut_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load dfs and combine
    mut_df = open_and_combine_dfs(snv_file, cna_combined_file)

    # add empty genes
    mut_df = add_empty_genes(mut_df, genes_oi)

    print(mut_df.shape)
    
    # save to files
    mut_df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    genes_oi = ['TP53', 'IDH1', 'PTEN', 'ATRX', 'EGFR', 'NF1', 'TERT', 'PIK3CA', 'RB1', 'CIC', 'FUBP1', 'CDKN2A', 'EGFR', 'PDGFRA', 'CDK4', 'MET', 'MDM2', 'MDM4', 'CCND2', 'MYCN', 'CDK6', 'ATRX']

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["snv_file=", "cna_combined_file=", "r_script=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--snv_file"):
            snv_file = str(arg)
        if opt in ("--cna_combined_file"):
            cna_combined_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()




