# Alec <PERSON>
library(optparse)
library(forcats)
library(ggplot2)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}


barplot = function(sample, df){

# subset to sample
df = df[df$sample == sample,]

# Separate data frames for gain and loss CNAs
df_gain <- subset(df, cna_classification == "gain")
df_loss <- subset(df, cna_classification == "loss")

# Plot
# ggplot(df, aes(x = chr, y = length, fill = software)) + plot_theme() +
# geom_bar(stat = "identity", position = "dodge") +

ggplot() + plot_theme() +
geom_bar(data = df_gain, aes(x = chr, y = length, fill = software), stat = "identity", position = "dodge") +
geom_bar(data = df_loss, aes(x = chr, y = -length, fill = software), stat = "identity", position = "dodge") +

labs(x = "Chromosome", y = "Cumulative length of CNAs (bp)", fill = "Software") +
ggtitle(sample)
# scale_y_log10() 

# theme(axis.text.x = element_text(angle = 45, hjust = 1))
}






pdf(opt$figure_file, width = 12, height = 6)

# load df
df = read.csv(opt$figure_stats_file, sep='\t')


# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# sort samples
sorted_vector <- unique(df$sample)[order(sapply(unique(df$sample), extract_numeric))]
df$sample = factor(df$sample, levels = sorted_vector)

# sort chromosomes
sorted_vector <- unique(df$chr)[order(sapply(unique(df$chr), extract_numeric))]
df$chr = factor(df$chr, levels = sorted_vector)

# df$length = log10(df$length)

# create plot
lapply(levels(df$sample), barplot, df)

dev.off()

print(opt$figure_file)






