# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



cancer_file = '/.mounts/labs/reimandlab/private/users/abahcheli/cancer_ref/cgc_v98_29062023.tsv'


# pdf(opt$figure_file, height = 10, width = 12)

# read in the data
mutation_df = read.csv(opt$figure_stats_file, sep='\t')

# read in the cancer df
cancer_df = read.csv(cancer_file, sep='\t')

# subset mutation_df to get only the cancer genes
cancer_mutation_df = mutation_df[mutation_df$gene %in% cancer_df$GENE_SYMBOL,]



dev.off()


print(opt$figure_file)



