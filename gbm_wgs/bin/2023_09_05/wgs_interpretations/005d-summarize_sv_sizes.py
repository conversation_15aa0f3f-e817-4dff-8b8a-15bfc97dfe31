# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import re, subprocess, glob
from concurrent.futures import ProcessPoolExecutor


help_message = '''
Failed
'''


def process_summary_df(sv_summary_file):
    df = pd.read_csv(sv_summary_file, sep="\t")

    # subset to inversions and duplications
    df = df[df['type'].str.contains("inversion|duplication")]

    # make sure events are same chromosome
    mask = df['breakpoint1'].str.split(":").str[0] == df['breakpoint2'].str.split(":").str[0]
    df = df[mask]

    # calculate size of events
    df['size'] = df['breakpoint2'].str.split(":").str[1].astype(int) - df['breakpoint1'].str.split(":").str[1].astype(int)

    return df



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process summary file
    res_df = process_summary_df(sv_summary_file)

    # save to file
    res_df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["sv_summary_file=", "r_script=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--sv_summary_file"):
            sv_summary_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)

    main()


