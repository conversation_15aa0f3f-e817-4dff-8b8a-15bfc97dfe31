# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from SigProfilerAssignment import Ana<PERSON><PERSON> as Analyze

help_message = '''
Failed
'''


def sigprofiler_extractor(input_dir, output_dir, reference_genome = "GRCh38"):
    # assign signatures
    Analyze.cosmic_fit(input_dir, output_dir, input_type="vcf", genome_build=reference_genome, exome=False)
    
    print("Done extracting signatures")



def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # run sigprofiler
    sigprofiler_extractor(input_dir, sigprofiler_res_dir)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["input_dir=", "sigprofiler_res_dir="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--input_dir"):
            input_dir = str(arg)

        if opt in ("--sigprofiler_res_dir"):
            sigprofiler_res_dir = str(arg)

            
    main()




