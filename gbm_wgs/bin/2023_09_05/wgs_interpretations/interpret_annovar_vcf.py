# Alec Ba<PERSON>cheli

import sys, getopt, time, os, subprocess

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''

def process_vcf(annotated_vcf):
    # subset columns based on names
    colnames = [col for col in annotated_vcf.columns if "Otherinfo" not in col]
    colnames.extend(['CHROM', 'POS', 'ID', 'REF', 'ALT', 'QUAL', 'FILTER', 'INFO', 'FORMAT'])

    # subset to length of colnames
    annotated_vcf = annotated_vcf.iloc[:, :len(colnames)]



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load annotated dfs
    annotated_vcf = pd.read_csv(annotated_vcf_file, sep="\t")

    # process vcf



    # create figures
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]


    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # r environment
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["annotated_vcf_file=", "gbm_genes_file=", "r_script=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--annotated_vcf_file"):
            annotated_vcf_file = str(arg)
        if opt in ("--gbm_genes_file"):
            gbm_genes_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()




