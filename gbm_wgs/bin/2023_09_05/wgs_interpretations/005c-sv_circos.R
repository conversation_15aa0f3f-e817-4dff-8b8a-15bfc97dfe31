# <PERSON>
library(optparse)
library(circlize)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



process_df = function(input_df){

# Split each element of the vector using regular expressions
split_vector <- lapply(input_df$breakpoint1, function(x) strsplit(x, "[:]"))

# Extract chromosome number and position for each element
# input_df$chr1 <- sapply(split_vector, function(x) gsub("chr", "", x[[1]][1]))
input_df$chr1 <- sapply(split_vector, function(x) x[[1]][1])
input_df$start1 <- as.numeric(sapply(split_vector, function(x) x[[1]][2]))
input_df$end1 = as.numeric(input_df$start1)

# Split each element of the vector using regular expressions
split_vector <- lapply(input_df$breakpoint2, function(x) strsplit(x, "[:]"))

# Extract chromosome number and position for each element
# input_df$chr2 <- sapply(split_vector, function(x) gsub("chr", "", x[[1]][1]))
input_df$chr2 <- sapply(split_vector, function(x) x[[1]][1])
input_df$start2 <- as.numeric(sapply(split_vector, function(x) x[[1]][2]))
input_df$end2 = as.numeric(input_df$start2)

return(input_df)

}




circos_plot = function(sample, input_df){

# subset to sample
input_df = input_df[input_df$sample == sample,]

# process the input_df
input_df = process_df(input_df)


# colors
color_palette <- rainbow(length(unique(input_df$type)))
translocation_colors <- setNames(color_palette, unique(input_df$type))



circos.clear()

circos.par("start.degree" = 90)

# Initialize the circos plot
circos.initializeWithIdeogram(species = "hg38")


# Add multiple input_df function
addMultipleBreakpoints <- function(fusions) {
  for(i in 1:nrow(fusions)) {
    circos.link(fusions$chr1[i], c(fusions$start1[i], fusions$start1[i]),
                fusions$chr2[i], c(fusions$start2[i], fusions$start2[i]),
                col = translocation_colors[fusions$type[i]]) # Use different colors for each fusion
  }
}

# Add multiple input_df to the plot
addMultipleBreakpoints(input_df)

legend("topright", legend = names(translocation_colors), fill = translocation_colors, title = "Translocation Type", cex=0.5)

title(sample, cex = 1.5)

}




pdf(opt$figure_file)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')

input_df = input_df[grepl('RLGS12', input_df$sample),]

# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# Extract and sort by numeric parts
sorted_vector <- unique(input_df$sample)[order(sapply(unique(input_df$sample), extract_numeric))]

# order samples
input_df$sample = factor(input_df$sample, levels = sorted_vector)



# create circos plots
lapply(levels(input_df$sample), circos_plot, input_df)

dev.off()


print(opt$figure_file)



