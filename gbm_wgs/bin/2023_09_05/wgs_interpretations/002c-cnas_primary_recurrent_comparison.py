# <PERSON>cheli

import sys, getopt, time, os, glob, subprocess

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import pyranges as pr


help_message = '''
Failed
'''

# calculate pairwise intersections
def pairwise_intersections(pr_list):
    intersections = []

    n = len(pr_list)
    for i in range(n):
        for j in range(i+1, n):
            intersection = pr_list[i].intersect(pr_list[j])
            if not intersection.empty:
                intersections.append(intersection)

    return pr.concat(intersections)


def calculate_overlaps(input_df):
    # rename columns for pyranges
    input_df = input_df.rename(columns = {'chr':'Chromosome', 'start':'Start', 'stop':'End'})

    res_df = []

    # process by cna_type
    for cna_type in ['gain', 'loss']:
        tmp_df = input_df[input_df['cna_classification'] == cna_type]

        # process by sample
        for sample in tmp_df['sample'].unique():
            df = tmp_df[tmp_df['sample'] == sample]

            # separate by software
            varscan2 = pr.PyRanges(df[df['software'] == 'varscan2'])
            hmmcopy = pr.PyRanges(df[df['software'] == 'hmmcopy'])
            freec = pr.PyRanges(df[df['software'] == 'freec'])
            cnvkit = pr.PyRanges(df[df['software'] == 'cnvkit'])
            cnvnator = pr.PyRanges(df[df['software'] == 'cnvnator'])

            # calculate common intervals and merge
            res = pairwise_intersections([varscan2, hmmcopy, freec, cnvkit, cnvnator])
            res = res.merge().df

            # add descriptions
            res['sample'] = sample
            res['cna_classification'] = cna_type
            res['type'] = sample.split("-")[1]
            res['sample_number'] = sample.split("-")[0]

            res_df.append(res)

    # rename columns
    res_df = pd.concat(res_df)
    res_df = res_df.rename(columns = {'Chromosome':'chr', 'Start':'start', 'End':'stop'})
    
    return res_df


def calculate_stats(df): 
    # Calculate the length of each segment
    df["length"] = df["stop"].astype('int') - df["start"].astype('int') + 1

    # Group by sample, software, chromosome, and cna_classification to summarize total bases
    summary_df = df.groupby(["sample", "chr", "cna_classification"])["length"].sum().reset_index()

    summary_df['type'] = summary_df['sample'].str.split("-").str[1].str.split("_").str[0]
    summary_df['sample_number'] = summary_df['sample'].str.split("-").str[0]

    return summary_df


    

def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load df
    df = pd.read_csv(cna_file, sep='\t')

    # calculate overlaps
    df = calculate_overlaps(df)

    # calculate stats
    stats_df = calculate_stats(df)

    # save to files
    df.to_csv(figure_data_file, sep='\t', index=False)
    stats_df.to_csv(figure_stats_file, sep='\t', index=False)
    
    # run R script
    cline = [rscript, r_script, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"
    

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["cna_file=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--cna_file"):
            cna_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()




