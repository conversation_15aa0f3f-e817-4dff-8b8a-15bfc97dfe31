# <PERSON>i

import sys, getopt, time, os, glob, subprocess

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import pyranges as pr


help_message = '''
Failed
'''


def separate_cnas(df):
    # pivot to matrix
    df1 = df[df['cna_classification'] == 'gain']
    df1 = df.pivot_table(index='chr', columns='sample', values='length')
    df2 = df[df['cna_classification'] == 'loss']
    df2 = df.pivot_table(index='chr', columns='sample', values='length')

    # add descriptions
    df1['description'] = 'gain'
    df2['description'] = 'loss'

    # merge
    df = pd.concat([df1, df2])

    # reset index
    df = df.reset_index()

    return df


def calculate_diff_cnas(df):
    # pivot to matrix
    df1 = df[df['cna_classification'] == 'gain']
    df1 = df1.pivot_table(index='chr', columns='sample', values='length')
    df2 = df[df['cna_classification'] == 'loss']
    df2 = df2.pivot_table(index='chr', columns='sample', values='length')

    # subtract
    df = df1.fillna(0) - df2.fillna(0)

    # reset index
    df = df.reset_index()

    df.columns = df.columns.str.split("_").str[0]

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load df
    df = pd.read_csv(input_stats_file, sep='\t')

    # process df
    df = calculate_diff_cnas(df)
    
    # save to file
    df.to_csv(figure_stats_file, sep='\t', index=False)
    
    # run R script
    cline = [rscript, r_script, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"
    

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["input_stats_file=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--input_stats_file"):
            input_stats_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()




