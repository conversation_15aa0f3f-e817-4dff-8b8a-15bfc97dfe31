# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import gzip

help_message = '''
Failed
'''


def convert_gzipped_to_unicode(input_file, output_file):
    try:
        # Open the gzipped file in binary mode
        with gzip.open(input_file, 'rb') as f_in:
            # Read the binary data and decode it as utf-8
            data = f_in.read().decode('utf-8')
            # Write the decoded data to the output file in unicode format
            with open(output_file, 'w', encoding='latin-1') as f_out:
                f_out.write(data)
        print(f"Conversion successful. Output written to '{output_file}'.")
    except FileNotFoundError:
        print(f"Error: File '{input_file}' not found.")
    except Exception as e:
        print(f"An error occurred: {e}")


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # run sigprofiler
    convert_gzipped_to_unicode(input_vcf, output_vcf)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["input_vcf=", "output_vcf="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--input_vcf"):
            input_vcf = str(arg)

        if opt in ("--output_vcf"):
            output_vcf = str(arg)
            
    main()




