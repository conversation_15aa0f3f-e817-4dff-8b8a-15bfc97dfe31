# <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import vcf

help_message = '''
Failed
'''


def pyclone_preparation(pyclone_tsv, pyclone_yaml):
    pyclone_yaml_content = f"""analysis:
  mode: 'full'
  mutation_file: '{pyclone_tsv}'
"""

    # write the yaml file
    with open(pyclone_yaml, 'w') as file:
        file.write(pyclone_yaml_content)



def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # run sigprofiler
    pyclone_preparation(pyclone_tsv, pyclone_yaml)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["pyclone_tsv=", "pyclone_yaml="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--pyclone_tsv"):
            pyclone_tsv = str(arg)

        if opt in ("--pyclone_yaml"):
            pyclone_yaml = str(arg)
            
    main()




