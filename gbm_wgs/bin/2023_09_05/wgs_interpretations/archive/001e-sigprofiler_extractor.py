# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from SigProfilerExtractor import sigpro as sig

help_message = '''
Failed
'''


def sigprofiler_extractor(input_vcf, output_dir, threads):
    reference_genome = "GRCh38"  # Reference genome: can be "GRCh37", "GRCh38", etc.

    # Generate mutation matrices from input data (if starting from VCF/BED/Maf files)
    # from SigProfilerMatrixGenerator import install as genInstall
    # genInstall.install('GRCh37') # Download the reference genome data
    # from SigProfilerMatrixGenerator import sigProfilerMatrixGeneratorFunc as genFunc
    # genFunc.sigProfilerMatrixGeneratorFunc(project, reference_genome, input_matrix, plot=True, exome=exome)

    # Parameters for extraction
    project = input_vcf.split("/")[-2]
    exome = False  # Set True if analyzing exome data, False if genome data
    minimum_signatures = 1  # Minimum number of signatures to extract
    maximum_signatures = 10  # Maximum number of signatures to extract

    # Extract signatures
    # sig.sigProfilerExtractor("vcf", output_dir, input_vcf, reference_genome=reference_genome, project=project, exome=exome, minimum_signatures=minimum_signatures, maximum_signatures=maximum_signatures, cpu=-1)
    sig.sigProfilerExtractor("vcf", output_dir, input_vcf, reference_genome=reference_genome, exome=exome, minimum_signatures=minimum_signatures, maximum_signatures=maximum_signatures, cpu=threads)
    
    print("Done extracting signatures")



def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # run sigprofiler
    sigprofiler_extractor(input_vcf, sigprofiler_res_dir, threads)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["input_vcf=", "sigprofiler_res_dir=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--input_vcf"):
            input_vcf = str(arg)

        if opt in ("--sigprofiler_res_dir"):
            sigprofiler_res_dir = str(arg)

        if opt in ("--threads"):
            threads = int(arg)
            
    main()




