# <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import vcf

help_message = '''
Failed
'''


def get_cn_at_position(df, position):
    # Filter the DataFrame based on the position
    filtered_df = df[(df['start'] <= position) & (df['stop'] >= position)]

    # If there are multiple rows for the position, return the most common copy number
    if len(filtered_df) > 0:
        cn = filtered_df['cna_classification'].mode()[0]
        if cn == 'gain':
            return 3
        elif cn == 'loss':
            return 1
        else:
            return 2
    else:
        return 2


def pyclone_preparation(vcf_file, output_tsv, cna_file):
    # load cna file
    cna_df = pd.read_csv(cna_file, sep='\t')

    # subset to sample
    sample = vcf_file.split("/")[-2]
    cna_df = cna_df[cna_df['sample'] == sample]

    # Open the VCF file using PyVCF
    vcf_reader = vcf.Reader(open(vcf_file, 'r'))

    # Create an empty list to store the data
    data = []

    # Loop through each record in the VCF file
    for record in vcf_reader:
        # Extract the necessary information for PyClone
        chrom = record.CHROM
        pos = record.POS
        ref = record.REF
        alt = ','.join([str(a) for a in record.ALT])
        mutation_id = f"{chrom}:{pos}_{ref}>{alt}"
        
        # Assume the first sample is the tumor sample and the second is the normal sample
        # Adjust the indexing if your VCF is structured differently
        tumor_sample = record.samples[0]
        normal_sample = record.samples[1]
        
        # Extract coverage and allele frequency information
        ref_counts = tumor_sample['AD'][0]  # Reference allele depth
        var_counts = sum(tumor_sample['AD'][1:])  # Variant allele depths summed
        normal_counts = sum(normal_sample['AD'])  # Total depth in normal sample
        vaf = var_counts / (ref_counts + var_counts)  # Variant allele frequency
        
        # Append the information as a dictionary to our data list
        data.append({
            'mutation_id': mutation_id,
            'ref_counts': ref_counts,
            'var_counts': var_counts,
            'normal_counts': normal_counts,
            'vaf': vaf
        })

    # Convert the data list into a pandas DataFrame
    df = pd.DataFrame(data)

    # Save the DataFrame to a CSV file compatible with PyClone
    df.to_csv(output_tsv, index=False)


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # run sigprofiler
    pyclone_preparation(input_vcf, output_tsv, cna_file)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["input_vcf=", "cna_file=", "output_tsv="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--input_vcf"):
            input_vcf = str(arg)
        if opt in ("--cna_file"):
            cna_file = str(arg)

        if opt in ("--output_tsv"):
            output_tsv = str(arg)
            
    main()




