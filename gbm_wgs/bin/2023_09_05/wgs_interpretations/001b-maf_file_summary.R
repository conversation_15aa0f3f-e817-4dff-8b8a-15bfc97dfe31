# <PERSON>
library(optparse)
library(forcats)
library(ggplot2)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}


genes_oi = c('TP53', 'IDH1', 'PTEN', 'ATRX', 'EGFR', 'NF1', 'TERT', 'PIK3CA', 'RB1', 'CIC', 'FUBP1', 'CDKN2A', 'EGFR', 'PDGFRA', 'CDK4', 'MET', 'MDM2', 'MDM4', 'CCND2', 'MYCN', 'CDK6', 'ATRX')



plot_mutation_rate = function(stats_df) {

p = ggplot(data=stats_df, aes(x = sample_number, y = mutations_per_mb, fill = type)) + plot_theme() +
geom_point(pch=21, size=4, alpha=0.8) +

scale_y_log10() + 
geom_hline(yintercept = 10, linetype = "dashed", color = "grey") +  # Add horizontal line at y = 10

ggtitle('') + ylab('Mutation rate (mutations / Mb)') + xlab('')

print(p)

}



plot_oncoprint = function(df){

ggplot(df, aes(x = sample, y = Hugo_Symbol, fill = mutation_type)) + plot_theme() +
# ggplot(df, aes(x = sample, y = Hugo_Symbol, fill = Variant_Classification)) + plot_theme() +
geom_tile(alpha = 0.3) +

ggtitle('') + ylab('Gene') + xlab('')


}




pdf(opt$figure_file, height=6, width = 6)

# load df
stats_df = read.csv(opt$figure_stats_file, sep='\t')

# collect sample names
stats_df$sample_number = sapply(stats_df$sample, function(x) strsplit(x, "-")[[1]][1])

# sort factor by max
stats_df$sample_number = fct_reorder(stats_df$sample_number, stats_df$mutations_per_mb, .fun = max, .desc=TRUE)


# plot mutation rate
plot_mutation_rate(stats_df)



# load df
input_df = read.csv(opt$figure_data_file, sep='\t')

# subset to genes of interest
input_df = input_df[input_df$Hugo_Symbol %in% genes_oi,]



# # Create a table of counts for each sample
# sample_counts <- table(input_df$sample)

# # Order the unique samples based on their counts
# ordered_samples <- names(sort(sample_counts, decreasing = TRUE))

# # Reorder the factor levels based on the ordered samples
# input_df$sample <- factor(input_df$sample, levels = (ordered_samples))



# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# order samples
sorted_vector <- unique(input_df$sample)[order(sapply(unique(input_df$sample), extract_numeric))]
input_df$sample = factor(input_df$sample, levels = sorted_vector)


                                
# Create a table of counts for each sample
sample_counts <- table(input_df$Hugo_Symbol)

# Order the unique samples based on their counts
ordered_samples <- names(sort(sample_counts, decreasing = TRUE))

# Reorder the factor levels based on the ordered samples
input_df$Hugo_Symbol <- factor(input_df$Hugo_Symbol, levels = rev(ordered_samples))




# plot mutations for top GBM genes
plot_oncoprint(input_df)


dev.off()


print(opt$figure_file)



