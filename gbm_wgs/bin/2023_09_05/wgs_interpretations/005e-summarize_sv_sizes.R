# <PERSON>
library(optparse)
library(forcats)
library(ggplot2)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}





plot_histogram <- function(sample, df) {

print(sample)

# subset to sample
sub_df = df[df$sample == sample,]

print(dim(sub_df))

p = ggplot(sub_df, aes(x = size)) + plot_theme() +
geom_histogram(color = 'black') +

labs(x = "Size (bp, log10)", y = "Frequency", title = "Histogram of Sizes") +
ggtitle(sample)


print(p)

return()
}




pdf(opt$figure_file)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')


# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# Extract and sort by numeric parts
sorted_vector <- unique(input_df$sample)[order(sapply(unique(input_df$sample), extract_numeric))]

# order samples
input_df$sample = factor(input_df$sample, levels = sorted_vector)


# log10 sizes
input_df$size = log10(input_df$size)

# create plots
lapply(levels(input_df$sample), plot_histogram, input_df)

dev.off()


print(opt$figure_file)



