# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(data.table)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--enriched_pathway_gene_contributions_file"), type="character", default=NULL,
            help="",
            dest="enriched_pathway_gene_contributions_file"),
    make_option(c("-b","--gene_contributions_file"), type="character", default=NULL,
            help="",
            dest="gene_contributions_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



# Colors for fill and dot color
fill_colors = c("red", "blue", 'grey')
names(fill_colors) = c("positive", "negative", 'neutral')

dot_plot = function(pathway, genes_df, pathways_df){

  # Subset to pathway
  sub_gene_df = genes_df[genes_df$pathway == pathway,]
  sub_gene_df$gene = fct_reorder(factor(sub_gene_df$gene), sub_gene_df$plot_pval, .fun = sum, .desc = TRUE)

  sub_pathways_df = pathways_df[pathways_df$pathway == pathway,]

  # Add alpha based on significance
  sub_gene_df$alpha_value <- ifelse(sub_gene_df$significant == "True", 1, 0.6)

  # Create a vector for x-axis label colors based on cgc_gene column (red for "True", black for "False")
  x_axis_colors <- ifelse(sub_gene_df$cgc_gene == "True", "red", "black")

  # Plot
  p = ggplot(data = sub_gene_df, aes(x = gene, y = source, fill = fc_class, size = plot_pval, alpha = alpha_value)) + plot_theme() +
    geom_point(pch = 21, color = 'black') +
    
    # Add colors to reflect positive or negative FC
    scale_fill_manual(values = fill_colors) +
    scale_size_continuous(limits = c(0, pval_limit)) +
    scale_alpha_continuous(range = c(0.2, 1)) +  # Map alpha to the significance

    # Add title
    ggtitle(sub_pathways_df$pathway_and_pval) + 
    ylab('') + xlab('') +
    
    # Customize guides
    guides(
      fill = guide_legend(title = "FC (direction)"),
      size = guide_legend(title = "P-adjusted \n(-log10)"),
      colour = guide_legend(title = "Significant \n(padj < 0.05)"),
      alpha = guide_legend(title = "Alpha (Significance)")
    ) +
    
    # Customize x-axis labels color
    scale_x_discrete(labels = setNames(as.character(sub_gene_df$gene), sub_gene_df$gene)) + 
    theme(
      axis.text.x = element_text(color = x_axis_colors, hjust = 1)  # Color the x-axis labels based on the condition
    ) +
    
    theme(strip.placement = "outside", strip.background = element_blank(),
          legend.position = "right")

  print(p)

  return()
}



pval_limit = 5

pdf(opt$figure_file, width=18, height = 5)

# loads dfs
pathways_df = read.csv(opt$enriched_pathway_gene_contributions_file, sep='\t')
genes_df = read.csv(opt$gene_contributions_file, sep='\t')

# subset to defining genes and set fc limits
genes_df$plot_pval[genes_df$plot_pval > pval_limit] = pval_limit

# order factors
pathways_df$pathway = fct_reorder(pathways_df$pathway, pathways_df$adjusted_p_val, .desc=FALSE)

lapply(levels(pathways_df$pathway), dot_plot, genes_df, pathways_df)

dev.off()


print(opt$figure_file)




