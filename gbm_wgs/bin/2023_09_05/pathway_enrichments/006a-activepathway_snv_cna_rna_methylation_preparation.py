# Alec Ba<PERSON>cheli

import sys, getopt, time, os

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''

# set minimum rna-seq fc to 1.5x 
def load_and_combine_dfs(dgea_file, differential_methylation_file, differential_cna_file, differential_snv_file, differential_sv_file):
    # load dfs
    dgea_df = pd.read_csv(dgea_file, sep='\t')
    dm_df = pd.read_csv(differential_methylation_file, sep='\t')
    cna_df = pd.read_csv(differential_cna_file, sep='\t')
    snv_df = pd.read_csv(differential_snv_file, sep='\t')
    sv_df = pd.read_csv(differential_sv_file, sep='\t')

    # preprocess dgea data
    dgea_df = preprocess_dgea(dgea_df)

    # Merge all dataframes on 'gene'
    df = dgea_df.merge(dm_df, on='gene', how='outer') \
                .merge(cna_df, on='gene', how='outer') \
                .merge(snv_df, on='gene', how='outer') \
                .merge(sv_df, on='gene', how='outer')

    # Subset to genes in either cna_df or sv_df
    cna_genes = set(cna_df['gene'])
    sv_genes = set(sv_df['gene'])
    snv_genes = set(snv_df['gene'])
    rna_genes = set(dgea_df['gene'])
    dm_genes = set(dm_df['gene'])
    keep_genes = cna_genes | sv_genes | snv_genes | rna_genes | dm_genes # Union
    # keep_genes = cna_genes & sv_genes & snv_genes & rna_genes & dm_genes # intersect

    df = df[df['gene'].isin(keep_genes)].set_index('gene')

    # Extract and rename p-values
    pval_df = df[['p_value', 'dpm_pval', 'ttest_pvalue_x', 'pvalue', 'ttest_pvalue_y']].rename(columns={
        'p_value': 'rna',
        'dpm_pval': 'dm',
        'ttest_pvalue_x': 'cna',
        'pvalue': 'snv',
        'ttest_pvalue_y': 'sv'
    })

    # Extract and rename fold changes
    fc_df = df[['logFC', 'fc_x', 'foldchange_x', 'foldchange_y', 'fc_y']].rename(columns={
        'logFC': 'rna',
        'fc_x': 'dm',
        'foldchange_x': 'cna',
        'foldchange_y': 'snv',
        'fc_y': 'sv'
    })

    # Fill missing values
    pval_df.fillna(1, inplace=True)
    fc_df.fillna(0, inplace=True)

    return pval_df, fc_df


def preprocess_dgea(dgea_df, dgea_method = 'gene_counts-deseq2_paired', minimum_rna_fc = 1, pval_column = 'p_value'):
    # subset to dgea method
    dgea_df = dgea_df.loc[dgea_df['data_type_dgea'] == dgea_method, :]

    # set p-values below minimum to 1
    mask = dgea_df['logFC'].abs() < minimum_rna_fc
    dgea_df.loc[mask, pval_column] = 1

    # rename pval_colummn
    dgea_df = dgea_df.rename(columns={pval_column: 'p_value'})

    return dgea_df


# set minimum rna-seq fc to 1.5x 
def load_and_combine_dfs_v2(dgea_file, differential_methylation_file, differential_snv_file, differential_sv_file):
    # load dfs
    dgea_df = pd.read_csv(dgea_file, sep='\t')
    dm_df = pd.read_csv(differential_methylation_file, sep='\t')
    snv_df = pd.read_csv(differential_snv_file, sep='\t')
    sv_df = pd.read_csv(differential_sv_file, sep='\t')

    # preprocess dgea data
    dgea_df = preprocess_dgea(dgea_df)

    # Merge all dataframes on 'gene'
    df = dgea_df.merge(dm_df, on='gene', how='outer') \
                .merge(snv_df, on='gene', how='outer') \
                .merge(sv_df, on='gene', how='outer')

    df = df.set_index('gene')
    
    # Extract and rename p-values
    pval_df = df[['p_value', 'dpm_pval', 'pvalue_x', 'pvalue_y']].rename(columns={
        'p_value': 'rna',
        'dpm_pval': 'dm',
        'pvalue_x': 'snv',
        'pvalue_y': 'sv'
    })

    # Extract and rename fold changes
    fc_df = df[['logFC', 'fc_x', 'foldchange', 'fc_y']].rename(columns={
        'logFC': 'rna',
        'fc_x': 'dm',
        'foldchange': 'snv',
        'fc_y': 'sv'
    })

    # Fill missing values
    pval_df.fillna(1, inplace=True)
    fc_df.fillna(0, inplace=True)

    return pval_df, fc_df



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and combine dfs
    # pval_df, fc_df = load_and_combine_dfs(dgea_file, differential_methylation_file, differential_cna_file, differential_snv_file, differential_sv_file)
    pval_df, fc_df = load_and_combine_dfs_v2(dgea_file, differential_methylation_file, differential_snv_file, differential_sv_file)

    # save to files
    pval_df.to_csv(pvalue_file, sep='\t')
    fc_df.to_csv(fc_file, sep='\t')

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["dgea_file=", "differential_methylation_file=", "differential_sv_file=", "differential_cna_file=", "differential_snv_file=", "pvalue_file=", "fc_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--dgea_file"):
            dgea_file = str(arg)
        if opt in ("--differential_methylation_file"):
            differential_methylation_file = str(arg)
        if opt in ("--differential_sv_file"):
            differential_sv_file = str(arg)
        if opt in ("--differential_cna_file"):
            differential_cna_file = str(arg)
        if opt in ("--differential_snv_file"):
            differential_snv_file = str(arg)

        if opt in ("--pvalue_file"):
            pvalue_file = str(arg)
        if opt in ("--fc_file"):
            fc_file = str(arg)
            
    main()





