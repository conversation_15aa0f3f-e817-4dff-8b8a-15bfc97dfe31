# <PERSON>
library(optparse)
library(ActivePathways)

# options list for parser options
option_list <- list(
    make_option(c("-a","--pvalue_file"), type="character", default=NULL,
            help="",
            dest="pvalue_file"),
    make_option(c("-b","--fc_file"), type="character", default=NULL,
            help="",
            dest="fc_file"),
    make_option(c("-c","--comparing_pvalues_file"), type="character", default=NULL,
            help="",
            dest="comparing_pvalues_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)



# load p-value df
pval_df = read.csv(opt$pvalue_file, sep='\t', row.names=1)
scores = as.matrix(pval_df)
rownames(scores) = rownames(pval_df)

# load fold change df
fc_df = read.csv(opt$fc_file, sep='\t', row.names=1)
scores_dir = as.matrix(fc_df)
rownames(scores_dir) = rownames(fc_df)

# merge using DPM
dpm_pvalues = merge_p_values(scores, scores_direction = scores_dir, constraints_vector = c(-1, 1), method='DPM')

# merge using Browns
browns_pvalues = merge_p_values(scores, method='Brown')

# create dfs for the p-values
dpm_pvalues = data.frame(dpm_pvalues)
dpm_pvalues$gene = rownames(dpm_pvalues)

browns_pvalues = data.frame(browns_pvalues)
browns_pvalues$gene = rownames(browns_pvalues)

# combine the two dfs based on "gene" column
merged_pvalues = merge(dpm_pvalues, browns_pvalues, by="gene")

# write the merged p-values to a file
write.table(merged_pvalues, file=opt$comparing_pvalues_file, sep='\t', row.names=FALSE, quote=FALSE)

print("P-value merging Complete")




