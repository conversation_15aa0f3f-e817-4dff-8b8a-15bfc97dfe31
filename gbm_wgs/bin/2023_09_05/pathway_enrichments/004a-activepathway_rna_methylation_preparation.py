# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


# set minimum rna-seq fc to 1.5x 
def load_and_combine_dfs(dgea_file, differential_methylation_file, dgea_method = 'gene_counts-deseq2_paired', minimum_rna_fc = 1, pval_column = 'fdr'):
    # load dfs
    dgea_df = pd.read_csv(dgea_file, sep='\t')
    dm_df = pd.read_csv(differential_methylation_file, sep='\t')

    # subset to dgea method
    dgea_df = dgea_df.loc[dgea_df['data_type_dgea'] == dgea_method, :]

    # set p-values below minimum to 1
    mask = dgea_df['logFC'].abs() < minimum_rna_fc
    dgea_df.loc[mask, pval_column] = 1

    # set index to gene
    dgea_df.index = dgea_df['gene'].to_numpy()
    dm_df.index = dm_df['gene'].to_numpy()

    # subset to common genes
    common_genes = np.intersect1d(dgea_df.index, dm_df.index)
    dgea_df = dgea_df.loc[common_genes, :]
    dm_df = dm_df.loc[common_genes, :]

    # separate pval and fc dfs
    pval_df = dgea_df.loc[:, [pval_column]]
    fc_df = dgea_df.loc[:, ['logFC']]

    # rename columns
    pval_df = pval_df.rename(columns={pval_column: 'rna'})
    fc_df = fc_df.rename(columns={'logFC': 'rna'})

    # add dm columns
    pval_df['dm'] = dm_df['dpm_pval']
    fc_df['dm'] = dm_df['fc']

    # fill pval_df na with 1 and fc_df na with 0
    pval_df = pval_df.fillna(1)
    fc_df = fc_df.fillna(0)

    return pval_df, fc_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and combine dfs
    pval_df, fc_df = load_and_combine_dfs(dgea_file, differential_methylation_file)

    # save to files
    pval_df.to_csv(pvalue_file, sep='\t')
    fc_df.to_csv(fc_file, sep='\t')

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["dgea_file=", "differential_methylation_file=", "pvalue_file=", "fc_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--dgea_file"):
            dgea_file = str(arg)
        if opt in ("--differential_methylation_file"):
            differential_methylation_file = str(arg)

        if opt in ("--pvalue_file"):
            pvalue_file = str(arg)
        if opt in ("--fc_file"):
            fc_file = str(arg)
            
    main()





