# <PERSON>
library(optparse)
library(ActivePathways)

# options list for parser options
option_list <- list(
    make_option(c("-a","--pvalue_file"), type="character", default=NULL,
            help="",
            dest="pvalue_file"),
    make_option(c("-b","--fc_file"), type="character", default=NULL,
            help="",
            dest="fc_file"),
    make_option(c("-c","--gmt_file"), type="character", default=NULL,
            help="",
            dest="gmt_file"),
    make_option(c("-d","--cytoscape_file_prefix"), type="character", default=NULL,
            help="",
            dest="cytoscape_file_prefix"),
    make_option(c("-e","--enriched_pathways_file"), type="character", default=NULL,
            help="",
            dest="enriched_pathways_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)



# gmt file limitations
max_genes = 750
min_genes = 50



# load p-value df
pval_df = read.csv(opt$pvalue_file, sep='\t', row.names=1)
scores = as.matrix(pval_df)
rownames(scores) = rownames(pval_df)

# load fold change df
fc_df = read.csv(opt$fc_file, sep='\t', row.names=1)
scores_dir = as.matrix(fc_df)
rownames(scores_dir) = rownames(fc_df)

# run ActivePathways
pos_enriched_pathways = ActivePathways(scores, scores_direction = scores_dir, constraints_vector = c(-1, 1, -1), opt$gmt_file, cytoscape_file_tag=opt$cytoscape_file_prefix, geneset_filter=c(min_genes, max_genes), merge_method='DPM', significant = 0.1)


# export enriched pathways
export_as_CSV(pos_enriched_pathways, opt$enriched_pathways_file)

print("ActivePathways Complete")




