# Alec Ba<PERSON>cheli

import sys, getopt, time, os

import pandas as pd 
import numpy as np

import statsmodels.stats.multitest as multitest

help_message = '''
Failed
'''


def round_to_significant_figures(number, significant_figures):
    if number == 0:
        return 0.0
    return round(number, significant_figures - int(np.floor(np.log10(abs(number)))) - 1)


def process_df_for_dotplots(pval_df, fc_df, max_p = 10, sig_p = 0.05, pval_col = 'padj', fc_col = 'logFC'):
    # fdr correct each column of the pval_df
    pval_df = pval_df.apply(lambda x: multitest.fdrcorrection(x)[1])
    
    # melt dfs
    pval_df = pval_df.melt(var_name='source', value_name='padj', ignore_index=False)
    fc_df = fc_df.melt(var_name='source', value_name='logFC', ignore_index=False)

    # add gene columns
    pval_df.reset_index(inplace=True)
    fc_df.reset_index(inplace=True)
    
    # merge dfs
    df = pd.merge(pval_df, fc_df, on=['gene', 'source'])
    
    # add max p-value and significance for figure
    df.loc[:,'plot_pval'] = -np.log10(df[pval_col])
    df.loc[df['plot_pval'] > max_p,'plot_pval'] = max_p

    df.loc[:,'significant'] = df[pval_col] < sig_p


    # classify fc as positive or negative based on sign
    df['fc_class'] = 'neutral'
    df.loc[df[fc_col] > 0, 'fc_class'] = 'positive'
    df.loc[df[fc_col] < 0, 'fc_class'] = 'negative'

    # reorder for plotting
    df = df.sort_values(['plot_pval'], ascending = [False])

    return df



def summarize_pathway_contributions(contributions_df, pathways_df, genes_arr, sig_figs=4):
    # define column of interest
    cols_oi = list(contributions_df.columns[contributions_df.columns.str.contains('Genes')])
    cols_oi.append('overlap')

    # create a new column that combines all the cols_ois into a list
    contributions_df['overlap'] = contributions_df.apply(lambda row: sum([str(row[col]).split('|') for col in cols_oi], []), axis=1)

    # remove nan values from each cell
    contributions_df['overlap'] = contributions_df['overlap'].apply(lambda x: [element for element in x if str(element) != 'nan'])


    # subset to columns of interest and rename
    contributions_df = contributions_df.loc[:,['term_name', 'overlap', 'evidence']]
    contributions_df = contributions_df.rename(columns={'term_name': 'pathway', 'overlap': 'genes'})
    
    # add p-value to pathway
    contributions_df.loc[:,'adjusted_p_val'] = list(map(lambda x: round_to_significant_figures(x, sig_figs), pathways_df['adjusted_p_val'].to_numpy()))

    contributions_df.loc[:,'pathway_and_pval'] = contributions_df['pathway'] + ", " + contributions_df['evidence'].str.replace('|', '/') + ", P=" + contributions_df['adjusted_p_val'].astype('str')

    return contributions_df


def prepare_figure_gene_file(gene_df, contributions_df):
    res_df = []

    for pathway in contributions_df['pathway']:
        # get genes in pathway
        genes_in_pathway = contributions_df.loc[contributions_df['pathway'] == pathway,'genes'].to_numpy()[0]

        # if there are genes from this contribution
        if len(genes_in_pathway) > 0:
            # get gene information
            gene_info_df = gene_df.loc[np.isin(gene_df['gene'], genes_in_pathway),:].copy()
            
            # add source and pathway information
            gene_info_df['pathway'] = pathway

            # add to res_df
            res_df.append(gene_info_df)

    # concatenate all the dataframes
    res_df = pd.concat(res_df, ignore_index=True)

    # remove p-values that are equal to 1
    res_df = res_df[res_df['padj'] != 1]

    return res_df


def annotate_cgc_genes(gene_contributions_df, cgc_file):
    # load cgc file
    cgc_df = pd.read_csv(cgc_file, sep='\t')

    # genes in cgc file should be called True in cgc_gene
    gene_contributions_df['cgc_gene'] = gene_contributions_df['gene'].isin(cgc_df['GENE_SYMBOL'])

    return gene_contributions_df


def generate_figure_data(pvalue_file, fc_file, enriched_pathways_file, pathways_text_file, cgc_file):
    # load dfs
    pval_df = pd.read_csv(pvalue_file, sep='\t', index_col=0)
    fc_df = pd.read_csv(fc_file, sep='\t', index_col=0)
    
    contributions_df = pd.read_csv(enriched_pathways_file)
    pathways_df = pd.read_csv(pathways_text_file, sep='\t')

    # process df for visualization
    input_df = process_df_for_dotplots(pval_df, fc_df)
    
    # process to get contributions from AP
    pathways_detailed_df = summarize_pathway_contributions(contributions_df, pathways_df, input_df['gene'].unique())

    # generate gene contributions
    gene_contributions_df = prepare_figure_gene_file(input_df, pathways_detailed_df)

    # drop genes from pathways_detailed_df
    pathways_detailed_df = pathways_detailed_df.drop(columns=['genes'])

    # annotate cgc genes
    gene_contributions_df = annotate_cgc_genes(gene_contributions_df, cgc_file)

    return gene_contributions_df, pathways_detailed_df



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process dfs for figure
    gene_contributions_df, pathways_detailed_df = generate_figure_data(pvalue_file, fc_file, enriched_pathways_file, pathways_text_file, cgc_file)

    # save to files
    pathways_detailed_df.to_csv(enriched_pathway_gene_contributions_file, sep='\t', index=False)
    gene_contributions_df.to_csv(gene_contributions_file, sep='\t', index=False)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["pvalue_file=", "fc_file=", "enriched_pathways_file=", "pathways_text_file=", "cgc_file=", "enriched_pathway_gene_contributions_file=", "gene_contributions_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--pvalue_file"):
            pvalue_file = str(arg)
        if opt in ("--fc_file"):
            fc_file = str(arg)

        if opt in ("--enriched_pathways_file"):
            enriched_pathways_file = str(arg)
        if opt in ("--pathways_text_file"):
            pathways_text_file = str(arg)

        if opt in ("--cgc_file"):
            cgc_file = str(arg)

        if opt in ("--enriched_pathway_gene_contributions_file"):
            enriched_pathway_gene_contributions_file = str(arg)
        if opt in ("--gene_contributions_file"):
            gene_contributions_file = str(arg)

            
    main()




