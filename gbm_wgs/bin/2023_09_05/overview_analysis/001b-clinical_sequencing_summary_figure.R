library(optparse)
library(forcats)
library(ggplot2)
library(dplyr)
library(grid)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



# Create a named vector for colors
color_vals <- c(
    "Astrocytic Tumour" = "#B73A20",  # Red
    "Yes" = "#2BC212",                # Green
    "nan" = "#EEEEEE",                # Grey
    "IV" = "#0D3EAA",                 # dark blue
    "M" = "#50BEDF",                  # Blue
    "F" = "#DF50DD",                  # Pink
    "< 56" = "#DFCC50",               # yellow
    "> 66" = "#DF8950",               # orange
    "56-66" = "#DF5B50",              # red
    "True" = "#2BC212",               # Green
    "False" = "#EEEEEE",                 # Grey
    "unmethylated" = "#B73A20",       # red
    "methylated" = "#2BC212",         # green
    "positive" = "#B73A20",           # red
    "Wild-type" = "#2BC212",          # red
    "unknown" = "#EEEEEE",               # Grey
    "IDH-1 wildtype" = "#AA0B3E",     # burgandy 
    "GBM/PNET" = "#AA930B",           # green-yellow
    "GBM/PNC" = "#3AAED4",            # dirty orange
    "Giant cell Glioblastoma" = "#AD3AD4",  # purple
    "Epithelioid GBM" = "#72AA0B",    # dirty green-yellow
    "Mesenchymal" = "#698947",        # 
    "RTK II" = "#9fc16b",             # 
    "Inflammatory" = "#c05252",       # 
    "Unknown" = "#EEEEEE",               # Grey
    "RTK I" = "#6BC18D",              # 
    "DMG K27" = "#C4F678",            # 
    "Mid" = "#567e42",                # 
    "IDH-mutant HG" = "#d7c93c",      # 
    "IDH-mutant IDH A" = "#dfbd34",   # 

    "2.0" = "#3AAED4",                # 
    "3.0" = "#B73A20",                # 

    'Proneural' = '#7452a1', 
    'Classical' = '#9bccea', 
    'Mesenchymal' = '#8bbf54'
    
)

clinical_order = c('who_grade', 'tumor_family', 'sex', 'necrosis_present', 'age_group')
sequencing_order = c('Illumina WGS', 'Illumina WTS', 'Nanopore WGS DNA', 'Nanopore WGS methylation')
tumor_order = c('idh_status', 'methylation_subtype', 'Clinical subtype', 'mgmt_promoter_status', 'atrx_status', 'transcription_subtype')

combined_order = c(clinical_order, sequencing_order, tumor_order, 'purity', 'ploidy')


tile_plot = function(category, input_df){

# subset to category
input_df = input_df[input_df$description == category,]

# make variables strings
input_df$variable = as.character(input_df$variable)

print(head(input_df))

# all combined
p = ggplot(input_df, aes(x = sample, y = variable, fill = value)) + plot_theme() +
geom_tile(color = "black") +

ggtitle(category) +
xlab("") + ylab('') +

scale_fill_manual(values = color_vals)

# extract legend
legend = cowplot::get_legend(p)

p = p + theme(legend.position='none')
    
print(p)

# add legend
grid.newpage()
grid.draw(legend)

return()
}

tile_plot_continuous = function(category, input_df){

# subset to category
input_df = input_df[input_df$description == category,]

# make variables continuous values
input_df <- input_df %>%
    mutate(value = ifelse(value == "unknown", NA, as.numeric(value)))

    
# all combined
p = ggplot(input_df, aes(x = sample, y = variable, fill = value)) + plot_theme() +
geom_tile(color = "black") +

ggtitle(category) +
xlab("") + ylab('') +

scale_fill_gradientn(colors = c("white", "firebrick"), 
values = scales::rescale(c(0, 1)), 
na.value = "#eeeeee")  +

guides(fill = guide_colourbar(ticks.colour = "black", frame.colour = 'black'))


# extract legend
legend = cowplot::get_legend(p)

p = p + theme(legend.position='none')
    
print(p)

# add legend
grid.newpage()
grid.draw(legend)

return()
}


sort_df = function(input_df){
    # Function to extract numeric parts from each element
    extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))
    
    # Extract and sort by numeric parts
    sorted_vector <- unique(input_df$sample)[order(sapply(unique(input_df$sample), extract_numeric))]

    # order samples
    input_df$sample = factor(input_df$sample, levels = sorted_vector)
    
    return(input_df)
}



pdf(opt$figure_file, width = 10, height = 6)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')

# sort df
input_df = sort_df(input_df)

# sort factor levels
input_df$variable = factor(input_df$variable, levels = rev(combined_order))

# # create plots
lapply(unique(input_df$description), tile_plot, input_df)

# create plot with continuous values
tile_plot_continuous('purity', input_df)

dev.off()

print(opt$figure_file)






