# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import scipy.stats as stats

help_message = '''
Failed
'''


def process_cna_df(df):
    # Pivot the table to get 'primary', 'recurrent', and 'shared' bases for each patient-cna_type pair
    df_pivot = df.melt(id_vars=["patient", "cna_type"], 
                        value_vars=["bases_primary", "bases_recurrent", "bases_common"],
                        var_name="category", 
                        value_name="bases")

    # Replace column names to make them more readable
    df_pivot["category"] = df_pivot["category"].replace({
        "bases_primary": "primary",
        "bases_recurrent": "recurrent",
        "bases_common": "shared"
    })

    # Pivot to arrange in desired format
    df_pivot = df_pivot.pivot(index=["patient", "cna_type"], columns="category", values="bases").reset_index()

    # Modify the DataFrame to create "sample" column as patient-primary and patient-recurrent
    df_pivot["sample_primary"] = df_pivot["patient"] + "-primary"
    df_pivot["sample_recurrent"] = df_pivot["patient"] + "-recurrent"

    # Calculate new values by summing shared bases with primary and recurrent bases
    df_pivot["primary"] = df_pivot["primary"] + df_pivot["shared"]
    df_pivot["recurrent"] = df_pivot["recurrent"] + df_pivot["shared"]

    # Reshape the DataFrame to have "sample" instead of separate primary/recurrent columns
    df_long = df_pivot.melt(id_vars=["cna_type", "sample_primary", "sample_recurrent"], 
                            value_vars=["primary", "recurrent"], 
                            var_name="mutation_type", 
                            value_name="bases")

    # Rename the sample columns appropriately
    df_long["sample"] = df_long.apply(lambda x: x["sample_primary"] if x["mutation_type"] == "primary" else x["sample_recurrent"], axis=1)

    # Drop unnecessary columns and reorder
    df_long = df_long[["sample", "cna_type", "bases"]]

    return df_long


def process_sv_df(df):
    # Group by sample and count the number of SVs
    df_grouped = df.groupby("sample").size().reset_index(name="sv_count")

    # Group by sample and sum the bases of SVs
    df_grouped_bases = df.groupby("sample")["size"].sum().reset_index(name="sv_bases")

    return df_grouped, df_grouped_bases


def run_correlations(snv_counts_file, cna_file, sv_file):
    # load dfs
    snv_counts_df = pd.read_csv(snv_counts_file, sep='\t')
    cna_df = pd.read_csv(cna_file, sep='\t')
    sv_df = pd.read_csv(sv_file, sep='\t')

    # process cna_df and subset to total
    cna_df = process_cna_df(cna_df)
    cna_df = cna_df[cna_df["cna_type"] == "total"].sort_values(by=["sample"]).loc[:, ["sample", "bases"]]

    # process sv_df and subset to number of svs
    sv_number_df, sv_bases_df = process_sv_df(sv_df)

    # subset to rlgs, sort by sample
    snv_counts_df = snv_counts_df[snv_counts_df["source"] == "rlgs"].sort_values(by=["sample"]).loc[:, ["sample", "mutation_count"]]

    # combine to single df
    df = snv_counts_df.merge(cna_df, on="sample", how="inner")
    df = df.merge(sv_number_df, on="sample", how="inner")
    # df = df.merge(sv_bases_df, on="sample", how="inner")


    # run spearman correlation between snv and cna
    res_df = []

    # snv vs sv_count
    corr, pval = stats.spearmanr(df['mutation_count'], df['bases'])
    res_df.append(["snv_vs_sv_count", corr, pval])

    # snv vs cna
    corr, pval = stats.spearmanr(df['mutation_count'], df['sv_count'])
    res_df.append(["snv_vs_cna", corr, pval])

    # sv_count vs cna
    corr, pval = stats.spearmanr(df['bases'], df['sv_count'])
    res_df.append(["sv_count_vs_cna", corr, pval])


    # # snv vs sv_bases
    # corr, pval = stats.spearmanr(df['mutation_count'], df['sv_bases'])
    # res_df.append(["snv_vs_sv_bases", corr, pval])

    # # sv_bases vs cna
    # corr, pval = stats.spearmanr(df['sv_bases'], df['sv_count'])
    # res_df.append(["sv_bases_vs_cna", corr, pval])

    res_df = pd.DataFrame(res_df, columns=["comparison", "correlation", "p_value"])

    print(res_df)
    
    return df, res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # summarize maf df
    df, stats_df = run_correlations(snv_counts_file, cna_file, sv_file)

    # save to files
    df.to_csv(compare_snv_vs_sv_and_cna_file, sep='\t', index=False)
    stats_df.to_csv(compare_snv_vs_sv_and_cna_stats_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["snv_counts_file=", "cna_file=", "sv_file=", "compare_snv_vs_sv_and_cna_file=", "compare_snv_vs_sv_and_cna_stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--snv_counts_file"):
            snv_counts_file = str(arg)
        if opt in ("--cna_file"):
            cna_file = str(arg)
        if opt in ("--sv_file"):
            sv_file = str(arg)

        if opt in ("--compare_snv_vs_sv_and_cna_file"):
            compare_snv_vs_sv_and_cna_file = str(arg)
        if opt in ("--compare_snv_vs_sv_and_cna_stats_file"):
            compare_snv_vs_sv_and_cna_stats_file = str(arg)

    main()




