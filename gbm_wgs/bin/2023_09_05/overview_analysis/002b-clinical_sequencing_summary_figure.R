# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)

library(grid)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




color_vals = c('60/30' = '#ff1313',
'40/15' = '#ff9797', 
'6x' = '#1200ff', 
'5x' = '#1693e9', 
'4x' = '#38bbc7', 
'3x' = '#17e8df', 
'avastin_4_years' = '#d8d627',
'lomustine_1_cycle' = '#46c43b',
'True' = '#de21d7')


tile_plot = function(input_df){

# sort variables factors
input_df$variable = factor(input_df$variable, levels = rev(c('radiotherapy_dose', 'chemotherapy_dose', 'other_therapy', 'hypermutated')))
input_df$value = factor(input_df$value, levels = c('60/30', '40/15', '6x', '5x', '4x', '3x', 'avastin_4_years', 'lomustine_1_cycle', 'True'))


# all combined
p = ggplot(input_df, aes(x = patient, y = variable, fill = value)) + plot_theme() +
geom_tile(color = "black") +

ggtitle('') +
xlab("") + ylab('') +

scale_fill_manual(values = color_vals)

# extract legend
legend = cowplot::get_legend(p)

p = p + theme(legend.position='none')
    
print(p)

# add legend
grid.newpage()
grid.draw(legend)

return()
}




sort_samples = function(df){
# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# order samples
sorted_vector <- unique(df$patient)[order(sapply(unique(df$patient), extract_numeric))]
df$patient = factor(df$patient, levels = sorted_vector)

return(df)
}



pdf(opt$figure_file)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')

input_df = sort_samples(input_df)

# plot dotplots
tile_plot(input_df)


dev.off()


print(opt$figure_file)




