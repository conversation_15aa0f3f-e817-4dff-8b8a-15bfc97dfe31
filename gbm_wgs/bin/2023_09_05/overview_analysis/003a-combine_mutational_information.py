# Alec <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def combine_files(nanopore_svs_pcg_file, sarek_cnas_pcg_file, sarek_snvs_pcg_file, genes_oi):
    # load the files
    df1 = pd.read_csv(nanopore_svs_pcg_file, sep='\t')
    df2 = pd.read_csv(sarek_cnas_pcg_file, sep='\t')
    df3 = pd.read_csv(sarek_snvs_pcg_file, sep='\t')

    # add data source column
    df1['data_source'] = 'nanopore_svs'
    df2['data_source'] = 'sarek_cnas'
    df3['data_source'] = 'sarek_snvs'

    # drop mutation type column
    df3.drop(columns=['mutation_type'], inplace=True)

    # rename columns to be "mutation_type"
    df1.rename(columns={'sv_type': 'mutation_type'}, inplace=True)
    df2.rename(columns={'cna': 'mutation_type'}, inplace=True)
    df3.rename(columns={'detailed_mutation_type': 'mutation_type'}, inplace=True)

    # subset to specific columns
    cols_oi = ['sample', 'data_source', 'gene', 'mutation_type']

    # concat the dfs
    res_df = pd.concat([df1[cols_oi], df2[cols_oi], df3[cols_oi]])

    # remove duplicated rows by sample, gene, mutation_type
    res_df['tmp'] = res_df['sample'] + res_df['gene'] + res_df['mutation_type']
    res_df.drop_duplicates(subset=['tmp'], inplace=True)
    res_df.drop(columns=['tmp'], inplace=True)

    # subset to genes of interest
    res_df = res_df[res_df['gene'].isin(genes_oi)]

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and combine files
    df = combine_files(nanopore_svs_pcg_file, sarek_cnas_pcg_file, sarek_snvs_pcg_file, genes_oi)

    # save the file
    df.to_csv(figure_data_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # genes of interest
    genes_oi = ['EGFR', 'ATRX', 'PTEN', 'CDK6', 'MET', 'CDKN2A', 'RB1', 'MDM4', 'CDK4', 'TP53', 'PDGFRA', 'FUBP1', 'NF1', 'MDM2', 'MYCN', 'PIK3CA', 'PIK3R1', 'CCND2', 'CIC', 'IDH1', 'TERTp', 'none']

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["nanopore_svs_pcg_file=", "sarek_cnas_pcg_file=", "sarek_snvs_pcg_file=", "figure_data_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--nanopore_svs_pcg_file"):
            nanopore_svs_pcg_file = str(arg)
        if opt in ("--sarek_cnas_pcg_file"):
            sarek_cnas_pcg_file = str(arg)
        if opt in ("--sarek_snvs_pcg_file"):
            sarek_snvs_pcg_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)

    main()


