# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import subprocess

help_message = '''
Failed
'''


def classify_age_group(df):
    # create column 'age_group' and classify into < 56, 56-66, > 66
    df['age_group'] = np.nan
    df.loc[df['age'] < 56, 'age_group'] = '< 56'
    df.loc[(df['age'] >= 56) & (df['age'] <= 66), 'age_group'] = '56-66'
    df.loc[df['age'] > 66, 'age_group'] = '> 66'

    # drop age column
    df = df.drop(columns=['age'])

    return df


def combine_melted_dfs(clinical_file, sequencing_details_file, tumor_data_file, ploidy_purity_file, transcription_subtype_file):
    # for each file, load and melt df
    clinical_df = pd.read_csv(clinical_file, sep='\t')
    sequencing_details_df = pd.read_csv(sequencing_details_file, sep='\t')
    tumor_data_df = pd.read_csv(tumor_data_file, sep='\t')
    ploidy_purity_df = pd.read_csv(ploidy_purity_file, sep='\t')
    transcription_subtype_df = pd.read_csv(transcription_subtype_file, sep='\t')

    # add description column
    clinical_df['description'] = 'clinical'
    sequencing_details_df['description'] = 'sequencing_details'
    tumor_data_df['description'] = 'tumor_data'
    transcription_subtype_df['description'] = 'transcription_subtype'

    # subset to significant subtypes and specific columns
    transcription_subtype_df = transcription_subtype_df.loc[transcription_subtype_df['significant'] == 'Yes', ['sample', 'subtype']]
    transcription_subtype_df.rename(columns={'subtype': 'value'}, inplace=True)
    
    # add description and variable columns
    transcription_subtype_df['description'] = 'clinical'
    transcription_subtype_df['variable'] = 'transcription_subtype'
    transcription_subtype_df['sample'] = transcription_subtype_df['sample'].str.replace("_", "-")


    # for ploidy_purity_df, melt then add description column because purity is continuous
    ploidy_purity_df = ploidy_purity_df.melt(id_vars=['sample'], var_name='variable', value_name='value')

    # add description column
    ploidy_purity_df.loc[ploidy_purity_df['variable'] == 'ploidy', 'description'] = 'ploidy'
    ploidy_purity_df.loc[ploidy_purity_df['variable'] == 'purity', 'description'] = 'purity'

    # classify age group in clinical_df
    clinical_df = classify_age_group(clinical_df)

    # melt dfs
    clinical_df = clinical_df.melt(id_vars=['sample', 'description'], var_name='variable', value_name='value')
    sequencing_details_df = sequencing_details_df.melt(id_vars=['sample', 'description'], var_name='variable', value_name='value')
    tumor_data_df = tumor_data_df.melt(id_vars=['sample', 'description'], var_name='variable', value_name='value')

    # concat dfs
    df = pd.concat([clinical_df, sequencing_details_df, tumor_data_df, ploidy_purity_df, transcription_subtype_df])

    # fill na with "unknown"
    df['value'] = df['value'].fillna('unknown')

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # combine and melt dfs
    df = combine_melted_dfs(clinical_file, sequencing_details_file, tumor_data_file, ploidy_purity_file, transcription_subtype_file)

    # save to files
    df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(' '.join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["clinical_file=", "sequencing_details_file=", "tumor_data_file=", "ploidy_purity_file=", "transcription_subtype_file=", "r_script=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--clinical_file"):
            clinical_file = str(arg)
        if opt in ("--sequencing_details_file"):
            sequencing_details_file = str(arg)
        if opt in ("--tumor_data_file"):
            tumor_data_file = str(arg)
        if opt in ("--ploidy_purity_file"):
            ploidy_purity_file = str(arg)
        if opt in ("--transcription_subtype_file"):
            transcription_subtype_file = str(arg)
        
        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)

    main()




