# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''

def process_treatment_file(treatment_file):
    # load the file
    df = pd.read_csv(treatment_file, sep='\t')

    # categorize radiotherapy doses
    df['radiotherapy_dose'] = df['radiotherapy_dose'].str.split(' ').str[0]

    # rename chermotherapy column
    df.rename(columns={'adjuvant_tmz_stop': 'chemotherapy_dose'}, inplace=True)

    # subset to columns of interest 
    df = df[['patient', 'radiotherapy_dose', 'chemotherapy_dose', 'other_therapy', 'hypermutated']]

    # duplicate the df to make 2 samples per patient "-primary" and "-recurrent"
    df['patient'] = df['patient'].astype(str)
    df['patient'] = df['patient'] + '-primary'
    df2 = df.copy()
    df2['patient'] = df2['patient'].str.replace('-primary', '-recurrent')

    # combine the dfs
    res_df = pd.concat([df, df2])

    # melt the df
    res_df = res_df.melt(id_vars=['patient'], var_name='variable', value_name='value')

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process and combine all dfs
    res_df = process_treatment_file(treatment_file)

    # save to files
    res_df.to_csv(figure_data_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["treatment_file=", "figure_data_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--treatment_file"):
            treatment_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        

    main()




