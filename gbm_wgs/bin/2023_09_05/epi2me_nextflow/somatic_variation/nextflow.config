process {
    executor = "uge"
    penv = "smp"
    memory = '24G'
    cpus = '1'
    time = '12h'
    clusterOptions = { "-V -l h_vmem=24G -V -P reimandlab -l h_stack=32M -l h_rt=48:00:00" }

    withName: minimap2_ubam {
        def customMemoryPerCPU = 3072 // Set memory per CPU to 3 GB
        def customCPUs = cpus // Use the actual number of CPUs
    clusterOptions = { "-V -l h_vmem=${customMemoryPerCPU}G -V -P reimandlab -l h_stack=32M -l h_rt=48:00:00" }

    }

    withName: makeReport {
        memory = '48G'
        cpus = '1'
        clusterOptions = { "-V -l h_vmem=48G -V -P reimandlab -l h_stack=32M -l h_rt=48:00:00" }
    }
}

executor {
    name = "uge"
    queueSize = 500
    queueStatInterval = "10s"
}
