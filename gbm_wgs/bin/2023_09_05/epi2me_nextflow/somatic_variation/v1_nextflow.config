process {
    executor = "sge"
    penv = "smp"
    beforeScript = 'source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh; source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh; conda activate nextflow'
    clusterOptions = { 
        def memory = task.memory ? task.memory.toMega() : 20480 // Default to 20G if not specified
        def cpus = task.cpus ?: 1 // Default to 1 CPU if not specified
        def memoryPerCPU = memory / cpus
        "-P reimandlab -l h_rt=48:00:00 -l h_vmem=${memoryPerCPU}M"
    }

    withName: makeReport {
        memory = '48G'
        cpus = '4'
        clusterOptions = { "-V -l h_vmem=12G -V -P reimandlab -l h_stack=32M -l h_rt=48:00:00 -pe smp 4" }
    }
}

executor {
    name = "sge"
    queueSize = 100
    queueStatInterval = "10s"
}

singularity {
    enabled = true
    autoMounts = true
}

env {
    SINGULARITY_CACHEDIR = "/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/"
}


