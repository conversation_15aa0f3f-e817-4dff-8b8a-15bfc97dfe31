process {
    executor = "sge"
    penv = "smp"
    beforeScript = 'source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh; source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh; conda activate nextflow'
    
    clusterOptions = { 
        def memory = task.memory ? task.memory.toMega() : 20480 // Default to 20G if not specified
        def cpus = task.cpus ?: 1 // Default to 1 CPU if not specified
        def memoryPerCPU = memory / cpus + 1024 // Calculate memory per CPU without adding extra

        // Adjust memory and memoryPerCPU based on task name
        if (task.name.contains("clairs")) { // Check for specific string in task name
            def customMemoryPerCPU = 40960 // Set memory per CPU to 40 GB (40960 MB)
            def customCPUs = cpus // Use the actual number of CPUs
            def customMemory = customMemoryPerCPU * customCPUs // Calculate total memory
            memoryPerCPU = customMemoryPerCPU
            memory = customMemory
        }
        if (task.name.contains("evaluate")) { // Check for specific string in task name
            def customMemoryPerCPU = 20480 // Set memory per CPU to 20 GB (20480 MB)
            def customCPUs = cpus // Use the actual number of CPUs
            def customMemory = customMemoryPerCPU * customCPUs // Calculate total memory
            memoryPerCPU = customMemoryPerCPU
            memory = customMemory
        }
        // Ensure memory values are in MB and properly formatted
        "-P reimandlab -l h_rt=48:00:00 -l h_vmem=${memoryPerCPU}M -l mem_free=${memory}M -l h_rss=${memory}M"
    }

    withName: makeReport {
        memory = '48G'
        cpus = 4
        clusterOptions = { "-V -l h_vmem=48G -l h_stack=32M -l h_rt=48:00:00 -pe smp 4" }
    }
}

executor {
    name = "sge"
    queueSize = 100
    queueStatInterval = "10s"
}

singularity {
    enabled = true
    autoMounts = true
}

env {
    SINGULARITY_CACHEDIR = "/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/"
}


