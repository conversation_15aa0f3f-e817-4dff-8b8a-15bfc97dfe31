#!/bin/bash
#$ -P reimandlab
#$ -N wf-somatic_variation_RLGS12-primary
#$ -l h_vmem=10G,h_rt=7:0:0:0
#$ -q all.q
#$ -o /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/nanopore/
#$ -e /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/nanopore/

source /u/abahcheli/.profile
source /u/abahcheli/.bashrc
source /etc/profile.d/uge_settings.sh

source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh

mamba activate nextflow

cd /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore_somatic/RLGS12-primary

export NXF_SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export NXF_OFFLINE='true'

nextflow run /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/nextflow_pipelines_offline/epi2me-labs-wf-somatic-variation_v1.3.0/v1_3_0 --bam_normal /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore/RLGS12-blood/RLGS12-blood.pass.bam --bam_tumor /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore/RLGS12-primary/RLGS12-primary.pass.bam --ref /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/Homo_sapiens_assembly38.fasta --sv --snv --sample_name RLGS12-primary --normal_min_coverage 0 --tumor_min_coverage 0 --ubam_map_threads 10 --min_sv_length 10 --classify_insert True --basecaller_cfg dna_r9.4.1_e8_sup@v3.3 --out_dir /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore_somatic/RLGS12-primary -profile singularity -c /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/epi2me_nextflow/nextflow.config -resume -process.maxDumpTasks 200

