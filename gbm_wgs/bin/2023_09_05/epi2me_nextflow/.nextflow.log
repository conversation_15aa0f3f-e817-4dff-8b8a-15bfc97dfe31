Apr-23 15:21:06.069 [main] DEBUG nextflow.cli.Launcher - $> nextflow run nf-core/sarek --help
Apr-23 15:21:06.165 [main] INFO  nextflow.cli.CmdRun - N E X T F L O W  ~  version 23.10.1
Apr-23 15:21:06.186 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/u/abahcheli/.nextflow/plugins; core-plugins: nf-amazon@2.1.4,nf-azure@1.3.3,nf-cloudcache@0.3.0,nf-codecommit@0.1.5,nf-console@1.0.6,nf-ga4gh@1.1.0,nf-google@1.8.3,nf-tower@1.6.3,nf-wave@1.0.1
Apr-23 15:21:06.201 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Apr-23 15:21:06.202 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Apr-23 15:21:06.205 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.4.1 in 'deployment' mode
Apr-23 15:21:06.214 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Apr-23 15:21:06.225 [main] DEBUG nextflow.scm.ProviderConfig - Using SCM config path: /u/abahcheli/.nextflow/scm
Apr-23 15:21:06.245 [main] DEBUG nextflow.scm.RepositoryFactory - Found Git repository result: [RepositoryFactory]
Apr-23 15:21:06.252 [main] INFO  nextflow.cli.CmdRun - Pulling nf-core/sarek ...
Apr-23 15:21:06.257 [main] DEBUG nextflow.scm.RepositoryProvider - Request [credentials -:-] -> https://api.github.com/repos/nf-core/sarek/contents/nextflow.config
Apr-23 15:21:07.647 [main] DEBUG nextflow.scm.RepositoryProvider - Request [credentials -:-] -> https://api.github.com/repos/nf-core/sarek/contents/main.nf
Apr-23 15:21:07.736 [main] DEBUG nextflow.scm.RepositoryProvider - Request [credentials -:-] -> https://api.github.com/repos/nf-core/sarek
Apr-23 15:21:07.820 [main] DEBUG nextflow.scm.AssetManager - Pulling nf-core/sarek -- Using remote clone url: https://github.com/nf-core/sarek.git
Apr-23 15:21:24.515 [main] INFO  nextflow.cli.CmdRun -  downloaded from https://github.com/nf-core/sarek.git
Apr-23 15:21:24.586 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /u/abahcheli/.nextflow/assets/nf-core/sarek/nextflow.config
Apr-23 15:21:24.587 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/epi2me_nextflow/nextflow.config
Apr-23 15:21:24.587 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /u/abahcheli/.nextflow/assets/nf-core/sarek/nextflow.config
Apr-23 15:21:24.588 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/epi2me_nextflow/nextflow.config
Apr-23 15:21:24.599 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
Apr-23 15:21:26.918 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
Apr-23 15:21:26.970 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declararion
Apr-23 15:21:26.971 [main] INFO  nextflow.cli.CmdRun - Launching `https://github.com/nf-core/sarek` [confident_raman] DSL2 - revision: ea88402912 [master]
Apr-23 15:21:26.972 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins declared=[nf-validation@1.1.3, nf-prov]
Apr-23 15:21:26.972 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Apr-23 15:21:26.972 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[nf-validation@1.1.3, nf-prov]
Apr-23 15:21:26.972 [main] DEBUG nextflow.plugin.PluginUpdater - Installing plugin nf-validation version: 1.1.3
Apr-23 15:21:26.975 [main] INFO  nextflow.plugin.PluginUpdater - Downloading plugin nf-validation@1.1.3
Apr-23 15:21:27.515 [main] INFO  org.pf4j.util.FileUtils - Expanded plugin zip 'nf-validation-1.1.3.zip' in 'nf-validation-1.1.3'
Apr-23 15:21:27.516 [main] DEBUG nextflow.plugin.PluginUpdater - Failed atomic move for plugin /tmp/pf4j-update-downloader2365377065440151975/nf-validation-1.1.3 -> /u/abahcheli/.nextflow/plugins/nf-validation-1.1.3 - Reason: /tmp/pf4j-update-downloader2365377065440151975/nf-validation-1.1.3 -> /u/abahcheli/.nextflow/plugins/nf-validation-1.1.3: Invalid cross-device link - Fallback on safe move
Apr-23 15:21:27.799 [main] INFO  org.pf4j.AbstractPluginManager - Plugin 'nf-validation@1.1.3' resolved
Apr-23 15:21:27.799 [main] INFO  org.pf4j.AbstractPluginManager - Start plugin 'nf-validation@1.1.3'
Apr-23 15:21:27.828 [main] DEBUG nextflow.plugin.BasePlugin - Plugin started nf-validation@1.1.3
Apr-23 15:21:27.828 [main] DEBUG nextflow.plugin.PluginUpdater - Installing plugin nf-prov version: latest
Apr-23 15:21:27.829 [main] INFO  nextflow.plugin.PluginUpdater - Downloading plugin nf-prov@1.2.2
Apr-23 15:21:28.031 [main] INFO  org.pf4j.util.FileUtils - Expanded plugin zip 'nf-prov-1.2.2.zip' in 'nf-prov-1.2.2'
Apr-23 15:21:28.031 [main] DEBUG nextflow.plugin.PluginUpdater - Failed atomic move for plugin /tmp/pf4j-update-downloader5251067840657959534/nf-prov-1.2.2 -> /u/abahcheli/.nextflow/plugins/nf-prov-1.2.2 - Reason: /tmp/pf4j-update-downloader5251067840657959534/nf-prov-1.2.2 -> /u/abahcheli/.nextflow/plugins/nf-prov-1.2.2: Invalid cross-device link - Fallback on safe move
Apr-23 15:21:28.260 [main] INFO  org.pf4j.AbstractPluginManager - Plugin 'nf-prov@1.2.2' resolved
Apr-23 15:21:28.261 [main] INFO  org.pf4j.AbstractPluginManager - Start plugin 'nf-prov@1.2.2'
Apr-23 15:21:28.267 [main] DEBUG nextflow.plugin.BasePlugin - Plugin started nf-prov@1.2.2
Apr-23 15:21:28.280 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /u/abahcheli/.nextflow/secrets/store.json
Apr-23 15:21:28.285 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@64cec4d0] - activable => nextflow.secret.LocalSecretsProvider@64cec4d0
Apr-23 15:21:28.333 [main] DEBUG nextflow.Session - Session UUID: affc99f8-643c-4b23-a3b5-ed682b635519
Apr-23 15:21:28.333 [main] DEBUG nextflow.Session - Run name: confident_raman
Apr-23 15:21:28.334 [main] DEBUG nextflow.Session - Executor pool size: 8
Apr-23 15:21:28.339 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Apr-23 15:21:28.358 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 23.10.1 build 5891
  Created: 12-01-2024 22:01 UTC (17:01 EDT)
  System: Linux 5.4.0-176-generic
  Runtime: Groovy 3.0.19 on OpenJDK 64-Bit Server VM 21-internal-adhoc.conda.src
  Encoding: UTF-8 (ISO-8859-1)
  Process: <EMAIL> [10.6.22.12]
  CPUs: 8 - Mem: 31.2 GB (790.6 MB) - Swap: 15.3 GB (13.6 GB) - Virtual threads ON
Apr-23 15:21:28.381 [main] DEBUG nextflow.Session - Work-dir: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/epi2me_nextflow/work [nfs]
Apr-23 15:21:28.395 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Apr-23 15:21:28.409 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Apr-23 15:21:28.428 [main] DEBUG nextflow.Session - Observer factory: ProvObserverFactory
Apr-23 15:21:28.520 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Apr-23 15:21:28.526 [main] DEBUG nextflow.util.CustomPoolFactory - Creating virtual thread pool
Apr-23 15:21:28.638 [main] DEBUG nextflow.Session - Session start
Apr-23 15:21:28.642 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/epi2me_nextflow/null/pipeline_info/execution_trace_2024-04-23_15-21-25.txt
Apr-23 15:21:29.065 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Apr-23 15:21:29.800 [main] DEBUG nextflow.script.IncludeDef - Loading included plugin extensions with names: [paramsSummaryMap:paramsSummaryMap]; plugin Id: nf-validation
Apr-23 15:21:30.222 [main] DEBUG nextflow.script.IncludeDef - Loading included plugin extensions with names: [paramsSummaryMap:paramsSummaryMap]; plugin Id: nf-validation
Apr-23 15:21:30.222 [main] DEBUG nextflow.script.IncludeDef - Loading included plugin extensions with names: [fromSamplesheet:fromSamplesheet]; plugin Id: nf-validation
Apr-23 15:21:30.364 [main] DEBUG nextflow.script.IncludeDef - Loading included plugin extensions with names: [paramsHelp:paramsHelp]; plugin Id: nf-validation
Apr-23 15:21:30.364 [main] DEBUG nextflow.script.IncludeDef - Loading included plugin extensions with names: [paramsSummaryLog:paramsSummaryLog]; plugin Id: nf-validation
Apr-23 15:21:30.364 [main] DEBUG nextflow.script.IncludeDef - Loading included plugin extensions with names: [validateParameters:validateParameters]; plugin Id: nf-validation
Apr-23 15:21:44.625 [main] DEBUG nextflow.script.IncludeDef - Loading included plugin extensions with names: [paramsSummaryMap:paramsSummaryMap]; plugin Id: nf-validation
Apr-23 15:21:44.626 [main] DEBUG nextflow.script.IncludeDef - Loading included plugin extensions with names: [fromSamplesheet:fromSamplesheet]; plugin Id: nf-validation
Apr-23 15:21:44.698 [main] DEBUG nextflow.script.IncludeDef - Loading included plugin extensions with names: [paramsHelp:paramsHelp]; plugin Id: nf-validation
Apr-23 15:21:44.698 [main] DEBUG nextflow.script.IncludeDef - Loading included plugin extensions with names: [paramsSummaryLog:paramsSummaryLog]; plugin Id: nf-validation
Apr-23 15:21:44.698 [main] DEBUG nextflow.script.IncludeDef - Loading included plugin extensions with names: [validateParameters:validateParameters]; plugin Id: nf-validation
Apr-23 15:21:45.811 [main] DEBUG nextflow.Nextflow - Using schema file: nextflow_schema.json
Apr-23 15:21:45.824 [main] INFO  nextflow.Nextflow - 

-[2m----------------------------------------------------[0m-
                                        [0;32m,--.[0;30m/[0;32m,-.[0m
[0;34m        ___     __   __   __   ___     [0;32m/,-._.--~'[0m
[0;34m  |\ | |__  __ /  ` /  \ |__) |__         [0;33m}  {[0m
[0;34m  | \| |       \__, \__/ |  \ |___     [0;32m\`-._,-`-,[0m
                                        [0;32m`._,._,'[0m
[0;37m      ____[0m
[0;37m    .´ _  `.[0m
[0;37m   /  [0;32m|\[0m`-_ \[0m     [0;34m __        __   ___     [0m
[0;37m  |   [0;32m| \[0m  `-|[0m    [0;34m|__`  /\  |__) |__  |__/[0m
[0;37m   \ [0;32m|   \[0m  /[0m     [0;34m.__| /¯¯\ |  \ |___ |  \[0m
[0;37m    `[0;32m|[0m____[0;32m\[0m´[0m

[0;35m  nf-core/sarek v3.4.1-gea88402[0m
-[2m----------------------------------------------------[0m-
Typical pipeline command:

  [0;36mnextflow run nf-core/sarek -profile <docker/singularity/.../institute> --input samplesheet.csv --outdir <OUTDIR>[0m

[4m[1mInput/output options[0m
  --input                             [2m[string]  [0mPath to comma-separated file containing information about the samples in the experiment.[2m[0m
  --step                              [2m[string]  [0mStarting step[2m (accepted: mapping, markduplicates, prepare_recalibration, recalibrate, variant_calling, 
                                                annotate) [default: mapping][0m 
  --outdir                            [2m[string]  [0mThe output directory where the results will be saved. You have to use absolute paths to storage on Cloud 
                                                infrastructure.[2m[0m 

[4m[1mMain options[0m
  --split_fastq                       [2m[integer] [0mSpecify how many reads each split of a FastQ file contains. Set 0 to turn off splitting at all.[2m [default: 
                                                50000000][0m 
  --wes                               [2m[boolean] [0mEnable when exome or panel data is provided.[2m[0m
  --intervals                         [2m[string]  [0mPath to target bed file in case of whole exome or targeted sequencing or intervals file.[2m[0m
  --nucleotides_per_second            [2m[integer] [0mEstimate interval size.[2m [default: 200000][0m
  --no_intervals                      [2m[boolean] [0mDisable usage of intervals.[2m[0m
  --tools                             [2m[string]  [0mTools to use for duplicate marking, variant calling and/or for annotation.[2m[0m
  --skip_tools                        [2m[string]  [0mDisable specified tools.[2m[0m

[4m[1mFASTQ Preprocessing[0m
  --trim_fastq                        [2m[boolean] [0mRun FastP for read trimming[2m[0m
  --umi_read_structure                [2m[string]  [0mSpecify UMI read structure[2m[0m

[4m[1mPreprocessing[0m
  --aligner                           [2m[string]  [0mSpecify aligner to be used to map reads to reference genome.[2m (accepted: bwa-mem, bwa-mem2, dragmap, 
                                                sentieon-bwamem) [default: bwa-mem][0m 
  --save_mapped                       [2m[boolean] [0mSave mapped files.[2m[0m
  --save_output_as_bam                [2m[boolean] [0mSaves output from mapping (if `--save_mapped`), Markduplicates & Baserecalibration as BAM file instead of 
                                                CRAM[2m[0m 
  --use_gatk_spark                    [2m[string]  [0mEnable usage of GATK Spark implementation for duplicate marking and/or base quality score recalibration[2m[0m

[4m[1mVariant Calling[0m
  --concatenate_vcfs                  [2m[boolean] [0mOption for concatenating germline vcf-files.[2m[0m
  --only_paired_variant_calling       [2m[boolean] [0mIf true, skips germline variant calling for matched normal to tumor sample. Normal samples without matched tumor 
                                                will still be processed through germline variant calling tools.[2m[0m 
  --joint_germline                    [2m[boolean] [0mTurn on the joint germline variant calling for GATK haplotypecaller[2m[0m
  --joint_mutect2                     [2m[boolean] [0mRuns Mutect2 in joint (multi-sample) mode for better concordance among variant calls of tumor samples from the 
                                                same patient. Mutect2 outputs will be stored in a subfolder named with patient ID under 
                                                `variant_calling/mutect2/` folder. Only a single normal sample per patient is allowed. Tumor-only mode is also 
                                                supported.[2m[0m 

[4m[1mAnnotation[0m
  --vep_custom_args                   [2m[string]  [0mAdd an extra custom argument to VEP.[2m [default: --everything --filter_common --per_gene --total_length 
                                                --offline --format vcf][0m 
  --vep_version                       [2m[string]  [0mShould reflect the VEP version used in the container.[2m [default: 111.0-0][0m
  --bcftools_annotations              [2m[string]  [0mA vcf file containing custom annotations to be used with bcftools annotate. Needs to be bgzipped.[2m[0m
  --bcftools_annotations_tbi          [2m[string]  [0mIndex file for `bcftools_annotations`[2m[0m
  --bcftools_header_lines             [2m[string]  [0mText file with the header lines of `bcftools_annotations`[2m[0m

[4m[1mReference genome options[0m
  --genome                            [2m[string]  [0mName of iGenomes reference.[2m [default: GATK.GRCh38][0m
  --dbsnp_vqsr                        [2m[string]  [0mlabel string for VariantRecalibration (haplotypecaller joint variant calling)[2m[0m
  --fasta                             [2m[string]  [0mPath to FASTA genome file.[2m[0m
  --fasta_fai                         [2m[string]  [0mPath to FASTA reference index.[2m[0m
  --known_indels_vqsr                 [2m[string]  [0mIf you use AWS iGenomes, this has already been set for you appropriately.

1st label string for 
                                                VariantRecalibration (haplotypecaller joint variant calling)[2m[0m 
  --known_snps                        [2m[string]  [0mIf you use AWS iGenomes, this has already been set for you appropriately.

Path to known snps file.[2m[0m
  --known_snps_tbi                    [2m[string]  [0mPath to known snps file snps.[2m[0m
  --known_snps_vqsr                   [2m[string]  [0mIf you use AWS iGenomes, this has already been set for you appropriately.

label string for VariantRecalibration 
                                                (haplotypecaller joint variant calling)[2m[0m 
  --ngscheckmate_bed                  [2m[string]  [0mPath to SNP bed file for sample checking with NGSCheckMate[2m[0m
  --snpeff_db                         [2m[string]  [0msnpEff DB version.[2m[0m
  --snpeff_genome                     [2m[string]  [0msnpEff genome.[2m[0m
  --vep_genome                        [2m[string]  [0mVEP genome.[2m[0m
  --vep_species                       [2m[string]  [0mVEP species.[2m[0m
  --vep_cache_version                 [2m[string]  [0mVEP cache version.[2m[0m
  --save_reference                    [2m[boolean] [0mSave built references.[2m[0m
  --build_only_index                  [2m[boolean] [0mOnly built references.[2m[0m
  --download_cache                    [2m[boolean] [0mDownload annotation cache.[2m[0m
  --igenomes_base                     [2m[string]  [0mDirectory / URL base for iGenomes references.[2m [default: s3://ngi-igenomes/igenomes/][0m
  --igenomes_ignore                   [2m[boolean] [0mDo not load the iGenomes reference config.[2m[0m
  --vep_cache                         [2m[string]  [0mPath to VEP cache.[2m [default: s3://annotation-cache/vep_cache/][0m
  --snpeff_cache                      [2m[string]  [0mPath to snpEff cache.[2m [default: s3://annotation-cache/snpeff_cache/][0m

[4m[1mGeneric options[0m
  --email                             [2m[string]  [0mEmail address for completion summary.[2m[0m
  --multiqc_title                     [2m[string]  [0mMultiQC report title. Printed as page header, used for filename if not otherwise specified.[2m[0m
  --multiqc_methods_description       [2m[string]  [0mCustom MultiQC yaml file containing HTML including a methods description.[2m[0m

[2m !! Hiding 89 params, use --validationShowHiddenParams to show them !!
[0m-[2m----------------------------------------------------[0m-
If you use nf-core/sarek for your analysis please cite:

* The pipeline
  10.12688/f1000research.16665.2, 10.1101/2023.07.19.549462, 10.5281/zenodo.3476425

* The nf-core framework
  https://doi.org/10.1038/s41587-020-0439-x

* Software dependencies
  https://github.com/nf-core/sarek/blob/master/CITATIONS.md
-[2m----------------------------------------------------[0m-
