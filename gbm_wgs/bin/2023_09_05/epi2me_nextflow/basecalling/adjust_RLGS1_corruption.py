# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

help_message = '''
Failed
'''


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # list all files of interest
    for filename in os.listdir(pass_dir):
        if filename == bad_file:
            print(f"Skipping {filename}")
            continue

        # Construct full paths
        source_file = os.path.join(pass_dir, filename)
        target_file = os.path.join(outdir, filename)
        
        # Create symbolic link
        try:
            os.symlink(source_file, target_file)
            print(f"Created symbolic link for {filename}")
        except OSError as e:
            print(f"Failed to create symbolic link for {filename}: {e}")

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # pass directory
    pass_dir = '/.mounts/labs/ont/PC24B242/230530_RLGS_0001_02_ligationSeq_run1/230530_RLGS_0001_02_ligationSeq_run1/20230530_1632_2B_PAK67607_bc9ed497/fast5_pass'

    outdir = '/.mounts/labs/reimandlab/private/users/abahcheli/tmp/RLGS1_pass'

    bad_file = 'PAK67607_pass_bc9ed497_20b788c4_6951.fast5'

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "samples_description_file=", "sample_recurrence_file=", "annotated_variants_file=", "r_script=", "figure_data_file=", "figure_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)
        if opt in ("--samples_description_file"):
            samples_description_file = str(arg)
        if opt in ("--sample_recurrence_file"):
            sample_recurrence_file = str(arg)

        if opt in ("--annotated_variants_file"):
            annotated_variants_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()


