#!/bin/bash
#$ -P reimandlab
#$ -N nanopore-wf_wf-basecalling_RLGS3-recurrent
#$ -l h_vmem=10G,h_rt=21:0:0:0
#$ -q all.q
#$ -o /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/nanopore/
#$ -e /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/nanopore/

source /u/abahcheli/.profile
source /u/abahcheli/.bashrc
source /etc/profile.d/uge_settings.sh

source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh

mamba activate nextflow

cd /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore/RLGS3-recurrent

export NXF_SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export NXF_OFFLINE='true'

nextflow run /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/nextflow_pipelines_offline/epi2me-labs-wf-basecalling_v1.1.7/v1_1_7 --input /.mounts/labs/ont/PC24B242/230607_RLGS_0003_03_ligationSeq_run1 --sample_name RLGS3-recurrent --output_bam True --dorado_ext fast5 --basecaller_cfg dna_r9.4.1_e8_sup@v3.3 --remora_cfg dna_r9.4.1_e8_sup@v3.3_5mCG_5hmCG@v0 --out_dir /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore/RLGS3-recurrent -profile singularity -c /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/epi2me_nextflow/basecalling/nextflow.config -resume -process.maxDumpTasks 200

