import os
import argparse
import h5py
import sys


in_dir = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore/RLGS1-primary/work/82/db35deee15db89ecb692df544ee169'

def main():
    fast5_files = find_all_fast5s(in_dir)
    print('  Found ' + int_to_str(len(fast5_files)) + ' reads\n')
    if not fast5_files:
        print("no fast5s")
        return
    print('Checking file integrity')
    good_fast5_count = 0
    bad_fast5_count = 0
    last_read_good = True
    for fast5_file in fast5_files:
        try:
            hdf5_file = h5py.File(fast5_file, 'r')
            get_hdf5_names(hdf5_file)
            good_fast5_count += 1
            last_read_good = True
            if good_fast5_count % 100 == 0:
                print('.', end='', flush=True)
        except (<PERSON><PERSON><PERSON><PERSON>, RuntimeError):
            bad_fast5_count += 1
            if last_read_good:
                print('', flush=True)
            print(fast5_file)
            last_read_good = False
    print('\n\nResults:')
    print('  ' + int_to_str(good_fast5_count) +
          ' good fast5 file' + ('' if good_fast5_count == 1 else 's'))
    print('  ' + int_to_str(bad_fast5_count) +
          ' bad fast5 file' + ('' if bad_fast5_count == 1 else 's'))


def find_all_fast5s(directory):
    fast5s = []
    for dir_name, _, filenames in os.walk(directory):
        for filename in filenames:
            if filename.endswith('.fast5'):
                fast5s.append(os.path.join(dir_name, filename))
    return fast5s


def get_hdf5_names(hdf5_file):
    names = []
    hdf5_file.visit(names.append)
    return names


def int_to_str(num):
    return '{:,}'.format(num)

main()

