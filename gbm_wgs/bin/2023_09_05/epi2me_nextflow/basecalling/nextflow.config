process {
    executor = "sge"
    penv = "smp"
    clusterOptions = { "-V -l h_vmem=16G -V -P reimandlab -l h_stack=32M -l h_rt=168:00:00" }

    withName: dorado {
        clusterOptions = { "-V -l h_vmem=8G -V -P reimandlab -l h_stack=32M -l gpu=2 -q gpu-short.q -l h_rt=2:0:0" }
    }

}

process."withLabel:gpu".maxForks = null

executor {
    name = "sge"
    queueSize = 500
    queueStatInterval = "10s"
}

singularity {
    enabled = true
    autoMounts = true
}

