Apr-08 12:40:15.694 [main] DEBUG nextflow.cli.Launcher - Setting http proxy: ProxyConfig[protocol=http; host=lb.hpc.oicr.on.ca; port=3128]
Apr-08 12:40:15.741 [main] DEBUG nextflow.cli.Launcher - Setting https proxy: ProxyConfig[protocol=https; host=lb.hpc.oicr.on.ca; port=3128]
Apr-08 12:40:15.742 [main] DEBUG nextflow.cli.Launcher - Setting ftp proxy: ProxyConfig[protocol=ftp; host=lb.hpc.oicr.on.ca; port=3128]
Apr-08 12:40:15.742 [main] DEBUG nextflow.cli.Launcher - $> nextflow run epi2me-labs/wf-alignment –help
Apr-08 12:40:15.915 [main] INFO  nextflow.cli.CmdRun - N E X T F L O W  ~  version 23.10.1
Apr-08 12:40:15.950 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/u/abahcheli/.nextflow/plugins; core-plugins: nf-amazon@2.1.4,nf-azure@1.3.3,nf-cloudcache@0.3.0,nf-codecommit@0.1.5,nf-console@1.0.6,nf-ga4gh@1.1.0,nf-google@1.8.3,nf-tower@1.6.3,nf-wave@1.0.1
Apr-08 12:40:15.994 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Apr-08 12:40:15.996 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Apr-08 12:40:16.001 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.4.1 in 'deployment' mode
Apr-08 12:40:16.038 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Apr-08 12:40:16.068 [main] DEBUG nextflow.scm.ProviderConfig - Using SCM config path: /u/abahcheli/.nextflow/scm
Apr-08 12:40:16.175 [main] DEBUG nextflow.scm.RepositoryFactory - Found Git repository result: [RepositoryFactory]
Apr-08 12:40:16.192 [main] INFO  nextflow.cli.CmdRun - Pulling epi2me-labs/wf-alignment ...
Apr-08 12:40:16.193 [main] DEBUG nextflow.scm.RepositoryProvider - Request [credentials -:-] -> https://api.github.com/repos/epi2me-labs/wf-alignment/contents/nextflow.config
Apr-08 12:40:19.812 [main] DEBUG nextflow.scm.RepositoryProvider - Request [credentials -:-] -> https://api.github.com/repos/epi2me-labs/wf-alignment/contents/main.nf
Apr-08 12:40:19.923 [main] DEBUG nextflow.scm.RepositoryProvider - Request [credentials -:-] -> https://api.github.com/repos/epi2me-labs/wf-alignment
Apr-08 12:40:20.014 [main] DEBUG nextflow.scm.AssetManager - Pulling epi2me-labs/wf-alignment -- Using remote clone url: https://github.com/epi2me-labs/wf-alignment.git
Apr-08 12:40:23.680 [main] INFO  nextflow.cli.CmdRun -  downloaded from https://github.com/epi2me-labs/wf-alignment.git
Apr-08 12:40:23.744 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /u/abahcheli/.nextflow/assets/epi2me-labs/wf-alignment/nextflow.config
Apr-08 12:40:23.746 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /u/abahcheli/.nextflow/assets/epi2me-labs/wf-alignment/nextflow.config
Apr-08 12:40:23.761 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
Apr-08 12:40:23.947 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declararion
Apr-08 12:40:23.950 [main] INFO  nextflow.cli.CmdRun - Launching `https://github.com/epi2me-labs/wf-alignment` [spontaneous_mendel] DSL2 - revision: e1fd7a51dc [master]
Apr-08 12:40:23.950 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Apr-08 12:40:23.950 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Apr-08 12:40:23.972 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /u/abahcheli/.nextflow/secrets/store.json
Apr-08 12:40:23.981 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@234cd86c] - activable => nextflow.secret.LocalSecretsProvider@234cd86c
Apr-08 12:40:24.059 [main] DEBUG nextflow.Session - Session UUID: cd7a9819-2ad5-4720-b8c2-d23dd90b474c
Apr-08 12:40:24.060 [main] DEBUG nextflow.Session - Run name: spontaneous_mendel
Apr-08 12:40:24.060 [main] DEBUG nextflow.Session - Executor pool size: 8
Apr-08 12:40:24.072 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Apr-08 12:40:24.250 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 23.10.1 build 5891
  Created: 12-01-2024 22:01 UTC (17:01 EDT)
  System: Linux 5.4.0-169-generic
  Runtime: Groovy 3.0.19 on OpenJDK 64-Bit Server VM 21-internal-adhoc.conda.src
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [10.30.0.78]
  CPUs: 8 - Mem: 20 GB (19.6 GB) - Swap: 0 (0) - Virtual threads ON
Apr-08 12:40:24.317 [main] DEBUG nextflow.Session - Work-dir: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/epi2me_nextflow/work [nfs]
Apr-08 12:40:24.360 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Apr-08 12:40:24.383 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Apr-08 12:40:24.480 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Apr-08 12:40:24.496 [main] DEBUG nextflow.util.CustomPoolFactory - Creating virtual thread pool
Apr-08 12:40:24.692 [main] DEBUG nextflow.Session - Session start
Apr-08 12:40:24.699 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/epi2me_nextflow/output/execution/trace.txt
Apr-08 12:40:24.779 [main] DEBUG nextflow.Session - Using default localLib path: /u/abahcheli/.nextflow/assets/epi2me-labs/wf-alignment/lib
Apr-08 12:40:24.787 [main] DEBUG nextflow.Session - Adding to the classpath library: /u/abahcheli/.nextflow/assets/epi2me-labs/wf-alignment/lib
Apr-08 12:40:24.788 [main] DEBUG nextflow.Session - Adding to the classpath library: /u/abahcheli/.nextflow/assets/epi2me-labs/wf-alignment/lib/nfcore_external_java_deps.jar
Apr-08 12:40:27.641 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Apr-08 12:40:28.536 [main] ERROR nextflow.Nextflow - Validation of pipeline parameters failed!
Apr-08 12:40:28.548 [main] ERROR nextflow.Nextflow - * Missing required parameter: --references
Apr-08 12:40:28.549 [main] ERROR nextflow.Nextflow - * Bad parameter configuration. You must select only one option of:
    --fastq
    --bam
