#!/bin/bash
#$ -P reimandlab
#$ -N wf-human_variation_RLGS11-blood
#$ -l h_vmem=10G,h_rt=7:0:0:0
#$ -q all.q
#$ -o /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/nanopore/
#$ -e /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/nanopore/

source /u/abahcheli/.profile
source /u/abahcheli/.bashrc
source /etc/profile.d/uge_settings.sh

source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh

mamba activate nextflow

cd /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore_human_variation/RLGS11-blood

export NXF_SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/


nextflow run /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/nextflow_pipelines_offline/epi2me-labs-wf-human-variation_v2.2.0/v2_2_0 \
--bam /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore_human_variation/RLGS11-blood/RLGS11-blood.pass.bam \
--ref /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore_human_variation/hg38_chr.fa \
--sv --snp --mod --cnv --str --phased \
--sample_name RLGS11-blood --sex XY \
--output_gene_summary \
--basecaller_cfg dna_r9.4.1_e8_hac@v3.3 \
--out_dir /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore_human_variation/RLGS11-blood \
--ubam_map_threads 20 \
-profile singularity \
-c /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/epi2me_nextflow/human_variation/nextflow.config \
-resume


