#!/bin/bash
#$ -P reimandlab
#$ -N wf-human_variation_RLGS11-blood
#$ -l h_vmem=10G,h_rt=7:0:0:0
#$ -q all.q
#$ -o /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/nanopore/
#$ -e /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/nanopore/

source /u/abahcheli/.profile
source /u/abahcheli/.bashrc
source /etc/profile.d/uge_settings.sh

source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh

mamba activate nextflow

cd /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore_human_variation/RLGS11-blood

export NXF_SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export NXF_OFFLINE='true'
export NXF_HOME="/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore_human_variation/RLGS11-blood/.nextflow"

nextflow run /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/nextflow_pipelines_offline/epi2me-labs-wf-human-variation_v2.3.0/v2_3_0 --bam /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore/RLGS11-blood/RLGS11-blood.pass.bam --ref /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/Homo_sapiens_assembly38.fasta --sv --cnv --bam_min_coverage 0 --sample_name RLGS11-blood --sex XY --ubam_map_threads 10 --override_basecaller_cfg dna_r9.4.1_e8_sup@v3.3 --out_dir /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore_human_variation/RLGS11-blood -work-dir /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore_human_variation/RLGS11-blood/work -profile singularity -c /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/epi2me_nextflow/nextflow.config -resume

