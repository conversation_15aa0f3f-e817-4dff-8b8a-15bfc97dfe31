process {
    executor = "sge"
    penv = "smp"
    memory = '40G'
    cpus = '4'
    time = '12h'
    clusterOptions = { "-V -l h_vmem=40G -V -P reimandlab -l h_stack=32M -l h_rt=48:00:00 -pe smp 4" }

    withName: 'summary' {
        cpus = '5'
        memory = '20G'
    }

    withName: 'minimap2_alignment' {
        cpus = '30'
        clusterOptions = { "-V -l h_vmem=3G -V -P reimandlab -l h_rt=48:00:00 -pe smp 30" }
    }

    withName: makeReport {
        memory = '48G'
        cpus = '4'
        clusterOptions = { "-V -l h_vmem=48G -V -P reimandlab -l h_stack=32M -l h_rt=48:00:00 -pe smp 4" }
    }
}

executor {
    name = "sge"
    queueSize = 100
    queueStatInterval = "10s"
}

singularity {
    enabled = true
    autoMounts = true
}

