Apr-23 15:13:01.212 [main] DEBUG nextflow.cli.Launcher - $> nextflow run isugifNF/GATK --help
Apr-23 15:13:01.655 [main] INFO  nextflow.cli.CmdRun - N E X T F L O W  ~  version 23.10.1
Apr-23 15:13:01.686 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/u/abahcheli/.nextflow/plugins; core-plugins: nf-amazon@2.1.4,nf-azure@1.3.3,nf-cloudcache@0.3.0,nf-codecommit@0.1.5,nf-console@1.0.6,nf-ga4gh@1.1.0,nf-google@1.8.3,nf-tower@1.6.3,nf-wave@1.0.1
Apr-23 15:13:01.705 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Apr-23 15:13:01.706 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Apr-23 15:13:01.709 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.4.1 in 'deployment' mode
Apr-23 15:13:01.744 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Apr-23 15:13:01.903 [main] DEBUG nextflow.scm.ProviderConfig - Using SCM config path: /u/abahcheli/.nextflow/scm
Apr-23 15:13:01.937 [main] DEBUG nextflow.scm.RepositoryFactory - Found Git repository result: [RepositoryFactory]
Apr-23 15:13:02.026 [main] INFO  nextflow.cli.CmdRun - Pulling isugifNF/GATK ...
Apr-23 15:13:02.037 [main] DEBUG nextflow.scm.RepositoryProvider - Request [credentials -:-] -> https://api.github.com/repos/isugifNF/GATK/contents/nextflow.config
Apr-23 15:13:05.907 [main] DEBUG nextflow.scm.RepositoryProvider - Request [credentials -:-] -> https://api.github.com/repos/isugifNF/GATK/contents/main.nf
Apr-23 15:13:06.106 [main] DEBUG nextflow.scm.RepositoryProvider - Request [credentials -:-] -> https://api.github.com/repos/isugifNF/GATK
Apr-23 15:13:06.337 [main] DEBUG nextflow.scm.AssetManager - Pulling isugifNF/GATK -- Using remote clone url: https://github.com/isugifNF/GATK-flow.git
Apr-23 15:13:08.481 [main] INFO  nextflow.cli.CmdRun -  downloaded from https://github.com/isugifNF/GATK-flow.git
Apr-23 15:13:08.554 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /u/abahcheli/.nextflow/assets/isugifNF/GATK/nextflow.config
Apr-23 15:13:08.554 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/epi2me_nextflow/nextflow.config
Apr-23 15:13:08.555 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /u/abahcheli/.nextflow/assets/isugifNF/GATK/nextflow.config
Apr-23 15:13:08.555 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/epi2me_nextflow/nextflow.config
Apr-23 15:13:08.565 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
Apr-23 15:13:08.669 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
Apr-23 15:13:08.750 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declararion
Apr-23 15:13:08.750 [main] INFO  nextflow.cli.CmdRun - Launching `https://github.com/isugifNF/GATK` [infallible_angela] DSL2 - revision: 73bfc89127 [master]
Apr-23 15:13:08.751 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Apr-23 15:13:08.751 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Apr-23 15:13:08.755 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /u/abahcheli/.nextflow/secrets/store.json
Apr-23 15:13:08.759 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@7b14c61] - activable => nextflow.secret.LocalSecretsProvider@7b14c61
Apr-23 15:13:08.808 [main] DEBUG nextflow.Session - Session UUID: 88a807fe-4cdd-4b30-946a-d335e94fb267
Apr-23 15:13:08.808 [main] DEBUG nextflow.Session - Run name: infallible_angela
Apr-23 15:13:08.809 [main] DEBUG nextflow.Session - Executor pool size: 8
Apr-23 15:13:08.827 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Apr-23 15:13:08.925 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 23.10.1 build 5891
  Created: 12-01-2024 22:01 UTC (17:01 EDT)
  System: Linux 5.4.0-176-generic
  Runtime: Groovy 3.0.19 on OpenJDK 64-Bit Server VM 21-internal-adhoc.conda.src
  Encoding: UTF-8 (ISO-8859-1)
  Process: <EMAIL> [**********]
  CPUs: 8 - Mem: 31.2 GB (1.1 GB) - Swap: 15.3 GB (13.6 GB) - Virtual threads ON
Apr-23 15:13:08.976 [main] DEBUG nextflow.Session - Work-dir: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/epi2me_nextflow/work [nfs]
Apr-23 15:13:08.977 [main] DEBUG nextflow.Session - Script base path does not exist or is not a directory: /u/abahcheli/.nextflow/assets/isugifNF/GATK/bin
Apr-23 15:13:08.985 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Apr-23 15:13:09.022 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Apr-23 15:13:09.108 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Apr-23 15:13:09.115 [main] DEBUG nextflow.util.CustomPoolFactory - Creating virtual thread pool
Apr-23 15:13:09.532 [main] DEBUG nextflow.Session - Session start
Apr-23 15:13:09.907 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Apr-23 15:13:11.655 [main] INFO  nextflow.Nextflow - 
   Usage:
   The typical command for running the pipeline are as follows:

   DNAseq:
     nextflow run main.nf --genome GENOME.fasta --reads "*_{R1,R2}.fastq.gz" --seq "dna" -profile singularity
     nextflow run main.nf --genome GENOME.fasta --reads_file READ_PATHS.txt --seq "dna" -profile singularity

   RNAseq:
     nextflow run main.nf --genome GENOME.fasta --gtf "genes.gtf" --reads "*_{R1,R2}.fastq.gz" --seq "rna" -profile singularity
  
   PacBio Long Reads:
     nextflow run main.nf --genome GENOME.fasta --long_reads "*.fastq.gz" --seq "longread" -profile singularity

   Mandatory arguments:
    --seq                   Specify input sequence type as 'dna', 'rna', or 'longread' [default:'dna'].
    --genome                Reference genome fasta file, against which reads will be mapped to find Variant sites

   Read input arguments:
    --reads                 Paired-end reads in fastq.gz format, will need to specify glob (e.g. "*_{R1,R2}.fastq.gz")
    --reads_file            Text file (tab delimited) with three columns [readname left_fastq.gz right_fastq.gz]. Will need full path for files.
    --long_reads            Long read file in fastq.gz format, will need to specify glob (e.g. "*.fastq.gz")

   Optional analysis arguments:
    --invariant             Output invariant sites [default:false]
    --gtf                   Gene Transfer Format file, only required for RNAseq input [default:false]
    --window                Window size passed to bedtools for parallel GATK Haplotype calls [default:100000]

   Optional configuration arguments:
    -profile                Configuration profile to use. Can use multiple (comma separated)
                            Available: local, slurm, singularity, docker [default:local]
    --container_img         Container image used for singularity and docker [default:'docker://ghcr.io/aseetharam/gatk:master']
    
   GATK:
    --gatk_app              Link to gatk executable [default: 'gatk']
    --java_options          Java options for gatk [default:'-Xmx80g -XX:+UseParallelGC -Djava.io.tmpdir=$TMPDIR']
    --gatk_cluster_options  GATK cluster options [default:'false']
    --gatk_haplotype_caller_params  Additional parameters to pass to GATK HaplotypeCaller [default:'']
    
   Aligners:
    --bwamem2_app           Link to bwamem2 executable [default: 'bwa-mem2']
    --star_app              Link to star executable [default: 'STAR']
    --star_index_params     Parameters for star index [default: ' ']
    --star_index_params     Parameters to pass to STAR index [default:'']
    --star_index_file       Optional: speedup by providing a prebuilt STAR indexed genome [default: 'false']
    --pbmm2_app             Link to pbmm2 executable [default: 'pbmm2']

   Other:
    --samtools_app          Link to samtools executable [default: 'samtools']
    --bedtools_app          Link to bedtools executable [default: 'bedtools']
    --datamash_app          Link to datamash executable [default: 'datamash']
    --vcftools_app          Link to vcftools executable [default: 'vcftools']

   Optional other arguments:
    --outdir                Output directory [default:'./GATK_Results']
    --threads               Threads per process [default:4 for local, 16 for slurm] 
    --queueSize             Maximum jobs to submit to slurm [default:40]
    --account               HPC account name for slurm sbatch, atlas and ceres requires this
    --help                  Print this help message
 

