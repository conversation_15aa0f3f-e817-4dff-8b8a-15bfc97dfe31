process {
    executor = "uge"
    penv = "smp"
    memory = '24G'
    cpus = '1'
    time = '12h'
    clusterOptions = { "-V -l h_vmem=24G -V -P reimandlab -l h_stack=32M -l h_rt=7:00:00:00 -q all.q" }

    withName: makeReport {
        memory = '48G'
        cpus = '1'
        clusterOptions = { "-V -l h_vmem=48G -V -P reimandlab -l h_stack=32M -l h_rt=48:00:00" }
    }

    withName: dorado {
        clusterOptions = { "-V -l h_vmem=8G -V -P reimandlab -l gpu=2 -q gpu-short.q -l h_rt=2:0:0" }
    }

}

executor {
    name = "uge"
    queueSize = 500
    queueStatInterval = "10s"
}
