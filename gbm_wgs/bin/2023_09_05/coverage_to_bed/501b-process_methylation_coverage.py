# <PERSON> Bahcheli
import argparse
import pandas as pd
import numpy as np
import gzip
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--primary_bed", required=True)
    parser.add_argument("--recurrent_bed", required=True)
    parser.add_argument("--output_bed", required=True)
    return parser.parse_args()


def load_methylation_bed(bed_file):
    """Load bedMethyl format file"""
    if bed_file.endswith('.gz'):
        df = pd.read_csv(bed_file, sep='\t', header=None, compression='gzip')
    else:
        df = pd.read_csv(bed_file, sep='\t', header=None)

    # Split the last column based on spaces (following the existing pattern)
    split_columns = df.iloc[:, -1].str.split(expand=True)

    # Assign the split columns back to the original DataFrame
    df = pd.concat([df.iloc[:, :-1], split_columns], axis=1)

    # bedMethyl format columns based on existing script
    columns = ['chr', 'start', 'end', 'modified_base_code', 'score', 'strand', 'start_pos', 'end_pos', 'color', 'Nvalid_cov', 'percent_modified', 'Nmod', 'Ncanonical', 'Nother_mod', 'Ndelete', 'Nfail', 'Ndiff', 'Nnocall']
    df.columns = columns

    # Keep only relevant columns
    df = df[['chr', 'start', 'end', 'percent_modified', 'Nvalid_cov']].copy()

    # Convert to numeric
    df['percent_modified'] = pd.to_numeric(df['percent_modified'], errors='coerce')
    df['Nvalid_cov'] = pd.to_numeric(df['Nvalid_cov'], errors='coerce')

    # Remove rows with invalid data
    df = df.dropna()

    return df


def bin_methylation_to_1kb(df):
    """Convert methylation data to 1kb bins"""
    # Create 1kb bins
    df['bin_start'] = (df['start'] // 1000) * 1000
    df['bin_end'] = df['bin_start'] + 1000

    # Weight methylation by coverage
    df['weighted_methylation'] = df['percent_modified'] * df['Nvalid_cov']

    # Group by chromosome and 1kb bins
    binned_df = df.groupby(['chr', 'bin_start', 'bin_end']).agg({
        'weighted_methylation': 'sum',
        'Nvalid_cov': 'sum'
    }).reset_index()

    # Calculate average methylation per bin
    binned_df['methylation_pct'] = binned_df['weighted_methylation'] / binned_df['Nvalid_cov']
    binned_df = binned_df[binned_df['Nvalid_cov'] > 0]  # Remove bins with no coverage

    binned_df = binned_df[['chr', 'bin_start', 'bin_end', 'methylation_pct']].copy()
    binned_df.columns = ['chr', 'start', 'end', 'methylation_pct']

    return binned_df


def calculate_methylation_difference(primary_df, recurrent_df):
    """Calculate methylation difference (recurrent - primary)"""
    # Note: correcting the naming - primary_bed is actually primary tumor, recurrent_bed is recurrent tumor
    # Merge primary and recurrent data
    merged_df = pd.merge(primary_df, recurrent_df, on=['chr', 'start', 'end'], 
                        suffixes=('_primary', '_recurrent'), how='outer')
    
    # Fill missing values with 0
    merged_df = merged_df.fillna(0)
    
    # Calculate methylation difference (recurrent - primary)
    merged_df['methylation_diff'] = merged_df['methylation_pct_recurrent'] - merged_df['methylation_pct_primary']
    
    # Create output dataframe
    output_df = merged_df[['chr', 'start', 'end', 'methylation_diff']].copy()
    
    return output_df


def main():
    """Main function"""
    args = parse_arguments()
    
    # Create output directory
    os.makedirs(os.path.dirname(args.output_bed), exist_ok=True)
    
    # Load methylation data
    # Note: The file naming is confusing - "normal" file is actually primary tumor, "tumor" file is recurrent tumor
    primary_df = load_methylation_bed(args.primary_bed)  # This is labeled "normal" but is primary tumor
    recurrent_df = load_methylation_bed(args.recurrent_bed)  # This is labeled "tumor" but is recurrent tumor
    
    # Convert to 1kb bins
    primary_1kb = bin_methylation_to_1kb(primary_df)
    recurrent_1kb = bin_methylation_to_1kb(recurrent_df)
    
    # Calculate methylation difference
    diff_df = calculate_methylation_difference(primary_1kb, recurrent_1kb)
    
    # Sort by chromosome and position
    diff_df = diff_df.sort_values(['chr', 'start'])
    
    # Save results
    diff_df.to_csv(args.output_bed, sep='\t', index=False, header=False)


if __name__ == "__main__":
    main()
