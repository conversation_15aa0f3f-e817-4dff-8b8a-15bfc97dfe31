# Alec Bahcheli
import argparse
import pandas as pd
import numpy as np
import gzip
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--primary_bed", required=True)
    parser.add_argument("--recurrent_bed", required=True)
    parser.add_argument("--output_bed", required=True)
    return parser.parse_args()


def load_methylation_bed(bed_file):
    """Load bedMethyl format file"""
    if bed_file.endswith('.gz'):
        df = pd.read_csv(bed_file, sep='\t', header=None, compression='gzip')
    else:
        df = pd.read_csv(bed_file, sep='\t', header=None)
    
    # bedMethyl format: chr, start, end, name, score, strand, thickStart, thickEnd, itemRgb, coverage, freqC, freqT, freqG, freqA, freqN, freqCpG, freqCHG, freqCHH
    df.columns = ['chr', 'start', 'end', 'name', 'score', 'strand', 'thick_start', 'thick_end', 'item_rgb', 'coverage', 'freq_c', 'freq_t', 'freq_g', 'freq_a', 'freq_n', 'freq_cpg', 'freq_chg', 'freq_chh']
    
    # Extract methylation percentage from the coverage field (format: coverage percentage)
    coverage_info = df['coverage'].astype(str).str.split(' ', expand=True)
    df['read_coverage'] = pd.to_numeric(coverage_info[0], errors='coerce')
    df['methylation_pct'] = pd.to_numeric(coverage_info[1], errors='coerce')
    
    # Keep only relevant columns
    df = df[['chr', 'start', 'end', 'read_coverage', 'methylation_pct']].copy()
    
    return df


def bin_methylation_to_1kb(df):
    """Convert methylation data to 1kb bins"""
    # Create 1kb bins
    df['bin_start'] = (df['start'] // 1000) * 1000
    df['bin_end'] = df['bin_start'] + 1000
    
    # Weight methylation by coverage
    df['weighted_methylation'] = df['methylation_pct'] * df['read_coverage']
    
    # Group by chromosome and 1kb bins
    binned_df = df.groupby(['chr', 'bin_start', 'bin_end']).agg({
        'weighted_methylation': 'sum',
        'read_coverage': 'sum'
    }).reset_index()
    
    # Calculate average methylation per bin
    binned_df['methylation_pct'] = binned_df['weighted_methylation'] / binned_df['read_coverage']
    binned_df = binned_df[binned_df['read_coverage'] > 0]  # Remove bins with no coverage
    
    binned_df = binned_df[['chr', 'bin_start', 'bin_end', 'methylation_pct']].copy()
    binned_df.columns = ['chr', 'start', 'end', 'methylation_pct']
    
    return binned_df


def calculate_methylation_difference(primary_df, recurrent_df):
    """Calculate methylation difference (recurrent - primary)"""
    # Note: correcting the naming - primary_bed is actually primary tumor, recurrent_bed is recurrent tumor
    # Merge primary and recurrent data
    merged_df = pd.merge(primary_df, recurrent_df, on=['chr', 'start', 'end'], 
                        suffixes=('_primary', '_recurrent'), how='outer')
    
    # Fill missing values with 0
    merged_df = merged_df.fillna(0)
    
    # Calculate methylation difference (recurrent - primary)
    merged_df['methylation_diff'] = merged_df['methylation_pct_recurrent'] - merged_df['methylation_pct_primary']
    
    # Create output dataframe
    output_df = merged_df[['chr', 'start', 'end', 'methylation_diff']].copy()
    
    return output_df


def main():
    """Main function"""
    args = parse_arguments()
    
    # Create output directory
    os.makedirs(os.path.dirname(args.output_bed), exist_ok=True)
    
    # Load methylation data
    # Note: The file naming is confusing - "normal" file is actually primary tumor, "tumor" file is recurrent tumor
    primary_df = load_methylation_bed(args.primary_bed)  # This is labeled "normal" but is primary tumor
    recurrent_df = load_methylation_bed(args.recurrent_bed)  # This is labeled "tumor" but is recurrent tumor
    
    # Convert to 1kb bins
    primary_1kb = bin_methylation_to_1kb(primary_df)
    recurrent_1kb = bin_methylation_to_1kb(recurrent_df)
    
    # Calculate methylation difference
    diff_df = calculate_methylation_difference(primary_1kb, recurrent_1kb)
    
    # Sort by chromosome and position
    diff_df = diff_df.sort_values(['chr', 'start'])
    
    # Save results
    diff_df.to_csv(args.output_bed, sep='\t', index=False, header=False)


if __name__ == "__main__":
    main()
