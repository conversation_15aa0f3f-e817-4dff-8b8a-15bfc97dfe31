# <PERSON>i
import argparse
import pandas as pd
import gzip
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--input_bed", required=True)
    parser.add_argument("--output_bed", required=True)
    return parser.parse_args()


def load_mosdepth_bed(bed_file):
    """Load mosdepth regions bed file"""
    if bed_file.endswith('.gz'):
        df = pd.read_csv(bed_file, sep='\t', header=None, compression='gzip')
    else:
        df = pd.read_csv(bed_file, sep='\t', header=None)
    
    df.columns = ['chr', 'start', 'end', 'coverage']
    return df


def convert_chromosome_names(df):
    """Convert NC_ chromosome names to chr format"""
    nc_to_chr = {
        "NC_000001.11": "chr1",
        "NC_000002.12": "chr2",
        "NC_000003.12": "chr3",
        "NC_000004.12": "chr4",
        "NC_000005.10": "chr5",
        "NC_000006.12": "chr6",
        "NC_000007.14": "chr7",
        "NC_000008.11": "chr8",
        "NC_000009.12": "chr9",
        "NC_000010.11": "chr10",
        "NC_000011.10": "chr11",
        "NC_000012.12": "chr12",
        "NC_000013.11": "chr13",
        "NC_000014.9":  "chr14",
        "NC_000015.10": "chr15",
        "NC_000016.10": "chr16",
        "NC_000017.11": "chr17",
        "NC_000018.10": "chr18",
        "NC_000019.10": "chr19",
        "NC_000020.11": "chr20",
        "NC_000021.9":  "chr21",
        "NC_000022.11": "chr22",
        "NC_000023.11": "chrX",
        "NC_000024.10": "chrY",
        "NC_012920.1":  "chrMT"
    }
    
    # Convert chromosome names
    df['chr'] = df['chr'].map(nc_to_chr).fillna(df['chr'])
    
    # Filter to only keep standard chromosomes
    standard_chrs = [f"chr{i}" for i in range(1, 23)] + ["chrX", "chrY", "chrMT"]
    df = df[df['chr'].isin(standard_chrs)]
    
    return df


def main():
    """Main function"""
    args = parse_arguments()
    
    # Create output directory
    os.makedirs(os.path.dirname(args.output_bed), exist_ok=True)
    
    # Load mosdepth coverage data
    df = load_mosdepth_bed(args.input_bed)
    
    # Convert chromosome names
    df = convert_chromosome_names(df)
    
    # Sort by chromosome and position
    df = df.sort_values(['chr', 'start'])
    
    # Save results
    df.to_csv(args.output_bed, sep='\t', index=False, header=False)


if __name__ == "__main__":
    main()
