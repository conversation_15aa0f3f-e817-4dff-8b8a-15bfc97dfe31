# Alec Bahcheli
import argparse
import pandas as pd
import numpy as np
import gzip
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--normal_bed", required=True)
    parser.add_argument("--tumor_bed", required=True)
    parser.add_argument("--output_bed", required=True)
    return parser.parse_args()


def load_coverage_bed(bed_file):
    """Load coverage bed file"""
    if bed_file.endswith('.gz'):
        df = pd.read_csv(bed_file, sep='\t', header=None, compression='gzip')
    else:
        df = pd.read_csv(bed_file, sep='\t', header=None)
    
    df.columns = ['chr', 'start', 'end', 'coverage']
    return df


def bin_to_1kb(df):
    """Convert coverage data to 1kb bins"""
    # Create 1kb bins
    df['bin_start'] = (df['start'] // 1000) * 1000
    df['bin_end'] = df['bin_start'] + 1000
    
    # Calculate weighted coverage for each bin
    df['interval_length'] = df['end'] - df['start']
    df['weighted_coverage'] = df['coverage'] * df['interval_length']
    
    # Group by chromosome and 1kb bins
    binned_df = df.groupby(['chr', 'bin_start', 'bin_end']).agg({
        'weighted_coverage': 'sum',
        'interval_length': 'sum'
    }).reset_index()
    
    # Calculate average coverage per bin
    binned_df['coverage'] = binned_df['weighted_coverage'] / binned_df['interval_length']
    binned_df = binned_df[['chr', 'bin_start', 'bin_end', 'coverage']]
    binned_df.columns = ['chr', 'start', 'end', 'coverage']
    
    return binned_df


def calculate_log2_ratio(normal_df, tumor_df):
    """Calculate log2(tumor/normal) ratio"""
    # Merge normal and tumor data
    merged_df = pd.merge(normal_df, tumor_df, on=['chr', 'start', 'end'], 
                        suffixes=('_normal', '_tumor'), how='outer')
    
    # Fill missing values with 0
    merged_df = merged_df.fillna(0)
    
    # Add pseudocount to avoid log(0)
    pseudocount = 0.1
    merged_df['coverage_normal'] = merged_df['coverage_normal'] + pseudocount
    merged_df['coverage_tumor'] = merged_df['coverage_tumor'] + pseudocount
    
    # Calculate log2 ratio
    merged_df['log2_ratio'] = np.log2(merged_df['coverage_tumor'] / merged_df['coverage_normal'])
    
    # Create output dataframe
    output_df = merged_df[['chr', 'start', 'end', 'log2_ratio']].copy()
    
    return output_df


def main():
    """Main function"""
    args = parse_arguments()
    
    # Create output directory
    os.makedirs(os.path.dirname(args.output_bed), exist_ok=True)
    
    # Load coverage data
    normal_df = load_coverage_bed(args.normal_bed)
    tumor_df = load_coverage_bed(args.tumor_bed)
    
    # Convert to 1kb bins
    normal_1kb = bin_to_1kb(normal_df)
    tumor_1kb = bin_to_1kb(tumor_df)
    
    # Calculate log2 ratio
    ratio_df = calculate_log2_ratio(normal_1kb, tumor_1kb)
    
    # Sort by chromosome and position
    ratio_df = ratio_df.sort_values(['chr', 'start'])
    
    # Save results
    ratio_df.to_csv(args.output_bed, sep='\t', index=False, header=False)


if __name__ == "__main__":
    main()
