# <PERSON>
# Test script for subsetting and compressing coverage files
import subprocess
import os
import gzip

def create_test_data():
    """Create test coverage data with various chromosomes"""
    print("Creating test coverage data...")
    
    # Create output directory
    os.makedirs("/Users/<USER>/Desktop/tmp", exist_ok=True)
    
    # Create test data with various chromosomes including non-standard ones
    test_data = [
        "chr1\t0\t1000\t2.5",
        "chr1\t1000\t2000\t3.2",
        "chr2\t0\t1000\t1.8",
        "chr22\t0\t1000\t4.1",
        "chrX\t0\t1000\t2.9",
        "chrY\t0\t1000\t1.2",
        "chrMT\t0\t1000\t15.6",  # Should be filtered out
        "HLA-A*01:01:01:01\t0\t1000\t2.8",  # Should be filtered out
        "chr3\t0\t1000\t3.7",
        "chr21\t0\t1000\t2.1"
    ]
    
    test_file = "/Users/<USER>/Desktop/tmp/test_coverage.bed"
    with open(test_file, 'w') as f:
        for line in test_data:
            f.write(line + '\n')
    
    print(f"Test data created: {test_file}")
    return test_file


def test_subset_and_compress():
    """Test subsetting and compression"""
    print("Testing subset and compression...")
    
    # Create test data
    test_file = create_test_data()
    
    cmd = [
        "/Users/<USER>/opt/miniconda3/envs/py_dev/bin/python",
        "/Users/<USER>/Library/CloudStorage/Dropbox/phd/reimand_lab_ab/gbm_wgs/bin/2023_09_05/coverage_to_bed/501f-subset_and_compress_coverage.py",
        "--input_bed", test_file,
        "--output_bed", "/Users/<USER>/Desktop/tmp/test_coverage_subset.bed.gz"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("Subset and compression: SUCCESS")
        print(f"Output saved to: /Users/<USER>/Desktop/tmp/test_coverage_subset.bed.gz")
        
        # Check the output
        with gzip.open("/Users/<USER>/Desktop/tmp/test_coverage_subset.bed.gz", 'rt') as f:
            lines = f.readlines()
            print(f"Output contains {len(lines)} lines:")
            for line in lines:
                print(f"  {line.strip()}")
                
    else:
        print("Subset and compression: FAILED")
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)


def test_with_existing_files():
    """Test with existing coverage files"""
    print("Testing with existing coverage files...")
    
    # Test with DNA coverage file
    if os.path.exists("/Users/<USER>/Desktop/tmp/RLGS1-primary_dna_coverage_1kb.bed"):
        cmd = [
            "/Users/<USER>/opt/miniconda3/envs/py_dev/bin/python",
            "/Users/<USER>/Library/CloudStorage/Dropbox/phd/reimand_lab_ab/gbm_wgs/bin/2023_09_05/coverage_to_bed/501f-subset_and_compress_coverage.py",
            "--input_bed", "/Users/<USER>/Desktop/tmp/RLGS1-primary_dna_coverage_1kb.bed",
            "--output_bed", "/Users/<USER>/Desktop/tmp/all_coverage_tracks/RLGS1-primary_dna_coverage_1kb.bed.gz"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("DNA coverage subset and compression: SUCCESS")
            
            # Check file size
            original_size = os.path.getsize("/Users/<USER>/Desktop/tmp/RLGS1-primary_dna_coverage_1kb.bed")
            compressed_size = os.path.getsize("/Users/<USER>/Desktop/tmp/all_coverage_tracks/RLGS1-primary_dna_coverage_1kb.bed.gz")
            compression_ratio = compressed_size / original_size * 100
            
            print(f"Original size: {original_size:,} bytes")
            print(f"Compressed size: {compressed_size:,} bytes")
            print(f"Compression ratio: {compression_ratio:.1f}%")
            
        else:
            print("DNA coverage subset and compression: FAILED")
            print("STDERR:", result.stderr)
    else:
        print("No existing DNA coverage file found for testing")


def main():
    """Run all tests"""
    print("Starting subset and compression tests...")
    print("=" * 50)
    
    test_subset_and_compress()
    print()
    
    test_with_existing_files()
    print()
    
    print("=" * 50)
    print("Testing complete!")


if __name__ == "__main__":
    main()
