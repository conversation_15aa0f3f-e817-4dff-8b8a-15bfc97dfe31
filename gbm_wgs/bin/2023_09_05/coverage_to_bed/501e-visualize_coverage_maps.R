# <PERSON>
library(optparse)
library(ggplot2)
library(dplyr)
library(RColorBrewer)
library(viridis)
library(gridExtra)

# options list for parser options
option_list <- list(
    make_option(c("-a","--combined_bed"), type="character", default=NULL,
            help="Combined coverage bed file",
            dest="combined_bed"),
    make_option(c("-b","--output_plot"), type="character", default=NULL,
            help="Output plot PDF file",
            dest="output_plot")
)

parser <- OptionParser(usage = "%prog -a input.bed -b output.pdf", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}

create_coverage_plots = function(coverage_df) {
    
    # 1. DNA coverage log2 ratio distribution
    p1 <- ggplot(coverage_df, aes(x = dna_log2_ratio)) +
        geom_histogram(bins = 50, alpha = 0.7, fill = "steelblue") +
        labs(title = "DNA Coverage Log2(Tumor/Normal) Distribution", 
             x = "Log2(Tumor/Normal)", y = "Count") +
        plot_theme()
    
    # 2. Methylation difference distribution
    p2 <- ggplot(coverage_df, aes(x = methylation_diff)) +
        geom_histogram(bins = 50, alpha = 0.7, fill = "darkgreen") +
        labs(title = "Methylation Difference Distribution", 
             x = "Methylation Difference (%)", y = "Count") +
        plot_theme()
    
    # 3. RNA-seq coverage distribution
    p3 <- ggplot(coverage_df, aes(x = log10(rnaseq_coverage + 1))) +
        geom_histogram(bins = 50, alpha = 0.7, fill = "darkred") +
        labs(title = "RNA-seq Coverage Distribution", 
             x = "Log10(Coverage + 1)", y = "Count") +
        plot_theme()
    
    # 4. Correlation plot: DNA vs RNA-seq
    p4 <- ggplot(coverage_df, aes(x = dna_log2_ratio, y = log10(rnaseq_coverage + 1))) +
        geom_point(alpha = 0.3, size = 0.5) +
        geom_smooth(method = "lm", color = "red") +
        labs(title = "DNA Coverage vs RNA-seq Coverage", 
             x = "DNA Log2(Tumor/Normal)", y = "Log10(RNA-seq Coverage + 1)") +
        plot_theme()
    
    # 5. Correlation plot: DNA vs Methylation
    p5 <- ggplot(coverage_df, aes(x = dna_log2_ratio, y = methylation_diff)) +
        geom_point(alpha = 0.3, size = 0.5) +
        geom_smooth(method = "lm", color = "red") +
        labs(title = "DNA Coverage vs Methylation Difference", 
             x = "DNA Log2(Tumor/Normal)", y = "Methylation Difference (%)") +
        plot_theme()
    
    # 6. Correlation plot: RNA-seq vs Methylation
    p6 <- ggplot(coverage_df, aes(x = log10(rnaseq_coverage + 1), y = methylation_diff)) +
        geom_point(alpha = 0.3, size = 0.5) +
        geom_smooth(method = "lm", color = "red") +
        labs(title = "RNA-seq Coverage vs Methylation Difference", 
             x = "Log10(RNA-seq Coverage + 1)", y = "Methylation Difference (%)") +
        plot_theme()
    
    # Print all plots
    print(p1)
    print(p2)
    print(p3)
    print(p4)
    print(p5)
    print(p6)
    
    # Combined plot
    combined_plot = grid.arrange(p1, p2, p3, p4, p5, p6, ncol = 2)
    print(combined_plot)
    
    return()
}

pdf(opt$output_plot, width = 16, height = 20)

# Load data
coverage_df <- read.csv(opt$combined_bed, sep='\t')

# Create plots
create_coverage_plots(coverage_df)

dev.off()
