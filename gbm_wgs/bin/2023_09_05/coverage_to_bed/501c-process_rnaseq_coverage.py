# Alec Bahcheli
import argparse
import pandas as pd
import numpy as np
import subprocess
import tempfile
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--forward_bw", required=True)
    parser.add_argument("--reverse_bw", required=True)
    parser.add_argument("--output_bed", required=True)
    return parser.parse_args()


def create_genome_size_file(temp_dir):
    """Create genome size file for bedtools"""
    # Standard human chromosome sizes (hg38/GRCh38)
    chr_sizes = {
        'chr1': 248956422, 'chr2': 242193529, 'chr3': 198295559, 'chr4': 190214555,
        'chr5': 181538259, 'chr6': 170805979, 'chr7': 159345973, 'chr8': 145138636,
        'chr9': 138394717, 'chr10': 133797422, 'chr11': 135086622, 'chr12': 133275309,
        'chr13': 114364328, 'chr14': 107043718, 'chr15': 101991189, 'chr16': 90338345,
        'chr17': 83257441, 'chr18': 80373285, 'chr19': 58617616, 'chr20': 64444167,
        'chr21': 46709983, 'chr22': 50818468, 'chrX': 156040895, 'chrY': 57227415
    }

    genome_file = os.path.join(temp_dir, "genome.chrom.sizes")
    with open(genome_file, 'w') as f:
        for chrom, size in chr_sizes.items():
            f.write(f"{chrom}\t{size}\n")

    return genome_file


def create_1kb_bins(genome_file, temp_dir):
    """Create 1kb bins using bedtools makewindows"""
    bins_file = os.path.join(temp_dir, "genome.1kb.bed")

    cmd = ['bedtools', 'makewindows', '-g', genome_file, '-w', '1000']

    with open(bins_file, 'w') as f:
        result = subprocess.run(cmd, stdout=f, stderr=subprocess.PIPE, text=True)

    if result.returncode != 0:
        raise RuntimeError(f"bedtools makewindows failed: {result.stderr}")

    return bins_file


def bigwig_to_bedgraph(bw_file, temp_dir):
    """Convert bigWig to bedGraph using bigWigToBedGraph"""
    bedgraph_file = os.path.join(temp_dir, f"{os.path.basename(bw_file)}.bedgraph")

    cmd = ['bigWigToBedGraph', bw_file, bedgraph_file]

    try:
        subprocess.run(cmd, check=True, capture_output=True)
        return bedgraph_file
    except subprocess.CalledProcessError:
        # If bigWigToBedGraph is not available, try alternative approach
        print(f"Warning: bigWigToBedGraph not found. Skipping {bw_file}")
        return None


def calculate_coverage_with_bedtools(bedgraph_file, bins_file, temp_dir):
    """Use bedtools map to calculate coverage for 1kb bins"""
    if bedgraph_file is None or not os.path.exists(bedgraph_file):
        return None

    output_file = os.path.join(temp_dir, f"{os.path.basename(bedgraph_file)}.coverage.bed")

    # Use bedtools map to calculate mean coverage for each 1kb bin
    cmd = ['bedtools', 'map', '-a', bins_file, '-b', bedgraph_file, '-c', '4', '-o', 'mean']

    with open(output_file, 'w') as f:
        result = subprocess.run(cmd, stdout=f, stderr=subprocess.PIPE, text=True)

    if result.returncode != 0:
        print(f"Warning: bedtools map failed for {bedgraph_file}: {result.stderr}")
        return None

    return output_file


def load_coverage_bed(coverage_file):
    """Load coverage bed file"""
    if coverage_file is None or not os.path.exists(coverage_file):
        return pd.DataFrame(columns=['chr', 'start', 'end', 'coverage'])

    df = pd.read_csv(coverage_file, sep='\t', header=None)
    df.columns = ['chr', 'start', 'end', 'coverage']

    # Replace '.' with 0 (bedtools uses '.' for no overlap)
    df['coverage'] = df['coverage'].replace('.', 0)
    df['coverage'] = pd.to_numeric(df['coverage'], errors='coerce').fillna(0)

    return df


def main():
    """Main function"""
    args = parse_arguments()

    # Create output directory
    os.makedirs(os.path.dirname(args.output_bed), exist_ok=True)

    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create genome size file and 1kb bins
        genome_file = create_genome_size_file(temp_dir)
        bins_file = create_1kb_bins(genome_file, temp_dir)

        # Convert bigWig files to bedGraph
        forward_bedgraph = bigwig_to_bedgraph(args.forward_bw, temp_dir)
        reverse_bedgraph = bigwig_to_bedgraph(args.reverse_bw, temp_dir)

        # Calculate coverage for 1kb bins using bedtools
        forward_coverage_file = calculate_coverage_with_bedtools(forward_bedgraph, bins_file, temp_dir)
        reverse_coverage_file = calculate_coverage_with_bedtools(reverse_bedgraph, bins_file, temp_dir)

        # Load coverage data
        forward_df = load_coverage_bed(forward_coverage_file)
        reverse_df = load_coverage_bed(reverse_coverage_file)

        # Load bins as base dataframe
        bins_df = pd.read_csv(bins_file, sep='\t', header=None)
        bins_df.columns = ['chr', 'start', 'end']

        # Merge coverage data with bins
        if not forward_df.empty:
            bins_df = pd.merge(bins_df, forward_df[['chr', 'start', 'end', 'coverage']],
                              on=['chr', 'start', 'end'], how='left', suffixes=('', '_forward'))
            bins_df = bins_df.rename(columns={'coverage': 'forward_coverage'})
        else:
            bins_df['forward_coverage'] = 0.0

        if not reverse_df.empty:
            bins_df = pd.merge(bins_df, reverse_df[['chr', 'start', 'end', 'coverage']],
                              on=['chr', 'start', 'end'], how='left', suffixes=('', '_reverse'))
            bins_df = bins_df.rename(columns={'coverage': 'reverse_coverage'})
        else:
            bins_df['reverse_coverage'] = 0.0

        # Fill missing values with 0
        bins_df['forward_coverage'] = bins_df['forward_coverage'].fillna(0)
        bins_df['reverse_coverage'] = bins_df['reverse_coverage'].fillna(0)

        # Calculate total coverage
        bins_df['total_coverage'] = bins_df['forward_coverage'] + bins_df['reverse_coverage']

        # Keep only relevant columns
        output_df = bins_df[['chr', 'start', 'end', 'total_coverage']].copy()

        # Sort by chromosome and position
        output_df = output_df.sort_values(['chr', 'start'])

        # Save results
        output_df.to_csv(args.output_bed, sep='\t', index=False, header=False)


if __name__ == "__main__":
    main()
