# Alec Bahcheli
import argparse
import pandas as pd
import numpy as np
import subprocess
import tempfile
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--forward_bw", required=True)
    parser.add_argument("--reverse_bw", required=True)
    parser.add_argument("--output_bed", required=True)
    return parser.parse_args()


def create_genome_bins(reference_genome_size_file=None, bin_size=1000):
    """Create 1kb bins across the genome using standard human chromosomes"""
    # Standard human chromosome sizes (hg38/GRCh38)
    chr_sizes = {
        'chr1': 248956422, 'chr2': 242193529, 'chr3': 198295559, 'chr4': 190214555,
        'chr5': 181538259, 'chr6': 170805979, 'chr7': 159345973, 'chr8': 145138636,
        'chr9': 138394717, 'chr10': 133797422, 'chr11': 135086622, 'chr12': 133275309,
        'chr13': 114364328, 'chr14': 107043718, 'chr15': 101991189, 'chr16': 90338345,
        'chr17': 83257441, 'chr18': 80373285, 'chr19': 58617616, 'chr20': 64444167,
        'chr21': 46709983, 'chr22': 50818468, 'chrX': 156040895, 'chrY': 57227415
    }

    bins = []
    for chrom, length in chr_sizes.items():
        for start in range(0, length, bin_size):
            end = min(start + bin_size, length)
            bins.append([chrom, start, end])

    return pd.DataFrame(bins, columns=['chr', 'start', 'end'])


def bigwig_to_bedgraph(bw_file, temp_dir):
    """Convert bigWig to bedGraph using bigWigToBedGraph"""
    bedgraph_file = os.path.join(temp_dir, f"{os.path.basename(bw_file)}.bedgraph")

    cmd = ['bigWigToBedGraph', bw_file, bedgraph_file]

    try:
        subprocess.run(cmd, check=True, capture_output=True)
        return bedgraph_file
    except subprocess.CalledProcessError:
        # If bigWigToBedGraph is not available, try alternative approach
        print(f"Warning: bigWigToBedGraph not found. Skipping {bw_file}")
        return None


def load_bedgraph(bedgraph_file):
    """Load bedGraph file"""
    if bedgraph_file is None or not os.path.exists(bedgraph_file):
        return pd.DataFrame(columns=['chr', 'start', 'end', 'coverage'])

    df = pd.read_csv(bedgraph_file, sep='\t', header=None)
    df.columns = ['chr', 'start', 'end', 'coverage']
    return df


def bin_coverage_to_1kb(coverage_df, bins_df):
    """Bin coverage data to 1kb resolution"""
    if coverage_df.empty:
        bins_df['coverage'] = 0.0
        return bins_df[['chr', 'start', 'end', 'coverage']]

    # Create a dictionary for fast lookup
    coverage_dict = {}
    for _, row in coverage_df.iterrows():
        chrom = row['chr']
        if chrom not in coverage_dict:
            coverage_dict[chrom] = []
        coverage_dict[chrom].append((row['start'], row['end'], row['coverage']))

    # Calculate coverage for each 1kb bin
    bin_coverage = []
    for _, bin_row in bins_df.iterrows():
        chrom = bin_row['chr']
        bin_start = bin_row['start']
        bin_end = bin_row['end']

        total_coverage = 0.0
        total_length = 0

        if chrom in coverage_dict:
            for cov_start, cov_end, coverage in coverage_dict[chrom]:
                # Calculate overlap
                overlap_start = max(bin_start, cov_start)
                overlap_end = min(bin_end, cov_end)

                if overlap_start < overlap_end:
                    overlap_length = overlap_end - overlap_start
                    total_coverage += coverage * overlap_length
                    total_length += overlap_length

        if total_length > 0:
            avg_coverage = total_coverage / total_length
        else:
            avg_coverage = 0.0

        bin_coverage.append(avg_coverage)

    bins_df['coverage'] = bin_coverage
    return bins_df[['chr', 'start', 'end', 'coverage']]


def main():
    """Main function"""
    args = parse_arguments()

    # Create output directory
    os.makedirs(os.path.dirname(args.output_bed), exist_ok=True)

    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Convert bigWig files to bedGraph
        forward_bedgraph = bigwig_to_bedgraph(args.forward_bw, temp_dir)
        reverse_bedgraph = bigwig_to_bedgraph(args.reverse_bw, temp_dir)

        # Load bedGraph files
        forward_df = load_bedgraph(forward_bedgraph)
        reverse_df = load_bedgraph(reverse_bedgraph)

        # Create 1kb bins
        bins_df = create_genome_bins()

        # Bin coverage data
        forward_binned = bin_coverage_to_1kb(forward_df, bins_df.copy())
        reverse_binned = bin_coverage_to_1kb(reverse_df, bins_df.copy())

        # Combine forward and reverse coverage
        combined_df = pd.merge(forward_binned, reverse_binned, on=['chr', 'start', 'end'],
                              suffixes=('_forward', '_reverse'))
        combined_df['total_coverage'] = combined_df['coverage_forward'] + combined_df['coverage_reverse']

        # Keep only relevant columns
        output_df = combined_df[['chr', 'start', 'end', 'total_coverage']].copy()

        # Sort by chromosome and position
        output_df = output_df.sort_values(['chr', 'start'])

        # Save results
        output_df.to_csv(args.output_bed, sep='\t', index=False, header=False)


if __name__ == "__main__":
    main()
