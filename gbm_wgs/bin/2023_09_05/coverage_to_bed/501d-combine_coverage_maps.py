# <PERSON>cheli
import argparse
import pandas as pd
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--dna_bed", required=True)
    parser.add_argument("--methylation_bed", required=True)
    parser.add_argument("--rnaseq_bed", required=True)
    parser.add_argument("--output_bed", required=True)
    return parser.parse_args()


def load_coverage_bed(bed_file, value_column_name):
    """Load coverage bed file"""
    df = pd.read_csv(bed_file, sep='\t', header=None)
    df.columns = ['chr', 'start', 'end', value_column_name]
    return df


def combine_coverage_maps(dna_df, methylation_df, rnaseq_df):
    """Combine all coverage maps into a single dataframe"""
    # Start with DNA coverage as the base
    combined_df = dna_df.copy()
    
    # Merge methylation data
    combined_df = pd.merge(combined_df, methylation_df, on=['chr', 'start', 'end'], how='outer')
    
    # Merge RNA-seq data
    combined_df = pd.merge(combined_df, rnaseq_df, on=['chr', 'start', 'end'], how='outer')
    
    # Fill missing values with 0
    combined_df = combined_df.fillna(0)
    
    return combined_df


def main():
    """Main function"""
    args = parse_arguments()
    
    # Create output directory
    os.makedirs(os.path.dirname(args.output_bed), exist_ok=True)
    
    # Load coverage data
    dna_df = load_coverage_bed(args.dna_bed, 'dna_log2_ratio')
    methylation_df = load_coverage_bed(args.methylation_bed, 'methylation_diff')
    rnaseq_df = load_coverage_bed(args.rnaseq_bed, 'rnaseq_coverage')
    
    # Combine coverage maps
    combined_df = combine_coverage_maps(dna_df, methylation_df, rnaseq_df)
    
    # Sort by chromosome and position
    combined_df = combined_df.sort_values(['chr', 'start'])
    
    # Save results
    combined_df.to_csv(args.output_bed, sep='\t', index=False)


if __name__ == "__main__":
    main()
