# <PERSON>
# Test script for coverage pipeline
import subprocess
import os

def test_dna_coverage():
    """Test DNA coverage processing"""
    print("Testing DNA coverage processing...")
    
    cmd = [
        "/Users/<USER>/opt/miniconda3/envs/py_dev/bin/python",
        "/Users/<USER>/Library/CloudStorage/Dropbox/phd/reimand_lab_ab/gbm_wgs/bin/2023_09_05/coverage_to_bed/501a-process_dna_coverage.py",
        "--normal_bed", "/Users/<USER>/Desktop/RLGS1-primary-blood.md.regions.bed.gz",
        "--tumor_bed", "/Users/<USER>/Desktop/RLGS1-primary-primary.md.regions.bed.gz",
        "--output_bed", "/Users/<USER>/Desktop/tmp/RLGS1_dna_coverage_1kb.bed"
    ]
    
    # Create output directory
    os.makedirs("/Users/<USER>/Desktop/tmp", exist_ok=True)
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("DNA coverage processing: SUCCESS")
        print(f"Output saved to: /Users/<USER>/Desktop/tmp/RLGS1_dna_coverage_1kb.bed")
    else:
        print("DNA coverage processing: FAILED")
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)


def test_methylation_coverage():
    """Test methylation coverage processing"""
    print("Testing methylation coverage processing...")
    
    # Note: We only have one methylation file for testing, so we'll use it for both primary and recurrent
    cmd = [
        "/Users/<USER>/opt/miniconda3/envs/py_dev/bin/python",
        "/Users/<USER>/Library/CloudStorage/Dropbox/phd/reimand_lab_ab/gbm_wgs/bin/2023_09_05/coverage_to_bed/501b-process_methylation_coverage.py",
        "--primary_bed", "/Users/<USER>/Desktop/5mC.RLGS1-primary_normal.bed.gz",
        "--recurrent_bed", "/Users/<USER>/Desktop/5mC.RLGS1-primary_normal.bed.gz",  # Using same file for testing
        "--output_bed", "/Users/<USER>/Desktop/tmp/RLGS1_methylation_coverage_1kb.bed"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("Methylation coverage processing: SUCCESS")
        print(f"Output saved to: /Users/<USER>/Desktop/tmp/RLGS1_methylation_coverage_1kb.bed")
    else:
        print("Methylation coverage processing: FAILED")
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)


def test_rnaseq_coverage():
    """Test RNA-seq coverage processing"""
    print("Testing RNA-seq coverage processing...")
    
    cmd = [
        "/Users/<USER>/opt/miniconda3/envs/py_dev/bin/python",
        "/Users/<USER>/Library/CloudStorage/Dropbox/phd/reimand_lab_ab/gbm_wgs/bin/2023_09_05/coverage_to_bed/501c-process_rnaseq_coverage.py",
        "--forward_bw", "/Users/<USER>/Desktop/RLGS9_recurrent.forward.bigWig",
        "--reverse_bw", "/Users/<USER>/Desktop/RLGS9_recurrent.reverse.bigWig",
        "--output_bed", "/Users/<USER>/Desktop/tmp/RLGS9_rnaseq_coverage_1kb.bed"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("RNA-seq coverage processing: SUCCESS")
        print(f"Output saved to: /Users/<USER>/Desktop/tmp/RLGS9_rnaseq_coverage_1kb.bed")
    else:
        print("RNA-seq coverage processing: FAILED")
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)


def test_combine_coverage():
    """Test combining coverage maps"""
    print("Testing coverage combination...")
    
    cmd = [
        "/Users/<USER>/opt/miniconda3/envs/py_dev/bin/python",
        "/Users/<USER>/Library/CloudStorage/Dropbox/phd/reimand_lab_ab/gbm_wgs/bin/2023_09_05/coverage_to_bed/501d-combine_coverage_maps.py",
        "--dna_bed", "/Users/<USER>/Desktop/tmp/RLGS1_dna_coverage_1kb.bed",
        "--methylation_bed", "/Users/<USER>/Desktop/tmp/RLGS1_methylation_coverage_1kb.bed",
        "--rnaseq_bed", "/Users/<USER>/Desktop/tmp/RLGS9_rnaseq_coverage_1kb.bed",
        "--output_bed", "/Users/<USER>/Desktop/tmp/combined_coverage_1kb.bed"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("Coverage combination: SUCCESS")
        print(f"Output saved to: /Users/<USER>/Desktop/tmp/combined_coverage_1kb.bed")
    else:
        print("Coverage combination: FAILED")
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)


def test_visualization():
    """Test visualization"""
    print("Testing visualization...")
    
    cmd = [
        "/Users/<USER>/opt/miniconda3/envs/r4_env/bin/Rscript",
        "/Users/<USER>/Library/CloudStorage/Dropbox/phd/reimand_lab_ab/gbm_wgs/bin/2023_09_05/coverage_to_bed/501e-visualize_coverage_maps.R",
        "--combined_bed", "/Users/<USER>/Desktop/tmp/combined_coverage_1kb.bed",
        "--output_plot", "/Users/<USER>/Desktop/tmp/coverage_maps_test.pdf"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("Visualization: SUCCESS")
        print(f"Output saved to: /Users/<USER>/Desktop/tmp/coverage_maps_test.pdf")
    else:
        print("Visualization: FAILED")
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)


def main():
    """Run all tests"""
    print("Starting coverage pipeline tests...")
    print("=" * 50)
    
    test_dna_coverage()
    print()
    
    test_methylation_coverage()
    print()
    
    test_rnaseq_coverage()
    print()
    
    test_combine_coverage()
    print()
    
    test_visualization()
    print()
    
    print("=" * 50)
    print("Testing complete!")


if __name__ == "__main__":
    main()
