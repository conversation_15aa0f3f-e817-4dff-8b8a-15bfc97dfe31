# <PERSON>cheli
import argparse
import pandas as pd
import gzip
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--input_bed", required=True)
    parser.add_argument("--output_bed", required=True)
    return parser.parse_args()


def load_coverage_bed(bed_file):
    """Load coverage bed file"""
    df = pd.read_csv(bed_file, sep='\t', header=None)
    
    # Determine number of columns and assign appropriate column names
    if df.shape[1] == 4:
        df.columns = ['chr', 'start', 'end', 'value']
    elif df.shape[1] == 5:
        df.columns = ['chr', 'start', 'end', 'value1', 'value2']
    elif df.shape[1] == 6:
        df.columns = ['chr', 'start', 'end', 'value1', 'value2', 'value3']
    else:
        # Generic column names for any number of columns
        df.columns = ['chr', 'start', 'end'] + [f'value{i}' for i in range(1, df.shape[1] - 2)]
    
    return df


def subset_to_standard_chromosomes(df):
    """Subset to standard chromosomes (chr1-22, chrX, chrY)"""
    # Define standard chromosomes
    standard_chrs = [f"chr{i}" for i in range(1, 23)] + ["chrX", "chrY"]
    
    # Filter to only keep standard chromosomes
    df = df[df['chr'].isin(standard_chrs)]
    
    # Sort by chromosome and position
    # Create a custom sort order for chromosomes
    chr_order = {f"chr{i}": i for i in range(1, 23)}
    chr_order["chrX"] = 23
    chr_order["chrY"] = 24
    
    df['chr_order'] = df['chr'].map(chr_order)
    df = df.sort_values(['chr_order', 'start'])
    df = df.drop('chr_order', axis=1)
    
    return df


def save_compressed_bed(df, output_file):
    """Save dataframe as compressed bed file"""
    # Create output directory
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    # Save as gzipped file
    with gzip.open(output_file, 'wt') as f:
        df.to_csv(f, sep='\t', index=False, header=False)


def main():
    """Main function"""
    args = parse_arguments()
    
    # Load coverage data
    df = load_coverage_bed(args.input_bed)
    
    # Subset to standard chromosomes
    df = subset_to_standard_chromosomes(df)
    
    # Save compressed results
    save_compressed_bed(df, args.output_bed)


if __name__ == "__main__":
    main()
