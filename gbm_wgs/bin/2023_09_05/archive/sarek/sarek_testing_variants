#!/bin/bash
#$ -P reimandlab
#$ -N sarek_testing
#$ -l h_vmem=20G,h_rt=1:0:0:0
#$ -q u20build
#$ -o /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/sarek/
#$ -e /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/sarek/


source /u/abahcheli/.profile
source /u/abahcheli/.bashrc
source /etc/profile.d/uge_settings.sh

source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh

mamba activate nextflow

cd /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2024_04_23

export NXF_SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/

nextflow run nf-core/sarek --input /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2024_04_23/sarek_nextflow_variantcalling.csv \
--outdir /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2024_04_23/wgs_processing \
-profile singularity -c /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/sarek/nextflow.config \
--tools 'freebayes,mutect2,strelka' \
--igenomes_base /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/igenomes/ \
-r 3.4.1 -resume


