# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import re, subprocess, glob
from concurrent.futures import ProcessPoolExecutor


help_message = '''
Failed
'''


def convert_BND(input_df, sv_type='translocation'):
    # subset to structural variants of type
    mask = input_df['INFO'].str.contains('SVTYPE=BND')
    df = input_df.loc[mask,:]

    # Initialize an empty list to store individual converted DataFrames
    converted_dfs = []
    
    # Iterate over each row in the DataFrame
    for index, row in df.iterrows():
        # Extract relevant information from the current row
        breakpoint1 = f"{row['#CHROM']}:{row['POS']}"
        
        # Define the regular expression pattern
        pattern = r'[\[|\]](.*?)[\[|\]]'
        matches = re.findall(pattern, row['ALT'])
        breakpoint2 = matches[0].lower()

        # Construct a DataFrame for the current row
        converted_df = pd.DataFrame({
            'breakpoint1': [breakpoint1],
            'breakpoint2': [breakpoint2],
            'type': [sv_type]
        })
        
        # Append the converted DataFrame to the list
        converted_dfs.append(converted_df)
    
    # Concatenate all converted DataFrames into a single DataFrame
    output_df = pd.concat(converted_dfs, ignore_index=True)
    
    return output_df


def convert_INS(input_df, sv_type='insertion'):
    # subset to structural variants of type
    mask = input_df['INFO'].str.contains('SVTYPE=INS')
    df = input_df.loc[mask,:]

    # Initialize an empty list to store individual converted DataFrames
    converted_dfs = []
    
    # Iterate over each row in the DataFrame
    for index, row in df.iterrows():
        # Extract relevant information from the current row
        breakpoint1 = f"{row['#CHROM']}:{row['POS']}"
        
        # Define the regular expression pattern
        # pattern = r"END=\d+"
        # matches = re.findall(pattern, row['INFO'])
        # breakpoint2 = f"{row['#CHROM']}:{matches[0].split('=')[1]}".lower()

        pattern = r"END=\d+"
        matches = re.findall(pattern, row['INFO'])
        end_start = matches[0].split('=')[1]
        
        pattern = r"SVINSLEN=\d+"
        matches = re.findall(pattern, row['INFO'])
        size = matches[0].split('=')[1]
        
        breakpoint2 = f"{row['#CHROM']}:{str(int(end_start) + int(size))}".lower()

        # Construct a DataFrame for the current row
        converted_df = pd.DataFrame({
            'breakpoint1': [breakpoint1],
            'breakpoint2': [breakpoint2],
            'type': [sv_type]
        })
        
        # Append the converted DataFrame to the list
        converted_dfs.append(converted_df)
    
    # Concatenate all converted DataFrames into a single DataFrame
    output_df = pd.concat(converted_dfs, ignore_index=True)
    
    return output_df


def convert_DUP(input_df, sv_type='duplication'):
    # subset to structural variants of type
    mask = input_df['INFO'].str.contains('SVTYPE=DUP')
    df = input_df.loc[mask,:]

    # Initialize an empty list to store individual converted DataFrames
    converted_dfs = []
    
    # Iterate over each row in the DataFrame
    for index, row in df.iterrows():
        # Extract relevant information from the current row
        breakpoint1 = f"{row['#CHROM']}:{row['POS']}"
        
        # Define the regular expression pattern
        pattern = r"END=\d+"
        matches = re.findall(pattern, row['INFO'])
        breakpoint2 = f"{row['#CHROM']}:{matches[0].split('=')[1]}".lower()

        # Construct a DataFrame for the current row
        converted_df = pd.DataFrame({
            'breakpoint1': [breakpoint1],
            'breakpoint2': [breakpoint2],
            'type': [sv_type]
        })
        
        # Append the converted DataFrame to the list
        converted_dfs.append(converted_df)
    
    # Concatenate all converted DataFrames into a single DataFrame
    output_df = pd.concat(converted_dfs, ignore_index=True)
    
    return output_df


def convert_DEL(input_df, sv_type='deletion'):
    # subset to structural variants of type
    mask = input_df['INFO'].str.contains('SVTYPE=DEL')
    df = input_df.loc[mask,:]

    # Initialize an empty list to store individual converted DataFrames
    converted_dfs = []
    
    # Iterate over each row in the DataFrame
    for index, row in df.iterrows():
        # Extract relevant information from the current row
        breakpoint1 = f"{row['#CHROM']}:{row['POS']}"
        
        # Define the regular expression pattern
        pattern = r"END=\d+"
        matches = re.findall(pattern, row['INFO'])
        breakpoint2 = f"{row['#CHROM']}:{matches[0].split('=')[1]}".lower()

        # Construct a DataFrame for the current row
        converted_df = pd.DataFrame({
            'breakpoint1': [breakpoint1],
            'breakpoint2': [breakpoint2],
            'type': [sv_type]
        })
        
        # Append the converted DataFrame to the list
        converted_dfs.append(converted_df)
    
    # Concatenate all converted DataFrames into a single DataFrame
    output_df = pd.concat(converted_dfs, ignore_index=True)
    
    return output_df


def capitalize_XY(s):
    return ''.join([c.upper() if c in {'x', 'y'} else c for c in s])

def convert_sv_df(file):
    # load df
    df = pd.read_csv(file, sep='\t', comment="#", header=None)
    df.columns = ['#CHROM', 'POS', 'ID', 'REF', 'ALT', 'QUAL', 'FILTER', 'INFO', 'FORMAT', 'DETAILS']

    # subset to pass
    df = df.loc[df['FILTER'] == 'PASS',:]

    # convert bnd
    bnd_df = convert_BND(df)

    # convert inv
    inv_df = convert_INS(df)

    # convert dup
    dup_df = convert_DUP(df)

    # convert del
    del_df = convert_DEL(df)

    # concatenate all converted DataFrames
    res_df = pd.concat([bnd_df, inv_df, dup_df, del_df], ignore_index=True)
    res_df['sample'] = file.split("/")[-2]

    # capitalize sex chromosomes
    res_df['breakpoint2'] = res_df['breakpoint2'].apply(capitalize_XY)

    return res_df


def load_and_process_dfs(file_list, threads):
    # multi-thread calculate p-values and fc
    with ProcessPoolExecutor(max_workers=threads) as executor:
        res_df = list(executor.map(convert_sv_df, file_list))


    # collect converted dataframes
    res_df = pd.concat(res_df, ignore_index=True)

    return res_df



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # list all files of interest
    file_regex = os.path.join(results_dir, "nanopore/*/*wf-somatic-sv.vcf.gz")
    nanopore_sv_files = glob.glob(file_regex)

    # process and combine all dfs
    res_df = load_and_process_dfs(nanopore_sv_files, threads)

    # save to file
    res_df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "r_script=", "figure_data_file=", "figure_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()


