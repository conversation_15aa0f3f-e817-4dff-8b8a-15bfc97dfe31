# Alec <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

import subprocess

help_message = '''
Failed
'''

def combine_dfs(file1, file2):
    # resulting dataframe
    res = []

    # load and combine dfs
    df1 = pd.read_csv(file1, sep='\t')
    df2 = pd.read_csv(file2, sep='\t')

    df = pd.concat([df1, df2], axis=0)

    # add details
    df['allele_def'] = df['allele'].map(allele_dict)

    # iterate through rows
    for i, row in df.iterrows():
        cols_oi = ['sample', 'gene', 'allele', 'allele_def', 'allele_type', 'methylation_type']

        if row['allele_def'] in ['allele1', 'allele2']:
            row['allele_type'] = row['allele_def']
            res.append(row[cols_oi])
        
        else:
            row1 = row.copy()
            row2 = row.copy()
            row1['allele_type'] = 'allele1'
            row2['allele_type'] = 'allele2'

            if row['allele_def'] == 'heterozygous':
                row3 = row.copy()
                row4 = row.copy()
                row3['allele_type'] = 'allele1'
                row4['allele_type'] = 'allele2'
                row3['methylation_type'] = 'unknown'
                row4['methylation_type'] = 'unknown'

                res.append(row3[cols_oi])
                res.append(row4[cols_oi])

            res.append(row1[cols_oi])
            res.append(row2[cols_oi])

    # create df
    df = pd.DataFrame(res)

    df['sample_allele'] = df['sample'] + '_' + df['allele_type']

    return df


def add_empty_genes(mut_df, genes_oi):
    # for each gene, add a row for a new sample
    for gene in genes_oi:
        tmp_df = pd.DataFrame({'gene': [gene], 'sample_allele': ['null13-sample'], 'methylation_type': ['null_methylation']})
        mut_df = pd.concat([mut_df, tmp_df])

    # make sure each sample is represented as well
    for sample in mut_df['sample_allele'].unique():
        tmp_df = pd.DataFrame({'gene': ['null_gene'], 'sample_allele': [sample], 'methylation_type': ['null_methylation']})
        mut_df = pd.concat([mut_df, tmp_df])

    return mut_df



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load dfs and combine
    df = combine_dfs(file1, file2)

    # add empty rows
    df = add_empty_genes(df, genes_oi)

    # save
    df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    allele_dict = {'1|0':'allele1', '0|1':'allele2', '1|1':'homozygous', '0|0':'homozygous', '1/1':'homozygous', '0/0':'homozygous', '1/0':'heterozygous', '0/1':'heterozygous'}

    genes_oi = ['TP53', 'IDH1', 'PTEN', 'ATRX', 'EGFR', 'NF1', 'TERT', 'PIK3CA', 'RB1', 'CIC', 'FUBP1', 'CDKN2A', 'EGFR', 'PDGFRA', 'CDK4', 'MET', 'MDM2', 'MDM4', 'CCND2', 'MYCN', 'CDK6', 'ATRX']


    file1 = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/_figure_data/003-phased_methyl-RLGS11-primary.tsv'
    file2 = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/_figure_data/003-phased_methyl-RLGS11-recurrent.tsv'

    r_script = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/nanopore_interpretations/003-visualize_methyl.R'

    figure_data_file = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/_figure_data/003-phased_methylation_combined.tsv'
    figure_file = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/_figures/003-phased_methylation_combined.pdf'
    
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["cna_combined_file=", "ensembl_gene_bed_file=", "figure_data_file=", "r_script=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--cna_combined_file"):
            cna_combined_file = str(arg)
        if opt in ("--ensembl_gene_bed_file"):
            ensembl_gene_bed_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        
        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)

            
    main()




