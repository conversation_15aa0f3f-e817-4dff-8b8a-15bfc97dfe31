# Alec <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

import subprocess

help_message = '''
Failed
'''


def subset_df(df, sample = 'RLGS11-primary'):
    # remove non-values
    df = df[~df['chr'].isna()]
    df = df[np.isin(df['gene'].astype(str), genes_oi)]

    # set start and stop
    df['start'] = df['start'].astype(int)
    df['end'] = df['end'].astype(int)

    # set index
    df.index = df['chr'] + ":" + df['start'].astype(int).astype(str) + ":" + df['end'].astype(int).astype(str) + ":" + df['sample']

    # subset
    df = df[df['sample'] == sample]

    return df


def load_methylation_data(file1, file2):
    # load df
    bed1 = pd.read_csv(file1, sep='\t', header=None)
    bed1.columns = ['chr', 'start', 'end', 'length', 'nCG', 'meanMethy1', 'meanMethy2',
        'diff.Methy', 'areaStat', 'sample', 'chr_gene', 'start_gene',
        'stop_gene', 'gene_id', 'unknown', 'strand', 'source', 'type',
        'unknown_2', 'details', 'gene', 'methylation_type', 
            'chr_methyl', 'start_methyl', 'end_methyl', 'type_methyl', 'reads_methyl', 'unknown_methyl', 'start2_methyl', 'end2_methyl', 'random_mathyl', 'percentage']
    bed1.index = bed1['chr'] + ":" + bed1['start'].astype(str) + ":" + bed1['end'].astype(str) + ":" + bed1['sample']

    bed2 = pd.read_csv(file2, sep='\t', header=None)
    bed2.columns = ['chr', 'start', 'end', 'length', 'nCG', 'meanMethy1', 'meanMethy2',
        'diff.Methy', 'areaStat', 'sample', 'chr_gene', 'start_gene',
        'stop_gene', 'gene_id', 'unknown', 'strand', 'source', 'type',
        'unknown_2', 'details', 'gene', 'methylation_type', 
            'chr_methyl', 'start_methyl', 'end_methyl', 'type_methyl', 'reads_methyl', 'unknown_methyl', 'start2_methyl', 'end2_methyl', 'random_mathyl', 'percentage']
    bed2.index = bed2['chr'] + ":" + bed2['start'].astype(str) + ":" + bed2['end'].astype(str) + ":" + bed2['sample']

    return bed1, bed2


def process_df(df, methylation_file1, methylation_file2):
    # process df
    df = subset_df(df)

    # load methylation data
    bed1, bed2 = load_methylation_data(methylation_file1, methylation_file2)

    # iterate through df
    for value in df.index.unique():
        # total percentage methylated
        b1 = np.sum(bed1.loc[value,'percentage'].str.split().str[1].astype(float))
        b2 = np.sum(bed2.loc[value,'percentage'].str.split().str[1].astype(float))

        # methylation type
        meth_type = df.loc[value, 'methylation_type']

        # set allele
        min_val = '1|0' if b1 < b2 else '0|1'
        max_val = '1|0' if b1 < b2 else '1|0'

        # if both are similarly methylated, set to 1|1
        if np.abs(b1 - b2) < len(bed1.index == value) * 0.01:
            min_val = '1|1'
            max_val = '1|1'
        
        if meth_type == 'hypo':
            df.loc[value, 'allele'] = min_val
        else:
            df.loc[value, 'allele'] = max_val

    return df



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load data
    df = pd.read_csv(input_file, sep='\t')

    # process dfs
    res_df = process_df(df, methylation_file1, methylation_file2)

    # save
    res_df.to_csv(figure_data_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    genes_oi = ['TP53', 'IDH1', 'PTEN', 'ATRX', 'EGFR', 'NF1', 'TERT', 'PIK3CA', 'RB1', 'CIC', 'FUBP1', 'CDKN2A', 'EGFR', 'PDGFRA', 'CDK4', 'MET', 'MDM2', 'MDM4', 'CCND2', 'MYCN', 'CDK6', 'ATRX']

    input_file = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/_figure_data/002-methyl_circos.tsv'

    methylation_file1 = '/.mounts/labs/reimandlab/private/users/abahcheli/tmp/RLGS11-primary.wf_mods.1.bed'
    methylation_file2 = '/.mounts/labs/reimandlab/private/users/abahcheli/tmp/RLGS11-primary.wf_mods.2.bed'
    

    figure_data_file = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/_figure_data/003-phased_methyl-RLGS11-primary.tsv'
    
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["cna_combined_file=", "ensembl_gene_bed_file=", "figure_data_file=", "r_script=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--cna_combined_file"):
            cna_combined_file = str(arg)
        if opt in ("--ensembl_gene_bed_file"):
            ensembl_gene_bed_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        
        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)

            
    main()




