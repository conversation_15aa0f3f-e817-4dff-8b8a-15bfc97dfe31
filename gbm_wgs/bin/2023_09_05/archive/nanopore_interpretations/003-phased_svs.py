# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

import subprocess

help_message = '''
Failed
'''


def closest_to_integer(series, target_integer):
    closest_value = None
    min_difference = float('inf')

    for value in series:
        difference = abs(value - target_integer)
        if difference < min_difference:
            min_difference = difference
            closest_value = value

    return closest_value

def check_within_range(value, series2, max_range = 1000):
    # subset to same same chr
    chr = value.split(":")[0]
    series2 = series2[series2.str.contains(chr)]

    # subset to same event type
    event = value.split(":")[2]
    series2 = series2[series2.str.contains(event)]

    # select closest 100 bp event
    mask = (series2.str.split(":").str[1].astype(int) - int(value.split(":")[1])).abs() <= max_range

    if np.sum(mask) == 0:
        if len(series2) > 0:
            closest = closest_to_integer(series2.str.split(":").str[1].astype(int), int(value.split(":")[1]))
            new_vals = series2[series2.str.contains(str(closest), regex=True)].str.split(":").str[3].unique()
            return ",".join(new_vals)
        else:
            return "0/0"
    
    if np.sum(mask) == 1:
        return str(series2[mask]).split(":")[3].strip("\nName")

    else:
        return ",".join(series2[mask].str.split(":").str[3])

def find_common_events(res, df):
    res_values = []

    print(res.shape)

    for value in res['info']:
        closest = check_within_range(value, df['breakpoint1'])
        res_values.append(closest)

    res['allele'] = res_values

    return res

def process_df(input_df, phased_files_dict):
    # add details
    input_df['info'] = input_df['breakpoint1'] + ":" + input_df['type']

    results = []

    # iterate through each sample
    for sample in phased_files_dict.keys():
        phased_file = phased_files_dict[sample]

        # load df
        df = pd.read_csv(phased_file, sep='\t', comment="#", header=None, compression='gzip')
        df.columns = ['#CHROM', 'POS', 'ID', 'REF', 'ALT', 'QUAL', 'FILTER', 'INFO', 'FORMAT', 'RLGS11-primary']

        # filter for PASS
        df = df[df['FILTER'].str.contains('PASS')]
        df['breakpoint1'] = df['#CHROM'] + ":" + df['POS'].astype('str')

        # add sv type
        sv_type_dict = {'INS':'insertion', 'DEL':'deletion', 'INV':'inversion', 'BND':'translocation', 'DUP':'duplication'}
        df['breakpoint1'] = df['#CHROM'] + ":" + df['POS'].astype('str') + ":" + df['ID'].str.split(".").str[1].map(sv_type_dict) + ":" + df['RLGS11-primary'].str.split(":").str[0]

        # process dfs
        results.append(find_common_events(input_df[input_df['sample'].str.contains(sample)], df))

    # combine results
    return pd.concat(results)



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load data
    df = pd.read_csv(input_file, sep='\t')

    # process for each sample
    res_df = process_df(df, phased_files_dict)

    # save
    res_df.to_csv(figure_data_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    input_file = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/_figure_data/001-sv_circos.tsv'

    phased_files_dict = {'RLGS11-primary':'/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore_human_variation/RLGS11-primary/RLGS11-primary.wf_sv.vcf.gz', 
                         'RLGS11-recurrent':'/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore_human_variation/RLGS11-recurrent_pass/RLGS11-recurrent_pass.wf_sv.vcf.gz'}
    

    figure_data_file = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/_figure_data/003-phased_sv.tsv'
    
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["cna_combined_file=", "ensembl_gene_bed_file=", "figure_data_file=", "r_script=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--cna_combined_file"):
            cna_combined_file = str(arg)
        if opt in ("--ensembl_gene_bed_file"):
            ensembl_gene_bed_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        
        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)

            
    main()




