library(optparse)
library(circlize)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}


# Create a function to prepare data for circos.genomicTrack
prepare_segment_data <- function(df) {
df$sector.index = df$chr
df = df[, c("sector.index", "start", "stop", "cna_classification")]
return(df)
}

# Prepare coverage data for circos plotting
prepare_coverage_data <- function(df) {
  df$sector.index = df$V1  # Assuming your coverage data has the chromosome in the first column
  df = df[,c(1,2,3,5)]  # V2=start, V3=end, V4=coverage value
  colnames(df) = c("sector.index", "start", "end", "coverage")
  return(df)
}




circos_plot = function(sample, coverage_df, input_df){
# circos_plot = function(sample, input_df){


# subset to sample
df = input_df[input_df$sample == sample,]
primary = df[grepl('primary', df$type),]
recurrent = df[grepl('recurrent', df$type),]

sub_coverage_df = coverage_df[coverage_df$sample_number == sample,]
primary_coverage = sub_coverage_df[grepl('primary', sub_coverage_df$type),]
recurrent_coverage = sub_coverage_df[grepl('recurrent', sub_coverage_df$type),]

# colors
cna_colors <- setNames(c("firebrick", "dodgerblue"), c("insertion", "deletion"))

# create a new plot
circos.clear()
    
# start with chr1 at the top; make each track 10% of the total height; reduce margins between tracks
circos.par("start.degree" = 90, track.height = 0.1, cell.padding = c(0, 0, 0, 0))

# Initialize the circos plot
circos.initializeWithIdeogram(species = "hg38")

    
# Prepare data for circos.genomicTrack
primary_segment_data = prepare_segment_data(primary)
recurrent_segment_data = prepare_segment_data(recurrent)

    
# Prepare and plot primary coverage data
coverage_data = prepare_coverage_data(primary_coverage)
circos.genomicTrackPlotRegion(coverage_data, panel.fun = function(region, value, ...) {
circos.genomicLines(region, value, col = "grey", lwd = 0.5, type = 'l')
}, bg.border = NA)

    
# Plot segments as rectangles with specified border color
circos.genomicTrack(primary_segment_data, panel.fun = function(region, value, ...) {
circos.genomicRect(region, value, col = cna_colors[value$cna_classification], 
                   border = NA)
}, bg.border = NA, ylim = c(0, 0.2))  # Add ylim here because there is no variance in the data

# Add label for Primary
circos.text(sector.index = "chr1", x = mean(primary$start), y = 0, labels = "Primary", facing = "inside", cex = 1, col = "black")

    
# Prepare and plot recurrent coverage data
coverage_data = prepare_coverage_data(recurrent_coverage)
circos.genomicTrackPlotRegion(coverage_data, panel.fun = function(region, value, ...) {
circos.genomicLines(region, value, col = "grey", lwd = 0.5, type = 'l')
}, bg.border = NA)


# Plot segments as rectangles with specified border color
circos.genomicTrack(recurrent_segment_data, panel.fun = function(region, value, ...) {
circos.genomicRect(region, value, col = cna_colors[value$cna_classification], 
                   border = NA)
}, bg.border = NA, ylim = c(0.1, 0.2))  # Add ylim here

# Add label for Recurrent
circos.text(sector.index = "chr1", x = mean(recurrent$start), y = 0.1, labels = "Recurrent", facing = "inside", cex = 1, col = "black")


# Customize the plot if needed, e.g., add legend
legend("topright", legend = names(cna_colors), fill = cna_colors, border = NA, bty = "n", ncol = length(cna_colors), inset = 0.05)
title(sample, cex = 1.5)

}




pdf(opt$figure_file)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')

# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# order samples
sorted_vector <- unique(input_df$sample_number)[order(sapply(unique(input_df$sample_number), extract_numeric))]
input_df$sample_number = factor(input_df$sample_number, levels = sorted_vector)


# create circos plots
lapply(levels(input_df$sample_number), circos_plot, input_df, stats_df)


dev.off()


print(opt$figure_file)






