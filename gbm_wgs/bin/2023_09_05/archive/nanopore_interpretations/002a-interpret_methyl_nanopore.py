# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd
import numpy as np

import pybedtools, subprocess, glob

help_message = '''
Failed
'''


# def find_intersections(cna_file, gene_file, additional_distance = 1000):
#     # Load the CNA regions BED file into a DataFrame for easy processing
#     cna_df = pd.read_csv(cna_file, sep='\t')
def find_intersections(file_regex, gene_file, additional_distance = 1000):
    # find all files
    files = glob.glob(file_regex)
    print(files)

    # Initialize a list to store the results
    results = []

    for file in files:
        # Load the BED file into a DataFrame for easy processing
        cna_df = pd.read_csv(file, sep='\t')

        cna_df['sample'] = file.split("/")[-5]

        # add distance to consider methylation at promoter region
        cna_df['start'] = cna_df['start'] - additional_distance
        cna_df['end'] = cna_df['end'] + additional_distance
        
        # Load the genes BED file
        genes_bed = pybedtools.BedTool(gene_file)

        # Convert the sample's DataFrame back to a BedTool object for intersection
        sample_bed = pybedtools.BedTool.from_dataframe(cna_df)

        # Find intersections with genes, retaining information from both
        intersection = sample_bed.intersect(genes_bed, wa=True, wb=True)

        # Iterate over each line in the intersection BedTool object
        for feature in intersection:
            # Each 'feature' is a line from the intersection result, split by tabs
            results.append(str(feature).strip("\n").split('\t'))

    # create combined df
    new_cols = ['chr', 'start', 'end', 'length', 'nCG', 'meanMethy1', 'meanMethy2', 'diff.Methy', 'areaStat', 'sample']
    new_cols.extend(["chr_gene", "start_gene", "stop_gene", "gene_id", "unknown", "strand", "source", "type", "unknown_2", "details"])
    results = pd.DataFrame(results, columns = new_cols)

    # Define a regular expression pattern to match 'gene_name=' part
    pattern = r'gene_name=([^;]+)'
    
    # Extract 'gene_name=' part using regular expression
    extracted = results['details'].str.extract(pattern, expand=False)
    results['gene'] = extracted

    # classify based on positive or negative value
    results['methylation_type'] = np.where(results['diff.Methy'].astype('float') > 0, 'hyper', 'hypo')

    return results


def add_empty_genes(mut_df, genes_oi = ['TP53', 'IDH1', 'PTEN', 'ATRX', 'EGFR', 'NF1', 'TERT', 'PIK3CA', 'RB1', 'CIC', 'FUBP1', 'CDKN2A', 'EGFR', 'PDGFRA', 'CDK4', 'MET', 'MDM2', 'MDM4', 'CCND2', 'MYCN', 'CDK6', 'ATRX'], samples_oi = ['RLGS1-primary', 'RLGS1-recurrent', 'RLGS2-primary', 'RLGS2-recurrent', 'RLGS3-primary', 'RLGS3-recurrent', 'RLGS4-primary', 'RLGS4-recurrent', 'RLGS5-primary', 'RLGS5-recurrent', 'RLGS6-primary', 'RLGS6-recurrent', 'RLGS7-primary', 'RLGS7-recurrent', 'RLGS8-primary', 'RLGS8-recurrent', 'RLGS9-primary', 'RLGS9-recurrent', 'RLGS10-primary', 'RLGS10-recurrent', 'RLGS11-primary', 'RLGS11-recurrent', 'RLGS12-primary', 'RLGS12-recurrent']):
    # for each gene, add a row for a new sample
    for gene in genes_oi:
        tmp_df = pd.DataFrame({'gene': [gene], 'sample': ['null13-sample'], 'methylation_type': ['null']})
        mut_df = pd.concat([mut_df, tmp_df], ignore_index=True)

    # make sure each sample is represented as well
    for sample in samples_oi:
        tmp_df = pd.DataFrame({'gene': ['null_gene'], 'sample': [sample], 'methylation_type': ['null']})
        mut_df = pd.concat([mut_df, tmp_df], ignore_index=True)

    return mut_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # # find intersections
    # methyl_genes_df = find_intersections(methylation_file, ensembl_gene_bed_file)
    file_regex = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/nanopore/*/*/mod/5mC/DMR/*'
    methyl_genes_df = find_intersections(file_regex, ensembl_gene_bed_file)

    # add empty genes and samples
    methyl_genes_df = add_empty_genes(methyl_genes_df)

    # save to file
    methyl_genes_df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"
    
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["methylation_file=", "ensembl_gene_bed_file=", "figure_data_file=", "r_script=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--methylation_file"):
            methylation_file = str(arg)
        if opt in ("--ensembl_gene_bed_file"):
            ensembl_gene_bed_file = str(arg)
        
        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        
        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_file"):
            figure_file = str(arg)

    main()


