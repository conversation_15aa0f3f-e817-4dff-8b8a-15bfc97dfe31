# <PERSON>
library(optparse)
library(forcats)
library(ggplot2)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}


genes_oi = c('TP53', 'IDH1', 'PTEN', 'ATRX', 'EGFR', 'NF1', 'TERT', 'PIK3CA', 'RB1', 'CIC', 'FUBP1', 'CDKN2A', 'EGFR', 'PDGFRA', 'CDK4', 'MET', 'MDM2', 'MDM4', 'CCND2', 'MYCN', 'CDK6', 'ATRX', 'null_gene')




plot_oncoprint = function(df){

    
ggplot(df, aes(x = sample_allele, y = gene, fill = methylation_type)) + plot_theme() +
geom_tile(alpha = 0.3) +

    scale_fill_manual(values = c("hypo" = "dodgerblue",
                                 "hyper" = "firebrick",
                                "null_methylation" = "white",
                                "unknown" = "black")) +

ggtitle('') + ylab('Gene') + xlab('')


}






pdf(opt$figure_file, width=5)


# load df
input_df = read.csv(opt$figure_data_file, sep='\t')

# subset to genes of interest
input_df = input_df[input_df$gene %in% genes_oi,]

# Reorder the factor levels based on the ordered samples
# input_df$gene <- factor(input_df$gene, levels = rev(ordered_samples))
input_df$gene <- factor(input_df$gene, levels = c('IDH1', 'CCND2', 'FUBP1', 'MYCN', 'PDGFRA', 'TERT', 'MDM2', 'PIK3CA', 'CDK4', 'MDM4', 'CIC', 'TP53', 'ATRX', 'CDKN2A', 'NF1', 'MET', 'CDK6', 'RB1', 'PTEN', 'EGFR', 'null_gene'))

input_df$sample_allele <- factor(input_df$sample_allele, levels = c('RLGS11-primary_allele1', 'RLGS11-primary_allele2', 'RLGS11-recurrent_allele1', 'RLGS11-recurrent_allele2'))


# plot mutations for top GBM genes
plot_oncoprint(input_df)


dev.off()


print(opt$figure_file)



