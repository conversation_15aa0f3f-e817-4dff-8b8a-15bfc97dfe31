# Alec Bahcheli

# run version (typically date)
VERSION='2023_09_05'

# project directory
MAIN_DIR='/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs'

# directory of scripts / binaries
BIN_DIR = "/".join([MAIN_DIR, "bin", VERSION])



# results directories
DATA_DIR = "/".join([MAIN_DIR, "data", VERSION])
REF_DATA_DIR = "/".join([MAIN_DIR, "data", "ref_data"])
RAW_DATA_DIR= "/".join([MAIN_DIR, "data", "raw_data"])

RES_DIR = "/".join([MAIN_DIR, "results", VERSION])
FIGURE_DATA_DIR = "/".join([RES_DIR, "_figure_data"])
FIGURE_DIR = "/".join([RES_DIR, "_figures"])



# location of R environment for running R scripts 
RSCRIPT='/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript'
# location of python
PYTHON='/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev/bin/python'





# fastq directory
## NOTE assumes that reads are paired-end and end with either "...R1.fastq.gz" or "...R2.fastq.gz"
FASTQ_DIR = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/data/raw_data/all_fastqs"



# # a fasta file of the genome that you are mapping to
# ## NOTE must be in the WGS_MAIN_DIR
# GENOME_VERSION = "hg38_chr"

# # a fasta file of the genome that you are mapping to
# original_genome_fasta = "/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/hg38_chr.fa.gz"
# genome_fasta = RES_DIR + "/hg38_chr.fa"

# # gtf file for calculating counts and tpm
# GTF_FILE = REF_DATA_DIR + "/gencode.v43.gtf"
# GFF_FILE = REF_DATA_DIR + "/gencode.v45.chr_patch_hapl_scaff.annotation.gff3"

# # variant calling files
# CENTROMERE_BED = REF_DATA_DIR + "/hg38_chr-centromeres.bed"
# EXOME_BED = REF_DATA_DIR + "/hg38_exons_no_mitochondria.bed"
# dbSNP_VCF = REF_DATA_DIR + "/clinvar.vcf.gz"



###########################
# Genome processing and variant calling information
###########################

# list of chromosomes
chromosome_list = ['chr' + str(i) for i in range(23)][1:]
chromosome_list.extend(['chrX', 'chrY'])


# hmm window size 
# WGS coverage requires ~200 reads per bin
hmm_window_size = 400
# sequencing read length
read_length = 150



###########################
# WGS sequence processing
###########################

# number of threads to use in each process for mapping
WGS_THREADS = '20'

wgs_sample_list_with_blood = ['RLGS1-primary_wgs_seq', 'RLGS1-recurrent_wgs_seq', 'RLGS1-blood_wgs_seq', 'RLGS2-primary_wgs_seq', 'RLGS2-recurrent_wgs_seq', 'RLGS2-blood_wgs_seq', 'RLGS3-primary_wgs_seq', 'RLGS3-recurrent_wgs_seq', 'RLGS3-blood_wgs_seq', 'RLGS4-primary_wgs_seq', 'RLGS4-recurrent_wgs_seq', 'RLGS4-blood_wgs_seq', 'RLGS5-primary_wgs_seq', 'RLGS5-recurrent_wgs_seq', 'RLGS5-blood_wgs_seq', 'RLGS6-primary_wgs_seq', 'RLGS6-recurrent_wgs_seq', 'RLGS6-blood_wgs_seq', 'RLGS7-primary_wgs_seq', 'RLGS7-recurrent_wgs_seq', 'RLGS7-blood_wgs_seq', 'RLGS8-primary_wgs_seq', 'RLGS8-recurrent_wgs_seq', 'RLGS8-blood_wgs_seq', 'RLGS9-primary_wgs_seq', 'RLGS9-recurrent_wgs_seq', 'RLGS9-blood_wgs_seq', 'RLGS10-primary_wgs_seq', 'RLGS10-recurrent_wgs_seq', 'RLGS10-blood_wgs_seq', 'RLGS11-primary_wgs_seq', 'RLGS11-recurrent_wgs_seq', 'RLGS11-blood_wgs_seq', 'RLGS12-primary_wgs_seq', 'RLGS12-recurrent_wgs_seq', 'RLGS12-blood_wgs_seq']

wgs_sample_list = ['RLGS1-primary_wgs_seq', 'RLGS1-recurrent_wgs_seq', 'RLGS2-primary_wgs_seq', 'RLGS2-recurrent_wgs_seq', 'RLGS3-primary_wgs_seq', 'RLGS3-recurrent_wgs_seq', 'RLGS4-primary_wgs_seq', 'RLGS4-recurrent_wgs_seq', 'RLGS5-primary_wgs_seq', 'RLGS5-recurrent_wgs_seq', 'RLGS6-primary_wgs_seq', 'RLGS6-recurrent_wgs_seq', 'RLGS7-primary_wgs_seq', 'RLGS7-recurrent_wgs_seq', 'RLGS8-primary_wgs_seq', 'RLGS8-recurrent_wgs_seq', 'RLGS9-primary_wgs_seq', 'RLGS9-recurrent_wgs_seq', 'RLGS10-primary_wgs_seq', 'RLGS10-recurrent_wgs_seq', 'RLGS11-primary_wgs_seq', 'RLGS11-recurrent_wgs_seq', 'RLGS12-primary_wgs_seq', 'RLGS12-recurrent_wgs_seq']

wgs_sample_patient_csv = 'RLGS1,RLGS10,RLGS11,RLGS12,RLGS2,RLGS3,RLGS4,RLGS5,RLGS6,RLGS7,RLGS8,RLGS9'
wgs_sample_patient_list = wgs_sample_patient_csv.split(",")






# tumor_types = ['primary', 'recurrent']

# dgea_analysis_types = ['deseq2_paired', 'deseq2_unpaired', 'edger_paired', 'edger_unpaired']
# rnaseq_data_types = ['raw_exon_counts', 'raw_counts', 'raw_protein_coding_counts', 'raw_transcript_counts']


# snv_software_types = ['mutect2', 'strelka_muse2_varscan2_common_variants']





###################################
# Complete workflow
###################################

rule all:
    input:
        expand("{bin_dir}/sarek/{patient}", bin_dir = BIN_DIR, patient = wgs_sample_patient_list)





#####################
# illumina wgs processing
#####################
include: "../snakemake/002-wgs_processing.smk"







