# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pysam
from collections import Counter
from concurrent.futures import ProcessPoolExecutor


help_message = '''
Failed
'''


# Function to read variants from a VCF file
def read_variants(file_name):
    vcf_reader = pysam.VariantFile(file_name, "r")
    return {
        (record.chrom, record.pos, record.ref, tuple(str(alt) for alt in record.alts))
        for record in vcf_reader
    }


# define common variants
def define_common_variants(varscan2_file, strelka_file, muse2_file, threads):
    with ProcessPoolExecutor(max_workers=threads) as executor:
        # Submit tasks to the executor
        future_varscan2 = executor.submit(read_variants, varscan2_file)
        future_strelka = executor.submit(read_variants, strelka_file)
        future_muse2 = executor.submit(read_variants, muse2_file)

        # Retrieve results when they are completed
        varscan2 = future_varscan2.result()
        strelka = future_strelka.result()
        muse2 = future_muse2.result()

    print('DONE')

    # Combine all variants into a single list for counting
    all_variants = list(varscan2) + list(strelka) + list(muse2)

    # Count occurrences of each variant
    variant_counts = Counter(all_variants)
    
    # Extract variants present in at least two files
    common_variants = {variant for variant, count in variant_counts.items() if count >= 2}

    return common_variants


# # write common variants to a new VCF file
# def write_common_variants(unwritten_variants, varscan2_file, strelka_file, muse2_file, output_file):
#     # for each file, read variants and write common variants to a new VCF file
#     for i, file in enumerate([muse2_file, varscan2_file, strelka_file]):
#         if i == 0:
#             vcf_writer = vcf.Writer(open(output_file, 'w'), vcf.Reader(open(file, 'r')))

#         else:
#             # Setup VCF writer
#             vcf_writer = vcf.Writer(open(output_file, 'a'), vcf.Reader(open(file, 'r')))

#         # Write common variants to the new VCF file
#         for record in vcf.Reader(open(file, 'r')):
#             variant_tuple = (record.CHROM, record.POS, record.REF, tuple(str(alt) for alt in record.ALT))

#             if variant_tuple in unwritten_variants:
#                 vcf_writer.write_record(record)
#                 unwritten_variants.remove(variant_tuple)
                
#         vcf_writer.close()


# def write_common_variants_pysam(unwritten_variants, varscan2_file, strelka_file, muse2_file, output_file):
#     # Initialize the VCF writer with None. It will be set up on the first iteration.
#     vcf_writer = None

#     for file in [muse2_file, varscan2_file, strelka_file]:
#         # Open the current VCF file for reading
#         with pysam.VariantFile(file, 'r') as vcf_reader:
#             # Setup the VCF writer using the header from the first file
#             if vcf_writer is None:
#                 # Create a new VCF file for writing. Copy the header from the reader.
#                 vcf_writer = pysam.VariantFile(output_file, 'w', header=vcf_reader.header)
#             # Iterate over each record in the current VCF file
#             for record in vcf_reader:
#                 # Create a tuple for the current variant
#                 variant_tuple = (record.chrom, record.pos, record.ref, tuple(str(alt) for alt in record.alts))

#                 # Check if the variant is one of the unwritten common variants
#                 if variant_tuple in unwritten_variants:
#                     # Write the common variant to the output file
#                     vcf_writer.write(record)
#                     # Remove the variant from the set to avoid writing it again
#                     unwritten_variants.remove(variant_tuple)

#     # Close the VCF writer after processing all files
#     vcf_writer.close()



def write_common_variants_pysam(unwritten_variants, varscan2_file, strelka_file, muse2_file, output_file):
    # # Open the first file to clone the header, modify as needed
    # with pysam.VariantFile(varscan2_file) as vcf_reader:
    #     header = vcf_reader.header
    #     # If manipulating the header, do so here. For example, remove sample-specific lines:
    #     # header.samples.clear()  # This would remove sample information, if that's acceptable
        
    # Open the output file with the adjusted header
    with pysam.VariantFile(output_file, 'w') as vcf_writer:
        for file in [muse2_file, varscan2_file, strelka_file]:
            with pysam.VariantFile(file) as vcf_reader:
                for record in vcf_reader:
                    variant_tuple = (record.chrom, record.pos, record.ref, tuple(str(alt) for alt in record.alts))
                    if variant_tuple in unwritten_variants:
                        # Since we're not copying sample-specific data, ensure the record fits the header
                        # This step might involve modifying the record to match the expected header structure
                        vcf_writer.write(record)
                        unwritten_variants.remove(variant_tuple)


# def write_common_variants_pysam(unwritten_variants, varscan2_file, strelka_file, muse2_file, output_file):
#     # Prepare the header based on one of the input files or create a new one
#     with pysam.VariantFile(varscan2_file) as first_file:
#         header = first_file.header.copy()
#         # Potentially modify the header here to fit the expected output structure

#     with pysam.VariantFile(output_file, 'w', header=header) as vcf_writer:
#         for file_path in [muse2_file, varscan2_file, strelka_file]:
#             with pysam.VariantFile(file_path) as vcf_reader:
#                 for record in vcf_reader:
#                     variant_tuple = (record.chrom, record.pos, record.ref, tuple(str(alt) for alt in record.alts))
#                     if variant_tuple in unwritten_variants:
#                         # Write the record to the output file
#                         try:
#                             vcf_writer.write(record)
#                         except ValueError as e:
#                             print(f"Error writing record: {e}")
#                         unwritten_variants.remove(variant_tuple)


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # define common variants
    common_variants = define_common_variants(varscan2_file, strelka_file, muse2_file, threads)

    print(f"variants: {len(common_variants)}")

    # write common variants to a new VCF file
    write_common_variants_pysam(common_variants, varscan2_file, strelka_file, muse2_file, output_file)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["varscan2_file=", "strelka_file=", "muse2_file=", "output_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--varscan2_file"):
            varscan2_file = str(arg)
        if opt in ("--strelka_file"):
            strelka_file = str(arg)
        if opt in ("--muse2_file"):
            muse2_file = str(arg)

        if opt in ("--output_file"):
            output_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)
            
    main()




