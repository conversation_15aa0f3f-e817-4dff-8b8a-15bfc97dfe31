# Alec Ba<PERSON>cheli

import sys, getopt, time, os, subprocess

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def main():
    print('XXX-XXX.py')
    t1 = time.time()


    # run hmmcopy in R
    cline = [rscript, hmmcopy_rscript, '--bigwig_gc_file', bigwig_gc_file, '--bigwig_mappability_file', bigwig_mappability_file, '--normal_wigfile', normal_wigfile, '--tumor_wigfile', tumor_wigfile, '--results_file', results_file]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # r environment
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["bigwig_gc_file=", "bigwig_mappability_file=", "tumor_wigfile=", "normal_wigfile=", "hmmcopy_rscript=", "results_file="])
    except getopt.GetoptError:
        print(help_message)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--bigwig_gc_file"):
            bigwig_gc_file = str(arg)
        if opt in ("--bigwig_mappability_file"):
            bigwig_mappability_file = str(arg)

        if opt in ("--tumor_wigfile"):
            tumor_wigfile = str(arg)
        if opt in ("--normal_wigfile"):
            normal_wigfile = str(arg)

        if opt in ("--hmmcopy_rscript"):
            hmmcopy_rscript = str(arg)
        if opt in ("--results_file"):
            results_file = str(arg)

    main()





