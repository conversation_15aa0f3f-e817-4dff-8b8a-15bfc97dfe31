# <PERSON>cheli

import sys, getopt, time, os, re

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''

def link_files_by_dict(source_directory, output_directory, normal_dict):
    # Iterate through the link_mapping dictionary
    for link_name, regex_pattern in normal_dict.items():
        # Find files that match the regex pattern
        matching_files = [filename for filename in os.listdir(source_directory) if re.match(regex_pattern, filename)]
        
        # Create symbolic links for the matching files
        for filename in matching_files:
            source_path = os.path.join(source_directory, filename)
            if filename.endswith(".bam"):
                link_path = os.path.join(output_directory, f'{link_name}-control.bam')
            else:
                link_path = os.path.join(output_directory, f'{link_name}-control.bai')
            
            # Create symbolic link
            try:
                os.symlink(source_path, link_path)
            except:
                pass
            
            print(f"Created symbolic link: {link_path} -> {source_path}")

    return



def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # link files
    link_files_by_dict(source_directory, output_directory, normal_dict)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # dictionary correlating tumor samples with normal
    normal_dict = {'KA_WGS3':'KA_WGS2-marked_duplicates_sorted.*',
                    'KA_WGS4':'KA_WGS2-marked_duplicates_sorted.*',
                    'KA_WGS5':'KA_WGS1-marked_duplicates_sorted.*',
                'KA_WGS6':'KA_WGS1-marked_duplicates_sorted.*',
                    'KA_WGS7':'KA_WGS2-marked_duplicates_sorted.*'
                }



    # normal_dict = {'SRR14097577': 'SRR14097596-marked_duplicates_sorted.*',
    #          'SRR14097578': 'SRR14097597-marked_duplicates_sorted.*',
    #          'SRR14097583': 'SRR14097591-marked_duplicates_sorted.*',
    #          'SRR14097584': 'SRR14097593-marked_duplicates_sorted.*',
    #          'SRR14097585': 'SRR14097594-marked_duplicates_sorted.*',
    #          'SRR14097592': 'SRR14097595-marked_duplicates_sorted.*'}
    
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["source_directory=", "output_directory="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--source_directory"):
            source_directory = str(arg)
        if opt in ("--output_directory"):
            output_directory = str(arg)
            
    main()


