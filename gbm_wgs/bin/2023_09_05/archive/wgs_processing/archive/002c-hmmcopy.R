# <PERSON>
library(optparse)
library(dplyr)

library(HMMcopy)

# options list for parser options
option_list <- list(
    make_option(c("-b","--bigwig_gc_file"), type="character", default=NULL,
            help="",
            dest="bigwig_gc_file"),
    make_option(c("-c","--bigwig_mappability_file"), type="character", default=NULL,
            help="",
            dest="bigwig_mappability_file"),
    make_option(c("-a","--normal_wigfile"), type="character", default=NULL,
            help="",
            dest="normal_wigfile"),
    make_option(c("-d","--tumor_wigfile"), type="character", default=NULL,
            help="",
            dest="tumor_wigfile"),
    make_option(c("-e","--results_file"), type="character", default=NULL,
            help="",
            dest="results_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)




# load control reads and correct counts
normal_reads = wigsToRangedData(opt$normal_wigfile, opt$bigwig_gc_file, opt$bigwig_mappability_file)
normal_copy = correctReadcount(normal_reads)

# repeat for tumor
tumor_reads = wigsToRangedData(opt$tumor_wigfile, opt$bigwig_gc_file, opt$bigwig_mappability_file)
tumor_copy = correctReadcount(tumor_reads)


# LOGARITHM IDENTITY: log(a) - log(b) == lob(a / b)
somatic_copy = tumor_copy
somatic_copy$copy = tumor_copy$copy - normal_copy$copy

# remove chr from chromosome names and ignore if mitochondria
somatic_copy$chr <- gsub("^chr", "", somatic_copy$chr)
somatic_copy = somatic_copy[somatic_copy$chr != 'M',]


# ignore mitochondria
somatic_segments = HMMsegment(somatic_copy, verbose = FALSE)


# save results to file
write.table(somatic_segments$segs, file = opt$results_file, sep = "\t", quote = FALSE, row.names = FALSE)



