# <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''



def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # content
    to_write = f"""[general]

chrLenFile = {sample_fasta_index}
ploidy = 2
window = {window_size}
chrFiles = {chr_directory}
outputDir = {output_dir}

[sample]

mateFile = {tumor_bam}
inputFormat = BAM
mateOrientation = 0

[control]
mateFile = {normal_bam}
inputFormat = BAM
mateOrientation = 0
"""

    with open(config_file, 'w') as file:
        # write details to file
        file.write(to_write)

    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # window size for WGS should be 50,000
    # https://boevalab.inf.ethz.ch/FREEC/tutorial.html
    window_size = 50000
    
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["tumor_bam=", "normal_bam=", "sample_fasta_index=", "chr_directory=", "output_dir=", "window_size=", "config_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--tumor_bam"):
            tumor_bam = str(arg)
        if opt in ("--normal_bam"):
            normal_bam = str(arg)

        if opt in ("--sample_fasta_index"):
            sample_fasta_index = str(arg)        
        if opt in ("--chr_directory"):
            chr_directory = str(arg)
        if opt in ("--output_dir"):
            output_dir = str(arg)

        if opt in ("--window_size"):
            window_size = str(arg)
        if opt in ("--config_file"):
            config_file = str(arg)
            
            
    main()







