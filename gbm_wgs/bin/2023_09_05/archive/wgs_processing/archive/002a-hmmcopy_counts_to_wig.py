# Alec <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''

def tsv_to_fixedstep(input_file, output_file):
    # read df
    df = pd.read_csv(input_file, sep='\t')
    
    # record step info based on first line
    step_info = df.loc[0,'end']

    wig_file_list = []

    # for each unqiue chromosome, add count to output file
    for chrom in df['chr'].unique():
        # add chromosome information to list for writing to output file 
        wig_file_list.append(f'fixedStep chrom={chrom} start=1 step={step_info} span={step_info}')

        # get sub_df
        sub_df = df.loc[df['chr'] == chrom,:]
        
        # record counts
        counts = sub_df['count'].to_numpy()
        wig_file_list.extend(counts)

    print(output_file)
    # write to output file
    with open(output_file, 'w') as f:
        f.write('\n'.join([str(i) for i in wig_file_list]))



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # create fixed step wig file
    tsv_to_fixedstep(segment_file, wig_file)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["segment_file=", "wig_file="])
    except getopt.GetoptError:
        print(help_message)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--segment_file"):
            segment_file = str(arg)

        if opt in ("--wig_file"):
            wig_file = str(arg)

    main()





