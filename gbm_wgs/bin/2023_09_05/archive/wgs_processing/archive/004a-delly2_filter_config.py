# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # content
    to_write = f"""{tumor_sample}\ttumor
{normal_sample}\tcontrol
"""

    # write details to file 
    with open(sample_description_file, 'w') as file:
        # write details to file
        file.write(to_write)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["tumor_sample=", "normal_sample=", "sample_description_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--tumor_sample"):
            tumor_sample = str(arg)
        if opt in ("--normal_sample"):
            normal_sample = str(arg)

        if opt in ("--sample_description_file"):
            sample_description_file = str(arg)
            
    main()




