import sys, getopt, time, subprocess, os, pathlib, glob, re

import pandas as pd 
import numpy as np


source_directories = {'230116_RLGS_0010_R_run1/20230116_1521_2C_PAM90818_15eb3a16/fast5_pass':'RLGS10-blood_nanopore',
                     '230116_RLGS_0010_T1_run1/20230116_1520_2A_PAM23013_977f38c5/fast5_pass':'RLGS10-primary_nanopore',
                     '230116_RLGS_0010_T2_run1/20230116_1522_2E_PAM37215_993e87b0/fast5_pass':'RLGS10-recurrent_nanopore'}

production_dir = "/.mounts/labs/reimandlab/production/"
fast5_dir = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/data/raw_data/fast5s/'
basecalled_dir = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/data/raw_data/basecalled_fastqs'


for original_dir in source_directories.keys():
    # original directory
    directory = os.path.join(production_dir, original_dir)

    # Destination directory for the symbolic links
    fast5_parent_dir = os.path.join(fast5_dir, source_directories[original_dir])
    
    

    # number of files for smaller basecalling directories
    n_dir = 20
    files = os.listdir(directory)
    num_files_per_dir = len(os.listdir(directory)) // n_dir
    
    

    # destination directories for files
    destination_dirs = list(map(lambda x: os.path.join(fast5_parent_dir, str(x)), range(21)))

    # make destination directory
    os.makedirs(fast5_parent_dir, exist_ok=True)
    
    
    
    # make directory for basecalling results
    new_basecall_dir = os.path.join(basecalled_dir, source_directories[original_dir])
    print(new_basecall_dir)
    os.makedirs(new_basecall_dir, exist_ok=True)
    
    new_basecall_dirs = list(map(lambda x: os.path.join(new_basecall_dir, str(x)), range(21)))
    for dest_dir in new_basecall_dirs:
        os.makedirs(dest_dir, exist_ok=True)
    
    
    # Iterate over the destination directories
    for i, dest_dir in enumerate(destination_dirs):
        # Create the directory if it doesn't exist
        os.makedirs(dest_dir, exist_ok=True)

        end_file_index = num_files_per_dir * (i + 1)
        if i == (n_dir - 1):
            end_file_index = len(files)

        # Copy the files to the destination directory
        for file in files[i * num_files_per_dir:end_file_index]:
            # file_path = os.path.join(directory, file)
            source_path = os.path.join(directory, file)
            destination_path = os.path.join(dest_dir, file)
            try:
                os.symlink(source_path, destination_path)
            except:
                pass
                


