# <PERSON>
library(optparse)
library(Rsubread)



# options list for parser options
option_list <- list(
    make_option(c("-a","--fastq_forward"), type="character", default=NULL,
            help="",
            dest="fastq_forward"),
    make_option(c("-b","--fastq_reverse"), type="character", default=NULL,
            help="",
            dest="fastq_reverse"),
    make_option(c("-c","--genome_version"), type="character", default=NULL,
            help="",
            dest="genome_version"),
    make_option(c("-d","--output_bam_file"), type="character", default=NULL,
            help="",
            dest="output_bam_file"),
    make_option(c("-e","--working_directory"), type="character", default=NULL,
            help="",
            dest="working_directory"),
    make_option(c("-f","--threads"), type="character", default=NULL,
            help="",
            dest="threads")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)


# set directory
setwd(opt$working_directory)


# align reads
align(index=opt$genome_version, readfile1=opt$fastq_forward, readfile2=opt$fastq_reverse, nthreads=opt$threads, output_file = opt$output_bam_file, sortReadsByCoordinates=TRUE)





