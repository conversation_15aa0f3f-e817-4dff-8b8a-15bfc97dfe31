# Alec <PERSON>
library(optparse)
library(Rsubread)



# options list for parser options
option_list <- list(
    make_option(c("-b","--genome_version"), type="character", default=NULL,
            help="",
            dest="genome_version"),
    make_option(c("-c","--genome_fasta"), type="character", default=NULL,
            help="",
            dest="genome_fasta"),
    make_option(c("-d","--working_directory"), type="character", default=NULL,
            help="",
            dest="working_directory")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)


# set directory
setwd(opt$working_directory)


# create mapping index
buildindex(basename=opt$genome_version, reference=opt$genome_fasta, memory=30000)






