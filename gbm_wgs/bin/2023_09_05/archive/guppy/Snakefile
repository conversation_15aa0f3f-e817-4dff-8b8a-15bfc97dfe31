# Alec Bahcheli

# configfile: "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/snakemake_config.yaml"

# run version (typically date)
VERSION='2023_05_25'

# project directory
MAIN_DIR='/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs'

# directory of scripts / binaries
BIN_DIR = "/".join([MAIN_DIR, "bin", VERSION])



# results directories
DATA_DIR = "/".join([MAIN_DIR, "data", VERSION])
REF_DATA_DIR = "/".join([MAIN_DIR, "data", "ref_data"])
RAW_DATA_DIR= "/".join([MAIN_DIR, "data", "raw_data"])

RES_DIR = "/".join([MAIN_DIR, "results", VERSION])
FIGURE_DATA_DIR = "/".join([RES_DIR, "_figure_data"])
FIGURE_DIR = "/".join([RES_DIR, "_figures"])



# location of R environment for running R scripts 
RSCRIPT='/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript'
# location of python
PYTHON='/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev/bin/python'


# fastq directory
## NOTE assumes that reads are paired-end and end with either "...R1.fastq.gz" or "...R2.fastq.gz"
FASTQ_DIR = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/data/raw_data/all_fastqs"


# a fasta file of the genome that you are mapping to
## NOTE must be in the WGS_MAIN_DIR
GENOME_VERSION = "hg38_chr"

# a fasta file of the genome that you are mapping to
original_genome_fasta = "/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/hg38_chr.fa.gz"
genome_fasta = RES_DIR + "/hg38_chr.fa"

# gtf file for calculating counts and tpm
GTF_FILE = REF_DATA_DIR + "/gencode.v43.gtf"

# list of chromosomes
chromosome_list = ['chr' + str(i) for i in range(23)][1:]
chromosome_list.extend(['chrX', 'chrY'])


# all sample codes
sample_codes = "RLGS10".split(",")




###########################
# Nanopore sequence processing
###########################

# fast5 and fastq directories
FAST5_DIRECTORY = RAW_DATA_DIR + '/fast5s'
BASECALLED_DIRECTORY = RAW_DATA_DIR + '/basecalled_fastqs'

# number of smaller directories for basecalling
n_basecalling_directories = 20

# number of threads to use
NANOPORE_THREADS = '15'


# sample list for processing
nanopore_sample_list = "RLGS10-blood_nanopore,RLGS10-primary_nanopore,RLGS10-recurrent_nanopore".split(",")
# nanopore_sample_list = "RLGS_0010_gl,RLGS_0010_t1,RLGS_0010_t2".split(",")



###########################
# WGS sequence processing
###########################

# number of threads to use in each process for mapping
WGS_THREADS = '10'


# WGS sample list
wgs_sample_list = "RLGS10-blood_wgs_seq,RLGS10-primary_wgs_seq,RLGS10-recurrent_wgs_seq".split(",")
wgs_sample_list_cancer = "RLGS10-primary_wgs_seq,RLGS10-recurrent_wgs_seq".split(",")



###########################
# RNA sequence processing
###########################

# number of threads to use in each process for mapping
RNA_THREADS = '20'

# list of sample names you want to process
rna_sample_list = "RLGS10-primary_rna_seq,RLGS10-recurrent_rna_seq".split(",")



###################################
# Complete workflow
###################################

# define the objective (make the output files)
rule all:
    input:
        # Nanopore seq
        expand("{res_dir}/nanopore/{sample}.per-base.bed.gz", res_dir = RES_DIR, sample = nanopore_sample_list),
        expand("{res_dir}/nanopore/{sample_code}-sniffles2.vcf.gz", res_dir = RES_DIR, sample_code = sample_codes),
        expand("{res_dir}/nanopore/{sample}/pileup.vcf.gz", res_dir = RES_DIR, sample = nanopore_sample_list)
        # expand("{fastq_dir}/{sample}/{sub_dir}/sequencing_summary.txt", fastq_dir = BASECALLED_DIRECTORY, sample = nanopore_sample_list, sub_dir = list(range(n_basecalling_directories)))

#         # WGS seq
#         expand("{res_dir}/wgs/{sample}-marked_duplicates.bam", res_dir = RES_DIR, sample = wgs_sample_list),

#         # RNA seq
#         RES_DIR + "/counts.tsv",
#         expand("{res_dir}/r_subread/{sample}.subread_genes.ent", res_dir = RES_DIR, sample = rna_sample_list)


# create main project directories
rule main_directories:        
    output:
        genome_fasta,
        res = RES_DIR + "/null.txt",
        figure_data = FIGURE_DATA_DIR + "/null.txt",
        figures = FIGURE_DIR + "/null.txt"
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        individual_core_memory = '10G'
        
    run:
        shell("mkdir -p {RES_DIR} {FIGURE_DATA_DIR} {FIGURE_DIR}")
        shell("touch {output.res} {output.figure_data} {output.figures}")
        shell("zcat {original_genome_fasta} > {genome_fasta}")






#####################
# nanopore processing
#####################
# include: "snakemake/001-epi2me_nanopore_processing.smk"
include: "snakemake/001-nanopore_processing.smk"

#####################
# illumina wgs processing
#####################
include: "snakemake/002-wgs_processing.smk"

#####################
# illumina wgs variant identification
#####################
include: "snakemake/003-wgs_snv_calling.smk"

#####################
# illumina rna processing
#####################
include: "snakemake/004-rna_processing.smk"



