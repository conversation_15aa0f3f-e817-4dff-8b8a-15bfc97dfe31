# Alec Bahcheli

# configfile: "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/snakemake_config.yaml"

# run version (typically date)
VERSION='2023_09_05'

# project directory
MAIN_DIR='/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs'

# directory of scripts / binaries
BIN_DIR = "/".join([MAIN_DIR, "bin", VERSION])



# results directories
DATA_DIR = "/".join([MAIN_DIR, "data", VERSION])
REF_DATA_DIR = "/".join([MAIN_DIR, "data", "ref_data"])
RAW_DATA_DIR= "/".join([MAIN_DIR, "data", "raw_data"])

RES_DIR = "/".join([MAIN_DIR, "results", VERSION])
FIGURE_DATA_DIR = "/".join([RES_DIR, "_figure_data"])
FIGURE_DIR = "/".join([RES_DIR, "_figures"])



# location of R environment for running R scripts 
RSCRIPT='/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript'
# location of python
PYTHON='/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev/bin/python'





# fastq directory
## NOTE assumes that reads are paired-end and end with either "...R1.fastq.gz" or "...R2.fastq.gz"
FASTQ_DIR = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/data/raw_data/all_fastqs"

# a fasta file of the genome that you are mapping to
## NOTE must be in the WGS_MAIN_DIR
GENOME_VERSION = "hg38_chr"

# a fasta file of the genome that you are mapping to
original_genome_fasta = "/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/hg38_chr.fa.gz"
genome_fasta = RES_DIR + "/hg38_chr.fa"

# gtf file for calculating counts and tpm
GTF_FILE = REF_DATA_DIR + "/gencode.v43.gtf"

# variant calling files
CENTROMERE_BED = REF_DATA_DIR + "/hg38_chr-centromeres.bed"
EXOME_BED = REF_DATA_DIR + "/hg38_exons_no_mitochondria.bed"
dbSNP_VCF = REF_DATA_DIR + "/clinvar.vcf.gz"



# list of chromosomes
chromosome_list = ['chr' + str(i) for i in range(23)][1:]
chromosome_list.extend(['chrX', 'chrY'])


# # all sample codes
# sample_codes = "RLGS10".split(",")
# wgs_tumor_types = "primary,recurrent".split(",")





###########################
# WGS sequence processing
###########################

# hmm window size 
# WGS coverage requires ~200 reads per bin
hmm_window_size = 400
# sequencing read length
read_length = 150

# number of threads to use in each process for mapping
WGS_THREADS = '20'






# wgs_sample_list = "RLGS1-subset_primary_wgs_seq_p1".split(",")
# wgs_sample_list = "RLGS1-blood_wgs_seq_p1,RLGS1-blood_wgs_seq_p2,RLGS1-primary_wgs_seq_p1,RLGS1-primary_wgs_seq_p2,RLGS1-primary_wgs_seq_p3,RLGS1-recurrent_wgs_seq_p1,RLGS1-recurrent_wgs_seq_p2,RLGS1-recurrent_wgs_seq_p3,RLGS10-blood_wgs_seq_p1,RLGS10-primary_wgs_seq_p1,RLGS10-recurrent_wgs_seq_p1,RLGS11-blood_wgs_seq_p1,RLGS11-primary_wgs_seq_p1,RLGS11-recurrent_wgs_seq_p1,RLGS12-blood_wgs_seq_p1,RLGS12-primary_wgs_seq_p1,RLGS12-recurrent_wgs_seq_p1,RLGS2-blood_wgs_seq_p1,RLGS2-primary_wgs_seq_p1,RLGS2-recurrent_wgs_seq_p1,RLGS3-blood_wgs_seq_p1,RLGS3-blood_wgs_seq_p2,RLGS3-primary_wgs_seq_p1,RLGS3-recurrent_wgs_seq_p1,RLGS4-blood_wgs_seq_p1,RLGS4-primary_wgs_seq_p1,RLGS4-recurrent_wgs_seq_p1,RLGS5-blood_wgs_seq_p1,RLGS5-primary_wgs_seq_p1,RLGS5-recurrent_wgs_seq_p1,RLGS6-blood_wgs_seq_p1,RLGS6-primary_wgs_seq_p1,RLGS6-recurrent_wgs_seq_p1,RLGS7-blood_wgs_seq_p1,RLGS7-primary_wgs_seq_p1,RLGS7-recurrent_wgs_seq_p1,RLGS8-blood_wgs_seq_p1,RLGS8-primary_wgs_seq_p1,RLGS8-recurrent_wgs_seq_p1,RLGS9-blood_wgs_seq_p1,RLGS9-primary_wgs_seq_p1,RLGS9-recurrent_wgs_seq_p1".split(",")


# wgs_sample_list = "RLGS1-blood_wgs_seq_p1,RLGS1-blood_wgs_seq_p2,RLGS1-primary_wgs_seq_p1,RLGS1-primary_wgs_seq_p2,RLGS1-primary_wgs_seq_p3,RLGS1-recurrent_wgs_seq_p1,RLGS1-recurrent_wgs_seq_p2,RLGS1-recurrent_wgs_seq_p3,RLGS10-blood_wgs_seq_p1,RLGS10-primary_wgs_seq_p1,RLGS10-recurrent_wgs_seq_p1,RLGS11-blood_wgs_seq_p1,RLGS11-primary_wgs_seq_p1".split(",")
# # ",RLGS11-recurrent_wgs_seq_p1,RLGS12-blood_wgs_seq_p1,RLGS12-primary_wgs_seq_p1,RLGS12-recurrent_wgs_seq_p1,RLGS2-blood_wgs_seq_p1,RLGS2-primary_wgs_seq_p1,RLGS2-recurrent_wgs_seq_p1,RLGS3-blood_wgs_seq_p1,RLGS3-blood_wgs_seq_p2,RLGS3-primary_wgs_seq_p1,RLGS3-recurrent_wgs_seq_p1,RLGS4-blood_wgs_seq_p1,RLGS4-primary_wgs_seq_p1,RLGS4-recurrent_wgs_seq_p1,RLGS5-blood_wgs_seq_p1,RLGS5-primary_wgs_seq_p1,RLGS5-recurrent_wgs_seq_p1,RLGS6-blood_wgs_seq_p1,RLGS6-primary_wgs_seq_p1,RLGS6-recurrent_wgs_seq_p1,RLGS7-blood_wgs_seq_p1,RLGS7-primary_wgs_seq_p1,RLGS7-recurrent_wgs_seq_p1,RLGS8-blood_wgs_seq_p1,RLGS8-primary_wgs_seq_p1,RLGS8-recurrent_wgs_seq_p1,RLGS9-blood_wgs_seq_p1,RLGS9-primary_wgs_seq_p1,RLGS9-recurrent_wgs_seq_p1"


# wgs_sample_list = "RLGS1-blood_wgs_seq_p1,RLGS1-blood_wgs_seq_p2,RLGS1-primary_wgs_seq_p1,RLGS1-primary_wgs_seq_p2,RLGS1-primary_wgs_seq_p3,RLGS1-recurrent_wgs_seq_p1,RLGS1-recurrent_wgs_seq_p2,RLGS1-recurrent_wgs_seq_p3,RLGS10-blood_wgs_seq_p1,RLGS10-primary_wgs_seq_p1,RLGS10-recurrent_wgs_seq_p1,RLGS11-blood_wgs_seq_p1,RLGS11-primary_wgs_seq_p1,RLGS11-recurrent_wgs_seq_p1,RLGS12-blood_wgs_seq_p1,RLGS12-primary_wgs_seq_p1,RLGS12-recurrent_wgs_seq_p1,RLGS2-blood_wgs_seq_p1,RLGS2-primary_wgs_seq_p1,RLGS2-recurrent_wgs_seq_p1,RLGS3-blood_wgs_seq_p1,RLGS3-blood_wgs_seq_p2,RLGS3-primary_wgs_seq_p1,RLGS3-recurrent_wgs_seq_p1,RLGS4-blood_wgs_seq_p1,RLGS4-primary_wgs_seq_p1".split(",")


# wgs_sample_list = "RLGS1-blood_wgs_seq_p1,RLGS1-blood_wgs_seq_p2,RLGS1-primary_wgs_seq_p1,RLGS1-primary_wgs_seq_p2,RLGS1-primary_wgs_seq_p3,RLGS1-recurrent_wgs_seq_p1,RLGS1-recurrent_wgs_seq_p2,RLGS1-recurrent_wgs_seq_p3,RLGS10-blood_wgs_seq_p1,RLGS10-primary_wgs_seq_p1,RLGS10-recurrent_wgs_seq_p1".split(",")

# "RLGS11-blood_wgs_seq_p1,RLGS11-primary_wgs_seq_p1,RLGS11-recurrent_wgs_seq_p1,RLGS12-blood_wgs_seq_p1,RLGS12-primary_wgs_seq_p1,RLGS12-recurrent_wgs_seq_p1,RLGS2-blood_wgs_seq_p1,RLGS2-primary_wgs_seq_p1,RLGS2-recurrent_wgs_seq_p1".split(",")

# "RLGS3-blood_wgs_seq_p1,RLGS3-blood_wgs_seq_p2,RLGS3-primary_wgs_seq_p1,RLGS3-recurrent_wgs_seq_p1,RLGS4-blood_wgs_seq_p1,RLGS4-primary_wgs_seq_p1RLGS4-recurrent_wgs_seq_p1,RLGS5-blood_wgs_seq_p1,RLGS5-primary_wgs_seq_p1,RLGS5-recurrent_wgs_seq_p1,RLGS6-blood_wgs_seq_p1,RLGS6-primary_wgs_seq_p1,RLGS6-recurrent_wgs_seq_p1".split(",")

# "RLGS7-blood_wgs_seq_p1,RLGS7-primary_wgs_seq_p1,RLGS7-recurrent_wgs_seq_p1,RLGS8-blood_wgs_seq_p1,RLGS8-primary_wgs_seq_p1,RLGS8-recurrent_wgs_seq_p1,RLGS9-blood_wgs_seq_p1,RLGS9-primary_wgs_seq_p1,RLGS9-recurrent_wgs_seq_p1".split(",")




# wgs_sample_list = "RLGS1-primary_wgs_seq,RLGS1-recurrent_wgs_seq".split(",")
# wgs_sample_list = "RLGS2-primary_wgs_seq,RLGS2-recurrent_wgs_seq,RLGS3-primary_wgs_seq,RLGS3-recurrent_wgs_seq,RLGS4-primary_wgs_seq,RLGS4-recurrent_wgs_seq".split(",")
# wgs_sample_list = "RLGS5-primary_wgs_seq,RLGS5-recurrent_wgs_seq,RLGS6-primary_wgs_seq,RLGS6-recurrent_wgs_seq,RLGS7-primary_wgs_seq,RLGS7-recurrent_wgs_seq,RLGS8-primary_wgs_seq,RLGS8-recurrent_wgs_seq".split(",")
wgs_sample_list = "RLGS9-primary_wgs_seq,RLGS9-recurrent_wgs_seq,RLGS10-primary_wgs_seq,RLGS10-recurrent_wgs_seq,RLGS11-primary_wgs_seq,RLGS11-recurrent_wgs_seq,RLGS12-primary_wgs_seq,RLGS12-recurrent_wgs_seq".split(",")




import os
import numpy as np

# Directory containing the files
directory = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/data/raw_data/all_fastqs"

# dictionary for associating files with subfiles
marked_duplicates_dict = {}

# List files in the directory
for filename in os.listdir(directory):
    # Check if the item is a file
    if "wgs" in filename:
        # Split the filename by "_" and get the first 3 parts as the prefix
        prefix = "_".join(filename.split("_")[:3])
        marked_duplicates_file = f"{RES_DIR}/wgs_processing/" + "_".join(filename.split("_")[:4]) + "-marked_duplicates.bam"
        
        # Add the filename to the dictionary under the prefix
        if prefix in marked_duplicates_dict:
            marked_duplicates_dict[prefix].append(marked_duplicates_file)
        else:
            marked_duplicates_dict[prefix] = [marked_duplicates_file]

for prefix in marked_duplicates_dict.keys():
    marked_duplicates_dict[prefix] = list(np.unique(marked_duplicates_dict[prefix]))

def process_sample_name(name):
    return '_'.join(name.split('_')[:-1])

    
###################################
# Complete workflow
###################################

rule all:
    input:
        # SNVs
        # mutect2
        expand("{res_dir}/wgs_processing/{sample}/{sample}-mutect2_{chromosome}.vcf.gz", res_dir = RES_DIR, sample = wgs_sample_list, chromosome = chromosome_list),

        # Strelka
        expand("{res_dir}/wgs_processing/{sample}/results/variants/somatic.snvs.vcf.gz", res_dir = RES_DIR, sample = wgs_sample_list, chromosome = chromosome_list),
        expand("{res_dir}/wgs_processing/{sample}/results/variants/somatic.indels.vcf.gz", res_dir = RES_DIR, sample = wgs_sample_list, chromosome = chromosome_list),

        # Varscan2
        expand("{res_dir}/wgs_processing/{sample}/{sample}.cnv", res_dir = RES_DIR, sample = wgs_sample_list, chromosome = chromosome_list),

        # Muse2
        expand("{res_dir}/wgs_processing/{sample}/{sample}-muse2.vcf", res_dir = RES_DIR, sample = wgs_sample_list),

        # octopus
        expand("{res_dir}/wgs_processing/{sample}/{sample}-octopus_{chromosome}.vcf.gz", res_dir = RES_DIR, sample = wgs_sample_list, chromosome = chromosome_list),


        # coverage
        # mosdepth
        expand("{res_dir}/wgs_processing/{sample}/coverage.regions.bed.gz", res_dir = RES_DIR, sample = wgs_sample_list),


        # CNAs
        # hmmcopy
        expand("{res_dir}/wgs_processing/{sample}/hmmcopy_results.tsv", res_dir = RES_DIR, sample = wgs_sample_list),

        # control-freec
        expand("{res_dir}/wgs_processing/{sample}/{sample}-sorted.bam_CNVs", res_dir = RES_DIR, sample = wgs_sample_list),

        # CNVkit
        expand("{res_dir}/wgs_processing/{sample}/cnv_kit.cnn", res_dir = RES_DIR, sample = wgs_sample_list),

        # CNVnator
        expand("{res_dir}/wgs_processing/{sample}/cnvnator.tsv", res_dir = RES_DIR, sample = wgs_sample_list),

        # Delly
        expand("{res_dir}/wgs_processing/{sample}/delly2_calls-filtered.vcf", res_dir = RES_DIR, sample = wgs_sample_list),

        # Manta
        expand("{res_dir}/wgs_processing/{sample}/manta/results/variants/candidateSmallIndels.vcf.gz", res_dir = RES_DIR, sample = wgs_sample_list)





# create main project directories
rule main_directories:
    output:
        genome_fasta,
        res = RES_DIR + "/null.txt",
        figure_data = FIGURE_DATA_DIR + "/null.txt",
        figures = FIGURE_DIR + "/null.txt"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '10G'
        
    run:
        shell("mkdir -p {RES_DIR} {FIGURE_DATA_DIR} {FIGURE_DIR}")
        shell("touch {output.res} {output.figure_data} {output.figures}")
        shell("zcat {original_genome_fasta} > {genome_fasta}")






#####################
# illumina wgs processing
#####################
include: "../snakemake/002-wgs_processing.smk"
# include: "snakemake/002-oicr_specific_processing.smk"

#####################
# illumina wgs variant identification
#####################
include: "../snakemake/003-wgs_snv_calling.smk"
include: "../snakemake/004-wgs_cna_sv_calling.smk"





