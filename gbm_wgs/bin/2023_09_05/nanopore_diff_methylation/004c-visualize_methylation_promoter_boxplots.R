# <PERSON>
library(optparse)
library(forcats)
library(ggplot2)
library(ggrepel)
library(dplyr)
library(tidyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



create_boxplot = function(gene, input_df, stats_df){

# subset to gene
input_df = input_df[input_df$gene == gene,]
stats_df = stats_df[stats_df$gene == gene,]
    

# recurrent df
recurrent_df = input_df[input_df$tumor_type == 'recurrent',]


p = ggplot(input_df, aes(x = tumor_type, y = percent_methylated)) + plot_theme() +
geom_boxplot(outlier.shape = NA) +
geom_point(size = 3, alpha = 0.6) +
geom_line(aes(group = patient, color = methylation_change), alpha = 0.25) +

scale_color_manual(values = c("True" = "blue", "False" = "red")) +

ggtitle(paste(gene, "p=", signif(stats_df$dpm_pval, 3), "FC=", signif(stats_df$fc, 3))) +
xlab("") + ylab('Mean methylation (%)') +
ylim(0, 100) +

theme(plot.title = element_text(size = 16)) +

geom_label_repel(data=recurrent_df, aes(label=patient), show.legend=FALSE,
                seed              = 1234,
				size				= 2,
				force             = 1,
                max.overlaps      = 100,
				nudge_x           = 0.01,
				hjust             = 0,
				segment.size      = 0.2,
                color = 'black'
)




print(p)
return()
}





pdf(opt$figure_file, width=9)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')

# sort by p-value
stats_df$gene = fct_reorder(factor(stats_df$gene), stats_df$dpm_pval, .desc = FALSE)

samples_oi = c('CBFA2T3', 'CCND3', 'CDH1', 'CDX2', 'ERG', 'FES', 'FLT3', 'FOXA1', 'FOXR1', 'GAS7', 'GRIN2A', 'HOXA13', 'HOXA9', 'HOXC11', 'HOXC13', 'HOXD11', 'IKZF1', 'IKZF3', 'KDSR', 'LYL1', 'MEN1', 'MGMT', 'MLF1', 'MNX1', 'NRG1', 'NSD1', 'PAX3', 'PAX5', 'PDE4DIP', 'PIK3R1', 'PRKCB', 'PTPRT', 'ROBO2', 'SALL4', 'SLC34A2', 'SMARCA4', 'SYK', 'TAL1', 'TEC', 'TLX3', 'USP6', 'WT1')

# create boxplots for samples of interest
lapply(samples_oi, create_boxplot, input_df, stats_df)

# # create boxplots for each sample
# lapply(c(head(levels(stats_df$gene),100)), create_boxplot, input_df, stats_df)


dev.off()


print(opt$figure_file)







