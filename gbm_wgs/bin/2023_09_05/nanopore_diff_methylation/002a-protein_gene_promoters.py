# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def calculate_promoters(protein_coding_genes_gff_file):
    # read gff file
    df = pd.read_csv(protein_coding_genes_gff_file, sep='\t')

    # make sure there is no negative value
    mask = df['promoter_start'] < 0
    df.loc[mask,'promoter_start'] = 0

    mask = df['promoter_end'] < 0
    df.loc[mask,'promoter_end'] = 0    

    # filter columns
    df = df[['chrom', 'promoter_start', 'promoter_end', 'gene']]
    df.columns = ['chrom', 'start', 'end', 'gene']

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # calculate promoter regions from gff
    promoter_df = calculate_promoters(protein_coding_genes_gff_file)

    # save to file
    promoter_df.to_csv(protein_coding_promoter_bed_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["protein_coding_genes_gff_file=", "protein_coding_promoter_bed_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--protein_coding_genes_gff_file"):
            protein_coding_genes_gff_file = str(arg)

        if opt in ("--protein_coding_promoter_bed_file"):
            protein_coding_promoter_bed_file = str(arg)
            
    main()




