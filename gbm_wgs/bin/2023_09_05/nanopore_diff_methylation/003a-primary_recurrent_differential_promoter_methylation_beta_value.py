# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from scipy import stats
from concurrent.futures import ProcessPoolExecutor

help_message = '''
Failed
'''

def run_stats(primary, recurrent, gene, sample_code):
    def safe_wilcoxon(x, y):
        try:
            return stats.wilcoxon(x, y)[1]
        except Exception:
            return 1

    def log_transform(x):
        return np.log10(x + 0.01)

    results = {
        'sample_code': sample_code,
        'paired_ttest_p_value': stats.ttest_rel(primary, recurrent)[1],
        'unpaired_ttest_p_value': stats.ttest_ind(primary, recurrent)[1],
        'paired_log_ttest_p_value': stats.ttest_rel(log_transform(primary), log_transform(recurrent))[1],
        'unpaired_log_ttest_p_value': stats.ttest_ind(log_transform(primary), log_transform(recurrent))[1],
        'wilcoxon_p_value': safe_wilcoxon(primary, recurrent),
        'u_test_p_value': stats.mannwhitneyu(primary, recurrent)[1],
        'difference': np.mean(recurrent) - np.mean(primary),
        'median_difference': np.median(recurrent) - np.median(primary),
        'log2_fc': np.log2((np.mean(recurrent) + 0.01) / (np.mean(primary) + 0.01)),
        'median_log2fc': np.log2((np.median(recurrent) + 0.01) / (np.median(primary) + 0.01)),
        'gene': gene
    }

    return pd.DataFrame([results])

def differential_promoter_methylation_beta_value(args, min_samples=3):
    # unpack arguments
    df, gene_list = args
    df = df.set_index('start')

    # separate primary and recurrent samples
    primary_df = df[df['tumor_type'] == 'primary']
    recurrent_df = df[df['tumor_type'] == 'recurrent']

    # results
    results = []

    for gene in gene_list:
        # subset to gene
        p_gene = primary_df[primary_df['gene'] == gene]
        r_gene = recurrent_df[recurrent_df['gene'] == gene]

        for sample_code in p_gene['sample_code'].unique():
            # for each sample code, match the percent methylation value between the primary and recurrent samples using the sample code
            p_sample = p_gene[p_gene['sample_code'] == sample_code]
            r_sample = r_gene[r_gene['sample_code'] == sample_code]

            common_positions = np.intersect1d(p_sample.index, r_sample.index)
            p_common = p_sample.loc[common_positions]
            r_common = r_sample.loc[common_positions]

            # calculate statistics if there are enough common positions
            if p_common.shape[0] > min_samples:
                primary_vals = p_common['percent_methylated'].to_numpy()
                recurrent_vals = r_common['percent_methylated'].to_numpy()
                results.append(run_stats(primary_vals, recurrent_vals, gene, sample_code))

    return results

def differential_methylation(promoter_methylation_combined_file, threads):
    # load df
    input_df = pd.read_csv(promoter_methylation_combined_file, sep='\t')

    # separate gene lists based on number of threads
    gene_lists = np.array_split(input_df['gene'].unique(), threads)

    # process each gene list
    args = [(input_df[input_df['gene'].isin(gene_list)], list(gene_list)) for gene_list in gene_lists]

    # calculate p-values per gene using multi-threads
    with ProcessPoolExecutor(max_workers=threads) as executor:
        results = list(executor.map(differential_promoter_methylation_beta_value, args))
    
    # flatten the list of lists and concatenate
    df = pd.concat([item for sublist in results for item in sublist], ignore_index=True)

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # process file for differential methylation
    df = differential_methylation(promoter_methylation_combined_file, threads)

    # save to files
    df.to_csv(promoter_methlyation_results_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # default threads
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["promoter_methylation_combined_file=", "promoter_methlyation_results_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--promoter_methylation_combined_file"):
            promoter_methylation_combined_file = str(arg)

        if opt in ("--promoter_methlyation_results_file"):
            promoter_methlyation_results_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()




