# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def add_tss_to_main_df(df, protein_coding_genes_gff_file, promoter_relative_tss=[-150,150]):
    # load gff
    gff_df = pd.read_csv(protein_coding_genes_gff_file, sep='\t')

    # subset to tss and gene
    gff_df = gff_df[['gene', 'tss']]

    # merge
    df = pd.merge(df, gff_df, on='gene')

    # subset to sites within the promoter_relative_tss
    df['site_relative_to_tss'] = df['start'] - df['tss']
    df = df[(df['site_relative_to_tss'] >= promoter_relative_tss[0]) & (df['site_relative_to_tss'] <= promoter_relative_tss[1])]
    
    return df


def differential_methylation(promoter_methylation_combined_file, protein_coding_genes_gff_file):
    # load df
    df = pd.read_csv(promoter_methylation_combined_file, sep='\t')

    # add tss
    df = add_tss_to_main_df(df, protein_coding_genes_gff_file)

    # for each gene, calculate the mean methylation for each sample
    df = df.groupby(['gene', 'sample']).agg({'percent_methylated': 'mean'}).reset_index()

    # add patient and tumor_type
    df['patient'] = df['sample'].str.split('-').str[0]
    df['tumor_type'] = df['sample'].str.split('-').str[1]


    # add column describing if there is an increase or decrease in methylation
    annotations = df.groupby(['gene', 'patient'])['percent_methylated'].transform(lambda x: x.diff())
    annotations = annotations[~annotations.isna()] > 0

    # Create a new Series where each element is duplicated and the index is shifted by -1
    duplicated_values = np.repeat(annotations.values, 2)
    duplicated_indices = np.ravel(np.array([(idx - 1,idx) for idx in annotations.index]))
    
    # Create the new Series with the duplicated values and shifted indices
    annotations = pd.Series(duplicated_values, index=duplicated_indices)
    
    df['methylation_change'] = annotations

    
    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # process file for differential methylation
    df = differential_methylation(promoter_methylation_combined_file, protein_coding_genes_gff_file)

    # save to files
    df.to_csv(mean_methylation_by_gene_sample_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["promoter_methylation_combined_file=", "protein_coding_genes_gff_file=", "mean_methylation_by_gene_sample_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--promoter_methylation_combined_file"):
            promoter_methylation_combined_file = str(arg)
        if opt in ("--protein_coding_genes_gff_file"):
            protein_coding_genes_gff_file = str(arg)

        if opt in ("--mean_methylation_by_gene_sample_file"):
            mean_methylation_by_gene_sample_file = str(arg)

    main()




