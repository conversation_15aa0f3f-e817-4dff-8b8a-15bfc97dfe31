# Alec <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def preprocess_gff(gff_file, upstream_length=150, downstream_length=150):
    # read gff file
    df = pd.read_csv(gff_file, sep='\t', header=None, comment="#")

    # add column names
    column_names = ['seqname', 'source', 'type', 'start', 'end', 'score', 'strand', 'phase', 'attributes']
    df.columns = column_names

    # add gene_type column
    df['gene_type'] = df['attributes'].str.extract(r'gene_biotype=([^;]+)')

    # subset to protein-coding genes
    df = df[df['gene_type'] == 'protein_coding']

    # add other detailed columns
    df['chrom'] = 'chr' + df['seqname'].str.split(".").str[0].str.split("_").str[1].str.replace(r'^0+', '', regex=True)
    df['gene'] = df['attributes'].str.extract(r'gene=([^;]+)')

    # calculate promoter regions based on strand
    positive_strand = df['strand'] == '+'
    negative_strand = df['strand'] == '-'

    df.loc[positive_strand, 'promoter_start'] = df.loc[positive_strand, 'start'] - upstream_length
    df.loc[positive_strand, 'promoter_end'] = df.loc[positive_strand, 'start'] + downstream_length

    # for negative genes, we consider the promoter to start inside the gene and end outside the gene for simplicity in subsequent code
    df.loc[negative_strand, 'promoter_start'] = df.loc[negative_strand, 'end'] - downstream_length
    df.loc[negative_strand, 'promoter_end'] = df.loc[negative_strand, 'end'] + upstream_length

    # make integer type
    df['promoter_start'] = df['promoter_start'].astype('int')
    df['promoter_end'] = df['promoter_end'].astype('int')

    # add tss
    df.loc[positive_strand,'tss'] = df.loc[positive_strand, 'start']
    df.loc[negative_strand,'tss'] = df.loc[negative_strand, 'end']

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # calculate promoter regions from gff
    promoter_df = preprocess_gff(gff_file)

    # save to file
    promoter_df.to_csv(protein_coding_genes_gff_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["gff_file=", "protein_coding_genes_gff_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--gff_file"):
            gff_file = str(arg)

        if opt in ("--protein_coding_genes_gff_file"):
            protein_coding_genes_gff_file = str(arg)
            
    main()




