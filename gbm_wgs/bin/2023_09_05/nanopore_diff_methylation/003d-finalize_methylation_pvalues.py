# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def fill_in_pvalues(original_method_pval_file, merged_pval_file):
    # load both dfs
    df1 = pd.read_csv(original_method_pval_file, sep='\t')
    df2 = pd.read_csv(merged_pval_file, sep='\t')

    # set index as gene
    df1.index = df1['gene'].to_numpy()
    df2.index = df2['gene'].to_numpy()

    # for genes missing in df2, fill in with pvalue and fc from df1
    missing_genes = list(set(df1['gene']) - set(df2['gene']))

    # fill in missing genes
    df1 = df1.loc[missing_genes,:]
    df2 = pd.concat([df2, df1])

    return df2

def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # process file for differential methylation
    df = fill_in_pvalues(original_method_pval_file, merged_pval_file)

    # save to files
    df.to_csv(final_pval_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["original_method_pval_file=", "merged_pval_file=", "final_pval_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--original_method_pval_file"):
            original_method_pval_file = str(arg)
        if opt in ("--merged_pval_file"):
            merged_pval_file = str(arg)

        if opt in ("--final_pval_file"):
            final_pval_file = str(arg)


    main()




