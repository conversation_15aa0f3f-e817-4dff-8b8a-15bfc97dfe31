# Alec <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''


def combine_files(files, genes_of_interest):
    res_df = []

    for f in files:
        try:
            # load the file
            df = pd.read_csv(f, sep='\t', header=None)

            # add the column names
            df.columns = ['chr', 'start', 'end', 'gene', 'type', 'attributes', 'chr_2', 'start_2', 'end_2', 'cna', 'mean_copy_number']

            # collect the consensus cna
            df['cna'] = df['cna'].str.split(',').str[0]

            # add null gene with cna null
            df = pd.concat([df, pd.DataFrame(data={'gene': 'none', 'cna': 'none'}, index=[0])], ignore_index=True)

            # add the file name as a column
            df['sample'] = f.split('/')[-1].split('_')[0]

            # append to the list
            res_df.append(df)

        except:
            # create a df with the gene "null" as a placeholder
            df = pd.DataFrame(data={'gene': 'none', 'cna': 'none'}, index=[0])

            # add sample name
            df['sample'] = f.split('/')[-1].split('_')[0]

            # append to the list
            res_df.append(df)

    # combine the dfs
    df = pd.concat(res_df)

    # subset to genes of interest
    df = df[df['gene'].isin(genes_of_interest)]

    # add none gene with mutation "none" for each sample 
    for sample in df['sample'].unique():
        df = pd.concat([df, pd.DataFrame([{'sample': sample, 'gene': 'none', 'cna': 'none'}])], ignore_index=True)

    # add a sample "null_13" that has "none" for each gene of interest
    for gene in genes_of_interest:
        df = pd.concat([df, pd.DataFrame([{'sample': 'null_13', 'gene': gene, 'cna': 'none'}])], ignore_index=True)

    # sort by first "-" delimited column of "sample"
    df['sample_number'] = df['sample'].str.extract('(\d+)')
    df['sample_number'] = df['sample_number'].astype(float)  # Convert to float first to handle NaNs
    df['sample_number'] = df['sample_number'].astype('Int64')  # Convert to nullable integer type

    df = df.sort_values(['sample_number', 'sample'])

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # define the files to read
    regex = f'{results_dir}/analysis_sarek/gene_impacts/*cnas_pcgs.tsv'
    files = glob.glob(regex)

    # load and combine files
    df = combine_files(files, all_genes)

    # save the file
    df.to_csv(combined_pcg_cna_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    # genes of interest
    # cna_genes = ['CDKN2A', 'EGFR', 'PDGFRA', 'CDK4', 'CDK6', 'PTEN', 'MDM2', 'MET', 'CCND2', 'MYCN', 'MDM4', 'ATRX', 'none']
    all_genes = ['EGFR', 'ATRX', 'PTEN', 'CDK6', 'MET', 'CDKN2A', 'RB1', 'MDM4', 'CDK4', 'TP53', 'PDGFRA', 'FUBP1', 'NF1', 'MDM2', 'MYCN', 'PIK3CA', 'PIK3R1', 'CCND2', 'CIC', 'IDH1', 'TERT', 'none']
    
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "combined_pcg_cna_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--combined_pcg_cna_file"):
            combined_pcg_cna_file = str(arg)

    main()


