# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def convert_to_bed_file_format(protein_coding_genes_gff_file):
    # read gff file
    df = pd.read_csv(protein_coding_genes_gff_file, sep='\t', header=None)

    # add column names
    column_names = ['seqname', 'source', 'type', 'start', 'end', 'score', 'strand', 'phase', 'attributes']
    df.columns = column_names

    # define gene name
    df['gene'] = df['attributes'].str.extract(r'gene=([^;]+)')
    
    # add chromosome
    df['chr'] = df['seqname'].str.split(".").str[0].str.split("_").str[1].str.replace(r'^0+', '', regex=True)

    # replace chr23 with chrX and chr24 with chrY
    mask = df['chr'] == '23'
    df.loc[mask, 'chr'] = 'X'
    mask = df['chr'] == '24'
    df.loc[mask, 'chr'] = 'Y'

    # subset to chromosomes 1-22 and x and y
    df = df[df['chr'].isin([str(i) for i in range(1, 23)] + ['X', 'Y'])]

    # sort values
    df = df.sort_values(['chr', 'start'])

    # filter columns
    df = df[['chr', 'start', 'end', 'gene', 'type', 'attributes']]

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # calculate promoter regions from gff
    promoter_df = convert_to_bed_file_format(protein_coding_genes_gff_file)

    print(promoter_df.shape)

    # save to file
    promoter_df.to_csv(protein_coding_bed_file, sep='\t', index=False, header=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["protein_coding_genes_gff_file=", "protein_coding_bed_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--protein_coding_genes_gff_file"):
            protein_coding_genes_gff_file = str(arg)

        if opt in ("--protein_coding_bed_file"):
            protein_coding_bed_file = str(arg)
            
    main()




