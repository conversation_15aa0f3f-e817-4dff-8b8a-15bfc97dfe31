# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''


def calculate_rolling_average(coverage_files, interval_size = 100000, window_size = 5, max_coverage = 15):
    res_df = []

    # for each file, processing rolling average
    for file in coverage_files:
        # load dfs
        df = pd.read_csv(file, sep='\t')

        # Create a new column for the window
        df['window'] = (df['start'] // interval_size) * interval_size

        # Group by chromosome and window, and calculate the mean of log2
        df = df.groupby(['chromosome', 'window']).agg({'log2': 'mean'}).reset_index()

        # calculate rolling averages
        df['rolling_average'] = df['log2'].rolling(window=window_size, min_periods=1).mean()

        # add start and stop coordinates
        df['start'] = df['window']
        df['stop'] = df['window'] + interval_size

        # set max coverage
        df.loc[df['rolling_average'] > max_coverage, 'rolling_average'] = max_coverage
        
        # add sample names
        sample = "-".join(file.split('/')[-1].split('-')[:2])
        df['sample'] = sample
        
        # add type
        df['type'] = sample.split('-')[1]

        # add sample number
        df['sample_number'] = sample.split('-')[0]

        res_df.append(df)

    return pd.concat(res_df)


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process coverage files
    regex = f'{results_dir}/sarek_wgs/*/variant_calling/cnvkit/*_vs_*/*.targetcoverage.cnn'
    files = glob.glob(regex)

    # calculate rolling average
    coverage_df = calculate_rolling_average(files)

    # save to file
    coverage_df.to_csv(processed_coverage_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "processed_coverage_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--processed_coverage_file"):
            processed_coverage_file = str(arg)
            
    main()


