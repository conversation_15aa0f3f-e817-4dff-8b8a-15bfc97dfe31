# Alec <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def processed_ascat_cna_file(raw_cnv_file):
    cols_oi = ['chrom', 'start', 'end', 'classification', 'copy_number']

    # read in file
    df = pd.read_csv(raw_cnv_file, sep='\t')

    # remove normal regions
    normal_mask = np.logical_and(df['nMajor'] == 1, df['nMinor'] == 1)
    df = df[~normal_mask]

    # classify regions as gains or losses
    df['classification'] = np.where(np.logical_or(df['nMajor'] > 1, df['nMinor'] > 1), 'gain', 'loss')

    # rename columns
    df = df.rename(columns={'chr': 'chrom', 'startpos': 'start', 'endpos': 'end'})

    # add copy number depending on classification
    df['copy_number'] = np.where(df['classification'] == 'gain', df['nMajor'] + df['nMinor'], df['nMajor'] - df['nMinor'])
    
    # reorder columns
    df = df.loc[:,cols_oi]

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process cnvkit file
    df = processed_ascat_cna_file(raw_cnv_file)

    # separate into gain and loss files
    mask = df['classification'] == 'gain'
    df_gain = df[mask]
    df_loss = df[~mask]

    # save to files
    df_gain.to_csv(processed_gain_file, sep='\t', index=False)
    df_loss.to_csv(processed_loss_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["raw_cnv_file=", "processed_gain_file=", "processed_loss_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--raw_cnv_file"):
            raw_cnv_file = str(arg)

        if opt in ("--processed_gain_file"):
            processed_gain_file = str(arg)
        if opt in ("--processed_loss_file"):
            processed_loss_file = str(arg)

    main()


