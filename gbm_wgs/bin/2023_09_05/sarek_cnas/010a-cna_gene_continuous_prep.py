# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np



help_message = '''
Failed
'''


def process_cnas_to_continuous(combined_pcg_cna_file):
    # load df
    df = pd.read_csv(combined_pcg_cna_file, sep="\t")

    # drop 'none' gene
    df = df[df['gene'] != 'none']

    # fill nas with expected gain or loss
    df['mean_copy_number'] = df.apply(
        lambda row: 1 if row['cna'] == 'loss' and pd.isna(row['mean_copy_number']) else
                    3 if row['cna'] == 'gain' and pd.isna(row['mean_copy_number']) else row['mean_copy_number'], axis=1)

    # calculate median value
    df['median_copy_number'] = df['mean_copy_number'].apply(lambda x: np.median([float(i) for i in str(x).split(",")]))

    # pivot the df
    df = df.pivot_table(index='gene', columns='sample', values='median_copy_number', aggfunc='mean')

    # if there are missing samples, add them
    all_sample_names = [f'RLGS{i}-primary' for i in range(1, 13)] + [f'RLGS{i}-recurrent' for i in range(1, 13)]
    missing_samples = list(set(all_sample_names) - set(df.columns))

    for sample in missing_samples:
        df[sample] = np.nan

    # sort columns
    df = df[all_sample_names]

    # fill missing values with 2
    df = df.fillna(2)

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load data and process
    df = process_cnas_to_continuous(combined_pcg_cna_file)

    # save to file
    df.to_csv(cna_continuous_file, sep="\t")

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["combined_pcg_cna_file=", "cna_continuous_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--combined_pcg_cna_file"):
            combined_pcg_cna_file = str(arg)
        
        if opt in ("--cna_continuous_file"):
            cna_continuous_file = str(arg)
            

    main()



