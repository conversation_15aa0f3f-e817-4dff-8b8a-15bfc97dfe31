# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from itertools import combinations
import scipy.stats as stats

help_message = '''
Failed
'''



def load_and_process_df(combined_pcg_cna_file, tpm_file):
    # load data
    cna_df = pd.read_csv(combined_pcg_cna_file, sep='\t')
    tpm_df = pd.read_csv(tpm_file, sep='\t')

    # drop gene name column
    tpm_df = tpm_df.drop(columns=['gene_name'])

    # subset to genes of interest
    genes = cna_df['gene'].unique()
    genes = np.unique([gene for gene in genes if gene != 'none'])

    tpm_df = tpm_df[tpm_df['gene_id'].isin(genes)]

    # melt tpm_df
    tpm_df = tpm_df.melt(id_vars=['gene_id'], var_name='sample', value_name='tpm')

    # replace "_" with "-" in sample names
    tpm_df['sample'] = tpm_df['sample'].str.replace('_', '-')
    
    # subset cna_df and merge cna column based on gene and sample
    cna_df = cna_df[['gene', 'sample', 'cna']]
    tpm_df = tpm_df.rename(columns={'gene_id': 'gene'})  

    # add cna column to tpm_df
    res_df = pd.merge(tpm_df, cna_df, on=['gene', 'sample'], how='left')

    # fill cna nan values with "normal"
    res_df['cna'] = res_df['cna'].fillna('normal')

    return res_df


def differential_expression(res_df):
    # DataFrame to store the results
    stats_df = []

    # Iterate over each unique gene
    for gene in res_df['gene'].unique():
        gene_df = res_df[res_df['gene'] == gene]

        # Get the unique CNA groups
        cna_groups = gene_df['cna'].unique()

        # Compare each CNA group vs. all other groups combined
        for group in cna_groups:
            # Subset TPM values for the current group
            group_values = gene_df[gene_df['cna'] == group]['tpm']

            # Subset TPM values for all other groups
            other_groups_values = gene_df[gene_df['cna'] != group]['tpm']

            # Perform statistical tests
            try:
                ttest_p_value = stats.ttest_ind(group_values, other_groups_values, equal_var=False)[1]
            except Exception:
                ttest_p_value = 1  # Default to 1 if an error occurs

            try:
                u_test_p_value = stats.mannwhitneyu(group_values, other_groups_values, alternative='two-sided')[1]
            except Exception:
                u_test_p_value = 1  # Default to 1 if an error occurs

            # Store the results in a dictionary
            stats_df.append({
                'gene': gene,
                'cna': group,
                'ttest_p_value': ttest_p_value,
                'u_test_p_value': u_test_p_value,
                'tpm': max(np.log10(group_values)) * 1.05
            })

    # Convert the results to a DataFrame
    stats_df = pd.DataFrame(stats_df)

    return stats_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process and combine all dfs
    res_df = load_and_process_df(combined_pcg_cna_file, tpm_file)

    # run stats on each group
    stats_df = differential_expression(res_df)

    # save to files
    res_df.to_csv(figure_data_file, sep='\t', index=False)
    stats_df.to_csv(figure_stats_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["combined_pcg_cna_file=", "tpm_file=", "figure_data_file=", "figure_stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--combined_pcg_cna_file"):
            combined_pcg_cna_file = str(arg)
        if opt in ("--tpm_file"):
            tpm_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)


    main()




