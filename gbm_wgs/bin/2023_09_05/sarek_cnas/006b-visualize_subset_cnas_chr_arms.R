# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




coverage_plot = function(gene, input_df, stats_df) {

# subset dfs
input_df = input_df[input_df$gene == gene,]
stats_df = stats_df[stats_df$gene == gene,]

main_df = input_df[input_df$description == 'default',]
    
# define gene position
gene_start = stats_df$start[1]

# define chromosome and position
chrom = input_df$chromosome[1]
arm = input_df$arm[1]


    
# plot
p = ggplot(input_df, aes(x = start, xend = end, y = copy_number, yend = copy_number, color = event)) + plot_theme() +
geom_segment(linewidth = 1, alpha=1) +
geom_segment(data = main_df, linewidth = 1, alpha=1) +

# add line at point
geom_vline(xintercept = gene_start, linetype = "dashed", color = "grey", linewidth = 1) +

# Set colors for the bars
scale_color_manual(values = c("gain" = "#B73A20", "loss" = "#0D3EAA", "normal" = "black")) +

ggtitle(paste0(gene, " - ", chrom, arm)) +
xlab('Chromosome position') + ylab('Copy number')

print(p)
  

return()
}




pdf(opt$figure_file)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')

# plot all unique genes
lapply(unique(input_df$gene), coverage_plot, input_df, stats_df)

dev.off()


print(opt$figure_file)





