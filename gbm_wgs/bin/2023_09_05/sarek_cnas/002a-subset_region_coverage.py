# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def load_df(pcg_bed_file, cna_genes, flanking_bp = 2_000_000):
    # load data
    df = pd.read_csv(pcg_bed_file, sep='\t', header=None)
    df.columns = ["chromosome", "start", "end", "gene", 'type', 'info']

    # subset data
    df = df[df['gene'].isin(cna_genes)]

    # add original start and stop
    df['original_start'] = df['start'].copy()
    df['original_end'] = df['end'].copy()

    # add 100kb on either side, minimum is 0
    df['start'] = df['start'] - 2_000_000
    df['start'] = np.where(df['start'] < 0, 0, df['start'])
    df['end'] = df['end'] + 2_000_000

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load data and add 100kb on either side
    df = load_df(pcg_bed_file, cna_genes)

    # save data
    df.to_csv(modified_gene_bed_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # genes of interest
    cna_genes = ['TERT', 'EGFR', 'RB1', 'ATRX', 'PTEN', 'MET', 'CDK6', 'CDKN2A', 'MDM2', 'CDK4', 'NF1', 'PIK3R1', 'TP53', 'FUBP1', 'PDGFRA', 'MDM4', 'PIK3CA', 'CIC', 'IDH1', 'MYCN', 'CCND2']


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["pcg_bed_file=", "modified_gene_bed_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--pcg_bed_file"):
            pcg_bed_file = str(arg)

        if opt in ("--modified_gene_bed_file"):
            modified_gene_bed_file = str(arg)

    main()


