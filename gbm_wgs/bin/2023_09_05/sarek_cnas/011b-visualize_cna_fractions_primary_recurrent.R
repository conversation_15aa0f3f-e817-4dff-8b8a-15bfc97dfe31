# Alec <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}





stacked_barplot = function(cna_type, df){

# subset to only the cna type
input_df = df[df$cna_type == cna_type,]

# plot
p = ggplot(input_df, aes(x = patient, y = fraction, fill = tumor_fraction)) + plot_theme() +
geom_bar(stat = "identity", position = "stack") +

# Set colors for the bars
scale_fill_manual(values = c("primary_fraction" = "#df7b20", "recurrent_fraction" = "#229a32", "shared_fraction" = "#b98acd")) +

ggtitle(cna_type) +
xlab('') + ylab('Proportion of mutations')

print(p)

return()
}




sort_samples = function(df){
# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# order samples
sorted_vector <- unique(df$patient)[order(sapply(unique(df$patient), extract_numeric))]
df$patient = factor(df$patient, levels = sorted_vector)

return(df)
}





pdf(opt$figure_file)

# load df
stats_df = read.csv(opt$figure_stats_file, sep='\t')

# sort tumor_fraction factors
stats_df$tumor_fraction = factor(stats_df$tumor_fraction, levels = c("primary_fraction", "recurrent_fraction", "shared_fraction"))

# sort by name
stats_df = sort_samples(stats_df)

# create the plot
lapply(unique(stats_df$cna_type), stacked_barplot, stats_df)


dev.off()


print(opt$figure_file)




