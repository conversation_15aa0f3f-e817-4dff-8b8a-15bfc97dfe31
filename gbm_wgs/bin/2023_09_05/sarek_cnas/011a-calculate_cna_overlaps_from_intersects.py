# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''

def get_coverage(file):
    # read as df
    df = pd.read_csv(file, sep='\t', header=None)

    # calculate coverage
    return (df[2] - df[1]).sum()


def calculate_coverage_fractions(intersected_file, primary_file, recurrent_file):
    # coverage of each file
    coverage_primary = get_coverage(primary_file)
    coverage_recurrent = get_coverage(recurrent_file)
    coverage_intersect = get_coverage(intersected_file)

    # Compute union coverage
    coverage_union = coverage_primary + coverage_recurrent - coverage_intersect

    # Calculate the fractions
    frac_common = coverage_intersect / coverage_union if coverage_union > 0 else 0
    frac_primary_only = (coverage_primary - coverage_intersect) / coverage_union if coverage_union > 0 else 0
    frac_recurrent_only = (coverage_recurrent - coverage_intersect) / coverage_union if coverage_union > 0 else 0

    # Put this information into a pandas DataFrame
    df = {
        "bases_common": [coverage_intersect],
        "bases_primary": [coverage_primary],
        "bases_recurrent": [coverage_recurrent],
        "shared_fraction": [frac_common],
        "primary_fraction": [frac_primary_only],
        "recurrent_fraction": [frac_recurrent_only]
    }

    return pd.DataFrame(df)


def calculate_total_cna_coverage(df):
    new_df = []

    for patient in df['patient'].unique():
        # subset to patient
        patient_df = df[df['patient'] == patient]

        # calculate the total coverage across both cnas
        total_common = patient_df['bases_common'].sum()
        total_primary = patient_df['bases_primary'].sum()
        total_recurrent = patient_df['bases_recurrent'].sum()

        # calculate the total fractions
        total_frac_common = total_common / (total_primary + total_recurrent - total_common) if (total_primary + total_recurrent - total_common) > 0 else 0
        total_frac_primary = (total_primary - total_common) / (total_primary + total_recurrent - total_common) if (total_primary + total_recurrent - total_common) > 0 else 0
        total_frac_recurrent = (total_recurrent - total_common) / (total_primary + total_recurrent - total_common) if (total_primary + total_recurrent - total_common) > 0 else 0

        # add to new df
        new_df.append({
            'patient': patient,
            'cna_type': 'total',
            'bases_common': total_common,
            'bases_primary': total_primary,
            'bases_recurrent': total_recurrent,
            'shared_fraction': total_frac_common,
            'primary_fraction': total_frac_primary,
            'recurrent_fraction': total_frac_recurrent
        })

    # create df
    new_df = pd.DataFrame(new_df)

    return pd.concat([df, new_df])


def process_cna_overlaps(intersected_files, primary_files, recurrent_files):
    # determine the patient ids across each file
    patient_ids = list(set([file.split('/')[-2] for file in intersected_files]))

    res_df = []
    
    for cna_type in ['gain', 'loss']:
        # subset files to the cna type
        cna_intersected_files = [file for file in intersected_files if cna_type in file]
        cna_primary_files = [file for file in primary_files if cna_type in file]
        cna_recurrent_files = [file for file in recurrent_files if cna_type in file]

        # for each patient, calculate the coverage fractions
        for patient in patient_ids:
            # get the files of interest
            intersected_file = [file for file in cna_intersected_files if patient in file][0]
            primary_file = [file for file in cna_primary_files if patient in file][0]
            recurrent_file = [file for file in cna_recurrent_files if patient in file][0]

            # calculate the coverage fractions
            df = calculate_coverage_fractions(intersected_file, primary_file, recurrent_file)

            # add patient id
            df['patient'] = patient

            # add cna type
            df['cna_type'] = cna_type

            # append to results
            res_df.append(df)

    # concat
    res_df = pd.concat(res_df)

    # calculate the total across both cnas for each patient
    res_df = calculate_total_cna_coverage(res_df)

    # subset and melt for stats file
    stats_df = res_df[['patient', 'cna_type', 'shared_fraction', 'primary_fraction', 'recurrent_fraction']]
    stats_df = stats_df.melt(id_vars=['patient', 'cna_type'], var_name='tumor_fraction', value_name='fraction')

    return stats_df, res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # determine file names
    file_regex = f'{results_dir}/analysis_sarek/consensus_cnas/*/*-intersected_primary_recurrent_*.tsv'
    intersected_files = glob.glob(file_regex)

    file_regex = f'{results_dir}/analysis_sarek/consensus_cnas/*/*-primary_cnas_merged_unique_*.tsv'
    primary_files = glob.glob(file_regex)

    file_regex = f'{results_dir}/analysis_sarek/consensus_cnas/*/*-recurrent_cnas_merged_unique_*.tsv'
    recurrent_files = glob.glob(file_regex)

    # process each patient
    stats_df, df = process_cna_overlaps(intersected_files, primary_files, recurrent_files)

    # save to files
    df.to_csv(cna_overlap_fractions_file, sep='\t', index=False)
    stats_df.to_csv(cna_overlap_fractions_stats_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "cna_overlap_fractions_file=", "cna_overlap_fractions_stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--cna_overlap_fractions_file"):
            cna_overlap_fractions_file = str(arg)
        if opt in ("--cna_overlap_fractions_stats_file"):
            cna_overlap_fractions_stats_file = str(arg)

    main()


