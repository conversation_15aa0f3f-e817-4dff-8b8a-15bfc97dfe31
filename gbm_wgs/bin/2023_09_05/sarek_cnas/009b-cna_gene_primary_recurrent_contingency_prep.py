# <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np



help_message = '''
Failed
'''


def process_cnas_to_contingency(combined_pcg_cna_file, n_samples = 24):
    # load df
    df = pd.read_csv(combined_pcg_cna_file, sep="\t")

    # drop 'none' gene
    df = df[df['gene'] != 'none']

    # add tumor type column and combine with "cna"
    df['tumor_type'] = df['sample'].str.split("-").str[1]
    df['cna_tumor_type'] = df['tumor_type'] + "_" + df['cna']

    # Group by gene and tumor_type, and calculate the value counts for "gain" and "loss"
    gene_counts = df.groupby(['gene'])['cna_tumor_type'].value_counts().unstack(fill_value=0)

    # add a column for the number of samples minus the number of gains and losses
    gene_counts['neutral_count'] = n_samples - gene_counts.sum(axis=1)

    # correct index
    gene_counts.index = gene_counts.index.to_numpy()

    print(gene_counts)
    
    return gene_counts


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load data and process into contingency table
    df = process_cnas_to_contingency(combined_pcg_cna_file)

    # save the contingency table
    df.to_csv(cna_contingency_file, sep="\t")

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["combined_pcg_cna_file=", "cna_contingency_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--combined_pcg_cna_file"):
            combined_pcg_cna_file = str(arg)
        
        if opt in ("--cna_contingency_file"):
            cna_contingency_file = str(arg)
            

    main()



