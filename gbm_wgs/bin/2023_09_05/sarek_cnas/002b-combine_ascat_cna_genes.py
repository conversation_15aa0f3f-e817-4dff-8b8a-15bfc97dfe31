# Alec <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''

def compare_samples_genes(df):
    # get unique samples
    samples = df['sample'].unique()

    # get unique genes
    genes = df['gene'].unique()

    # create a list of all possible sample-gene pairs
    all_pairs = [(sample, gene) for sample in samples for gene in genes]

    # create a list of all existing sample-gene pairs
    existing_pairs = list(zip(df['sample'], df['gene']))

    # find the missing sample-gene pairs
    missing_pairs = list(set(all_pairs) - set(existing_pairs))

    for pair in missing_pairs:
        sample, gene = pair

        # find a sample-gene pair that exists
        mask = (df['sample'] == 'RLGS1-primary') & (df['gene'] == gene)

        # get the start, end, and chromosome columns
        start = df[mask]['start'].values[0]
        end = df[mask]['end'].values[0]
        chromosome = df[mask]['chromosome'].values[0]

        # create a list of missing sample-gene pairs
        missing_pairs = pd.DataFrame([[sample, gene]], columns=['sample', 'gene'])

        # add depth 0 to missing pairs
        missing_pairs['depth'] = 0

        # add the start and end columns from another sample-gene pair
        missing_pairs['start'] = start
        missing_pairs['end'] = end
        missing_pairs['chromosome'] = chromosome
        
        # append missing pairs to the dataframe
        df = pd.concat([df, missing_pairs], ignore_index=True)

    return df



def combine_coverage(files, max_depth=4, min_depth=-2.5):
    res = []

    # read files
    for file in files:
        df = pd.read_csv(file, sep='\t', header=None)
        df.columns = ["chromosome", "start1", "end1", "gene", 'type', 'start2', 'end2', 'info', 'chr2', 'start', 'end', 'depth']

        # subset 
        df = df[['chromosome', 'start', 'end', 'gene', 'depth']]

        # add sample name
        df['sample'] = "-".join(file.split('/')[-1].split('-')[:2])

        res.append(df)

    # combine files
    res = pd.concat(res)

    # set max and min depth
    mask = (res['depth'] > max_depth)
    res.loc[mask, 'depth'] = max_depth

    mask = (res['depth'] < min_depth)
    res.loc[mask, 'depth'] = min_depth

    # check to see each sample-gene pair exists; if not, add it with depth 0
    res = compare_samples_genes(res)

    return res


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process coverage files
    regex = f'{results_dir}/analysis_sarek/consensus_cnas/*/*-ascat_coverage_modified_gene_regions.tsv'
    files = glob.glob(regex)

    # combine coverage files
    df = combine_coverage(files)

    # save data
    df.to_csv(cna_coverage_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "cna_coverage_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--cna_coverage_file"):
            cna_coverage_file = str(arg)
            
    main()


