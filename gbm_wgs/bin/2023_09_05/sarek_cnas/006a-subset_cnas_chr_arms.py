# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''


def load_intersected_df(cna_file_intersected, max_cna = 10):
    # load df
    df = pd.read_csv(cna_file_intersected, sep="\t", header=None)

    # add columns
    df.columns = ["chr1", "start1", "end1", "gene", "chromosome", "arm", "range", 'chr2', 'start2', 'end2', 'event', 'copy_number', 'sample', 'sample_number', 'sample_type']

    res = []

    # iterate through each gene
    for gene in df["gene"].unique():
        df_gene = df[df["gene"] == gene]

        # extract range df
        main_columns = ["chromosome", "start1", "end1", "arm", "range", "gene"]
        main_df = df_gene[main_columns].iloc[[0],:]

        # subset to other columns
        columns_of_interest = ["chromosome", "start2", "end2", 'event', 'copy_number', 'gene', 'sample']
        df_gene = df_gene.loc[:, columns_of_interest]

        # rename columns for consistency
        df_gene.columns = ["chromosome", "start", "end", "event", "copy_number", 'gene', "sample"]
        main_df.columns = ["chromosome", "start", "end", "arm", "range", "gene"]

        # add expected copy number to main_df
        main_df['copy_number'] = 2
        main_df['event'] = "normal"

        # add descriptions to both
        main_df["description"] = "default"
        df_gene["description"] = "sample"

        # cap the start and end to be within the interval of main_df
        df_gene["start"] = np.maximum(df_gene["start"], main_df["start"].values[0])
        df_gene["end"] = np.minimum(df_gene["end"], main_df["end"].values[0])

        # add both to res
        res.append(main_df)
        res.append(df_gene)

    # concat res
    res = pd.concat(res)

    # set max copy number
    res['copy_number'] = res['copy_number'].apply(lambda x: min(x, max_cna))

    return res


def subset_pcg_file(pcg_bed_file, cna_genes):
    # load file
    df = pd.read_csv(pcg_bed_file, sep="\t", header=None)

    # add columns
    df.columns = ["chromosome", "start", "end", "gene", 'type', 'info']

    # subset to genes of interest
    df = df[df['gene'].isin(cna_genes)]

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load data
    df = load_intersected_df(cna_file_intersected)

    # load gff and subset to genes of interest
    stats_df = subset_pcg_file(pcg_bed_file, cna_genes)

    # save to files
    df.to_csv(figure_data_file, sep="\t", index=False)
    stats_df.to_csv(figure_stats_file, sep="\t", index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    # genes of interest
    cna_genes = ['CDKN2A', 'EGFR', 'PDGFRA', 'CDK4', 'CDK6', 'PTEN', 'MDM2', 'MET', 'CCND2', 'MYCN', 'MDM4', 'ATRX']


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["cna_file_intersected=", "pcg_bed_file=", "figure_data_file=", "figure_stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--cna_file_intersected"):
            cna_file_intersected = str(arg)
        if opt in ("--pcg_bed_file"):
            pcg_bed_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)

    main()


