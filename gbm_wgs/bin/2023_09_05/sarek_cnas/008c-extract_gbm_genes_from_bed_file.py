# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''

def subset_to_gbm_genes(all_gene_bed_file, gbm_genes):
    # load df
    df = pd.read_csv(all_gene_bed_file, sep='\t')

    # subset to gbm genes
    df = df[df['gene'].isin(gbm_genes)]

    # replace chr23 with chrx
    mask = df['chrom'].str.contains('chr23')
    df.loc[mask, 'chrom'] = 'chrX'
    
    # remove non-standard chromosomes (anything out of 1-22, X, Y)
    chr_of_interest = ['chr' + str(i) for i in range(1, 23)] + ['chrX', 'chrY']
    df = df[df['chrom'].isin(chr_of_interest)]

    # rename some columns
    df = df.rename(columns={'chrom':'chromosome', 'end':'stop'})

    # subset to columns of interest
    df = df[['chromosome', 'start', 'stop', 'gene']]

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # subset to gbm genes
    df = subset_to_gbm_genes(all_gene_bed_file, gbm_genes)

    # save to files
    df.to_csv(gbm_genes_bed_file, sep='\t', index=False)

    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # all gbm-associated genes
    gbm_genes = ['EGFR', 'ATRX', 'PTEN', 'CDK6', 'MET', 'CDKN2A', 'RB1', 'MDM4', 'CDK4', 'TP53', 'PDGFRA', 'FUBP1', 'NF1', 'MDM2', 'MYCN', 'PIK3CA', 'PIK3R1', 'CCND2', 'CIC', 'IDH1', 'TERT']

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["all_gene_bed_file=", "gbm_genes_bed_file=", "figure_stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--all_gene_bed_file"):
            all_gene_bed_file = str(arg)

        if opt in ("--gbm_genes_bed_file"):
            gbm_genes_bed_file = str(arg)

    main()




