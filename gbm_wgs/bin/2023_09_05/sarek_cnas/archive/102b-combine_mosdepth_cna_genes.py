# Alec <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''

def combine_coverage(files):
    res = []

    # read files
    for file in files:
        df = pd.read_csv(file, sep='\t', header=None)
        df.columns = ["chromosome", "start1", "end1", "gene", 'type', 'start2', 'end2', 'info', 'chr2', 'start', 'end', 'depth']

        # subset 
        df = df[['chromosome', 'start', 'end', 'gene', 'depth']]

        # add sample name
        df['sample'] = "-".join(file.split('/')[-1].split('-')[:2])

        res.append(df)

    # combine files
    res = pd.concat(res)

    return res


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process coverage files
    regex = f'{results_dir}/analysis_sarek/consensus_cnas/*/*-mosdepth_coverage_modified_gene_regions.tsv'
    files = glob.glob(regex)

    # combine coverage files
    df = combine_coverage(files)

    # save data
    df.to_csv(cna_coverage_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "cna_coverage_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--cna_coverage_file"):
            cna_coverage_file = str(arg)
            
    main()


