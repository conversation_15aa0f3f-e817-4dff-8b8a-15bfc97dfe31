# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''


def combine_cnas(cna_files):
    # read all cna files
    dfs = []
    for cna_file in cna_files:
        df = pd.read_csv(cna_file, sep='\t')

        # add sample name
        sample = "-".join(cna_file.split('/')[-1].split('_')[0].split('-')[:2])
        df['sample'] = sample

        dfs.append(df)

    # merge all cna files
    df = pd.concat(dfs, ignore_index=True)

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # list of cna files
    regex = f'{results_dir}/sarek_wgs/*/variant_calling/ascat/*/*-blood.cnvs.txt'
    cna_files = glob.glob(regex)

    # merge files
    df = combine_cnas(cna_files)

    # save results
    df.to_csv(cna_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "cna_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--cna_file"):
            cna_file = str(arg)

    main()




