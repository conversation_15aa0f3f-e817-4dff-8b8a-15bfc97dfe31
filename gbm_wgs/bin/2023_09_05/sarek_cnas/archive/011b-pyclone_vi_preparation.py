# <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import vcf
from concurrent.futures import ProcessPoolExecutor


help_message = '''
Failed
'''


def get_cn_at_position(df, position, chrom):
    # get cna at position if the position is profiled
    mask = (df['chr'] == chrom) & (df['startpos'].astype('int') <= int(position)) & (df['endpos'].astype('int') >= int(position))
    major_cn = df.loc[mask, 'nMajor']
    minor_cn = df.loc[mask, 'nMinor']

    # if the position does not exist, assume 1,1
    if len(major_cn) == 0:
        major_cn, minor_cn = [1,1]

    # extract just the value
    else:
        major_cn = major_cn.values[0]
        minor_cn = minor_cn.values[0]
    
    return major_cn, minor_cn


def extract_counts(ref, alt, format_field, tumor):
    # extract counts from tumor
    ref_counts = 0
    alt_counts = 0

    # mutect2 and freebayes
    if 'AD' in format_field:
        for i, f in enumerate(format_field.split(':')):
            if f == 'AD':
                ref_counts, alt_counts = tumor.split(':')[i].split(',')

    # strelka
    elif 'AU' in format_field:
        # Determine index of each nucleotide count field based on the reference and alternate alleles
        allele_field_map = {
            'A': 'AU',
            'C': 'CU',
            'G': 'GU',
            'T': 'TU'
        }

        ref_field = allele_field_map[ref]
        alt_field = allele_field_map[alt]

        # Identify which index corresponds to the reference and alternate fields
        ref_index = format_field.split(':').index(ref_field)
        alt_index = format_field.split(':').index(alt_field)

        # Extract the reference and alternate counts
        ref_counts = tumor.split(':')[ref_index].split(",")[0]
        alt_counts = tumor.split(':')[alt_index].split(",")[0]

    # strelka indels
    elif 'TIR' in format_field:
        #FORMAT=<ID=TOR,Number=2,Type=Integer,Description="Other reads (weak support or insufficient indel breakpoint overlap) for tiers 1,2">
        #FORMAT=<ID=TIR,Number=2,Type=Integer,Description="Reads strongly supporting indel allele for tiers 1,2">
        ref_counts = tumor.split(':')[format_field.split(':').index('TOR')].split(',')[0]
        alt_counts = tumor.split(':')[format_field.split(':').index('TIR')].split(',')[0]

    else:
        print("ERROR: Unknown format")
        print(format_field)
        print(tumor)

    return ref_counts, alt_counts



def generate_record(args):
    sample, vcf_df, cna_df, ploidy_purity_df = args
    
    # process each line in vcf
    records = []

    # get purity and ploidy
    purity = ploidy_purity_df['purity'][ploidy_purity_df['sample'] == sample].iloc[0]
        
    ploidy = 2
    
    for _, row in vcf_df.iterrows():
        # major and minor cna at position
        minor_cn, major_cn = get_cn_at_position(cna_df, row['POS'], row['CHROM'])

        # extract coverage information
        ref_counts, alt_counts = extract_counts(row['REF'], row['ALT'], row['FORMAT'], row['TUMOR'])

        records.append([row['variant_id'], sample, ref_counts, alt_counts, major_cn, minor_cn, ploidy, purity])

    return records

def pyclone_preparation(vcf_file, cna_file, ploidy_purity_file, threads):
    # load file
    cna_df = pd.read_csv(cna_file, sep='\t')
    ploidy_purity_df = pd.read_csv(ploidy_purity_file, sep='\t')
    vcf_df = pd.read_csv(vcf_file, sep='\t')

    # adjust chromosome names
    cna_df['chr'] = 'chr' + cna_df['chr']

    # arguments to process each sample
    args = [(sample, vcf_df[vcf_df['sample'] == sample], cna_df[cna_df['sample'] == sample], ploidy_purity_df) for sample in vcf_df['sample'].unique()]

    # multiprocessing
    with ProcessPoolExecutor(max_workers=threads) as executor:
        results = executor.map(generate_record, args)

    # combine results
    records = []

    for res in results:
        records += res

    # create dataframe
    res_df = pd.DataFrame(records, columns=['mutation_id', 'sample_id', 'ref_counts', 'alt_counts', 'major_cn', 'minor_cn', 'normal_cn', 'tumour_content'])

    return res_df

def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process and combine vcfs
    res_df = pyclone_preparation(input_vcf, cna_file, ploidy_purity_file, threads)

    # save results
    res_df.to_csv(pyclone_input_file, sep='\t', index=False)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["input_vcf=", "cna_file=", "ploidy_purity_file=", "pyclone_input_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--input_vcf"):
            input_vcf = str(arg)
        if opt in ("--cna_file"):
            cna_file = str(arg)
        if opt in ("--ploidy_purity_file"):
            ploidy_purity_file = str(arg)

        if opt in ("--pyclone_input_file"):
            pyclone_input_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()




