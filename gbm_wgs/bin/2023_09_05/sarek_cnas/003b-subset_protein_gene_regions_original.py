# Alec <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

help_message = '''
Failed
'''

def load_and_process(modified_gene_bed_file):
    # read df
    df = pd.read_csv(modified_gene_bed_file, sep='\t')

    # replace start and end with original values
    df['start'] = df['original_start']
    df['end'] = df['original_end']

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load df and replace start and end with original values
    df = load_and_process(modified_gene_bed_file)

    # save df
    df.to_csv(original_gene_bed_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["modified_gene_bed_file=", "original_gene_bed_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--modified_gene_bed_file"):
            modified_gene_bed_file = str(arg)

        if opt in ("--original_gene_bed_file"):
            original_gene_bed_file = str(arg)
            
    main()


