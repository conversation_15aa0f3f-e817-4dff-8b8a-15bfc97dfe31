# Alec <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from concurrent.futures import ThreadPoolExecutor
import scipy.stats as stats

help_message = '''
Failed
'''

def run_stats_on_row(row):
    # compare gains to any other
    primary_gain = row['primary_gain']
    recurrent_gain = row['recurrent_gain']
    primary_not_gain = 12 - row['primary_gain'] 
    recurrent_not_gain = 12 - row['recurrent_gain']

    # perform fisher's exact test
    contingency = np.array([[primary_gain, primary_not_gain], [recurrent_gain, recurrent_not_gain]])

    gain_pvalue = stats.fisher_exact(contingency)[1]

    # repeat for losses
    primary_loss = row['primary_loss']
    recurrent_loss = row['recurrent_loss']
    primary_not_loss = 12 - row['primary_loss']
    recurrent_not_loss = 12 - row['recurrent_loss']

    contingency = np.array([[primary_loss, primary_not_loss], [recurrent_loss, recurrent_not_loss]])

    loss_pvalue = stats.fisher_exact(contingency)[1]

    # compare any gain / loss to normal
    primary_any = row['primary_gain'] + row['primary_loss']
    recurrent_any = row['recurrent_gain'] + row['recurrent_loss']
    primary_not_any = 12 - primary_any
    recurrent_not_any = 12 - recurrent_any

    contingency = np.array([[primary_any, primary_not_any], [recurrent_any, recurrent_not_any]])

    any_pvalue = stats.fisher_exact(contingency)[1]

    # return p-values
    return (row['gene'], gain_pvalue, loss_pvalue, any_pvalue)


def run_stats_on_contingency_table(cna_contingency_file, threads):
    # load df
    df = pd.read_csv(cna_contingency_file, sep="\t", index_col=0)

    # add gene columns
    df['gene'] = df.index.to_numpy()

    # create arguments for multi-threading
    args = [(row) for i, row in df.iterrows()]

    # run stats on each row
    with ThreadPoolExecutor(max_workers=threads) as executor:
        results = executor.map(run_stats_on_row, args)

    # create a new df with p-values
    pvalues = pd.DataFrame(results, columns=['gene', 'gain_pvalue', 'loss_pvalue', 'any_pvalue'])

    return pvalues


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and process file
    df = run_stats_on_contingency_table(cna_contingency_file, threads)

    # save stats table
    df.to_csv(cna_gene_contingency_stats_file, sep="\t", index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["cna_contingency_file=", "cna_gene_contingency_stats_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:        
        if opt in ("--cna_contingency_file"):
            cna_contingency_file = str(arg)
            
        if opt in ("--cna_gene_contingency_stats_file"):
            cna_gene_contingency_stats_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()



