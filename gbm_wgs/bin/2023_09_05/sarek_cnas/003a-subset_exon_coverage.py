# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def convert_to_bed_file_format(gtf_file, genes_of_interest_dict, annotation_type_regex = 'xon'):
    # read gff file
    df = pd.read_csv(gtf_file, sep='\t', header=None, comment='#')

    # add column names
    column_names = ['seqname', 'source', 'type', 'start', 'end', 'score', 'strand', 'phase', 'attributes']
    df.columns = column_names

    # define gene name
    df['gene'] = df['attributes'].str.extract(r'gene "([^;]+)"')
    df['transcript_id'] = df['attributes'].str.extract(r'transcript_id "([^;]+)"')
    
    # subset to genes of interest and their gene_ids
    # df = df[df['gene'].isin(list(genes_of_interest_dict.keys()))]
    df = df[df['transcript_id'].isin(list(genes_of_interest_dict.values()))]

    # subset to annotation type of interest
    df = df[df['type'].str.contains(annotation_type_regex)]

    # add chromosome
    df['chr'] = df['seqname'].str.split(".").str[0].str.split("_").str[1].str.replace(r'^0+', '', regex=True)

    # subset to chromosomes 1-22 and x and y
    df = df[df['chr'].isin([str(i) for i in range(1, 23)] + ['X', 'Y'])]

    # add exon number
    df['exon_number'] = df['attributes'].str.extract(r'exon_number "([^;]+)"')

    # sort values
    df = df.sort_values(['chr', 'start'])

    # filter columns
    df = df[['chr', 'start', 'end', 'gene', 'transcript_id', 'exon_number', 'type', 'attributes']]

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process cnvkit file
    df = convert_to_bed_file_format(gtf_file, genes_of_interest_dict)

    # save to files
    df.to_csv(bed_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # genes of interest
    genes_of_interest_dict = {'EGFR':'NM_005228.5', 'TERT':'NM_198253.3', "MDM2":'NM_002392.6'}

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["gtf_file=", "bed_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--gtf_file"):
            gtf_file = str(arg)

        if opt in ("--bed_file"):
            bed_file = str(arg)

    main()


