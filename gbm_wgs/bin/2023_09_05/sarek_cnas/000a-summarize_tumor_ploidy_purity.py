# Alec <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''


def load_tumor_ploidy_purity(files):
    # results df
    res_df = []

    # load each file and add details to res_df
    for file in files:
        df = pd.read_csv(file, sep='\t')

        # define ploidty and purity
        ploidy = np.round(df.iloc[0,1])
        purity = df.iloc[0,0]

        # extract sample name
        sample = "-".join(file.split('/')[-1].split('-')[:2])

        res_df.append([sample, purity, ploidy])

    # convert to df
    res_df = pd.DataFrame(res_df, columns=['sample', 'purity', 'ploidy'])

    # add all samples if missing
    all_samples = pd.DataFrame({'sample': [f"RLGS{i}-{tumor}" for i in range(1, 13) for tumor in ['primary', 'recurrent']]})
    res_df = all_samples.merge(res_df, on='sample', how='left')

    print(res_df)

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # list files
    regex = f'{results_dir}/sarek_wgs/*/variant_calling/ascat/*/*.purityploidy.txt'
    files = glob.glob(regex)

    # load and combine dfs
    df = load_tumor_ploidy_purity(files)

    # save to files
    df.to_csv(ploidy_purity_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "ploidy_purity_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--ploidy_purity_file"):
            ploidy_purity_file = str(arg)

    main()