# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''


def combined_files(files):
    # read in all files
    dfs = []

    for file in files:
        df = pd.read_csv(file, sep='\t', header=None)

        # add column names
        df.columns = ['chromosome', 'start', 'end', 'classification', 'coverage']
        df['classification'] = df['classification'].str.split(",").str[0]

        # add sample name
        sample = file.split('/')[-1].split('_')[0]
        df['sample'] = sample

        # add sample number and type
        df['sample_number'] = df['sample'].str.split('-').str[0]
        df['type'] = df['sample'].str.split('-').str[1]

        # cleanup classification
        df['classification'] = df['classification'].str.split(",").str[0]

        # add chromosome string
        df['chromosome'] = 'chr' + df['chromosome'].astype('str')

        # subset to unique rows
        df = df.drop_duplicates()

        dfs.append(df)

    # combine all files
    df = pd.concat(dfs)

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # list files in results directory
    regex = f'{results_dir}/analysis_sarek/consensus_cnas/*_cnas_merged_unique.tsv'
    files = glob.glob(regex)

    # process cnvkit file
    df = combined_files(files)

    # save to file
    df.to_csv(figure_stats_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "figure_stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
            
    main()


