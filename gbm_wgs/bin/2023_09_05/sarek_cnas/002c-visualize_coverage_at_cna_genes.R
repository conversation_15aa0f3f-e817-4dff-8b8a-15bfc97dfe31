# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




coverage_plot = function(gene, input_df, stats_df) {

# subset dfs
input_df = input_df[input_df$gene == gene,]
stats_df = stats_df[stats_df$gene == gene,]

# plot
p = ggplot(input_df, aes(x = start, y = depth)) + plot_theme() +
geom_point(pch=21, fill='red', size=1) +

# add gene loci
geom_segment(data = stats_df, aes(x = original_start, xend = original_end), y=0, linewidth = 1, color = 'purple') +

facet_wrap(~ sample, ncol = 2, scales = "fixed") +  # Adjust the number of rows automatically

ylim(min_y, max_y) +  # Set the y-axis limits

ggtitle(gene) +
xlab('Chromosome position') + ylab('Coverage')

print(p)

return()
}



pdf(opt$figure_file, height=15, width = 5.5)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')


# define the min and max values for the y-axis
min_y = min(input_df$depth)
max_y = max(input_df$depth)

# plot all unique genes
lapply(unique(input_df$gene), coverage_plot, input_df, stats_df)

dev.off()


print(opt$figure_file)





