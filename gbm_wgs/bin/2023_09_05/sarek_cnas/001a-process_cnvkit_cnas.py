# Alec <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def process_cnvkit_file(raw_cnv_file, p_cutoff=0.05):
    cols_oi = ['chrom', 'start', 'end', 'classification', 'copy_number']
    
    # read in file
    df = pd.read_csv(raw_cnv_file, sep='\t')

    # filter by p-value
    df = df[df['p_ttest'] < p_cutoff]

    # subset to non-normal cnas
    df = df[df['cn'] != 2]

    # classify as gain or loss
    df['classification'] = np.where(df['cn'] > 2, 'gain', 'loss')

    # remove "chr" from chromosome
    df['chromosome'] = df['chromosome'].str.replace('chr', '')

    # subset to relevant columns
    df = df.rename(columns={'chromosome':'chrom', 'cn':'copy_number'})
    df = df[cols_oi]

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process cnvkit file
    df = process_cnvkit_file(raw_cnv_file)

    # separate into gain and loss files
    mask = df['classification'] == 'gain'
    df_gain = df[mask]
    df_loss = df[~mask]

    # save to files
    df_gain.to_csv(processed_gain_file, sep='\t', index=False)
    df_loss.to_csv(processed_loss_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["raw_cnv_file=", "processed_gain_file=", "processed_loss_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--raw_cnv_file"):
            raw_cnv_file = str(arg)

        if opt in ("--processed_gain_file"):
            processed_gain_file = str(arg)
        if opt in ("--processed_loss_file"):
            processed_loss_file = str(arg)

    main()


