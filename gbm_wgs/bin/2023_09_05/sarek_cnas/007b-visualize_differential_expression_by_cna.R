# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)
library(ggpubr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



boxplot = function(gene, input_df, stats_df) {

# Subset to gene
input_df = input_df[input_df$gene == gene,]
stats_df = stats_df[stats_df$gene == gene,]

    
# Create the boxplot
p = ggplot(input_df, aes(x = cna, y = tpm)) + plot_theme() +
geom_boxplot(outlier.shape = NA) +  # Remove outliers from the boxplot
geom_jitter(aes(color = cna), width = 0.2, size = 1.5, alpha = 0.7) +  # Add jittered points
ggtitle(gene) +
xlab("CNA group") + ylab('Expression (TPM, log10)') +

# Add p-values
geom_text(data = stats_df, aes(label = signif(u_test_p_value, 3))) +

# # Add pairwise p-values with jittered y positions
# stat_compare_means(
# method = "wilcox.test",  # Choose the appropriate test
# aes(label = ..p.signif..),  # Use significance stars
# comparisons = pairwise_comparisons,
# label.y = jitter(
# rep(max(input_df$tpm, na.rm = TRUE) + seq(0.5, 1.5, length.out = length(pairwise_comparisons)), 
# each = 1), 
# amount = max(input_df$tpm, na.rm = TRUE)/10 # Adjust the jitter range
# )
# ) +

theme(legend.position = "none")  # Remove the legend

print(p)

  return()
}



pdf(opt$figure_file)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')

# log transform tpm
input_df$tpm = log10(input_df$tpm)

# apply to all genes
lapply(unique(input_df$gene), boxplot, input_df, stats_df)


dev.off()


print(opt$figure_file)





