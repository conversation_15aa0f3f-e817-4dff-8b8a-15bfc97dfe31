# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)
library(hexbin)  # Load hexbin package


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




density_plot = function(df){

min_value = 0
max_value = 1000

# density polygons
p = ggplot(df, aes(x = primary, y = recurrent)) + plot_theme() +
geom_hex(bins = 50, na.rm = FALSE) +  # Use geom_hex for hexbin plot with specified number of bins
  
facet_wrap(~ patient_correlation, nrow = 2) +  # Facet by patient into 2 rows and 6 columns

scale_fill_gradientn(colours = c("white", "#1d00bf"), name = "Density Level",
        limits = c(min_value, max_value),  # Set minimum and maximum density levels
        oob = scales::squish, # Squish values above max_value to max_value
        na.value = "white")+ 

ggtitle('') +
xlab('Primary methylation rate') + ylab('Recurrent methylation rate') +
guides(fill = guide_colourbar(ticks.colour = "black", frame.colour = 'black')) +

theme(axis.text.x = element_text(angle = 0))

print(p)

    
return()

}


pdf(opt$figure_file, width=22, height=10)
# pdf(opt$figure_file)

# load dfs
df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')

# sort patients by correlation coefficient
stats_df$patient_correlation = fct_reorder(stats_df$patient_correlation, stats_df$correlation, .desc = TRUE)
df$patient_correlation = factor(df$patient_correlation, levels = levels(stats_df$patient_correlation))

print(unique(df$patient_correlation))

# apply to all patients
density_plot(df)


dev.off()


print(opt$figure_file)




