# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import scipy.stats as stats
from statsmodels.stats import multitest

help_message = '''
Failed
'''


def calculate_mean_methylation_by_gene_sample(combined_promoter_methylation_file):
    # load df
    df = pd.read_csv(combined_promoter_methylation_file, sep='\t')

    # subset to relevant columns
    df = df[['gene', 'sample', 'percent_methylated']]

    # calculate mean methylation per gene and sample
    df = df.groupby(['gene', 'sample']).mean().reset_index()

    # add primary or recurrent status and sample ID
    df['tumor_type'] = df['sample'].str.split('-').str[1]
    df['patient_id'] = df['sample'].str.split('-').str[0]

    # rename percent_methylated to mean_methylation
    df = df.rename(columns={'percent_methylated': 'mean_methylation'})

    return df

def calculate_stats(df):
    # mann-whitney u test between primary and recurrent combined
    primary = df[df['tumor_type'] == 'primary']
    recurrent = df[df['tumor_type'] == 'recurrent']

    p = stats.mannwhitneyu(primary['mean_methylation'], recurrent['mean_methylation'])[1]

    # calculate fold change
    fold_change = np.mean(recurrent['mean_methylation']) / np.mean(primary['mean_methylation'])

    return p, fold_change

def compare_primary_recurrent_methylation(df):
    # calculate stats for whole df
    p, fold_change = calculate_stats(df)

    # create list for results
    res_df = [[p, fold_change, 'combined']]

    # for each patient_id, compare primary and recurrent
    for patient_id in df['patient_id'].unique():
        # subset to patient
        sub_df = df[df['patient_id'] == patient_id]
        
        # calculate stats
        p, fold_change = calculate_stats(sub_df)

        # add to results
        res_df.append([p, fold_change, patient_id])

    # create df
    res_df = pd.DataFrame(res_df, columns=['p_value', 'fold_change', 'patient_id'])

    # fdr correction
    res_df['fdr'] = multitest.multipletests(res_df['p_value'], method='fdr_bh')[1]
    
    return res_df


def process_methlyation_file(combined_promoter_methylation_file):
    # process methylation by gene file
    df = calculate_mean_methylation_by_gene_sample(combined_promoter_methylation_file)

    # compare primary and recurrent methylation
    res_df = compare_primary_recurrent_methylation(df)

    return df, res_df

def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # process methylation file
    df, res_df = process_methlyation_file(combined_promoter_methylation_file)

    # save to files
    df.to_csv(mean_promoter_methylation_by_gene_sample_file, sep='\t', index=False)
    res_df.to_csv(primary_recurrent_gene_promoter_methylation_stats_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["combined_promoter_methylation_file=", "mean_promoter_methylation_by_gene_sample_file=", "primary_recurrent_gene_promoter_methylation_stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--combined_promoter_methylation_file"):
            combined_promoter_methylation_file = str(arg)

        if opt in ("--mean_promoter_methylation_by_gene_sample_file"):
            mean_promoter_methylation_by_gene_sample_file = str(arg)
        if opt in ("--primary_recurrent_gene_promoter_methylation_stats_file"):
            primary_recurrent_gene_promoter_methylation_stats_file = str(arg)

    main()


