library(optparse)
library(forcats)
library(ggplot2)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




cols = c('primary'= '#df7b20',
        'recurrent' = '#229a32')


# histogram for all samples combined
histogram_cpgs = function(input_df){

# plot
p = ggplot(input_df, aes(x = percent_methylated, color=tumor_type)) + plot_theme() +
geom_density(linewidth = 1) + 

# add colors
scale_color_manual(values = cols) +

ggtitle('CpG methylation rates in gene promoters') +
xlab('Percent methylated') + ylab('Density')


print(p)

return()
}



pdf(opt$figure_file)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')

# plot all samples together
histogram_cpgs(input_df)

dev.off()


print(opt$figure_file)






