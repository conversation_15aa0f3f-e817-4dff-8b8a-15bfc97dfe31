library(optparse)
library(forcats)
library(ggplot2)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




cols = c('primary'= '#df7b20',
        'recurrent' = '#229a32')


# histogram for all patient_ids combined
combined_density_boxplots = function(input_df, stats_df){

pval = signif(stats_df$fdr[stats_df$patient_id == 'combined'], 3)

# plot
p = ggplot(input_df, aes(x = mean_methylation, color=tumor_type, fill = tumor_type)) + plot_theme() +
geom_density(linewidth = 1, alpha = 0.2) + 
# add colors
scale_color_manual(values = cols) +
scale_fill_manual(values = cols) +
ggtitle('Gene promoter methylation rates') +
xlab('Mean promoter methylation') + ylab('Number of genes (protein-coding)') +
# the angle of the x-axis labels
theme(axis.text.x = element_text(angle = 0))

print(p)

# plot
p = ggplot(input_df, aes(x = mean_methylation, color=tumor_type, fill = tumor_type)) + plot_theme() +
geom_histogram(bins=100) + 
# add colors
scale_color_manual(values = cols) +
scale_fill_manual(values = cols) +
ggtitle('Gene promoter methylation rates') +
xlab('Mean promoter methylation') + ylab('Number of genes (protein-coding)') +
# the angle of the x-axis labels
theme(axis.text.x = element_text(angle = 0))

print(p)

# plot boxplots
p = ggplot(input_df, aes(x = tumor_type, y = mean_methylation)) + plot_theme() +
geom_violin(trim = FALSE) +
geom_boxplot(width = 0.2, outlier.shape = NA) +

ggtitle(paste0("All patients, FDR=", pval)) +
xlab('') + ylab('Mean promoter methylation')

print(p)

return()
}


# plot histogram and boxplots for each type of tumor
patient_id_density_boxplots = function(patient_id, input_df, stats_Df){

input_df = input_df[input_df$patient_id == patient_id,]
pval = signif(stats_df$fdr[stats_df$patient_id == patient_id], 3)

# plot
p = ggplot(input_df, aes(x = mean_methylation, color=tumor_type, fill = tumor_type)) + plot_theme() +
geom_density(linewidth = 1, alpha = 0.2) + 
# add colors
scale_color_manual(values = cols) +
scale_fill_manual(values = cols) +
ggtitle('Gene promoter methylation rates') +
xlab('Mean promoter methylation') + ylab('Number of genes (protein-coding)') +
# the angle of the x-axis labels
theme(axis.text.x = element_text(angle = 0))

print(p)

# plot boxplots
p = ggplot(input_df, aes(x = tumor_type, y = mean_methylation)) + plot_theme() +
geom_boxplot() +
ggtitle(paste0(patient_id, ", FDR=", pval)) +
xlab('') + ylab('Mean promoter methylation')

print(p)

return()
}





sort_patients = function(df){
# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# order patient_ids
sorted_vector <- unique(df$patient_id)[order(sapply(unique(df$patient_id), extract_numeric))]
df$patient_id = factor(df$patient_id, levels = sorted_vector)

return(df)
}



pdf(opt$figure_file)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')

# order patient_ids
input_df = sort_patients(input_df)

# plot all patient_ids together
combined_density_boxplots(input_df, stats_df)

# plot boxplots for each patient_id
lapply(levels(input_df$patient_id), patient_id_density_boxplots, input_df, stats_df)

dev.off()


print(opt$figure_file)






