# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob
from scipy import stats

help_message = '''
Failed
'''

def load_file(file):
    # load file
    df = pd.read_csv(file, sep='\t')

    # sample name
    sample = file.split('/')[-1].split('_')[0]
    patient = sample.split('-')[0]

    # subset to chromosomes of interest
    chr_oi = [f'chr{i}' for i in range(1, 23)] + ['chrX', 'chrY']
    df = df[df['chrom'].isin(chr_oi)]

    # calculate mean coverage
    return [sample, patient, df['mean'].mean()]


def run_stats(df):
    # calculate correlation between mean coverage and methylation correlation
    correlation, pvalue = stats.spearmanr(df['mean_coverage'], df['correlation'])

    # correlate coverage difference with methylation correlation
    correlation_diff, pvalue_diff = stats.spearmanr(df['coverage_diff'], df['correlation'])

    # create df
    stats_df = pd.DataFrame([['mean_coverage', correlation, pvalue], ['coverage_diff', correlation_diff, pvalue_diff]], columns = ['variable', 'correlation', 'pvalue'])

    return stats_df


def load_and_correlate(results_dir, methylation_correlation_file):
    # list files with regex
    file_regex = f'{results_dir}//nanopore_somatic/*/*/qc/coverage/*_tumor.mosdepth.summary.txt'
    files = glob.glob(file_regex)

    # load each files and calculate the mean coverage across all chromosomes
    coverage_df = [load_file(file) for file in files]
    coverage_df = pd.DataFrame(coverage_df, columns=['sample', 'patient', 'mean_coverage'])

    # load methylation correlation file
    methylation_correlation_df = pd.read_csv(methylation_correlation_file, sep='\t')

    # for each patients in coverage_df, calculate mean coverage and the difference in coverage between both samples
    summary_df = []

    for patient in coverage_df['patient'].unique():
        sub_df = coverage_df[coverage_df['patient'] == patient]

        # calculate absolute difference in coverage between samples
        coverage_diff = np.abs(sub_df.iloc[0, 2] - sub_df.iloc[1, 2])

        # calculate mean coverage
        mean_coverage = sub_df['mean_coverage'].mean()

        summary_df.append([patient, coverage_diff, mean_coverage])

    summary_df = pd.DataFrame(summary_df, columns=['patient', 'coverage_diff', 'mean_coverage'])

    # merge with methylation correlation
    df = pd.merge(summary_df, methylation_correlation_df, on='patient')

    # run stats
    stats_df = run_stats(df)

    return df, stats_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load all files and associate coverage with methylation correlation
    df, stats_df = load_and_correlate(results_dir, methylation_correlation_file)

    # save to files
    df.to_csv(figure_data_file, sep='\t', index=False)
    stats_df.to_csv(figure_stats_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "methylation_correlation_file=", "figure_data_file=", "figure_stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)
        if opt in ("--methylation_correlation_file"):
            methylation_correlation_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)

    main()


