# <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import re

help_message = '''
Failed
'''

def load_df(file):
    # columns
    columns = ['chrom', 'start', 'end', 'modified base code and motif', 'score', 'strand', 'start position', 'end position', 'color', 'Nvalid_cov', 'percent modified', 'Nmod', 'Ncanonical', 'Nother_mod', 'Ndelete', 'Nfail', 'Ndiff', 'Nnocall']

    # for each file, load as df and subset to relevant columns
    df = pd.read_csv(file, sep='\t', header=None, compression='gzip')

    # Split the last column based on spaces
    split_columns = df.iloc[:, -1].str.split(expand=True)

    # Assign the split columns back to the original DataFrame
    df = pd.concat([df.iloc[:, :-1], split_columns], axis=1)
    df.columns = columns

    # subset to chromosomes of interest
    chr_of_interest = [f'chr{i}' for i in range(1, 23)] + ['chrX', 'chrY']
    df = df[df['chrom'].isin(chr_of_interest)]

    # set index as chrom and start
    df.index = df['chrom'].astype('str') + '-' + df['start'].astype('str')

    # subset to relevant columns
    df = df[['percent modified']]

    # rename columns
    sample = file.split("/")[-1].split("_")[1].split(".")[0]
    sample = re.sub(r'tumor', 'recurrent', sample)
    sample = re.sub(r'normal', 'primary', sample)

    df.columns = [sample]

    return df


def combined_methylation_files(primary_methylation_file, recurrent_methylation_file, nsites = 1000000):
    # Load the primary and recurrent methylation files
    primary_df = load_df(primary_methylation_file)
    recurrent_df = load_df(recurrent_methylation_file)

    # merge on index
    res_df = pd.merge(primary_df, recurrent_df, left_index=True, right_index=True, how ='inner')

    # reset index to "cpg_site"
    res_df = res_df.reset_index()
    res_df = res_df.rename(columns = {'index': 'cpg_site'})

    # set patient_id
    res_df['patient_id'] = primary_methylation_file.split("/")[-1].split(".")[1].split("-")[0]

    # remove rows with NA in either primary or recurrent
    res_df = res_df.dropna()

    # seed and randomly sample n rows
    np.random.seed(1234)
    res_df = res_df.sample(nsites)

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # combine the methylation files
    df = combined_methylation_files(primary_methylation_file, recurrent_methylation_file)

    # save to file
    df.to_csv(figure_data_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["primary_methylation_file=", "recurrent_methylation_file=", "figure_data_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--primary_methylation_file"):
            primary_methylation_file = str(arg)
        if opt in ("--recurrent_methylation_file"):
            recurrent_methylation_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
            

    main()


