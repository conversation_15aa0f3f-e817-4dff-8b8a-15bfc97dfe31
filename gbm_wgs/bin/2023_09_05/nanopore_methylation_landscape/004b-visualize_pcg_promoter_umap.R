# <PERSON>

library(optparse)
library(ggplot2)
library(ggrepel)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




create_umap_plot = function(source, input_df) {

# subset to source
input_df = input_df[input_df$source == source,]

# color by status
p = ggplot(input_df, aes(x=UMAP1, y=UMAP2, fill=tumor_type)) + plot_theme() +
geom_point(pch=21) +

ggtitle(paste(source, "of tpm")) +
xlab(paste(source, "1")) + 
ylab(paste(source, "2")) + 

theme(axis.text.x = element_text(angle = 0, hjust = 0.5)) +

# add labels of patient
geom_label_repel(data=input_df, aes(label=patient_id), show.legend=FALSE,
        seed            = 1234,
        size            = 3,
        force           = 0.5,
        max.overlaps    = 10,
        nudge_x         = 0.01,
        hjust           = 0,
        segment.size    = 0.2,
        color = 'black'
)


print(p)


# color by patient
p = ggplot(input_df, aes(x=UMAP1, y=UMAP2, fill=patient_id)) + plot_theme() +
geom_point(pch=21) +

ggtitle(paste(source, "of tpm")) +
xlab(paste(source, "1")) + 
ylab(paste(source, "2")) + 

theme(axis.text.x = element_text(angle = 0, hjust = 0.5)) +

# add labels of tumor status
geom_label_repel(data=input_df, aes(label=tumor_type), show.legend=FALSE,
        seed            = 1234,
        size            = 3,
        force           = 0.5,
        max.overlaps    = 10,
        nudge_x         = 0.01,
        hjust           = 0,
        segment.size    = 0.2,
        color = 'black'
)

print(p)

return()
}





pdf(opt$figure_file)

# load dfs
# input_df = read.csv(opt$figure_data_file, sep='\t')
input_df = read.csv(opt$figure_data_file)


# create umap plots
lapply(unique(input_df$source), create_umap_plot, input_df)

dev.off()


print(opt$figure_file)






