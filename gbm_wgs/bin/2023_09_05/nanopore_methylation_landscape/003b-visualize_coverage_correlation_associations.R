# Alec <PERSON>

library(optparse)
library(forcats)
library(ggplot2)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}






# plot 
coverage_diff_plot = function(input_df, stats_df){

sub_stats_df = stats_df[stats_df$variable == 'coverage_diff',]

correlation = signif(sub_stats_df$correlation, 3)
pval = signif(sub_stats_df$pvalue, 3)

# plot
p = ggplot(input_df, aes(x = coverage_diff, y=correlation)) + plot_theme() +
geom_point(pch=21, color='black', size=2) +

# Add labels for each point from the "patient" column
geom_text(aes(label = patient), hjust = 0.5, vjust = -0.5, size = 3, color = "black") +  # Labels

# add line of best fit
geom_smooth(method = 'lm', se=FALSE, color='blue') +

ggtitle(paste0('Correlation: ', correlation, ' pval=', pval)) +
xlab('Nanopore coverage difference (mean)') + ylab('Correlation coefficient (Spearman p)') +

# the angle of the x-axis labels
theme(axis.text.x = element_text(angle = 0))

print(p)

return()
}


# plot 
mean_coverage_plot = function(input_df, stats_df){

sub_stats_df = stats_df[stats_df$variable == 'mean_coverage',]

correlation = signif(sub_stats_df$correlation, 3)
pval = signif(sub_stats_df$pvalue, 3)

# plot
p = ggplot(input_df, aes(x = mean_coverage, y=correlation)) + plot_theme() +
geom_point(pch=21, color='black', size=2) +

# Add labels for each point from the "patient" column
geom_text(aes(label = patient), hjust = 0.5, vjust = -0.5, size = 3, color = "black") +  # Labels
    
# add line of best fit
geom_smooth(method = 'lm', se=FALSE, color='blue') +

ggtitle(paste0('Correlation: ', correlation, ' pval=', pval)) +
xlab('Coverage of both samples (mean)') + ylab('Correlation coefficient (Spearman p)') +

# the angle of the x-axis labels
theme(axis.text.x = element_text(angle = 0))

print(p)

return()
}




pdf(opt$figure_file)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')


# plot boxplots for each patient_id
coverage_diff_plot(input_df, stats_df)
mean_coverage_plot(input_df, stats_df)

dev.off()


print(opt$figure_file)






