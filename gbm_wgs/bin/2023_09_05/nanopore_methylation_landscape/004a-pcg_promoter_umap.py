# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import umap
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler

help_message = '''
Failed
'''


def load_and_umap(combined_promoter_methylation_file):
    # load data
    df = pd.read_csv(combined_promoter_methylation_file, sep='\t')

    # remove NA samples
    df = df.dropna(subset=['sample'])
    
    # set chr_start column
    df['chr_start'] = df['chr'] + ':' + df['start'].astype(str)

    # remove duplicate rows
    df = df.drop_duplicates(subset=['chr_start', 'sample'])
    
    # Pivoting the DataFrame
    pivot_df = df.pivot(index='chr_start', columns='sample', values='percent_methylated')

    # remove rows with NaN values and transpose
    pivot_df = pivot_df.dropna().transpose()
    
    # Scale data (mean 0, std 1)
    scaled_data = StandardScaler().fit_transform(pivot_df)

    # Run UMAP
    reducer = umap.UMAP()
    embedding = reducer.fit_transform(scaled_data)

    umap_df = pd.DataFrame(embedding, columns=['UMAP1', 'UMAP2'], index=pivot_df.index)

    # Run PCA
    pca = PCA(n_components=2)
    pca_embedding = pca.fit_transform(scaled_data)

    pca_df = pd.DataFrame(pca_embedding, columns=['UMAP1', 'UMAP2'], index=pivot_df.index)

    # Add source labels and combine
    umap_df['source'] = 'umap'
    pca_df['source'] = 'pca'
    combined_df = pd.concat([umap_df, pca_df])

    # Reset index and extract metadata
    combined_df = combined_df.reset_index().rename(columns={'index': 'sample'})
    combined_df['patient_id'] = combined_df['sample'].str.split('-').str[0]
    combined_df['tumor_type'] = combined_df['sample'].str.split('-').str[1]

    return combined_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and run umap and pca
    df = load_and_umap(combined_promoter_methylation_file)

    # save to files
    df.to_csv(pcg_promoter_umap_pca_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["combined_promoter_methylation_file=", "pcg_promoter_umap_pca_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--combined_promoter_methylation_file"):
            combined_promoter_methylation_file = str(arg)

        if opt in ("--pcg_promoter_umap_pca_file"):
            pcg_promoter_umap_pca_file = str(arg)

    main()


