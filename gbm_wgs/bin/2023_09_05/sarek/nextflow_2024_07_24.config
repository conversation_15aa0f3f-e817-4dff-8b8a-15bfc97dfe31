process {
    executor = "uge"
    penv = "smp"
    beforeScript = '''
        source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh
        source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
        conda activate nextflow
    '''
    clusterOptions = {
        def baseMemory = task.memory ? task.memory.toMega() : 20480 // Default to 20G if not specified
        def cpus = task.cpus ?: 1 // Default to 1 CPU if not specified
        def memoryPerCPU = baseMemory / cpus

        if (task.name.contains("MARKDUPLICATES")) { // Check for specific string in task name
            baseMemory += 10240 // Increase memory by 10G
            memoryPerCPU = baseMemory / cpus // Recalculate memory per CPU
        }

        "-P reimandlab -l h_vmem=${memoryPerCPU}M -l h_rss=${baseMemory}M -l mem_free=${baseMemory}M h_rt=21:0:0:0"
    }
}

executor {
    name = "uge"
    queueSize = 200
    queueStatInterval = "10s"
}

singularity {
    enabled = true
    autoMounts = true
}
