# Alec <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob, re

help_message = '''
Failed
'''


# setup sarek
def setup_sarek(fastq_dir_regex, bin_dir, results_dir):
    # determine files from fastq_dir_regex
    files = pd.Series(glob.glob(fastq_dir_regex))

    # determine patients from files
    patients = np.unique(files.str.split("/").str[-1].str.split("-").str[0])

    for patient in patients:
        df = []
        
        # specific files for that patient
        files_oi = [file for file in files if re.search(f"{patient}-", file)]

        # unique sequence runs
        uniq_runs = np.unique(pd.Series(files_oi).str.split("/").str[-1].str.split("_").str[:-1])

        # iteratively add the runs
        for run in uniq_runs:
            run = "_".join(run)
            status = '0' if 'blood' in run else '1'
            sex = sex_dict.get(patient)
            sample = patient + "-" + run.split("-")[1].split("_")[0]
            lane = run[-1]

            m1 = pd.Series(files_oi).str.contains(run)
            m2 = pd.Series(files_oi).str.contains('R1')
            fastq1 = np.array(files_oi)[np.logical_and(m1, m2)][0]

            m2 = pd.Series(files_oi).str.contains('R2')
            fastq2 = np.array(files_oi)[np.logical_and(m1, m2)][0]

            # add to df
            df.append([patient, sex, status, sample, lane, fastq1, fastq2])

        df = pd.DataFrame(df, columns = ['patient', 'sex', 'status', 'sample', 'lane', 'fastq_1', 'fastq_2'])

        outfile = os.path.join(bin_dir, f'{patient}.csv')
        df.to_csv(outfile, index=False)


        # make dir for processing
        new_directory_path = os.path.join(results_dir, patient)
        if not os.path.exists(new_directory_path):
            os.makedirs(new_directory_path)

        # directory for analysis
        processing_dir = f'/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/sarek_wgs/{patient}'

        # create processing file
        sarek_file = os.path.join(bin_dir, patient)
        to_write = f"""#!/bin/bash
#$ -P reimandlab
#$ -N {patient}
#$ -l h_vmem=10G,h_rt=7:0:0:0
#$ -q all.q
#$ -o /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/sarek/
#$ -e /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/sarek/


source /u/abahcheli/.profile
source /u/abahcheli/.bashrc
source /etc/profile.d/uge_settings.sh

source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh

mamba activate nextflow

cd {processing_dir}

export NXF_SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export NXF_OFFLINE='true'


nextflow run /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/nextflow_pipelines_offline/nf-core-sarek_3.4.2/3_4_2 \
--input {os.path.join(bin_dir, patient)}.csv \
--outdir {os.path.join(results_dir, patient)} \
-profile singularity -c {os.path.join(bin_dir, 'nextflow.config')} \
--tools 'freebayes,mutect2,strelka,manta,tiddit,ascat,cnvkit,controlfreec,msisensorpro,snpeff,vep,merge' \
--snpeff_cache /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/snpeff_cache/ \
--vep_cache /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/vep_cache/ \
--igenomes_base /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/igenomes/ \
-work-dir {processing_dir}/work \
-resume \
-process.maxDumpTasks 200

    """
        with open(sarek_file, "w") as outfile:
            outfile.write(to_write)




def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # define regex for relevant files
    file_regex = f'{fastq_dir}/*wgs*'

    # create files
    setup_sarek(file_regex, bin_dir, results_dir)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # sex of patients
    sex_dict = {'RLGS1':'XY', 'RLGS2':'XY', 'RLGS3':'XX', 'RLGS4':'XY', 'RLGS5':'XY', 'RLGS6':'XY', 'RLGS7':'XX', 'RLGS8':'XX', 'RLGS9':'XY', 'RLGS10':'XY', 'RLGS11':'XY', 'RLGS12':'XY'}

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["fastq_dir=", "bin_dir=", "results_dir="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--fastq_dir"):
            fastq_dir = str(arg)

        if opt in ("--bin_dir"):
            bin_dir = str(arg)
        if opt in ("--results_dir"):
            results_dir = str(arg)

    main()


