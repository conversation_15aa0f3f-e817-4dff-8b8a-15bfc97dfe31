files = pd.Series(glob.glob("/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/data/raw_data/all_fastqs/*wgs*"))

# list samples but remove blood samples
patient_tumor = np.unique(files.str.split("/").str[-1].str.split("_").str[0])
patient_tumor = [sample for sample in patient_tumor if not "blood" in sample]

base_path = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/sarek_wgs'  # Adjust this path as per your requirement
outdir = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/sarek'


sex_dict = {'RLGS1':'XY', 'RLGS2':'XY', 'RLGS3':'XX', 'RLGS4':'XY', 'RLGS5':'XY', 'RLGS6':'XY', 'RLGS7':'XX', 'RLGS8':'XX', 'RLGS9':'XY', 'RLGS10':'XY', 'RLGS11':'XY', 'RLGS12':'XY'}



for patient in patient_tumor:
    df = []
    
    # specific files for that sample
    files_oi = [file for file in files if re.search(f"{patient}", file)]

    # add the blood samples
    patient_id = patient.split("-")[0]
    other_files = [file for file in files if re.search(f"{patient_id}-blood", file)]

    files_oi.extend(other_files)

    # unique sequence runs
    uniq_runs = np.unique(pd.Series(files_oi).str.split("/").str[-1].str.split("_").str[:-1])

    # iteratively add the runs
    for run in uniq_runs:
        run = "_".join(run)
        status = '0' if 'blood' in run else '1'
        sex = sex_dict.get(patient_id)
        sample = patient + "-" + run.split("-")[1].split("_")[0]
        lane = run[-1]

        m1 = pd.Series(files_oi).str.contains(run)
        m2 = pd.Series(files_oi).str.contains('R1')
        fastq1 = np.array(files_oi)[np.logical_and(m1, m2)][0]

        m2 = pd.Series(files_oi).str.contains('R2')
        fastq2 = np.array(files_oi)[np.logical_and(m1, m2)][0]

        # add to df
        df.append([patient, sex, status, sample, lane, fastq1, fastq2])

    df = pd.DataFrame(df, columns = ['patient', 'sex', 'status', 'sample', 'lane', 'fastq_1', 'fastq_2'])

    outfile = os.path.join(outdir, f'{patient}.csv')
    df.to_csv(outfile, index=False)


    # make dir for processing
    new_directory_path = os.path.join(base_path, patient)
    if not os.path.exists(new_directory_path):
        os.makedirs(new_directory_path)

    # create processing file
    sarek_file = os.path.join(outdir, patient)
    to_write = f"""#!/bin/bash
#$ -P reimandlab
#$ -N {patient}
#$ -l h_vmem=10G,h_rt=7:0:0:0
#$ -q all.q
#$ -o /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/sarek/
#$ -e /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/sarek/


source /u/abahcheli/.profile
source /u/abahcheli/.bashrc
source /etc/profile.d/uge_settings.sh

source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh

mamba activate nextflow

cd /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/sarek_wgs/{patient}

export NXF_SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export NXF_OFFLINE='true'


nextflow run /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/nextflow_pipelines_offline/nf-core-sarek_3.4.2/3_4_2 \
--input /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/sarek/{patient}.csv \
--outdir /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/sarek_wgs/{patient} \
--tools 'freebayes,mutect2,strelka,manta,tiddit,ascat,cnvkit,controlfreec,msisensorpro,snpeff,vep,merge' \
--snpeff_cache /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/snpeff_cache/ \
--vep_cache /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/vep_cache/ \
--igenomes_base /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/igenomes/ \
-profile singularity -c /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/sarek/nextflow.config \
-resume \
-process.maxDumpTasks 200

"""
    with open(sarek_file, "w") as outfile:
        outfile.write(to_write)

