process {
    executor = "uge"
    penv = "smp"
    beforeScript = '''
        source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh
        source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
        conda activate nextflow
    '''
    clusterOptions = {
        "-V -P reimandlab -V -l h_rt=21:0:0:0 -V -l h_vmem=${task.memory.toMega()}M"
    }
    withName: 'FREEBAYES' {
        memory = '40960 MB'
    }
    withName: 'BWAMEM_MEM2_DRAGMAP_SENTIEON_BWAMEM1_MEM' {
        memory = '5120 MB'
    }
}

executor {
    name = "uge"
    queueSize = 500
    queueStatInterval = "10s"
}

