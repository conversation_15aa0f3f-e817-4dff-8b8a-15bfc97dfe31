# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os


import pandas as pd 
import numpy as np

help_message = '''
Failed
'''


def select_lowest_pval(nanopore_sv_gene_stats_file, cna_continuous_stats_file):
    # load dfs
    df1 = pd.read_csv(nanopore_sv_gene_stats_file, sep="\t")
    df2 = pd.read_csv(cna_continuous_stats_file, sep="\t")

    # create a df that have pvales from both dfs and the genes
    pvals_df = df1.loc[:,['gene', 'utest_pvalue', 'fc']].rename(columns={'utest_pvalue': 'sv_pval', 'fc': 'sv_fc'})
    pvals_df = pvals_df.merge(df2.loc[:,['gene', 'utest_pvalue', 'foldchange']], on='gene', how='outer')

    # rename columns
    pvals_df = pvals_df.rename(columns={'utest_pvalue': 'cna_pval', 'foldchange': 'cna_fc'})

    # set nas to 1
    pvals_df = pvals_df.set_index('gene')
    pvals_df = pvals_df.fillna(1)

    # get the lowest pval for each gene
    mask = pvals_df['sv_pval'] < pvals_df['cna_pval']

    # create a new df with the lowest pval
    df = pd.DataFrame(index=pvals_df.index)
    df['gene'] = pvals_df.index
    df['pvalue'] = np.where(mask, pvals_df['sv_pval'], pvals_df['cna_pval'])

    # add the fc based on the mask
    df['fc'] = np.where(mask, pvals_df['sv_fc'], pvals_df['cna_fc'])

    print(df)

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and process file
    df = select_lowest_pval(nanopore_sv_gene_stats_file, cna_continuous_stats_file)

    # save stats table
    df.to_csv(nanopore_wgs_cnas_svs_merged_file, sep="\t", index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["nanopore_sv_gene_stats_file=", "cna_continuous_stats_file=", "nanopore_wgs_cnas_svs_merged_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:        
        if opt in ("--nanopore_sv_gene_stats_file"):
            nanopore_sv_gene_stats_file = str(arg)
        if opt in ("--cna_continuous_stats_file"):
            cna_continuous_stats_file = str(arg)

        if opt in ("--nanopore_wgs_cnas_svs_merged_file"):
            nanopore_wgs_cnas_svs_merged_file = str(arg)

    main()



