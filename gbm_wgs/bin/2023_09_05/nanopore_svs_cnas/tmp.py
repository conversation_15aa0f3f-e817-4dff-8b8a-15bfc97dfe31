# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import re

help_message = '''
Failed
'''


def add_breakpoint_by_variant(row):
    # Extract relevant information from the current row
    breakpoint1 = f"{row['CHROM']}:{row['POS']}"
    
    if row['variant_type'] == 'BND':
        # Define the regular expression pattern
        pattern = r'[\[|\]](.*?)[\[|\]]'
        matches = re.findall(pattern, row['ALT'])
        breakpoint2 = matches[0].lower()
        sv_type = 'break_end'

    elif row['variant_type'] == 'INS':
        pattern = r"SVLEN=\d+"
        matches = re.findall(pattern, row['INFO'])
        size = matches[0].split('=')[1]
        breakpoint2 = f"{row['CHROM']}:{str(int(row['POS']) + int(size))}".lower()
        sv_type = 'insertion'

    elif row['variant_type'] == 'DUP':
        pattern = r"END=\d+"
        matches = re.findall(pattern, row['INFO'])
        breakpoint2 = f"{row['CHROM']}:{matches[0].split('=')[1]}".lower()
        sv_type = 'duplication'

    elif row['variant_type'] == 'DEL':
        pattern = r"END=\d+"
        matches = re.findall(pattern, row['INFO'])
        breakpoint2 = f"{row['CHROM']}:{matches[0].split('=')[1]}".lower()
        sv_type = 'deletion'

    elif row['variant_type'] == 'INV':
        pattern = r"SVLEN=\d+"
        matches = re.findall(pattern, row['INFO'])
        size = matches[0].split('=')[1]
        breakpoint2 = f"{row['CHROM']}:{str(int(row['POS']) + int(size))}".lower()
        sv_type = 'inversion'

    return breakpoint1, breakpoint2, sv_type


def extract_info_vep(info_str):
    """Helper function to extract relevant information from the INFO field."""
    info_dict = {}
    
    # vep fields
    vep_fields = ['Allele', 'Annotation', 'Annotation_Impact', 'Gene_Name', 'Gene_ID', 'Feature_Type', 'Feature_ID', 'Transcript_BioType', 'Rank', 'HGVS.c', 'HGVS.p', 'cDNA.pos_cDNA.length', 'CDS.pos_CDS.length', 'AA.pos_AA.length', 'Distance', 'Other_warnings']

    # collect the CSQ fields (VEP annotations)
    info_str = info_str.split("|")[:len(vep_fields)]

    # for each annotations, collect field information
    for n, annotation in enumerate(info_str):
        info_dict[vep_fields[n]] = annotation

    return info_dict


def load_and_process_df(file, min_sv_size = 50):
    # read the vcf file
    df = pd.read_csv(file, sep='\t', comment='#', header=None)
    df.columns = ['CHROM', 'POS', 'ID', 'REF', 'ALT', 'QUAL', 'FILTER', 'INFO', 'FORMAT', 'SAMPLE']

    # add IDs
    variant_ids = df['CHROM'].astype(str) + '_' + df['POS'].astype(str) + '_' + df['REF'] + '_' + df['ALT'].str.replace("\<|\>", "")
    df['variant_id'] = variant_ids

    # add variant type
    df['variant_type'] = df['INFO'].str.split(";").str[1].str.split("=").str[1]
    
    # classify the variants
    classifications = []

    # for each variant, extract the INFO field and classify the variant
    for _, row in df.iterrows():
        # extract the info field
        info_dict = extract_info_vep(row['INFO'])

        # add details to row based on the variant type
        breakpoint1, breakpoint2, sv_type = add_breakpoint_by_variant(row)

        # add breakpoint information to the row
        row['breakpoint1'] = breakpoint1
        row['breakpoint2'] = breakpoint2
        row['sv_type'] = sv_type

        # drop the info field
        row = row.drop('INFO')

        # add info fields to the row
        row = {**row, **info_dict}

        # add the row to the classifications
        classifications.append(row)

    # convert the classifications to a dataframe
    df = pd.DataFrame(classifications)

    # add sample id, tumor, and patient
    df['sample'] = file.split("/")[-1].split(".")[0]
    df['tumor'] = df['sample'].str.split("-").str[1]
    df['patient'] = df['sample'].str.split("-").str[0]

    # add the size of the variant
    df['size'] = df['breakpoint2'].str.split(":").str[1].astype(int) - df['breakpoint1'].str.split(":").str[1].astype(int)

    # set the size of break_ends to 1,000,000,000
    df.loc[df['sv_type'] == 'break_end', 'size'] = **********

    # subset to variants larger than min_sv_size
    df = df[df['size'] > min_sv_size]

    # subset to normal chromosomes
    chromosome_list = [str(i) for i in range(1, 23)]
    chromosome_list.extend(['X', 'Y'])
    chromosome_list = ['chr' + i for i in chromosome_list]
    
    df = df[df['CHROM'].isin(chromosome_list)]
    
    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process the df
    res_df = load_and_process_df(vcf_file)

    # save to files
    res_df.to_csv(processed_vcf_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["vcf_file=", "expanded_vcf_file=", "processed_vcf_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--vcf_file"):
            vcf_file = str(arg)

        if opt in ("--processed_vcf_file"):
            processed_vcf_file = str(arg)

    main()




