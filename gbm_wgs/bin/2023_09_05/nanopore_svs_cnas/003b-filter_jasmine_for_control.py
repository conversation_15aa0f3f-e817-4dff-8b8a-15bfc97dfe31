# <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import re

help_message = '''
Failed
'''


def remove_vcf_duplicates(input_vcf, output_vcf):
    with open(input_vcf, 'r') as infile, open(output_vcf, 'w') as outfile:
        for line in infile:
            if line.startswith("#"):
                # Write headers directly
                outfile.write(line)
            else:
                fields = line.strip().split('\t')
                if len(fields) < 5:
                    print(f"Error: {line}")  # skip malformed lines
                    continue
                key = fields[7] # search for SUPP_VEC=10 in the INFO field, which is binary stating the variant exists only in the tumor sample
                if re.search("SUPP_VEC=01", key):
                    outfile.write(line)


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # remove duplicates from the vcf file
    remove_vcf_duplicates(processed_vcf_file, nanopore_somatic_svs_file)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["processed_vcf_file=", "nanopore_somatic_svs_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--processed_vcf_file"):
            processed_vcf_file = str(arg)

        if opt in ("--nanopore_somatic_svs_file"):
            nanopore_somatic_svs_file = str(arg)

    main()



