# <PERSON>
library(optparse)

library(MASS)
library(parallel)

# options list for parser options
option_list <- list(
    make_option(c("-a","--svs_per_gene_file"), type="character", default=NULL,
            help="",
            dest="svs_per_gene_file"),
    make_option(c("-b","--nanopore_sv_gene_stats_file"), type="character", default=NULL,
            help="",
            dest="nanopore_sv_gene_stats_file"),

    make_option(c("-c","--threads"), type="character", default=NULL,
            help="",
            dest="threads")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



nb_anova_testing_multi_threaded = function(gene, input_df){
  
# Subset to gene
df = input_df[input_df['gene'] == gene, ]

# Subset to measure mutations
prim_obs = df[df$mutation_type == 'observed' & df$tumor_type == 'primary', 'mutations']
prim_exp = df[df$mutation_type == 'expected' & df$tumor_type == 'primary', 'mutations']

rec_obs = df[df$mutation_type == 'observed' & df$tumor_type == 'recurrent', 'mutations']
rec_exp = df[df$mutation_type == 'expected' & df$tumor_type == 'recurrent', 'mutations']
    
# Try to generate models
tryCatch({
model_prim <- glm.nb(prim_obs ~ prim_exp)
model_rec <- glm.nb(rec_obs ~ rec_exp)

# Run ANOVA between models
anova_results = anova(model_prim, model_rec, test = "Chisq")

# calculate the mean fc between prim and rec
mean_fc = log2(mean(rec_obs+0.0001) / mean(prim_obs+0.0001))

# Return gene and p-value
return(c(gene, max(anova_results$`Pr(Chi)`[2], 1e-5), mean_fc))

}, error = function(e) {
# If both models fail, try generating just model_rec
tryCatch({
  model_rec <- glm.nb(rec_obs ~ rec_exp)

  # set mean_fc to 1 because it exists in rec
    mean_fc = 1

  # If model_rec succeeds, return gene and p-value
  return(c(gene, max(summary(model_rec)$coefficients["rec_exp", "Pr(>|z|)"], 1e-5), mean_fc))
  
}, error = function(e) {
  # If even model_rec fails, try generating just model_prim
tryCatch({
  model_prim <- glm.nb(prim_obs ~ prim_exp)

  # set mean_fc to -1 because it exists in prim
    mean_fc = -1

  # If model_prim succeeds, return gene and p-value
  return(c(gene, max(summary(model_prim)$coefficients["prim_exp", "Pr(>|z|)"], 1e-5), mean_fc))
  
}, error = function(e) {
  # If even model_prim fails, return 1
  return(c(gene, 1, 0))
})
})
})
}




# load df
input_df = read.csv(opt$svs_per_gene_file, sep='\t')

# multi-threaded anova testing
res = mclapply(unique(input_df$gene), nb_anova_testing_multi_threaded, input_df, mc.cores=opt$threads)

# generate df
res_df = as.data.frame(do.call(rbind, res))

# rename columns
colnames(res_df) = c('gene', 'p_value', 'fc')

# write to file
write.table(res_df, file=opt$nanopore_sv_gene_stats_file, sep='\t', row.names=FALSE, quote=FALSE)

print("Done NB Anova testing")


