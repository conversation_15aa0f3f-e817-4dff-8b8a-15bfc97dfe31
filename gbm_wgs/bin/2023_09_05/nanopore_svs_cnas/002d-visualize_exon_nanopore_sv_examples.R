# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}





# Define the color palette
col = c(
  "insertion" = "#F7599D", 
  "duplication" = "blue", 
  "inversion" = "#BA6D45", 
  "deletion" = "#ed1219", 
  "break_end" = "#B047B8",
    "none" = "grey"
)

plot_sv = function(sample, input_df, stats_df) {

# define gene
gene = unique(stats_df$gene)

# subset to sample
input_df = input_df[input_df$sample == sample,]

# separate break_end from the rest
break_end_df = input_df[input_df$sv_type == 'break_end',]
input_df = input_df[input_df$sv_type != 'break_end',]


# plot
p = ggplot(input_df, aes(y = y, color = sv_type)) + plot_theme() +
geom_segment(aes(x = start, xend = end), linewidth = 3) +

# add gene colors
scale_color_manual(values = col) +

# add gene loci
geom_segment(data = stats_df, aes(x = start, xend = end), y=0, linewidth = 1, color = 'purple') +

# Add exon number above each segment with vertical jitter
geom_text(data = stats_df, 
          aes(x = (start + end) / 2, 
              y = 0, 
              label = exon_number), 
          vjust = -0.5, 
          color = 'black', 
          size = 0.7, 
          position = position_jitter(height = 1)) +  # Add vertical jitter
        
ylim(min_y, max_y) +  # Set the y-axis limits

ggtitle(paste0(gene, " - ", sample)) +
xlab('Chromosome position') + ylab('Coverage')

# if break_end exists, add it to the plot
if (nrow(break_end_df) > 0) {
    p = p + geom_point(data = break_end_df, aes(x = start, y = y), color = 'black', size = 3)
}

print(p)

}


coverage_plot = function(gene, input_df, stats_df) {

# subset dfs
input_df = input_df[input_df$gene == gene,]
stats_df = stats_df[stats_df$gene == gene,]

# for each sample, generate a plot
lapply(unique(input_df$sample), plot_sv, input_df, stats_df)

return()
}



pdf(opt$figure_file, height = 4)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')

# define the min and max values for the y-axis
min_y = 0
max_y = 5.5

# plot all unique genes
lapply(unique(stats_df$gene), coverage_plot, input_df, stats_df)

dev.off()


print(opt$figure_file)





