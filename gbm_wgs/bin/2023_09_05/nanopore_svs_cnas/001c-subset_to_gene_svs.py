# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''

def load_and_process_df(intersected_bed_file, all_genes):
    # load df
    df = pd.read_csv(intersected_bed_file, sep='\t', header=None)
    df.columns = ['chr', 'start', 'end', 'gene', 'type', 'info', 'chromosome', 'sv_start', 'sv_end', 'CHROM', 'POS', 'ID', 'variant_id', 'variant_type', 'breakpoint1', 'breakpoint2', 'sv_type', 'sample', 'tumor', 'patient', 'size']

    # list samples
    samples = df['sample'].unique()

    # subset to unique genes interactions per sample
    df['sample_variant_gene'] = df['sample'] + '_' + df['sv_type'] + '_' + df['gene']
    df = df.drop_duplicates(subset=['sample_variant_gene'])

    # subset to relevant columns
    df = df[['sample', 'gene', 'sv_type', 'tumor', 'patient', 'variant_id', 'breakpoint1', 'breakpoint2', 'size']]

    # add none gene with mutation "none" for each sample 
    for sample in samples:
        df = pd.concat([df, pd.DataFrame([{'sample': sample, 'gene': 'none', 'sv_type': 'none'}])], ignore_index=True)

    # add a sample "null_13" that has "none" for each gene of interest
    for gene in all_genes:
        df = pd.concat([df, pd.DataFrame([{'sample': 'null_13', 'gene': gene, 'sv_type': 'none'}])], ignore_index=True)

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process and combine all dfs
    res_df = load_and_process_df(intersected_bed_file, all_genes)

    # save to files
    res_df.to_csv(gene_svs_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    # genes of interest
    # nanopore_snv_genes = ['TP53', 'IDH1', 'PTEN', 'ATRX', 'EGFR', 'NF1', 'TERT', 'PIK3CA', 'CIC', 'RB1', 'PIK3R1', 'FUBP1']
    all_genes = ['EGFR', 'ATRX', 'PTEN', 'CDK6', 'MET', 'CDKN2A', 'RB1', 'MDM4', 'CDK4', 'TP53', 'PDGFRA', 'FUBP1', 'NF1', 'MDM2', 'MYCN', 'PIK3CA', 'PIK3R1', 'CCND2', 'CIC', 'IDH1', 'TERT', 'none']
    
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["intersected_bed_file=", "gene_svs_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--intersected_bed_file"):
            intersected_bed_file = str(arg)

        if opt in ("--gene_svs_file"):
            gene_svs_file = str(arg)
        

    main()




