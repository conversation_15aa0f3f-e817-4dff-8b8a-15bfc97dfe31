# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import re, glob

from concurrent.futures import ProcessPoolExecutor

help_message = '''
Failed
'''


def parse_info_field(info, pattern):
    match = re.search(pattern, info)
    return match.group(1) if match else ''


def extract_breakpoint2(row):
    chrom = row['CHROM']
    pos = int(row['POS'])
    info = row['INFO']
    alt = row['ALT']

    if row['variant_type'] == 'BND':
        match = re.search(r'[\[\]](.*?)[\[\]]', alt)
        return match.group(1).lower() if match else '', 'break_end'
    elif row['variant_type'] in ['DEL', 'DUP']:
        end = parse_info_field(info, r"END=(\d+)")
        return f"{chrom}:{end}".lower(), 'deletion' if row['variant_type'] == 'DEL' else 'duplication'
    elif row['variant_type'] in ['INS', 'INV']:
        svlen = parse_info_field(info, r"SVLEN=(\d+)")
        bp2 = pos + int(svlen) if svlen else pos
        sv_type = 'insertion' if row['variant_type'] == 'INS' else 'inversion'
        return f"{chrom}:{bp2}".lower(), sv_type
    return '', ''


def process_file(file, min_sv_size=50):
    df = pd.read_csv(file, sep='\t', comment='#', header=None,
                    names=['CHROM', 'POS', 'ID', 'REF', 'ALT', 'QUAL', 'FILTER', 'INFO', 'FORMAT', 'SAMPLE'])

    # Add IDs and parse variant type
    df['variant_id'] = df['CHROM'].astype(str) + '_' + df['POS'].astype(str) + '_' + df['ID']
    df['variant_type'] = df['INFO'].str.extract(r"SVTYPE=([^;]+)")
    df['breakpoint1'] = df['CHROM'] + ':' + df['POS'].astype(str)

    # Apply vectorized breakpoint2 and sv_type extraction
    extracted = df.apply(extract_breakpoint2, axis=1, result_type='expand')
    df[['breakpoint2', 'sv_type']] = extracted

    # drop info column
    df = df.drop(columns=['INFO'])

    # Add sample metadata
    df['sample'] = file.split("/")[-1].split("_")[0]
    df['tumor'] = df['sample'].str.split("-").str[1]
    df['patient'] = df['sample'].str.split("-").str[0]

    # Calculate size
    df['size'] = df['breakpoint2'].str.split(":").str[1].astype(float) - df['breakpoint1'].str.split(":").str[1].astype(float)
    df.loc[df['sv_type'] == 'break_end', 'size'] = 1_000_000_000

    # Filter by size
    df = df[df['size'] > min_sv_size]

    # Keep only normal chromosomes
    valid_chroms = ['chr' + str(i) for i in range(1, 23)] + ['chrX', 'chrY']
    df = df[df['CHROM'].isin(valid_chroms)]

    # drop columns
    df = df.drop(columns=['REF', 'ALT', 'QUAL', 'FILTER', 'FORMAT', 'SAMPLE'])

    return df


def load_and_process_dfs(files, threads):
    # parallelize
    with ProcessPoolExecutor(max_workers=threads) as executor:
        res_df = list(executor.map(process_file, files))

    # Concatenate all dataframes
    res_df = pd.concat(res_df, ignore_index=True)

    # remove duplicates by sample and variant_ids
    res_df = res_df.drop_duplicates(subset=['sample', 'variant_id'])

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # list files
    # regex = f'{results_dir}/analysis_nanopore/processing_non_somatic_svs/somatic_svs/*_blood_only-somatic.vcf'
    regex = f'{results_dir}/nanopore_human_variation/*blood/*blood.wf_sv.vcf.gz'
    files = glob.glob(regex)

    # process and combine all dfs
    res_df = load_and_process_dfs(files, threads)

    # save to files
    res_df.to_csv(combined_sv_vcf_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "combined_sv_vcf_file=", 'threads="])'])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--combined_sv_vcf_file"):
            combined_sv_vcf_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()




