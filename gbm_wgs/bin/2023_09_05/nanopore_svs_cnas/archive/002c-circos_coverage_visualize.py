# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import subprocess

help_message = '''
Failed
'''


def process_cna_file(cna_summary_file, minimum_length = 50, addition_to_visualize = 500000):
    # load cna file
    df = pd.read_csv(cna_summary_file, sep='\t')
    
    # subset to duplications and deletions
    mask = df['type'].isin(['deletion', 'duplication', 'insertion'])
    df = df.loc[mask,:]

    # Split 'breakpoint1' into 'chr', 'start' columns
    df['chr'] = df['breakpoint1'].str.split(':').str[0]
    df['start'] = df['breakpoint1'].str.split(':').str[1].astype(int)
    
    # Split 'breakpoint2' into 'chr', 'stop' columns
    df['stop'] = df['breakpoint2'].str.split(':').str[1]
    df['stop'] = df['stop'].astype(int)

    # calculate length
    df['length'] = df['stop'] - df['start']

    # subset to minimum length
    mask = df['length'] >= minimum_length
    df = df.loc[mask,:]

    # add some data for visualization
    df['stop'] = df['stop'] + addition_to_visualize
    
    # Reorder columns
    df = df[['chr', 'start', 'stop', 'sample', 'type']]
    df = df.rename(columns = {'type':'cna_classification'})

    # add sample type
    df['type'] = df['sample'].str.split("-").str[1]
    df['sample'] = df['sample'].str.split("-").str[0]
    
    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # calculate rolling average
    coverage_df = process_cna_file(cna_summary_file)

    # save to file
    coverage_df.to_csv(figure_stats_file, sep='\t', index=False)
    
    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["figure_data_file=", "cna_summary_file=", "r_script=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--cna_summary_file"):
            cna_summary_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()

figure_data_file
