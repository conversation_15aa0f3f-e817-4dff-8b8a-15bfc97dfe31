# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)

library(gridExtra)
library(eulerr)
library(grid)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




euler_venn_diagram = function(data_list, condition) {
    venn = euler(data_list)
    
    # Create the plot object instead of printing directly
    p = plot(venn, 
             counts = TRUE, 
             quantities = TRUE, 
             fills = list(fill = c("darkcyan", "darkorange", "grey", alpha = 0.2)), 
             main = condition, 
             cex.main = 1.2)
    
    return(p)  # Return the plot object
}



# plot venn diagram
venn_diagrams = function(variant, raw_df){

input_df = raw_df[raw_df$variant_type == variant,]
plots = list()

# for each variant type, create a venn diagram
for (patient_id in unique(input_df$patient)){
    # subset to variant type
    sub_df = input_df[input_df$patient == patient_id,]

    # separate into primary and recurrent tumors
    primary_df = sub_df[sub_df[["tumor"]] == "primary",]
    recurrent_df = sub_df[sub_df[["tumor"]] == "recurrent",]

    # get the data for the venn diagram (common_variant_id)
    data_list = list(
        primary = unique(primary_df$common_variant_id),
        recurrent = unique(recurrent_df$common_variant_id)
    )
    
    percent_common = signif(length(intersect(primary_df$common_variant_id, recurrent_df$common_variant_id)) / length(unique(c(primary_df$common_variant_id, recurrent_df$common_variant_id))) * 100, 2)
    # plot the venn diagram
    p = euler_venn_diagram(data_list, paste0(patient_id, " - ", variant, " ", percent_common, "%"))
    
    plots[[length(plots) + 1]] = p  # Add the plot to the list
}

do.call(grid.arrange, c(plots, ncol = 4, nrow = 3))
    
return()
}




pdf(opt$figure_file, width = 16, height = 12)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')

# plot each patient
lapply(unique(input_df$variant_type), venn_diagrams, input_df)


dev.off()


print(opt$figure_file)





