# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

help_message = '''
Failed
'''


def count_total_svs(df):
    # # filter out duplications and deletions because these are accounted for in WGS data
    # df = df[~df['sv_type'].isin(['duplication', 'deletion'])]

    # Use pd.crosstab to count occurrences of gene-sample pairs
    count_df = pd.crosstab(df['gene'], df['sample'], dropna=False)

    # Fill missing values with 0
    count_df = count_df.fillna(0)

    # add gene column and reset index
    count_df['gene'] = count_df.index
    count_df = count_df.reset_index(drop=True)

    return count_df


def expected_mutations_per_gene(filtered_svs_file, genome_size_file, pcg_bed_file, nucleotide_range=2000):
    # load dfs
    filtered_svs_df = pd.read_csv(filtered_svs_file, sep="\t")

    genome_size = pd.read_csv(genome_size_file, sep="\t").loc[0, 'accessible_genome_size']

    pcg_df = pd.read_csv(pcg_bed_file, sep="\t", header=None)
    pcg_df.columns = ['chr', 'start', 'end', 'gene'] + pcg_df.columns[4:].tolist()

    # calculate total mutations for primary and recurrent tumors
    primary_mutations = filtered_svs_df[filtered_svs_df['tumor'] == 'primary'].shape[0]
    recurrent_mutations = filtered_svs_df[filtered_svs_df['tumor'] == 'recurrent'].shape[0]

    # Calculate the size of each gene, including the additional bps considered for intersecting with SVs
    gene_sizes = (pcg_df['end'] + nucleotide_range) - (pcg_df['start'] - nucleotide_range)

    # Calculate the expected number of mutations in each gene
    primary_gene_mutations = gene_sizes / genome_size * primary_mutations
    recurrent_gene_mutations = gene_sizes / genome_size * recurrent_mutations

    # Create the expected mutations DataFrame
    expected_mutations = pd.DataFrame({
        'gene': pcg_df['gene'],
        'expected_primary': primary_gene_mutations,
        'expected_recurrent': recurrent_gene_mutations
    })

    return expected_mutations


def run_nanopore_sv_stats(intersected_bed_file, filtered_svs_file, pcg_bed_file, genome_size_file):
    # load dfs
    pcg_df = pd.read_csv(intersected_bed_file, sep="\t")
    pcg_df.columns = ['chr', 'start', 'end', 'gene', 'type', 'info', 'chromosome', 'sv_start', 'sv_end', 'CHROM', 'POS', 'ID', 'REF', 'ALT', 'QUAL', 'FILTER', 'FORMAT', 'SAMPLE', 'variant_id', 'variant_type', 'breakpoint1', 'breakpoint2', 'sv_type', 'size', 'sample', 'tumor', 'patient']

    # count the number of mutations per sample, per gene
    pcg_df = count_total_svs(pcg_df)

    # count the expected number of mutations per gene
    expected_mutations_df = expected_mutations_per_gene(filtered_svs_file, genome_size_file, pcg_bed_file)

    # merge the dfs including only the genes in the df
    df = pd.merge(pcg_df, expected_mutations_df, on='gene', how='inner')

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and process file
    df = run_nanopore_sv_stats(intersected_bed_file, filtered_svs_file, pcg_bed_file, genome_size_file)

    # save stats table
    df.to_csv(svs_per_gene_file, sep="\t", index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["intersected_bed_file=", "filtered_svs_file=", "pcg_bed_file=", "genome_size_file=", "svs_per_gene_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:        
        if opt in ("--intersected_bed_file"):
            intersected_bed_file = str(arg)
        if opt in ("--filtered_svs_file"):
            filtered_svs_file = str(arg)

        if opt in ("--pcg_bed_file"):
            pcg_bed_file = str(arg)
        if opt in ("--genome_size_file"):
            genome_size_file = str(arg)

        if opt in ("--svs_per_gene_file"):
            svs_per_gene_file = str(arg)

    main()



