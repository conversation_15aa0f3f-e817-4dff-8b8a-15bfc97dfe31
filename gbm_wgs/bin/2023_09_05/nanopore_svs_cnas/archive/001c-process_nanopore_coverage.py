# <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''


def calculate_average_coverage(df, interval_size = 200000):
    # Define the original column order
    original_columns = df.columns.copy()

    # Calculate new 'start' and 'stop' values
    df['start'] = df['start'] // interval_size * interval_size
    df['stop'] = ((df['stop'] - 1) // interval_size + 1) * interval_size

    # Group by chromosome, start, and stop, then calculate the mean coverage
    result = df.groupby(['chromosome', 'start', 'stop', 'sample_number', 'type']).agg({'mean_coverage': 'mean', 'rolling_average': 'mean'}).reset_index()

    # Reorder the columns
    result = result[original_columns]

    return result


def calculate_rolling_average(coverage_files, window_size = 10, max_coverage = 3):
    res_df = []

    # classify samples based on first "-" delimited string
    class_df = pd.DataFrame({'sample': [x.split('/')[-5] for x in coverage_files]})
    class_df['file'] = coverage_files


    # for each file, processing rolling average
    for sample in class_df['sample'].str.split("-").str[0].unique():
        # find files
        files = class_df.loc[class_df['sample'].str.contains(sample), 'file']
        mask = np.logical_and(files.str.contains('primary'), files.str.contains('tumor\.regions'))
        primary_file = files.to_numpy()[mask]
        mask = np.logical_and(files.str.contains('recurrent'), files.str.contains('tumor\.regions'))
        recurrent_file = files.to_numpy()[mask]
        mask = np.logical_and(files.str.contains('recurrent'), files.str.contains('normal\.regions'))
        normal_file = files.to_numpy()[mask]


        # load dfs
        primary_df = pd.read_csv(primary_file[0], sep='\t', compression='gzip', header=None)
        primary_df.columns = ['chromosome', 'start', 'stop', 'mean_coverage']

        recurrent_df = pd.read_csv(recurrent_file[0], sep='\t', compression='gzip', header=None)
        recurrent_df.columns = ['chromosome', 'start', 'stop', 'mean_coverage']

        normal_df = pd.read_csv(normal_file[0], sep='\t', compression='gzip', header=None)
        normal_df.columns = ['chromosome', 'start', 'stop', 'mean_coverage']

        # calculate relative coverage, log2
        primary_df['mean_coverage'] = np.log2((primary_df['mean_coverage'].to_numpy() + 1) / (normal_df['mean_coverage'].to_numpy() + 1))
        recurrent_df['mean_coverage'] = np.log2((recurrent_df['mean_coverage'].to_numpy() + 1) / (normal_df['mean_coverage'].to_numpy() + 1))
        
        # calculate rolling averages
        primary_df['rolling_average'] = primary_df['mean_coverage'].rolling(window=window_size, min_periods=1).mean()
        recurrent_df['rolling_average'] = recurrent_df['mean_coverage'].rolling(window=window_size, min_periods=1).mean()

        # set max coverage
        primary_df.loc[primary_df['rolling_average'] > max_coverage, 'rolling_average'] = max_coverage
        recurrent_df.loc[recurrent_df['rolling_average'] > max_coverage, 'rolling_average'] = max_coverage
        
        # add sample names
        primary_df['sample_number'] = sample
        recurrent_df['sample_number'] = sample

        # add type
        primary_df['type'] = 'primary'
        recurrent_df['type'] = 'recurrent'

        # add sample combined
        primary_df['sample'] = f'{sample}-primary'
        recurrent_df['sample'] = f'{sample}-recurrent'
        
        # add to list
        res_df.extend([primary_df, recurrent_df])

    return pd.concat(res_df)


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process coverage files
    regex = f'{results_dir}/nanopore_somatic/*/*/qc/coverage/*regions.bed.gz'
    files = glob.glob(regex)

    # calculate rolling average
    coverage_df = calculate_rolling_average(files)

    # save to file
    coverage_df.to_csv(processed_coverage_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "processed_coverage_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--processed_coverage_file"):
            processed_coverage_file = str(arg)
            
    main()


