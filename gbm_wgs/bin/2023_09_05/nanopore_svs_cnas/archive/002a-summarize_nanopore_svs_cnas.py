# <PERSON> Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import subprocess


help_message = '''
Failed
'''


def process_length(sv_cna_file, minimum_length = 50):
    # load data
    df = pd.read_csv(sv_cna_file, sep='\t')

    # mask out the break_ends
    mask = df['type'] != 'break_end'

    # add total length of SVs and CNAs
    df.loc[mask,'total_length'] = df.loc[mask,'breakpoint2'].str.split(':').str[1].astype('int') - df.loc[mask,'breakpoint1'].str.split(':').str[1].astype('int')

    # summarize number of variants per sample for variants of minimum length; set break_ends to 1000
    df = df.fillna(1000)
    mask2 = df['total_length'] > minimum_length

    # subset df
    sub_df = df.loc[mask2,:]
    summary_df = sub_df.groupby(['sample', 'type']).size().reset_index(name='count')

    # add sample number and tumor_type
    summary_df['sample_number'] = summary_df['sample'].str.split('-').str[0]
    summary_df['tumor_type'] = summary_df['sample'].str.split('-').str[1]

    return df, summary_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # add total length of SVs and CNAs
    df, stats_df = process_length(sv_cna_file)

    # save to files
    df.to_csv(figure_data_file, sep='\t', index=False)
    stats_df.to_csv(figure_stats_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_stats_file', figure_stats_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["sv_cna_file=", "r_script=", "figure_data_file=", "figure_stats_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--sv_cna_file"):
            sv_cna_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)

    main()


