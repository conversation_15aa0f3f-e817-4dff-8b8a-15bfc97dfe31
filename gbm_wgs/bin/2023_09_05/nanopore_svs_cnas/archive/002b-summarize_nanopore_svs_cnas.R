# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




summary_plots = function(type, input_stats_df, input_df){

df = input_stats_df[input_stats_df$type == type,]
    
# reorder factors according to max count
df$sample_number = fct_reorder(df$sample_number, df$count, max, .desc=TRUE)

# barplot of number of variants
p = ggplot(df, aes(x = sample_number, y = count, fill = tumor_type)) + plot_theme() +
geom_bar(stat = 'identity', position = 'dodge') +

ggtitle(paste0('Number of ',  type, ' variants')) +
xlab('Patient') + ylab('Count') 
# theme(axis.text.x = element_text(angle = 0))

print(p)


if (type != 'break_end'){

df = input_df[input_df$type == type,]

# reorder factors according to median size
df$sample = fct_reorder(df$sample, df$total_length, median, .desc=TRUE)

# barplot of size of variants
p = ggplot(df, aes(x = sample, y = log10(total_length), fill = sample_number)) + plot_theme() +
geom_boxplot() +

ggtitle(paste0('Size of ',  type, ' variants')) +
xlab('Patient') + ylab('Size (bp, log10)')

print(p)
}

    
return()

}



pdf(opt$figure_file)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')


# plot
lapply(unique(input_df$type), summary_plots, stats_df, input_df)

dev.off()


print(opt$figure_file)



