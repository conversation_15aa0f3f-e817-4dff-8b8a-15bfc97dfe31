# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

help_message = '''
Failed
'''


def count_total_svs(df, min_samples = 3):
    # # subset to unique gene-tumor pairs
    # df = df.drop_duplicates(subset=['gene', 'sample'])

    # # filter out duplications and deletions because these are accounted for in WGS data
    # df = df[~df['sv_type'].isin(['duplication', 'deletion'])]

    # Use pd.crosstab to count occurrences of gene-sample pairs
    count_df = pd.crosstab(df['gene'], df['sample'], dropna=False)

    # Fill missing values with 0
    count_df = count_df.fillna(0)

    # # filter to genes with mutations in at least min_samples
    # count_df = count_df[count_df.sum(axis=1) >= min_samples]

    # add gene column and reset index
    count_df['gene'] = count_df.index
    count_df = count_df.reset_index(drop=True)

    return count_df


def expected_mutations_per_gene(samples, filtered_svs_file, genome_size_file, pcg_bed_file, nucleotide_range=2000):
    # load dfs
    filtered_svs_df = pd.read_csv(filtered_svs_file, sep="\t")

    genome_size = pd.read_csv(genome_size_file, sep="\t").loc[0, 'accessible_genome_size']

    pcg_df = pd.read_csv(pcg_bed_file, sep="\t", header=None)
    pcg_df.columns = ['chr', 'start', 'end', 'gene'] + pcg_df.columns[4:].tolist()


    # Calculate the size of each gene, including the additional bps considered for intersecting with SVs
    gene_sizes = (pcg_df['end'] + nucleotide_range) - (pcg_df['start'] - nucleotide_range)

    res = []

    # for each sample, calculate the total number of mutations
    for sample in samples:
        # filter to sample of interest
        sample_df = filtered_svs_df[filtered_svs_df['sample'] == sample]

        # calculate total mutation size for each sample
        # mutations = np.sum(sample_df['end'] - sample_df['start']) / genome_size
        mutations = sample_df.shape[0]

        # Calculate the expected number of mutations in each gene
        gene_mutations = gene_sizes / genome_size * mutations

        # add to results
        res.append(gene_mutations)

    # create df with columns as f"samples_expected_mutations"
    res_df = pd.DataFrame(res).T
    res_df.columns = [f"{sample}_expected_mutations" for sample in samples]

    # add gene column
    res_df['gene'] = pcg_df['gene']

    return res_df


def run_nanopore_sv_stats(intersected_bed_file, filtered_svs_file, pcg_bed_file, genome_size_file):
    # load dfs
    sv_per_gene_df = pd.read_csv(intersected_bed_file, sep="\t")
    sv_per_gene_df.columns = ['chr', 'start', 'end', 'gene', 'type', 'info', 'chromosome', 'sv_start', 'sv_end', 'CHROM', 'POS', 'ID', 'variant_id', 'variant_type', 'breakpoint1', 'breakpoint2', 'sv_type', 'sample', 'tumor', 'patient', 'size']

    # count the number of mutations per sample, per gene
    sv_per_gene_df = count_total_svs(sv_per_gene_df)

    # the samples of interest
    samples = sv_per_gene_df.columns[~sv_per_gene_df.columns.isin(['gene'])]

    # count the expected number of mutations per gene
    expected_mutations_df = expected_mutations_per_gene(samples, filtered_svs_file, genome_size_file, pcg_bed_file)

    # subset to common genes
    common_genes = set(sv_per_gene_df['gene']).intersection(set(expected_mutations_df['gene']))

    # merge the dfs including only common genes
    df = sv_per_gene_df[sv_per_gene_df['gene'].isin(common_genes)].merge(expected_mutations_df[expected_mutations_df['gene'].isin(common_genes)], on='gene', how='inner')
    
    # melt the df using "gene" as the id_vars
    df = pd.melt(df, id_vars='gene', var_name='sample', value_name='mutations')

    # add columns describing if expected or observed and primary or recurrent
    df['mutation_type'] = np.where(df['sample'].str.contains('expected'), 'expected', 'observed')
    df['tumor_type'] = np.where(df['sample'].str.contains('primary'), 'primary', 'recurrent')

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and process file
    df = run_nanopore_sv_stats(intersected_bed_file, filtered_svs_file, pcg_bed_file, genome_size_file)

    # save stats table
    df.to_csv(svs_per_gene_file, sep="\t", index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["intersected_bed_file=", "filtered_svs_file=", "pcg_bed_file=", "genome_size_file=", "svs_per_gene_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:        
        if opt in ("--intersected_bed_file"):
            intersected_bed_file = str(arg)
        if opt in ("--filtered_svs_file"):
            filtered_svs_file = str(arg)

        if opt in ("--pcg_bed_file"):
            pcg_bed_file = str(arg)
        if opt in ("--genome_size_file"):
            genome_size_file = str(arg)

        if opt in ("--svs_per_gene_file"):
            svs_per_gene_file = str(arg)

    main()



