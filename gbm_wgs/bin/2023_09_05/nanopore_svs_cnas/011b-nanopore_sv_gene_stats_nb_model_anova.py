# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from concurrent.futures import ThreadPoolExecutor
import scipy.stats as stats

help_message = '''
Failed
'''


def negative_binomial_test(df):
    res = []

    for gene in df['gene'].unique():
        sub_df = df[df['gene'] == gene]

        # Separate into primary and recurrent observed and expected
        primary_observed = sub_df.loc[np.logical_and(sub_df['tumor_type'] == 'primary', sub_df['mutation_type'] == 'observed'), 'mutations'].to_numpy()
        primary_expected = sub_df.loc[np.logical_and(sub_df['tumor_type'] == 'primary', sub_df['mutation_type'] == 'expected'), 'mutations'].to_numpy()

        recurrent_observed = sub_df.loc[np.logical_and(sub_df['tumor_type'] == 'recurrent', sub_df['mutation_type'] == 'observed'), 'mutations'].to_numpy()
        recurrent_expected = sub_df.loc[np.logical_and(sub_df['tumor_type'] == 'recurrent', sub_df['mutation_type'] == 'expected'), 'mutations'].to_numpy()

        if len(primary_observed) < 2 or len(recurrent_observed) < 2:
            continue  # Skip genes with insufficient data

        # Step 1: Fit the Negative Binomial model to the observed data for each group
        mean_prim, var_prim = np.mean(primary_expected), np.var(primary_expected)
        n_A = mean_prim**2 / (var_prim - mean_prim) if var_prim > mean_prim else 1
        p_A = mean_prim / var_prim if var_prim > mean_prim else 0.5

        mean_rec, var_rec = np.mean(recurrent_expected), np.var(recurrent_expected)
        n_B = mean_rec**2 / (var_rec - mean_rec) if var_rec > mean_rec else 1
        p_B = mean_rec / var_rec if var_rec > mean_rec else 0.5

        # Step 2: Calculate the residuals for both groups
        expected_prim = stats.nbinom.mean(n_A, p_A)
        expected_rec = stats.nbinom.mean(n_B, p_B)

        print(primary_observed, primary_expected)
        
        prim_residuals_real = primary_observed - primary_expected
        rec_residuals_real = recurrent_observed - recurrent_expected

        prim_residuals_pred = primary_observed - expected_prim
        rec_residuals_pred = recurrent_observed - expected_rec

        # Step 3: Perform ANOVA on the residuals
        # Handle cases where ANOVA fails
        p_value_real = stats.f_oneway(prim_residuals_real, rec_residuals_real)[1]
        p_value_pred = stats.f_oneway(prim_residuals_pred, rec_residuals_pred)[1]

        # are the differences in residuals significant in paired tests
        p_value_wilxocon = stats.wilcoxon(prim_residuals_real, rec_residuals_real)[1]
        p_value_ttest = stats.ttest_rel(prim_residuals_real, rec_residuals_real)[1]

        # calculate fc
        fc = np.log2((np.mean(recurrent_observed) + 0.0001) / (np.mean(primary_observed) + 0.0001))

        res.append([gene, p_value_real, p_value_pred, p_value_wilxocon, p_value_ttest, fc])

    return pd.DataFrame(res, columns=['gene', 'pvalue_real', 'pvalue_pred', 'pvalue_wilxocon', 'pvalue_ttest', 'fc'])


def run_nanopore_sv_stats(svs_per_gene_file, threads):
    # load df
    df = pd.read_csv(svs_per_gene_file, sep="\t")

    # setup args for negative binomial test
    gene_sets = np.array_split(df['gene'].unique()[:20], threads)
    args = [df[df['gene'].isin(gene_set)] for gene_set in gene_sets]

    # run negative binomial test
    with ThreadPoolExecutor(max_workers=threads) as executor:
        results = executor.map(negative_binomial_test, args)

    # merge results
    res_df = pd.concat(results)

    return res_df.fillna(1)

def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and process file
    df = run_nanopore_sv_stats(svs_per_gene_file, threads)

    # save stats table
    df.to_csv(nanopore_sv_gene_stats_file, sep="\t", index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["svs_per_gene_file=", "nanopore_sv_gene_stats_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:        
        if opt in ("--svs_per_gene_file"):
            svs_per_gene_file = str(arg)

        if opt in ("--nanopore_sv_gene_stats_file"):
            nanopore_sv_gene_stats_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()



