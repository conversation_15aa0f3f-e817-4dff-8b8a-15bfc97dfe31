# Alec <PERSON>
library(optparse)
library(ActivePathways)

# options list for parser options
option_list <- list(
    make_option(c("-a","--nanopore_sv_gene_stats_file"), type="character", default=NULL,
            help="",
            dest="nanopore_sv_gene_stats_file"),
    make_option(c("-b","--cna_continuous_stats_file"), type="character", default=NULL,
            help="",
            dest="cna_continuous_stats_file"),
    make_option(c("-c","--nanopore_wgs_cnas_svs_merged_file"), type="character", default=NULL,
            help="",
            dest="nanopore_wgs_cnas_svs_merged_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)



# load dfs
nanopore_df = read.csv(opt$nanopore_sv_gene_stats_file, sep='\t')
wgs_df = read.csv(opt$cna_continuous_stats_file, sep='\t')

# merge dfs "utest_pvalue" based on gene column
pval_nanopore = nanopore_df[,c('ttest_pvalue', 'gene')]
pval_wgs = wgs_df[,c('ttest_pvalue', 'gene')]
merged_df = merge(pval_nanopore, pval_wgs, by="gene")

# rename columns
colnames(merged_df) = c("gene", "nanopore", "wgs")

# create matrix of p-values
scores = as.matrix(merged_df[,c("nanopore", "wgs")])
rownames(scores) = merged_df$gene



# repeat for fc
fc_nanopore = nanopore_df[,c('fc', 'gene')]
fc_wgs = wgs_df[,c('foldchange', 'gene')]
merged_df_fc = merge(fc_nanopore, fc_wgs, by="gene")

# rename columns
colnames(merged_df_fc) = c("gene", "nanopore", "wgs")

# create matrix of fcs
scores_dir = as.matrix(merged_df_fc[,c("nanopore", "wgs")])
rownames(scores_dir) = merged_df_fc$gene

# fill na with 0
scores_dir[is.na(scores_dir)] = 0

# merge using DPM
res = merge_p_values(scores, scores_direction = scores_dir, constraints_vector = c(1, 1), method='DPM')
# res = merge_p_values(scores, method='Brown')


# create df
res = data.frame(res)
colnames(res) = 'pvalue'
res$gene = rownames(scores)


# add fc to df
res$fc = merged_df_fc$wgs


# add values that are not in the merged df
nanopore_only = nanopore_df[!nanopore_df$gene %in% res$gene,]
wgs_only = wgs_df[!wgs_df$gene %in% res$gene,]

# add to res
res_nanopore_only = data.frame(gene=nanopore_only$gene, pvalue=nanopore_only$utest_pvalue, fc=nanopore_only$fc)
res_wgs_only = data.frame(gene=wgs_only$gene, pvalue=wgs_only$utest_pvalue, fc=wgs_only$foldchange)



res = rbind(res, res_nanopore_only)
res = rbind(res, res_wgs_only)


# write the merged p-values to a file
write.table(res, file=opt$nanopore_wgs_cnas_svs_merged_file, sep='\t', row.names=FALSE, quote=FALSE)

print("P-value merging Complete")




