# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from concurrent.futures import ThreadPoolExecutor
import scipy.stats as stats

help_message = '''
Failed
'''


def count_total_svs(df):
    # Use pd.crosstab to count occurrences of gene-sample pairs
    count_df = pd.crosstab(df['gene'], df['sample'], dropna=False)

    # Fill missing values with 0
    count_df = count_df.fillna(0)

    return count_df


def count_sample_svs(raw_df, n_null=12):
    # subset to unique gene-tumor pairs
    raw_df = raw_df.drop_duplicates(subset=['gene', 'sample'])

    # group by gene and tumor and count the number of times each gene occurs
    df = raw_df.groupby(['gene', 'tumor']).size().reset_index(name='count')

    # pivot to count the number of mutations in each gene
    df = df.pivot(index='gene', columns='tumor', values='count').reset_index()
    df = df.fillna(0)
    
    # add columns of n_not_primary, n_not_recurrent
    df['n_not_primary'] = n_null - df['primary']
    df['n_not_recurrent'] = n_null - df['recurrent']

    return df


def fishers_test(df):
    res_df = []

    # for each row in the df, perform fisher's exact test
    for _, row in df.iterrows():
        # create contingency table
        contingency = np.array([[row['primary'], row['n_not_primary']], [row['recurrent'], row['n_not_recurrent']]])

        # perform fisher's exact test
        pvalue = stats.fisher_exact(contingency)[1]

        res_df.append((row['gene'], pvalue))

    return pd.DataFrame(res_df, columns=['gene', 'fisher_pvalue'])


def continuous_tests(df):
    res_df = []

    # for each row in the df, perform fisher's exact test
    for gene, row in df.iterrows():
        # separate into primary and recrurrent
        primary_array = row[row.index.str.contains('primary')].to_numpy(dtype='int')
        recurrent_array = row[row.index.str.contains('recurrent')].to_numpy(dtype='int')

        # compare recurrent to primary
        fc = np.log2(np.mean(recurrent_array) / np.mean(primary_array))

        # if the arrays are the same, the p-value is 1
        if np.all(primary_array == recurrent_array):
            ttest_pvalue = 1
            utest_pvalue = 1
        else:
            # run paired t-test and paired u-test test
            ttest_pvalue = stats.ttest_rel(primary_array, recurrent_array).pvalue
            utest_pvalue = stats.wilcoxon(primary_array, recurrent_array).pvalue

        res_df.append((gene, fc, ttest_pvalue, utest_pvalue))

    return pd.DataFrame(res_df, columns=['gene', 'fc', 'ttest_pvalue', 'utest_pvalue'])


def run_nanopore_sv_stats(intersected_bed_file, threads):
    # load df
    df = pd.read_csv(intersected_bed_file, sep="\t")
    df.columns = ['chr', 'start', 'end', 'gene', 'type', 'info', 'chromosome', 'sv_start', 'sv_end', 'CHROM', 'POS', 'ID', 'variant_id', 'variant_type', 'breakpoint1', 'breakpoint2', 'sv_type', 'sample', 'tumor', 'patient', 'size']

    # process into contingency table for each gene counting the total svs and number of samples impacted with svs
    total_svs_df = count_total_svs(df)
    samples_impacted_df = count_sample_svs(df)

    # group rows into number of threads for multi-threading
    row_groups = np.array_split(samples_impacted_df.index, threads)

    # create arguments for multi-threading
    args = [(samples_impacted_df.loc[group,:]) for group in row_groups]

    # run stats on each row
    with ThreadPoolExecutor(max_workers=threads) as executor:
        fishers_results = executor.map(fishers_test, args)

    # repeat for continuous tests
    # group rows into number of threads for multi-threading
    row_groups = np.array_split(total_svs_df.index, threads)

    # create arguments for multi-threading
    args = [(total_svs_df.loc[group,:]) for group in row_groups]

    # run stats on each row
    with ThreadPoolExecutor(max_workers=threads) as executor:
        continuous_results = executor.map(continuous_tests, args)

    # combine results
    fishers_results = pd.concat(fishers_results)
    continuous_results = pd.concat(continuous_results)

    # merge results
    df = pd.merge(fishers_results, continuous_results, on='gene', how='outer')

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and process file
    df = run_nanopore_sv_stats(intersected_bed_file, threads)

    # save stats table
    df.to_csv(nanopore_sv_gene_stats_file, sep="\t", index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["intersected_bed_file=", "nanopore_sv_gene_stats_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:        
        if opt in ("--intersected_bed_file"):
            intersected_bed_file = str(arg)
            
        if opt in ("--nanopore_sv_gene_stats_file"):
            nanopore_sv_gene_stats_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()



