# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)
library(tidyr)
library(ComplexHeatmap)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



oncoprint_plot = function(input_df, desired_row_order, small=FALSE){

    
# create matrix for oncoprint
res_df <- input_df %>% mutate(mutation_info = paste(sv_type))


mutation_matrix <- res_df %>%
  group_by(gene, sample) %>%
  summarise(mutation_info = paste(unique(mutation_info), collapse = ";")) %>%
  pivot_wider(names_from = sample, values_from = mutation_info, values_fill = list(mutation_info = ""))

    
mutation_matrix <- as.data.frame(mutation_matrix)
rownames(mutation_matrix) <- mutation_matrix$gene
mutation_matrix$gene <- NULL
mutation_matrix <- as.matrix(mutation_matrix)

print(desired_row_order)
print(length(desired_row_order))
    
print(rownames(mutation_matrix))
print(length(rownames(mutation_matrix)))

    
# Define the color palette
col = c(
  "insertion" = "#F7599D", 
  "duplication" = "blue", 
  "inversion" = "#BA6D45", 
  "deletion" = "#ed1219", 
  "break_end" = "#B047B8",
    "none" = "grey"
)



# Define the alter_fun list with 4 sections
alter_fun = list(
  background = function(x, y, w, h) {
    # Draw background polygon (a square divided into 4 triangles)
    grid.polygon(
      unit.c(x - 0.5*w, x + 0.5*w, x + 0.5*w), 
      unit.c(y - 0.5*h, y - 0.5*h, y + 0.5*h),
      gp = gpar(fill = "grey", col = "white"))
    grid.polygon(
      unit.c(x + 0.5*w, x - 0.5*w, x - 0.5*w), 
      unit.c(y + 0.5*h, y - 0.5*h, y + 0.5*h),
      gp = gpar(fill = "grey", col = "white"))
  },
  
  insertion = function(x, y, w, h) {
    # Right section: Insertion
    grid.polygon(
      unit.c(x, x + 0.5*w, x + 0.5*w), 
      unit.c(y, y + 0.5*h, y - 0.5*h),
      gp = gpar(fill = col["insertion"], col = NA))
  },
  
  deletion = function(x, y, w, h) {
    # Left section: Deletion
    grid.polygon(
      unit.c(x, x - 0.5*w, x - 0.5*w), 
      unit.c(y, y + 0.5*h, y - 0.5*h),
      gp = gpar(fill = col["deletion"], col = NA))
  },
  
  duplication = function(x, y, w, h) {
    # Top section: Duplication
    grid.polygon(
      unit.c(x - 0.5*w, x + 0.5*w, x), 
      unit.c(y + 0.5*h, y + 0.5*h, y),
      gp = gpar(fill = col["duplication"], col = NA))
  },
  
  inversion = function(x, y, w, h) {
    # Bottom section: Inversion
    grid.polygon(
      unit.c(x - 0.5*w, x + 0.5*w, x), 
      unit.c(y - 0.5*h, y - 0.5*h, y),
      gp = gpar(fill = col["inversion"], col = NA))
  },


  break_end = function(x, y, w, h) {
    grid.segments(x - w*0.4, y - h*0.4, x + w*0.4, y + h*0.4, gp = gpar(lwd = 2))
    grid.segments(x + w*0.4, y - h*0.4, x - w*0.4, y + h*0.4, gp = gpar(lwd = 2))
  },

  none = function(x, y, w, h) {
    grid.rect(x, y, w*0.9, h*0.9, gp = gpar(fill = col["none"], col = NA))
  }
)


# Sort rows (genes) by the total number of mutations
# row_order <- order(rowSums(mutation_matrix != ""), decreasing = TRUE)
row_order = match(desired_row_order, rownames(mutation_matrix))
    
# Sort columns (samples) by the total number of mutations
# column_order <- order(colSums(mutation_matrix != ""), decreasing = TRUE)

    
# Define the specific column order
desired_order <- c('RLGS1-primary', 'RLGS1-recurrent', 'RLGS2-primary', 'RLGS2-recurrent',
                   'RLGS3-primary', 'RLGS3-recurrent', 'RLGS4-primary', 'RLGS4-recurrent',
                   'RLGS5-primary', 'RLGS5-recurrent', 'RLGS6-primary', 'RLGS6-recurrent',
                   'RLGS7-primary', 'RLGS7-recurrent', 'RLGS8-primary', 'RLGS8-recurrent',
                   'RLGS9-primary', 'RLGS9-recurrent', 'RLGS10-primary', 'RLGS10-recurrent',
                   'RLGS11-primary', 'RLGS11-recurrent', 'RLGS12-primary', 'RLGS12-recurrent', 
                   'null_13')


# Reorder columns based on the desired order
column_order <- match(desired_order, colnames(mutation_matrix))
print(column_order)
    
row_size = ifelse(small, 0.5, 10)
    
# Generate the OncoPrint with column names
print(oncoPrint(mutation_matrix, 
          alter_fun = alter_fun, 
          col = col,
          row_order = row_order,
          row_names_gp = grid::gpar(fontsize = row_size),  # Adjust the fontsize for row names

          column_order = column_order,
          column_title = "Mutation OncoPrint",
          column_names_gp = grid::gpar(fontsize = 10),  # Adjust the fontsize for column names
          column_names_rot = 45,  # Rotate column names for better readability
                
          heatmap_legend_param = list(title = "Mutations", 
                                      at = names(col), 
                                      labels = names(col)),
          alter_fun_is_vectorized = FALSE,  # Set this to FALSE
            show_column_names = TRUE
))
    

return()

}




pdf(opt$figure_file)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')

# keep these mutations that are <50bp
# # remove insertions and deletions
# input_df = input_df[!grepl('insertion', input_df$sv_type),]
# input_df = input_df[!grepl('deletion', input_df$sv_type),]

# # create oncoprint
# oncoprint_plot(input_df, small=TRUE)

# repeat for select genes
genes_oi = c('EGFR', 'ATRX', 'PTEN', 'CDK6', 'MET', 'CDKN2A', 'RB1', 'MDM4', 'CDK4', 'TP53', 'PDGFRA', 'FUBP1', 'NF1', 'MDM2', 'MYCN', 'PIK3CA', 'PIK3R1', 'CCND2', 'CIC', 'IDH1', 'TERT', 'none')

input_df = input_df[input_df$gene %in% genes_oi,]

# create oncoprint
oncoprint_plot(input_df, genes_oi)


dev.off()


print(opt$figure_file)





