# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def process_to_bed(combined_sv_vcf_file, nucleotide_range = 2000):
    # load df
    df = pd.read_csv(combined_sv_vcf_file, sep='\t')

    # for each row, convert into bed file
    bed = []

    for _, row in df.iterrows():
        # add start and end coordinates depending on sv type
        chrom = row['breakpoint1'].split(":")[0].strip("chr")
        start = max(int(row['breakpoint1'].split(":")[1]) - nucleotide_range, 0)
        end = int(row['breakpoint2'].split(":")[1]) + nucleotide_range

        if row['sv_type'] == 'break_end':
            # BND is a special case that requires duplicating the row and information from the breakpoint1
            end = int(row['breakpoint1'].split(":")[1]) + nucleotide_range

            # second breakpoint information
            chrom2 = row['breakpoint2'].split(":")[0].strip("chr")
            start2 = max(int(row['breakpoint2'].split(":")[1]) - nucleotide_range, 0)
            end2 = int(row['breakpoint2'].split(":")[1]) + nucleotide_range

            row2 = row.copy()
            row2['chromosome'] = chrom2
            row2['start'] = start2
            row2['end'] = end2

            bed.append(row2)
        
        # add to bed
        row['chromosome'] = chrom
        row['start'] = start
        row['end'] = end

        bed.append(row)

    # convert to df
    bed_df = pd.DataFrame(bed)

    # reorder columns to start with chromosome, start, end, then the rest of the columns
    cols = ['chromosome', 'start', 'end'] + [col for col in bed_df.columns if col not in ['chromosome', 'start', 'end']]
    bed_df = bed_df[cols]

    return bed_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and process df to bed file
    df = process_to_bed(combined_sv_vcf_file)

    # save bed file
    df.to_csv(sv_bed_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["combined_sv_vcf_file=", "sv_bed_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--combined_sv_vcf_file"):
            combined_sv_vcf_file = str(arg)
        
        if opt in ("--sv_bed_file"):
            sv_bed_file = str(arg)

    main()




