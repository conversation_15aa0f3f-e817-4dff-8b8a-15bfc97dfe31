# Alec <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def set_min_max_coordinates_exons(df, gene_df):
    # iterate over genes and set min and max for df
    for gene in gene_df['gene'].unique():
        # get start and end loci for gene
        start = gene_df[gene_df['gene'] == gene]['start'].min()
        end = gene_df[gene_df['gene'] == gene]['end'].max()

        # subset to gene
        mask = df['gene'] == gene

        # for each gene, get the start and end loci and make sure the sv is within the gene
        df.loc[mask, 'start'] = np.maximum(df.loc[mask, 'start'].astype('int'), start)
        df.loc[mask, 'end'] = np.minimum(df.loc[mask, 'end'].astype('int'), end)

        # make sure break end start is within the gene
        mask = np.logical_and(df['sv_type'] == 'break_end', df['gene'] == gene)
        df.loc[mask, 'start'] = np.maximum(df.loc[mask, 'start'].astype('int'), start)
        df.loc[mask, 'start'] = np.minimum(df.loc[mask, 'start'].astype('int'), end)

    # add y coordinate depending on sv type
    df['y'] = 0
    df.loc[df['sv_type'] == 'deletion', 'y'] = 1
    df.loc[df['sv_type'] == 'duplication', 'y'] = 2
    df.loc[df['sv_type'] == 'insertion', 'y'] = 3
    df.loc[df['sv_type'] == 'inversion', 'y'] = 4
    df.loc[df['sv_type'] == 'break_end', 'y'] = 5
    
    return df


def subset_to_nanopore_sv_examples(gene_svs_file, exon_loci_bed):
    # load dfs
    df = pd.read_csv(gene_svs_file, sep='\t')
    gene_df = pd.read_csv(exon_loci_bed, sep='\t')

    # remove null samples
    df = df[df['sv_type'] != 'none']

    # subset to genes in gene_df
    df = df[df['gene'].isin(gene_df['gene'])]

    # modify breakpoints to be start and end loci
    df['start'] = df['breakpoint1'].str.split(":").str[1]
    df['end'] = df['breakpoint2'].str.split(":").str[1]
    df['chromosome'] = df['breakpoint1'].str.split(":").str[0]

    # subset to columns of interest
    df = df[['sample', 'patient', 'gene', 'chromosome', 'start', 'end', 'sv_type']]

    # for each gene, get the start and end loci and make sure the sv is within the gene
    df = set_min_max_coordinates_exons(df, gene_df)

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process df
    df = subset_to_nanopore_sv_examples(gene_svs_file, exon_loci_bed)

    # save to file
    df.to_csv(figure_data_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["gene_svs_file=", "exon_loci_bed=", "figure_data_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--gene_svs_file"):
            gene_svs_file = str(arg)
        if opt in ("--exon_loci_bed"):
            exon_loci_bed = str(arg)
            
        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)

    main()




