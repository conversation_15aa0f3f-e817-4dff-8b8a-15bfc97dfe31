process {
    executor = "uge"
    penv = "smp"
    beforeScript = '''
        source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh
        source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
        conda activate nextflow
    '''
    clusterOptions = {
        "-V -P reimandlab -V -l h_rt=21:0:0:0 -V -l h_vmem=${task.memory.toMega() / task.cpus as Integer}M"
    }
    withName: 'TRIMGALORE' {
        memory = '10240 MB'
    }
    withName: 'FASTQC' {
        memory = '10240 MB'
    }
    withName: 'BWA_MEM' {
        memory = '10240 MB'
    }
}




executor {
    name = "uge"
    queueSize = 500
    queueStatInterval = "10s"
}

