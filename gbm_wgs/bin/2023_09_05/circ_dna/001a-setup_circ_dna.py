# Alec Ba<PERSON>i

import argparse, time, os

import pandas as pd 
import numpy as np

import glob, re


def parse_args():
    parser = argparse.ArgumentParser()

    parser.add_argument('--fastq_dir', type=str, required=True)
    parser.add_argument('--bin_dir', type=str, required=True)
    
    parser.add_argument('--fasta_file', type=str, required=True)
    parser.add_argument('--mosek_license_dir', type=str, required=True)
    parser.add_argument('--aa_data_repo', type=str, required=True)
    parser.add_argument('--bwa_mem_hg38_index_dir', type=str, required=True)

    parser.add_argument('--results_dir', type=str, required=True)

    return parser.parse_args()


# setup circdna
def setup_circ_dna_nextflow(fastq_dir_regex, bin_dir, fasta_file, mosek_license_dir, aa_data_repo, bwa_mem_hg38_index_dir, results_dir):
    # determine files from fastq_dir_regex
    files = pd.Series(glob.glob(fastq_dir_regex))

    # determine patients from files, ignore blood controls
    samples = np.unique(files.str.split("/").str[-1].str.split("_").str[0])

    # remove "blood" patients
    samples = [sample for sample in samples if 'blood' not in sample]

    for sample in samples:
        df = []
        
        # specific files for that patient
        files_oi = [file for file in files if re.search(f"{sample}", file)]

        # unique sequence runs
        uniq_runs = np.unique(pd.Series(files_oi).str.split("/").str[-1].str.split("_").str[-2])

        # iteratively add the runs
        for run in uniq_runs:
            m1 = pd.Series(files_oi).str.contains(run)
            m2 = pd.Series(files_oi).str.contains('R1')
            fastq1 = np.array(files_oi)[np.logical_and(m1, m2)][0]

            m2 = pd.Series(files_oi).str.contains('R2')
            fastq2 = np.array(files_oi)[np.logical_and(m1, m2)][0]

            # add to df
            df.append([sample, fastq1, fastq2])

        df = pd.DataFrame(df, columns = ['sample', 'fastq_1', 'fastq_2'])

        outfile = os.path.join(bin_dir, f'{sample}.csv')
        df.to_csv(outfile, index=False)


        # make dir for processing
        new_directory_path = os.path.join(results_dir, sample)
        if not os.path.exists(new_directory_path):
            os.makedirs(new_directory_path)

        # directory for analysis
        processing_dir = f'/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/circ_dna/{sample}'

        # create processing file
        circdna_file = os.path.join(bin_dir, sample)
        to_write = f"""#!/bin/bash
#$ -P reimandlab
#$ -N {sample}_circDNA
#$ -l h_vmem=10G,h_rt=7:0:0:0
#$ -q all.q
#$ -o /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/circ_dna/
#$ -e /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/circ_dna/


source /u/abahcheli/.profile
source /u/abahcheli/.bashrc
source /etc/profile.d/uge_settings.sh

source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh

mamba activate nextflow

cd {processing_dir}

export NXF_SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export NXF_OFFLINE='true'


nextflow run /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/nextflow_pipelines_offline/nf-core-circdna_1.1.0/1_1_0 \
--input {os.path.join(bin_dir, sample)}.csv \
--outdir {os.path.join(results_dir, sample)} \
--fasta {fasta_file} \
--bwa_index {bwa_mem_hg38_index_dir} \
--input_format FASTQ \
--circle_identifier ampliconarchitect \
--mosek_license_dir {mosek_license_dir} \
--aa_data_repo {aa_data_repo} \
--reference_build GRCh38 \
-profile singularity -c {os.path.join(bin_dir, 'nextflow.config')} \
-work-dir {processing_dir}/work \
-resume 
    """
        with open(circdna_file, "w") as outfile:
            outfile.write(to_write)

# --circle_identifier circle_finder,circexplorer2,circle_map_realign,circle_map_repeats,ampliconarchitect,unicycler \


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    args = parse_args()

    # Assign to individual variables
    fastq_dir = args.fastq_dir
    bin_dir = args.bin_dir
    
    fasta_file = args.fasta_file
    mosek_license_dir = args.mosek_license_dir
    aa_data_repo = args.aa_data_repo
    bwa_mem_hg38_index_dir = args.bwa_mem_hg38_index_dir
    
    results_dir = args.results_dir
    
    # define regex for relevant files
    file_regex = f'{fastq_dir}/*wgs*'

    # create files
    setup_circ_dna_nextflow(file_regex, bin_dir, fasta_file, mosek_license_dir, aa_data_repo, bwa_mem_hg38_index_dir, results_dir)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    main()

