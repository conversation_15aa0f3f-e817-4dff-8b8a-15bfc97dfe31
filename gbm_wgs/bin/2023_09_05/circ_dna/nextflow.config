process {
    executor = "uge"
    penv = "smp"
    beforeScript = '''
        export TMPDIR=/tmp
        source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh
        source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
        conda activate nextflow
    '''
    // Always set TMPDIR for all jobs
    environment = {
        TMPDIR = "/tmp"
    }
    clusterOptions = {
        "-V -P reimandlab -V -l h_rt=21:0:0:0 -V -l h_vmem=${task.memory.toMega() / task.cpus as Integer}M"
    }
    process {
        withName: UNICYCLER {
            memory = '100 GB'
            cpus = 12
        }
        withName: SPADES {
            memory = '100 GB'
            cpus = 12
        }
    }
}




executor {
    name = "uge"
    queueSize = 500
    queueStatInterval = "10s"
}

