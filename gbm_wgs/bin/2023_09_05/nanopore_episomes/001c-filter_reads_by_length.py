# Alec Bahcheli

import argparse, time, os

import pysam

help_message = '''
This script filters reads from a BAM file based on their length relative to the region size.
'''

def parse_args():
    parser = argparse.ArgumentParser(description=help_message)
    
    parser.add_argument('--region_bam', type=str, required=True,
                        help='Input BAM file with reads from a specific region')
    parser.add_argument('--region', type=str, required=True,
                        help='Region in format chr_start_end')
    parser.add_argument('--length_margin', type=float, default=0.2,
                        help='Length margin as a fraction (e.g., 0.2 for 20%)')
    parser.add_argument('--output_fastq', type=str, required=True,
                        help='Output FASTQ file with filtered reads')
    
    return parser.parse_args()


def filter_reads_by_length(region_bam, region, length_margin, output_fastq):
    """Filter reads based on their length relative to the region size"""
    # Parse region
    chrom, start, end = region.split('_')
    start = int(start)
    end = int(end)
    region_size = end - start
    
    # Calculate length thresholds
    min_length = region_size * (1 - length_margin)
    max_length = region_size * (1 + length_margin)
    
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_fastq), exist_ok=True)
    
    # Filter reads
    filtered_count = 0
    total_count = 0
    
    try:
        with pysam.AlignmentFile(region_bam, "rb") as bam, open(output_fastq, 'w') as fastq:
            for read in bam.fetch():
                total_count += 1
                read_length = read.query_length
                
                if min_length <= read_length <= max_length:
                    filtered_count += 1
                    
                    # Write to FASTQ
                    seq = read.query_sequence
                    qual = ''.join([chr(q + 33) for q in read.query_qualities])
                    
                    fastq.write(f"@{read.query_name}\n")
                    fastq.write(f"{seq}\n")
                    fastq.write(f"+\n")
                    fastq.write(f"{qual}\n")
    
    except Exception as e:
        print(f"Error filtering reads: {e}")
        # Create empty FASTQ file
        with open(output_fastq, 'w') as f:
            pass
        return 0, 0
    
    return filtered_count, total_count


def main():
    print('001c-filter_reads_by_length.py')
    t1 = time.time()
    
    args = parse_args()
    
    # Filter reads
    filtered_count, total_count = filter_reads_by_length(
        args.region_bam,
        args.region,
        args.length_margin,
        args.output_fastq
    )
    
    print(f"Filtered {filtered_count} out of {total_count} reads for region {args.region}")
    print(f"Runtime: {round(time.time() - t1, 2)} seconds")
    print('001c-filter_reads_by_length.py COMPLETE')


if __name__ == "__main__":
    main()
