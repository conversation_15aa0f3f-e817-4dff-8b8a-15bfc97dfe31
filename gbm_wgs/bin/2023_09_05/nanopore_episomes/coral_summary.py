#!/usr/bin/env python
# Alec Bahcheli

import argparse
import time
import os
import pandas as pd
import matplotlib.pyplot as plt

help_message = '''
This script generates a summary report of CoRAL ecDNA reconstruction results.
'''

def parse_args():
    parser = argparse.ArgumentParser(description=help_message)
    
    parser.add_argument('--graph_files', type=str, required=True,
                        help='Space-separated list of CoRAL graph files')
    parser.add_argument('--annotation_files', type=str, required=True,
                        help='Space-separated list of CoRAL annotation files')
    parser.add_argument('--summary_file', type=str, required=True,
                        help='Output summary TSV file')
    parser.add_argument('--summary_plot', type=str, required=True,
                        help='Output summary plot PDF file')
    
    return parser.parse_args()

def parse_graph_file(graph_file):
    """Parse CoRAL graph file to extract ecDNA information"""
    if not os.path.exists(graph_file) or os.path.getsize(graph_file) == 0:
        return None
    
    # Extract sample ID from filename
    # Expected format: /path/to/analysis_nanopore/ecdna/coral/{patient}-{tumor}_graph.txt
    filename = os.path.basename(graph_file)
    sample_id = filename.replace('_graph.txt', '')
    
    try:
        # Read graph file
        with open(graph_file, 'r') as f:
            lines = f.readlines()
        
        # Extract ecDNA information
        ecdna_count = 0
        ecdna_sizes = []
        ecdna_segments = []
        
        for line in lines:
            if line.startswith('ecDNA'):
                ecdna_count += 1
                parts = line.strip().split('\t')
                if len(parts) >= 3:
                    size = int(parts[2])
                    ecdna_sizes.append(size)
                    segments = int(parts[1])
                    ecdna_segments.append(segments)
        
        return {
            'sample_id': sample_id,
            'ecdna_count': ecdna_count,
            'avg_ecdna_size': sum(ecdna_sizes) / len(ecdna_sizes) if ecdna_sizes else 0,
            'max_ecdna_size': max(ecdna_sizes) if ecdna_sizes else 0,
            'avg_segments': sum(ecdna_segments) / len(ecdna_segments) if ecdna_segments else 0,
            'max_segments': max(ecdna_segments) if ecdna_segments else 0
        }
    
    except Exception as e:
        print(f"Error processing {graph_file}: {e}")
        return None

def parse_annotation_file(annotation_file):
    """Parse CoRAL annotation file to extract gene information"""
    if not os.path.exists(annotation_file) or os.path.getsize(annotation_file) == 0:
        return None
    
    # Extract sample ID from filename
    # Expected format: /path/to/analysis_nanopore/ecdna/coral/{patient}-{tumor}_ecdna_annotations.tsv
    filename = os.path.basename(annotation_file)
    sample_id = filename.replace('_ecdna_annotations.tsv', '')
    
    try:
        # Read annotation file
        df = pd.read_csv(annotation_file, sep='\t')
        
        # Extract gene information
        gene_count = len(df['gene_name'].unique()) if 'gene_name' in df.columns else 0
        oncogene_count = 0
        
        # List of common oncogenes
        oncogenes = ['MYC', 'EGFR', 'ERBB2', 'KRAS', 'BRAF', 'PIK3CA', 'AKT1', 'MDM2', 'CDK4', 'CCND1']
        
        if 'gene_name' in df.columns:
            oncogene_count = sum(1 for gene in df['gene_name'].unique() if gene in oncogenes)
        
        return {
            'sample_id': sample_id,
            'gene_count': gene_count,
            'oncogene_count': oncogene_count
        }
    
    except Exception as e:
        print(f"Error processing {annotation_file}: {e}")
        return None

def generate_summary(graph_files, annotation_files, summary_file, summary_plot):
    """Generate summary report of CoRAL results"""
    # Parse file lists
    graph_files = graph_files.strip().split()
    annotation_files = annotation_files.strip().split()
    
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(summary_file), exist_ok=True)
    
    # Process graph files
    graph_results = []
    for graph_file in graph_files:
        result = parse_graph_file(graph_file)
        if result:
            graph_results.append(result)
    
    # Process annotation files
    annotation_results = []
    for annotation_file in annotation_files:
        result = parse_annotation_file(annotation_file)
        if result:
            annotation_results.append(result)
    
    # Combine results
    graph_df = pd.DataFrame(graph_results)
    annotation_df = pd.DataFrame(annotation_results)
    
    if not graph_df.empty and not annotation_df.empty:
        summary_df = pd.merge(graph_df, annotation_df, on='sample_id', how='outer')
    elif not graph_df.empty:
        summary_df = graph_df
    elif not annotation_df.empty:
        summary_df = annotation_df
    else:
        summary_df = pd.DataFrame()
    
    # Save summary to file
    if not summary_df.empty:
        summary_df.to_csv(summary_file, sep='\t', index=False)
        
        # Generate summary plot
        plt.figure(figsize=(12, 8))
        
        # Plot ecDNA count by sample
        if 'ecdna_count' in summary_df.columns:
            plt.subplot(2, 2, 1)
            sns.barplot(x='sample_id', y='ecdna_count', data=summary_df)
            plt.title('ecDNA Count by Sample')
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
        
        # Plot average ecDNA size by sample
        if 'avg_ecdna_size' in summary_df.columns:
            plt.subplot(2, 2, 2)
            sns.barplot(x='sample_id', y='avg_ecdna_size', data=summary_df)
            plt.title('Average ecDNA Size by Sample')
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
        
        # Plot gene count by sample
        if 'gene_count' in summary_df.columns:
            plt.subplot(2, 2, 3)
            sns.barplot(x='sample_id', y='gene_count', data=summary_df)
            plt.title('Gene Count by Sample')
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
        
        # Plot oncogene count by sample
        if 'oncogene_count' in summary_df.columns:
            plt.subplot(2, 2, 4)
            sns.barplot(x='sample_id', y='oncogene_count', data=summary_df)
            plt.title('Oncogene Count by Sample')
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
        
        plt.savefig(summary_plot, dpi=300, bbox_inches='tight')
        plt.close()
    
    return summary_df

def main():
    print('coral_summary.py')
    t1 = time.time()
    
    args = parse_args()
    
    # Generate summary
    summary_df = generate_summary(args.graph_files, args.annotation_files, args.summary_file, args.summary_plot)
    
    if summary_df.empty:
        print("No results found in the provided files")
    else:
        print(f"Generated summary for {len(summary_df)} samples")
    
    print(f"Runtime: {round(time.time() - t1, 2)} seconds")
    print('coral_summary.py COMPLETE')

if __name__ == "__main__":
    main()
