# Alec Ba<PERSON>cheli

import argparse, time, os

help_message = '''
This script converts a GFA assembly file to FASTA format.
'''

def parse_args():
    parser = argparse.ArgumentParser(description=help_message)
    
    parser.add_argument('--input_gfa', type=str, required=True,
                        help='Input GFA assembly file')
    parser.add_argument('--output_fasta', type=str, required=True,
                        help='Output FASTA file')
    
    return parser.parse_args()

def convert_gfa_to_fasta(input_gfa, output_fasta):
    """Convert GFA to FASTA format"""
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_fasta), exist_ok=True)
    
    # Check if GFA file is empty
    if os.path.getsize(input_gfa) == 0:
        print("GFA file is empty, creating empty FASTA file")
        with open(output_fasta, 'w') as f:
            pass
        return False
    
    try:
        with open(input_gfa, 'r') as gfa, open(output_fasta, 'w') as fasta:
            for line in gfa:
                if line.startswith('S'):
                    parts = line.strip().split('\t')
                    if len(parts) >= 3:
                        seq_id = parts[1]
                        sequence = parts[2]
                        fasta.write(f">{seq_id}\n{sequence}\n")
        
        # Check if output is empty
        if os.path.getsize(output_fasta) == 0:
            print("No sequences found in GFA file")
            return False
        
        return True
    
    except Exception as e:
        print(f"Error converting GFA to FASTA: {e}")
        # Create empty output file
        with open(output_fasta, 'w') as f:
            pass
        return False

def main():
    print('001f-gfa_to_fasta.py')
    t1 = time.time()
    
    args = parse_args()
    
    # Convert GFA to FASTA
    success = convert_gfa_to_fasta(args.input_gfa, args.output_fasta)
    
    if success:
        print(f"Successfully converted {args.input_gfa} to {args.output_fasta}")
    else:
        print(f"Failed to convert {args.input_gfa} to {args.output_fasta}")
    
    print(f"Runtime: {round(time.time() - t1, 2)} seconds")
    print('001f-gfa_to_fasta.py COMPLETE')

if __name__ == "__main__":
    main()