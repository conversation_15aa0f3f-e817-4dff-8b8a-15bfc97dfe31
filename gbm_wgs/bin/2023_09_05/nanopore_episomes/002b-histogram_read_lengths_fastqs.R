# Alec <PERSON>
library(optparse)
library(forcats)
library(ggplot2)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




create_histogram = function(cna_id, raw_df){

# subset to cna_id
input_df = raw_df[raw_df$cna_id == cna_id,]

p = ggplot(input_df, aes(x = read_length)) + plot_theme() +
geom_histogram() +

ggtitle(cna_id) +
xlab("Read length (nts)") + ylab('Number of reads') + 

theme(plot.title = element_text(size = 16))


print(p)

return()
}





pdf(opt$figure_file)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')

# Order the levels of 'sample' alphabetically
ordered_samples = sort(unique(input_df$sample))

# Reorder the 'cna_id' column based on the alphabetical order of 'sample'
input_df$cna_id = factor(input_df$cna_id, levels = unique(input_df$cna_id[order(input_df$sample)]))


# create boxplots for each sample
lapply(levels(input_df$cna_id), create_histogram, input_df)


dev.off()


print(opt$figure_file)






