# <PERSON>cheli

import argparse, time, os, glob

import pandas as pd
import numpy as np

help_message = '''
Failed
'''

def parse_args():
    parser = argparse.ArgumentParser()
    
    parser.add_argument('--flye_assemblies', type=str, required=True)
    parser.add_argument('--miniasm_assemblies', type=str, required=True)
    parser.add_argument('--output_report', type=str, required=True)
    
    return parser.parse_args()

def count_fasta_contigs(fasta_file):
    """Count the number of contigs in a FASTA file"""
    if not os.path.exists(fasta_file) or os.path.getsize(fasta_file) == 0:
        return 0
    
    with open(fasta_file, 'r') as f:
        return sum(1 for line in f if line.startswith('>'))

def calculate_fasta_length(fasta_file):
    """Calculate the total length of sequences in a FASTA file"""
    if not os.path.exists(fasta_file) or os.path.getsize(fasta_file) == 0:
        return 0
    
    total_length = 0
    current_seq = ""
    
    with open(fasta_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line.startswith('>'):
                # Add length of previous sequence
                total_length += len(current_seq)
                current_seq = ""
            else:
                current_seq += line
    
    # Add length of last sequence
    total_length += len(current_seq)
    
    return total_length

def generate_summary(flye_assemblies, miniasm_assemblies, output_report):
    """Generate a summary report of the assemblies"""
    # Convert string of space-separated files to list
    flye_files = flye_assemblies.split()
    miniasm_files = miniasm_assemblies.split()
    
    # Create a dictionary to store results
    results = []
    
    # Process each flye assembly
    for flye_file in flye_files:
        region = os.path.basename(flye_file).split('.flye.fasta')[0]
        
        # Find corresponding miniasm file
        miniasm_file = next((f for f in miniasm_files if region in f), None)
        
        if miniasm_file:
            # Count contigs and calculate lengths
            flye_contigs = count_fasta_contigs(flye_file)
            flye_length = calculate_fasta_length(flye_file)
            
            miniasm_contigs = count_fasta_contigs(miniasm_file)
            miniasm_length = calculate_fasta_length(miniasm_file)
            
            # Add to results
            results.append({
                'Region': region,
                'Flye_Contigs': flye_contigs,
                'Flye_Total_Length': flye_length,
                'Miniasm_Contigs': miniasm_contigs,
                'Miniasm_Total_Length': miniasm_length
            })
    
    # Create DataFrame and save to file
    df = pd.DataFrame(results)
    df.to_csv(output_report, sep='\t', index=False)
    
    return df

def main():
    print('001f-generate_ecdna_summary.py')
    t1 = time.time()
    
    args = parse_args()
    
    # Generate summary report
    summary_df = generate_summary(
        args.flye_assemblies,
        args.miniasm_assemblies,
        args.output_report
    )
    
    print(f"Generated summary report with {len(summary_df)} regions")
    print(round(time.time() - t1, 2))
    print('001f-generate_ecdna_summary.py COMPLETE')

if __name__ == "__main__":
    main()