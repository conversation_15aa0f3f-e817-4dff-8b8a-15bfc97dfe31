# <PERSON> Bahcheli

import argparse, os, time

import pandas as pd
import glob 

help_message = '''
This script creates a dictionary of regions to be processed for ecDNA reconstruction.
It takes consensus CNAs files and extracts gain regions for each sample.
'''

def parse_args():
    parser = argparse.ArgumentParser(description=help_message)
    
    parser.add_argument('--res_dir', type=str, required=True,
                        help='Results directory containing consensus CNAs')
    parser.add_argument('--output_dict', type=str, required=True,
                        help='Output file for the region dictionary')
    
    return parser.parse_args()

def create_region_dict(res_dir, output_dict):
    # Find all gain files
    gain_files = glob.glob(os.path.join(res_dir, "analysis_sarek/consensus_cnas/*/*_combine_intersected_results_gain.tsv"))
    
    # Dictionary to store sample -> regions mapping
    region_dict = {}
    
    # Process each gain file
    for gain_file in gain_files:
        if not os.path.exists(gain_file):
            print(f"Warning: File {gain_file} does not exist. Skipping.")
            continue
        
        # Extract patient-tumor ID from filename
        # Expected format: /path/to/analysis_sarek/consensus_cnas/{patient}/002-{patient}-{tumor}_combine_intersected_results_gain.tsv
        filename = os.path.basename(gain_file)
        parts = filename.split('-')
        if len(parts) < 3 or not filename.endswith('_combine_intersected_results_gain.tsv'):
            print(f"Warning: Unexpected filename format: {filename}. Skipping.")
            continue
        
        patient = parts[1]
        tumor = parts[2].split('_')[0]
        sample_id = f"{patient}-{tumor}"
        
        # Read gain regions file
        try:
            df = pd.read_csv(gain_file, sep='\t', header=None)
            if df.shape[1] < 3:  # Ensure at least chrom, start, end columns
                print(f"Warning: File {gain_file} has unexpected format. Skipping.")
                continue
                
            # Create region IDs
            regions = []
            for _, row in df.iterrows():
                chrom = row[0]
                start = row[1]
                end = row[2]
                region_id = f"{chrom}_{start}_{end}"
                regions.append(region_id)
            
            # Add to dictionary
            if regions:
                region_dict[sample_id] = regions
                print(f"Added {len(regions)} regions for sample {sample_id}")
            
        except Exception as e:
            print(f"Error processing file {gain_file}: {str(e)}")
    
    # Write dictionary to output file
    with open(output_dict, 'w') as f:
        f.write("sample_id\tregion_id\n")
        for sample_id, regions in region_dict.items():
            for region in regions:
                f.write(f"{sample_id}\t{region}\n")
    
    print(f"Created region dictionary with {len(region_dict)} samples")
    return region_dict

def main():
    print('001a-create_region_dict.py')
    t1 = time.time()
    
    args = parse_args()
    
    # Create region dictionary
    create_region_dict(args.res_dir, args.output_dict)
    
    print(f"Runtime: {round(time.time() - t1, 2)} seconds")
    print('001a-create_region_dict.py COMPLETE')

if __name__ == "__main__":
    main()
