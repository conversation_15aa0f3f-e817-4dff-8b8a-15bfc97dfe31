# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob
import concurrent.futures


help_message = '''
Failed
'''


def process_fastq_file(file):
    df = []

    # get the sample name and file id
    file_id = ".".join(file.split('/')[-1].split('.')[:-1])
    sample = file_id.split("_")[-1]
    region = "_".join(file_id.split("_")[:3])
    event = file_id.split("_")[3]
    copy_number = file_id.split("_")[4]
    size = file_id.split("_")[5]

    cna_id = sample + "_" + region + "_" + event

    with open(file, 'r') as f:
        while True:
            # Read the four lines of a FASTQ entry (sequence entry format)
            header = f.readline().strip()
            sequence = f.readline().strip()
            plus_line = f.readline().strip()
            quality = f.readline().strip()
            
            if not sequence:  # End of file
                break
            
            # Calculate the length of the sequence and append to the df with the sample name and file id
            read_length = len(sequence)
            df.append([sample, cna_id, region, event, copy_number, size, read_length])

    # Return the list of rows to be added to the final dataframe
    return df


def calculate_read_lengths(fastq_files, threads):
    res_df = []
    
    # Use ThreadPoolExecutor to process the files in parallel
    with concurrent.futures.ThreadPoolExecutor(max_workers=threads) as executor:
        results = executor.map(process_fastq_file, fastq_files)

    # Flatten the results list and create a DataFrame
    for result in results:
        res_df.extend(result)

    # Create a dataframe from the res_df
    res_df = pd.DataFrame(res_df, columns=['sample', 'cna_id', 'region', 'event', 'copy_number', 'copy_number_length', 'read_length'])

    return res_df


# def calculate_read_lengths(fastq_files):
#     res_df = []
    
#     # read each file
#     for file in fastq_files:
#         df = []

#         # get the sample name and file id
#         file_id = ".".join(file.split('/')[-1].split('.')[:-1])
#         sample = file_id.split("_")[-1]
#         region = "_".join(file_id.split("_")[:3])
#         event = file_id.split("_")[3]
#         copy_number = file_id.split("_")[4]
#         size = file_id.split("_")[5]

#         cna_id = sample + "_" + region + "_" + event

#         with open(file, 'r') as f:
#             while True:
#                 # Read the four lines of a FASTQ entry (sequence entry format)
#                 header = f.readline().strip()
#                 sequence = f.readline().strip()
#                 plus_line = f.readline().strip()
#                 quality = f.readline().strip()
                
#                 if not sequence:  # End of file
#                     break
                
#                 # Calculate the length of the sequence and append to the df with the sample name and file id
#                 read_length = len(sequence)
#                 df.append([sample, cna_id, region, event, copy_number, size, read_length])
    
#         # append the df to the res_df
#         res_df.extend(df)

#     # create a dataframe from the res_df
#     res_df = pd.DataFrame(res_df, columns=['sample', 'cna_id', 'region', 'event', 'copy_number', 'copy_number_length', 'read_length'])

#     return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # list files
    file_regex = os.path.join(results_dir, 'analysis_nanopore/episomes/fastq_files/*.fastq')
    fastq_files = glob.glob(file_regex)

    # calculate read lengths
    df = calculate_read_lengths(fastq_files, threads)

    # save to files
    df.to_csv(read_lengths_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "read_lengths_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--read_lengths_file"):
            read_lengths_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()




