# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import concurrent.futures


help_message = '''
Failed
'''


def evaluate_region(args):
    factor_cutoff = 5
    bin_size = 1000

    df, cna_id = args

    # try binning first
    # bin from 0 to highest, nearest l-length
    bins = np.linspace(0, np.ceil(max(df['read_length'])/bin_size) * bin_size, 99)
    digitized = np.digitize(df['read_length'], bins)

    # the number of counts (reads) in each bin (height of histogram)
    bin_counts = np.array([len(df['read_length'][digitized == i]) for i in range(1, len(bins))])

    # avoid dividing by 0
    bin_counts += 1

    print(cna_id, bin_counts, np.logical_and((bin_counts / np.roll(bin_counts, 1) > factor_cutoff), (bin_counts / np.roll(bin_counts, -1) > 
    factor_cutoff)), bins)

    # divide by the adjacent counts to get relative size
    mask = np.logical_and((bin_counts / np.roll(bin_counts, 1) > factor_cutoff), (bin_counts / np.roll(bin_counts, -1) > 
    factor_cutoff))
    
    # if there is a peak, return the bin size of that peak
    if np.any(mask[1:]):
        # the maximum size with obvious output
        return [cna_id, np.ceil(max(bin_counts[mask]))]

    else:
        return [cna_id, 0]



def prioritize_gains_for_episomes(read_lengths_file, threads):
    # read in lengths and calculate distribution
    lengths_df = pd.read_csv(read_lengths_file, sep='\t')

    # arguments for multi-threaded processing
    args = [(lengths_df[lengths_df["cna_id"] == cna_id], cna_id) for cna_id in lengths_df['cna_id'].unique()]

    # Use ThreadPoolExecutor to process the files in parallel
    with concurrent.futures.ThreadPoolExecutor(max_workers=threads) as executor:
        results = executor.map(evaluate_region, args)

    # create df from results
    df = pd.DataFrame(results, columns = ['cna_id', 'episome_size'])

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load and prioritize gains for episomes
    df = prioritize_gains_for_episomes(read_lengths_file, threads)

    # save to files
    df.to_csv(prioritized_cnas_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["read_lengths_file=", "prioritized_cnas_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--read_lengths_file"):
            read_lengths_file = str(arg)

        if opt in ("--prioritized_cnas_file"):
            prioritized_cnas_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()




