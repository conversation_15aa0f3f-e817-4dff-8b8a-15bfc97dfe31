# Alec Bahcheli

import argparse, time, subprocess, os

help_message = '''
This script assembles filtered reads using miniasm.
'''

def parse_args():
    parser = argparse.ArgumentParser(description=help_message)
    
    parser.add_argument('--filtered_fastq', type=str, required=True,
                        help='Input filtered FASTQ file')
    parser.add_argument('--paf_file', type=str, required=True,
                        help='Input PAF file from minimap2 self-alignment')
    parser.add_argument('--output_gfa', type=str, required=True,
                        help='Output GFA assembly file')
    parser.add_argument('--min_reads', type=int, default=10,
                        help='Minimum number of reads required for assembly')
    parser.add_argument('--threads', type=int, default=1,
                        help='Number of threads to use')
    
    return parser.parse_args()

def count_fastq_reads(fastq_file):
    """Count the number of reads in a FASTQ file"""
    try:
        with open(fastq_file, 'r') as f:
            read_count = sum(1 for line in f if line.startswith('@'))
        return read_count
    except Exception as e:
        print(f"Error counting reads: {e}")
        return 0

def run_miniasm(filtered_fastq, paf_file, output_gfa):
    """Assemble reads using miniasm"""
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_gfa), exist_ok=True)
    
    # Check if PAF file is empty
    if os.path.getsize(paf_file) == 0:
        print("PAF file is empty, cannot perform assembly")
        with open(output_gfa, 'w') as f:
            pass
        return False
    
    # Run miniasm
    miniasm_path = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/miniasm"
    
    cmd = [
        miniasm_path,
        "-f", filtered_fastq,
        paf_file
    ]
    
    try:
        with open(output_gfa, 'w') as f:
            subprocess.run(cmd, check=True, stdout=f)
        
        # Check if output is empty
        if os.path.getsize(output_gfa) == 0:
            print("Miniasm produced an empty assembly")
            return False
        
        return True
    
    except subprocess.CalledProcessError as e:
        print(f"Error running miniasm: {e}")
        # Create empty output file
        with open(output_gfa, 'w') as f:
            pass
        return False

def main():
    print('001e-assemble_with_miniasm.py')
    t1 = time.time()
    
    args = parse_args()
    
    # Count reads
    read_count = count_fastq_reads(args.filtered_fastq)
    print(f"Found {read_count} reads in {args.filtered_fastq}")
    
    # Check if we have enough reads
    if read_count < args.min_reads:
        print(f"Not enough reads for assembly (found {read_count}, need {args.min_reads})")
        # Create empty output file
        os.makedirs(os.path.dirname(args.output_gfa), exist_ok=True)
        with open(args.output_gfa, 'w') as f:
            pass
    else:
        # Run miniasm
        success = run_miniasm(args.filtered_fastq, args.paf_file, args.output_gfa)
        if success:
            print(f"Successfully assembled {read_count} reads with miniasm")
        else:
            print("Miniasm assembly failed")
    
    print(f"Runtime: {round(time.time() - t1, 2)} seconds")
    print('001e-assemble_with_miniasm.py COMPLETE')

if __name__ == "__main__":
    main()
