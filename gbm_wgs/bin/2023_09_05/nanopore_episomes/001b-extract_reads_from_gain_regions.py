# Alec Bahcheli

import argparse, time, subprocess, os

import pysam

help_message = '''
This script extracts reads from CRAM files that overlap with specified gain regions.
'''

def parse_args():
    parser = argparse.ArgumentParser(description=help_message)
    
    parser.add_argument('--cram_file', type=str, required=True,
                        help='Input CRAM file')
    parser.add_argument('--region', type=str, required=True,
                        help='Region in format chr_start_end')
    parser.add_argument('--fasta', type=str, required=True,
                        help='Reference FASTA file')
    parser.add_argument('--output_bam', type=str, required=True,
                        help='Output BAM file with extracted reads')
    parser.add_argument('--threads', type=int, default=1,
                        help='Number of threads to use')
    
    return parser.parse_args()


def extract_reads(cram_file, region, fasta, output_bam, threads):
    """Extract reads from CRAM file that overlap with the specified region"""
    # Parse region
    chrom, start, end = region.split('_')
    start = int(start)
    end = int(end)
    
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_bam), exist_ok=True)
    
    # Extract reads using samtools
    samtools_path = "/.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools"
    
    cmd = [
        samtools_path, "view",
        "-h",  # Include header
        "-b",  # Output BAM
        "-T", fasta,  # Reference FASTA
        "-@", str(threads),  # Threads
        "-o", output_bam,  # Output file
        cram_file,  # Input file
        f"chr{chrom}:{start}-{end}"  # Region
    ]
    
    try:
        subprocess.run(cmd, check=True)
        
        # Index the BAM file
        index_cmd = [samtools_path, "index", output_bam]
        subprocess.run(index_cmd, check=True)
        
        # Count reads
        with pysam.AlignmentFile(output_bam, "rb") as bam:
            read_count = sum(1 for _ in bam.fetch())
        
        print(f"Extracted {read_count} reads from region {region}")
        return read_count
    
    except subprocess.CalledProcessError as e:
        print(f"Error extracting reads: {e}")
        # Create empty BAM file
        with open(output_bam, 'w') as f:
            pass
        return 0


def main():
    print('001b-extract_reads_from_gain_regions.py')
    t1 = time.time()
    
    args = parse_args()
    
    # Extract reads
    read_count = extract_reads(
        args.cram_file,
        args.region,
        args.fasta,
        args.output_bam,
        args.threads
    )
    
    print(f"Extracted {read_count} reads from region {args.region}")
    print(f"Runtime: {round(time.time() - t1, 2)} seconds")
    print('001b-extract_reads_from_gain_regions.py COMPLETE')

if __name__ == "__main__":
    main()
