# Alec Bahcheli

import argparse, time, subprocess, os

help_message = '''
This script checks if there are enough reads in a FASTQ file and aligns them to themselves using minimap2.
'''

def parse_args():
    parser = argparse.ArgumentParser(description=help_message)
    
    parser.add_argument('--input_fastq', type=str, required=True,
                        help='Input FASTQ file')
    parser.add_argument('--output_paf', type=str, required=True,
                        help='Output PAF file from minimap2')
    parser.add_argument('--min_reads', type=int, default=10,
                        help='Minimum number of reads required')
    parser.add_argument('--threads', type=int, default=1,
                        help='Number of threads to use')
    
    return parser.parse_args()

def count_fastq_reads(fastq_file):
    """Count the number of reads in a FASTQ file"""
    try:
        with open(fastq_file, 'r') as f:
            read_count = sum(1 for line in f if line.startswith('@'))
        return read_count
    except Exception as e:
        print(f"Error counting reads: {e}")
        return 0

def align_reads(input_fastq, output_paf, threads):
    """Align reads to themselves using minimap2"""
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_paf), exist_ok=True)
    
    # Run minimap2
    minimap2_path = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/minimap2"
    
    cmd = [
        minimap2_path,
        "-x", "ava-ont",  # Preset for Oxford Nanopore all-vs-all mapping
        "-t", str(threads),  # Threads
        input_fastq,  # Input file
        input_fastq   # Input file again (self-alignment)
    ]
    
    try:
        with open(output_paf, 'w') as f:
            subprocess.run(cmd, check=True, stdout=f)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error aligning reads: {e}")
        # Create empty PAF file
        with open(output_paf, 'w') as f:
            pass
        return False

def main():
    print('001d-check_read_count_and_align.py')
    t1 = time.time()
    
    args = parse_args()
    
    # Count reads
    read_count = count_fastq_reads(args.input_fastq)
    print(f"Found {read_count} reads in {args.input_fastq}")
    
    # Check if we have enough reads
    if read_count < args.min_reads:
        print(f"Not enough reads (found {read_count}, need {args.min_reads})")
        # Create empty PAF file
        os.makedirs(os.path.dirname(args.output_paf), exist_ok=True)
        with open(args.output_paf, 'w') as f:
            pass
    else:
        # Align reads
        success = align_reads(args.input_fastq, args.output_paf, args.threads)
        if success:
            print(f"Successfully aligned {read_count} reads")
        else:
            print("Alignment failed")
    
    print(f"Runtime: {round(time.time() - t1, 2)} seconds")
    print('001d-check_read_count_and_align.py COMPLETE')

if __name__ == "__main__":
    main()