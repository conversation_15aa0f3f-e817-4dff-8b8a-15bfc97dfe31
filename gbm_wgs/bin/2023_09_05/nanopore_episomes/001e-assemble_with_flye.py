# Alec Ba<PERSON>cheli

import argparse, time, subprocess, os, shutil

help_message = '''
This script assembles filtered reads using Flye.
'''

def parse_args():
    parser = argparse.ArgumentParser(description=help_message)
    
    parser.add_argument('--filtered_fastq', type=str, required=True,
                        help='Input filtered FASTQ file')
    parser.add_argument('--output_fasta', type=str, required=True,
                        help='Output FASTA assembly file')
    parser.add_argument('--outdir', type=str, required=True,
                        help='Output directory for Flye')
    parser.add_argument('--min_reads', type=int, default=10,
                        help='Minimum number of reads required for assembly')
    parser.add_argument('--threads', type=int, default=1,
                        help='Number of threads to use')
    
    return parser.parse_args()

def count_fastq_reads(fastq_file):
    """Count the number of reads in a FASTQ file"""
    try:
        with open(fastq_file, 'r') as f:
            read_count = sum(1 for line in f if line.startswith('@'))
        return read_count
    except Exception as e:
        print(f"Error counting reads: {e}")
        return 0

def run_flye(filtered_fastq, output_fasta, outdir, threads):
    """Assemble reads using Flye"""
    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(output_fasta), exist_ok=True)
    
    # Remove existing output directory if it exists
    if os.path.exists(outdir):
        shutil.rmtree(outdir)
    
    # Run Flye
    flye_path = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/flye"
    
    cmd = [
        flye_path,
        "--nano-raw", filtered_fastq,  # Input reads
        "--out-dir", outdir,  # Output directory
        "--threads", str(threads)  # Threads
    ]
    
    try:
        subprocess.run(cmd, check=True)
        
        # Copy assembly to output location
        assembly_file = os.path.join(outdir, "assembly.fasta")
        if os.path.exists(assembly_file):
            shutil.copy(assembly_file, output_fasta)
            return True
        else:
            print(f"Flye did not produce an assembly file")
            with open(output_fasta, 'w') as f:
                pass
            return False
    
    except subprocess.CalledProcessError as e:
        print(f"Error running Flye: {e}")
        # Create empty output file
        with open(output_fasta, 'w') as f:
            pass
        return False

def main():
    print('001e-assemble_with_flye.py')
    t1 = time.time()
    
    args = parse_args()
    
    # Count reads
    read_count = count_fastq_reads(args.filtered_fastq)
    print(f"Found {read_count} reads in {args.filtered_fastq}")
    
    # Check if we have enough reads
    if read_count < args.min_reads:
        print(f"Not enough reads for assembly (found {read_count}, need {args.min_reads})")
        # Create empty output file
        os.makedirs(os.path.dirname(args.output_fasta), exist_ok=True)
        with open(args.output_fasta, 'w') as f:
            pass
    else:
        # Run Flye
        success = run_flye(args.filtered_fastq, args.output_fasta, args.outdir, args.threads)
        if success:
            print(f"Successfully assembled {read_count} reads with Flye")
        else:
            print("Flye assembly failed")
    
    print(f"Runtime: {round(time.time() - t1, 2)} seconds")
    print('001e-assemble_with_flye.py COMPLETE')

if __name__ == "__main__":
    main()