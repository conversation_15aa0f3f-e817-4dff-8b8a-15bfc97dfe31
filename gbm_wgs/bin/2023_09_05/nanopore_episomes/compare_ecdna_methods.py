#!/usr/bin/env python
# Alec Bahcheli

import argparse
import time
import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

help_message = '''
This script compares ecDNA reconstruction results from different methods (CoRAL, decoil, manual assembly).
'''

def parse_args():
    parser = argparse.ArgumentParser(description=help_message)
    
    parser.add_argument('--coral_results', type=str, required=True,
                        help='Space-separated list of CoRAL result files')
    parser.add_argument('--decoil_results', type=str, required=True,
                        help='Space-separated list of decoil result files')
    parser.add_argument('--manual_results', type=str, required=True,
                        help='Space-separated list of manual assembly result files')
    parser.add_argument('--comparison_file', type=str, required=True,
                        help='Output comparison TSV file')
    parser.add_argument('--comparison_plot', type=str, required=True,
                        help='Output comparison plot PDF file')
    
    return parser.parse_args()

def parse_coral_results(coral_file):
    """Parse CoRAL results to extract ecDNA information"""
    if not os.path.exists(coral_file) or os.path.getsize(coral_file) == 0:
        return None
    
    # Extract sample ID from filename
    # Expected format: /path/to/analysis_nanopore/ecdna/coral/{patient}-{tumor}_graph.txt
    filename = os.path.basename(coral_file)
    sample_id = filename.replace('_graph.txt', '')
    
    try:
        # Read graph file