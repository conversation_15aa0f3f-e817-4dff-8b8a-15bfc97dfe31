# <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

help_message = '''
Failed
'''


def combine_file_sort(sample_1_file, sample_2_file):
    # read in the files
    df1 = pd.read_csv(sample_1_file, sep='\t')
    df2 = pd.read_csv(sample_2_file, sep='\t')

    sample1 = "-".join(sample_1_file.split("/")[-1].split("-")[:2])
    sample2 = "-".join(sample_2_file.split("/")[-1].split("-")[:2])

    # create unique identifier for regions
    df1['region'] = df1['chr'] + '_' + df1['startpos'].astype(str) + '_' + df1['endpos'].astype(str) + '_' + sample1
    df2['region'] = df2['chr'] + '_' + df2['startpos'].astype(str) + '_' + df2['endpos'].astype(str) + '_' + sample2

    # create a unique identifier for the alleles
    df1['allele'] = df1['nMajor'].astype('str') + '_' + df1['nMinor'].astype('str')
    df2['allele'] = df2['nMajor'].astype('str') + '_' + df2['nMinor'].astype('str')

    # remove non-cnas
    df1 = df1[df1['allele'] != '1_1']
    df2 = df2[df2['allele'] != '1_1']

    return df1, df2


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process and combine vcfs
    df1, df2 = combine_file_sort(sample_1_file, sample_2_file)

    # write to file
    df1.to_csv(primary_cna_file, sep='\t', index=False, header=False)
    df2.to_csv(recurrent_cna_file, sep='\t', index=False, header=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["sample_1_file=", "sample_2_file=", "primary_cna_file=", "recurrent_cna_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--sample_1_file"):
            sample_1_file = str(arg)
        if opt in ("--sample_2_file"):
            sample_2_file = str(arg)

        if opt in ("--primary_cna_file"):
            primary_cna_file = str(arg)
        if opt in ("--recurrent_cna_file"):
            recurrent_cna_file = str(arg)

    main()



