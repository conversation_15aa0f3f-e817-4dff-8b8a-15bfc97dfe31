# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

help_message = '''
Failed
'''


def combine_file_sort(sample_1_file, sample_2_file):
    # read in the files
    df1 = pd.read_csv(sample_1_file, sep='\t')
    df2 = pd.read_csv(sample_2_file, sep='\t')

    # create unique identifier for regions
    df1['region'] = df1['chrom'] + '_' + df1['start'].astype(str) + '_' + df1['end'].astype(str) + '_' + df1['classification']
    df2['region'] = df2['chrom'] + '_' + df2['start'].astype(str) + '_' + df2['end'].astype(str) + '_' + df2['classification']

    # merge the two dataframes
    df = pd.concat([df1, df2])

    # sort by chr then start
    df = df.sort_values(by=['chrom', 'start'])

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process and combine vcfs
    df = combine_file_sort(sample_1_file, sample_2_file)

    # write to file
    df.to_csv(patient_combined_cna_file, sep='\t', index=False, header=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["sample_1_file=", "sample_2_file=", "patient_combined_cna_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--sample_1_file"):
            sample_1_file = str(arg)
        if opt in ("--sample_2_file"):
            sample_2_file = str(arg)

        if opt in ("--patient_combined_cna_file"):
            patient_combined_cna_file = str(arg)

    main()



