# <PERSON>
library(optparse)
library(Canopy)

# options list for parser options
option_list <- list(
    make_option(c("-a","--canopy_total_depth_X_file"), type="character", default=NULL,
            help="",
            dest="canopy_total_depth_X_file"),
    make_option(c("-b","--canopy_mutant_allele_depth_R_file"), type="character", default=NULL,
            help="",
            dest="canopy_mutant_allele_depth_R_file"),

    make_option(c("-c","--canopy_major_cna_WM_file"), type="character", default=NULL,
            help="",
            dest="canopy_major_cna_WM_file"),
    make_option(c("-d","--canopy_minor_cna_Wm_file"), type="character", default=NULL,
            help="",
            dest="canopy_minor_cna_Wm_file"),

    make_option(c("-e","--canopy_overlapping_cna_C_file"), type="character", default=NULL,
            help="",
            dest="canopy_overlapping_cna_C_file"),
    make_option(c("-f","--canopy_snv_affected_cna_Y_file"), type="character", default=NULL,
            help="",
            dest="canopy_snv_affected_cna_Y_file"),

    make_option(c("-g","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)



# create pdf
pdf(opt$figure_file, width=10, height=10)


# calculate patient_id
patient_id <- strsplit(opt$figure_file, "/")[[1]]
patient_id <- strsplit(patient_id[length(patient_id)], "_")[[1]]
patient_id <- strsplit(patient_id[1], "-")[[1]][2]

# load dfs
X = as.matrix(read.csv(opt$canopy_total_depth_X_file, sep='\t', row.names=1))
R = as.matrix(read.csv(opt$canopy_mutant_allele_depth_R_file, sep='\t', row.names=1))

# ##################
# # clustering mutations first time to determine number of clusters
# ##################
# num_cluster=2:9 # Range of number of clusters to run
num_run=10 # How many EM runs per clustering step for each mutation cluster wave

# # cluster mutations
# canopy_cluster = canopy.cluster(R = R, X = X, 
# num_cluster = num_cluster, num_run = num_run)


# # BIC to determine the optimal number of mutation clusters
# bic_output=canopy_cluster$bic_output
# plot(num_cluster, bic_output, xlab='Number of mutation clsuters', ylab='BIC', type='b', main='BIC for model selection')
# abline(v=num_cluster[which.max(bic_output)],lty=2)



##################
# clustering mutations with know number of clusters
##################

# recluster using know number of clusters 
# MANUALLY DETERMINED CLUSTERS from BIC visual
clusters_list = list(4)
color_list = list(c('dodgerblue', 'firebrick', 'darkorange', 'darkcyan'))
shape_list = list(c(0,1,3,4))
names(clusters_list) = c('RLGS1')
names(color_list) = c('RLGS1')

new_num_cluster = clusters_list[[patient_id]]
pchc = shape_list[[patient_id]]
colc = color_list[[patient_id]]

canopy_cluster = canopy.cluster(R = R, X = X, 
num_cluster = new_num_cluster, num_run = num_run)


# Visualization of clustering results
Mu=canopy_cluster$Mu # VAF centroid for each cluster
Tau=canopy_cluster$Tau  # Prior for mutation cluster, with a K+1 component
sna_cluster=canopy_cluster$sna_cluster # cluster identity for each mutation


plot((R/X)[,1],(R/X)[,2],xlab='Sample1 VAF',ylab='Sample2 VAF',col=colc[sna_cluster],pch=pchc[sna_cluster],ylim=c(0,max(R/X)),xlim=c(0,max(R/X)))









# # results from clustering
# bic_output = canopy_cluster$bic_output
# Mu = canopy_cluster$Mu
# Tau = canopy_cluster$Tau
# sna_cluster = canopy_cluster$sna_cluster



# ##################
# # MCMC sampling
# ##################
# K = 2:8 #number of subclones
# numchain = 20 # number of chains with random initiations


# sampchain = canopy.sample(R = R, X = X, WM = WM, Wm = Wm, epsilonM = epsilonM,
# epsilonm = epsilonm, C = C, Y = Y, K = K, numchain = numchain,
# max.simrun = 50000, min.simrun = 10000,
# writeskip = 200, projectname = projectname, cell.line = TRUE,
# plot.likelihood = TRUE)






# # load the rest of the dfs
# WM_df = read.csv(opt$canopy_major_cna_WM_file, sep='\t', row.names=1)
# Wm_df = read.csv(opt$canopy_minor_cna_Wm_file, sep='\t', row.names=1)

# C_df = read.csv(opt$canopy_overlapping_cna_C_file, sep='\t', row.names=1)
# Y_df = read.csv(opt$canopy_snv_affected_cna_Y_file, sep='\t', row.names=1)



dev.off()









