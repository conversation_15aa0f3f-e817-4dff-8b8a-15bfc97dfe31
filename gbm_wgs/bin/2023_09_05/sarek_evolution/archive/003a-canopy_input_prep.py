# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''

def fill_nas_with_mosdepth(total_depth_df, samples, results_dir):
    # Extract the chromosome, position, ref, and alt from mutation_id
    total_depth_df['chr'] = total_depth_df.index.str.split('_').str[0]
    total_depth_df['position'] = total_depth_df.index.str.split('_').str[1].astype(int)
    total_depth_df['ref'] = total_depth_df.index.str.split('_').str[2]
    total_depth_df['alt'] = total_depth_df.index.str.split('_').str[3]

    # Iterate through each sample to fill missing values
    for sample in samples:
        # Load the mosdepth file
        mosdepth_file = f'{results_dir}/sarek_wgs/{sample}/reports/mosdepth/{sample}-{sample.split("-")[1]}/{sample}-{sample.split("-")[1]}.recal.regions.bed.gz'
        
        mosdepth_df = pd.read_csv(mosdepth_file, sep='\t', compression='gzip', header=None)
        mosdepth_df.columns = ['chr', 'start', 'stop', 'depth']
        
        # Subset total_depth_df to get rows where the coverage is NaN for the current sample
        missing_positions_df = total_depth_df[total_depth_df[sample].isna()]
            
        # Loop through each missing position
        for idx, row in missing_positions_df.iterrows():
            chr_ = row['chr']
            position = row['position']
            
            # Filter mosdepth data for the current position and chromosome where position falls within the range [start, stop]
            mosdepth_row = mosdepth_df[(mosdepth_df['chr'] == chr_) & (mosdepth_df['start'] <= position) & (mosdepth_df['stop'] > position)]
            
            # If there's coverage data available for this position, fill it in the total_depth_df
            if not mosdepth_row.empty:
                depth = round(mosdepth_row['depth'].values[0])
                
                # Update the value in the total_depth_df
                total_depth_df.at[idx, sample] = depth

    # Drop the temporary columns used for matching
    total_depth_df = total_depth_df.drop(columns=['chr', 'position', 'ref', 'alt'])

    return total_depth_df


def get_cn_at_position(df, position, chrom):
    # get cna at position if the position is profiled
    mask = (df['chr'] == chrom) & (df['start'].astype('int') <= int(position)) & (df['end'].astype('int') >= int(position))
    region = df.loc[mask, 'region']

    # if the position does not exist, assume 1,1
    if np.sum(mask) == 0:
        region = 'non-cna_region'
    else:
        region = region.iloc[0]
    
    return region


def extract_counts(ref, alt, format_field, tumor):
    # extract counts from tumor
    ref_counts = 0
    alt_counts = 0

    # mutect2 and freebayes
    if 'AD' in format_field:
        for i, f in enumerate(format_field.split(':')):
            if f == 'AD':
                ref_counts, alt_counts = tumor.split(':')[i].split(',')

    # strelka
    elif 'AU' in format_field:
        # Determine index of each nucleotide count field based on the reference and alternate alleles
        allele_field_map = {
            'A': 'AU',
            'C': 'CU',
            'G': 'GU',
            'T': 'TU'
        }

        ref_field = allele_field_map[ref]
        alt_field = allele_field_map[alt]

        # Identify which index corresponds to the reference and alternate fields
        ref_index = format_field.split(':').index(ref_field)
        alt_index = format_field.split(':').index(alt_field)

        # Extract the reference and alternate counts
        ref_counts = tumor.split(':')[ref_index].split(",")[0]
        alt_counts = tumor.split(':')[alt_index].split(",")[0]

    # strelka indels
    elif 'TIR' in format_field:
        #FORMAT=<ID=TOR,Number=2,Type=Integer,Description="Other reads (weak support or insufficient indel breakpoint overlap) for tiers 1,2">
        #FORMAT=<ID=TIR,Number=2,Type=Integer,Description="Reads strongly supporting indel allele for tiers 1,2">
        ref_counts = tumor.split(':')[format_field.split(':').index('TOR')].split(',')[0]
        alt_counts = tumor.split(':')[format_field.split(':').index('TIR')].split(',')[0]

    else:
        print("ERROR: Unknown format")
        print(format_field)
        print(tumor)

    return ref_counts, alt_counts


def generate_record(sample, vcf_df, cna_df):    
    # process each line in vcf
    records = []
    
    for _, row in vcf_df.iterrows():
        # major and minor cna at position
        cna_region = get_cn_at_position(cna_df, row['POS'], row['CHROM'])

        # extract coverage information
        ref_counts, alt_counts = extract_counts(row['REF'], row['ALT'], row['FORMAT'], row['TUMOR'])

        records.append([row['variant_id'], sample, ref_counts, (ref_counts + alt_counts), cna_region])

    return records


def process_cna_df(cna_df, patient):
    # adjust chromosome names
    cna_df['chr1'] = 'chr' + cna_df['chr1']

    # create a df to define overlapping regions
    cna_overlap_df = []
    cna_df_for_snvs = []

    # major and minor allele dfs
    major_df = []
    minor_df = []

    # create a dictionary to count the number of overlapping regions based on chr1
    cna_overlap_dict = {cna_df['chr1'].unique()[i]: 0 for i in range(len(cna_df['chr1'].unique()))}

    # iterate through rows to define general cna regions
    for _, row in cna_df.iterrows():
        # update the count of overlapping regions
        cna_overlap_dict[row['chr1']] += 1

        if row['cna1_id'] == row['cna2_id']:
            # there is no overlapping cna for this region, it's just a single CNA
            region = row['chr1'] + '_' + str(cna_overlap_dict[row['chr1']])

            # add to cna_overlap_df
            cna_overlap_df.append([region, region])

        else:
            # there is an overlapping cna for this region
            region = row['chr1'] + '_' + str(cna_overlap_dict[row['chr1']])
            region_primary = region + '_primary'
            region_recurrent = region + '_recurrent'

            # add to cna_overlap_df
            cna_overlap_df.append([region, region_primary])
            cna_overlap_df.append([region, region_recurrent])

        # add major and minor alleles to dfs
        major_primary = [region, row['nMajor_1'], f'{patient}-primary']
        minor_primary = [region, row['nMinor_1'], f'{patient}-primary']
        major_recurrent = [region, row['nMajor_2'], f'{patient}-recurrent']
        minor_recurrent = [region, row['nMinor_2'], f'{patient}-recurrent']

        major_df.append(major_primary)
        major_df.append(major_recurrent)
        minor_df.append(minor_primary)
        minor_df.append(minor_recurrent)

        # add primary and recurrent to cna_df_for_snvs
        primary = [row['chr1'], row['start1'], row['end1'], row['nMajor_1'], row['nMinor_1'], row['sample1_id'], row['cna1_id'], f'{patient}-primary', region]
        recurrent = [row['chr2'], row['start2'], row['end2'], row['nMajor_2'], row['nMinor_2'], row['sample2_id'], row['cna2_id'], f'{patient}-recurrent', region]

        cna_df_for_snvs.append(primary)
        cna_df_for_snvs.append(recurrent)

    # create df and pivot to define overlapping regions
    cna_overlap_df = pd.DataFrame(cna_overlap_df, columns=['region', 'region_overlap'])
    cna_overlap_df = cna_overlap_df.pivot_table(index=['region'], columns='region_overlap', aggfunc=lambda x: 1, fill_value=0)

    # create a df for snvs
    cna_df_for_snvs = pd.DataFrame(cna_df_for_snvs, columns=['chr', 'start', 'end', 'nMajor', 'nMinor', 'sample_id', 'cna_id', 'sample', 'region'])

    # create major and minor allele dfs
    major_df = pd.DataFrame(major_df, columns=['region', 'nMajor', 'sample'])
    minor_df = pd.DataFrame(minor_df, columns=['region', 'nMinor', 'sample'])

    # pivot major and minor dfs
    major_df = major_df.pivot_table(index='region', columns='sample', values='nMajor')
    minor_df = minor_df.pivot_table(index='region', columns='sample', values='nMinor')

    return cna_df_for_snvs, cna_overlap_df, major_df, minor_df


def canopy_preparation(patient_id, vcf_file, cna_file, results_dir):
    # load file
    cna_df = pd.read_csv(cna_file, sep='\t', header=None)
    cna_df.columns = ['chr1', 'start1', 'end1', 'nMajor_1', 'nMinor_1', 'sample1_id', 'cna1_id', 'chr2', 'start2', 'end2', 'nMajor_2', 'nMinor_2', 'sample2_id', 'cna2_id']

    vcf_df = pd.read_csv(vcf_file, sep='\t')

    # filter cna df to only CNAs
    cna_df, cna_overlap_df, major_df, minor_df = process_cna_df(cna_df, patient_id)

    
    # combine results
    records = []
    
    # the samples associated with this patient_id
    samples = np.unique(vcf_df['sample'][vcf_df['sample'].str.split("-").str[0] == patient_id])

    
    for sample in samples:
        # subset dfs to sample
        sub_vcf_df = vcf_df[vcf_df['sample'] == sample]
        cna_df = cna_df[cna_df['sample'] == sample]

        # add sample processed data
        records.extend(generate_record(sample, sub_vcf_df, cna_df))

    # create dataframe
    res_df = pd.DataFrame(records, columns=['mutation_id', 'sample_id', 'ref_counts', 'total_counts', 'region'])

    # Specify the desired data types
    dtypes = {'mutation_id': str, 'sample_id': str, 'ref_counts': int, 'total_counts': int, 'region': str}
    res_df = res_df.astype(dtypes)


    # create mutant allele depth df
    mutant_allele_df = res_df.pivot_table(index='mutation_id', columns='sample_id', values='ref_counts', fill_value=0)
    
    # Create a total depth df
    total_depth_df = res_df.pivot_table(index='mutation_id', columns='sample_id', values='total_counts', fill_value=np.nan)

    # matrix of snvs affected by cnas
    cna_snv_df = res_df.pivot_table(index='mutation_id', columns='region', aggfunc='size', fill_value=0)
    
    # Fill in missing values in the total depth df
    total_depth_df = fill_nas_with_mosdepth(total_depth_df, samples, results_dir)
    

    return mutant_allele_df, total_depth_df, major_df, minor_df, cna_overlap_df, cna_snv_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # determine patient id from canopy input file
    patient_id = canopy_mutant_allele_depth_R_file.split("/")[-1].split('-')[-1].split(".")[0].split("_")[0]
    print(patient_id)

    # process and combine vcfs
    mutant_allele_df, total_depth_df, major_df, minor_df, cna_overlap_df, cna_snv_df = canopy_preparation(patient_id, input_vcf, cna_file, results_dir)

    # save to files
    mutant_allele_df.to_csv(canopy_mutant_allele_depth_R_file, sep='\t')
    total_depth_df.to_csv(canopy_total_depth_X_file, sep='\t')

    major_df.to_csv(canopy_major_cna_WM_file, sep='\t')
    minor_df.to_csv(canopy_minor_cna_Wm_file, sep='\t')

    cna_overlap_df.to_csv(canopy_overlapping_cna_C_file, sep='\t')
    cna_snv_df.to_csv(canopy_snv_affected_cna_Y_file, sep='\t')


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["input_vcf=", "cna_file=", "results_dir=", "canopy_total_depth_X_file=", "canopy_mutant_allele_depth_R_file=", "canopy_major_cna_WM_file=", "canopy_minor_cna_Wm_file=", "canopy_overlapping_cna_C_file=", "canopy_snv_affected_cna_Y_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--input_vcf"):
            input_vcf = str(arg)
        if opt in ("--cna_file"):
            cna_file = str(arg)
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--canopy_total_depth_X_file"):
            canopy_total_depth_X_file = str(arg)
        if opt in ("--canopy_mutant_allele_depth_R_file"):
            canopy_mutant_allele_depth_R_file = str(arg)

        if opt in ("--canopy_major_cna_WM_file"):
            canopy_major_cna_WM_file = str(arg)
        if opt in ("--canopy_minor_cna_Wm_file"):
            canopy_minor_cna_Wm_file = str(arg)

        if opt in ("--canopy_overlapping_cna_C_file"):
            canopy_overlapping_cna_C_file = str(arg)
        if opt in ("--canopy_snv_affected_cna_Y_file"):
            canopy_snv_affected_cna_Y_file = str(arg)

    main()



