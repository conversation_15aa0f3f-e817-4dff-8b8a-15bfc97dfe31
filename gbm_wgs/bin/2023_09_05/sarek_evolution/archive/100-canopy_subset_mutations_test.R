# Alec <PERSON>
library(optparse)
library(Canopy)
source("/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/sarek_evolution/canopy_cluster.R")


# options list for parser options
option_list <- list(
    make_option(c("-a","--canopy_total_depth_X_file"), type="character", default=NULL,
            help="",
            dest="canopy_total_depth_X_file"),
    make_option(c("-b","--canopy_mutant_allele_depth_R_file"), type="character", default=NULL,
            help="",
            dest="canopy_mutant_allele_depth_R_file"),

    make_option(c("-c","--canopy_major_cna_WM_file"), type="character", default=NULL,
            help="",
            dest="canopy_major_cna_WM_file"),
    make_option(c("-d","--canopy_minor_cna_Wm_file"), type="character", default=NULL,
            help="",
            dest="canopy_minor_cna_Wm_file"),

    make_option(c("-e","--canopy_overlapping_cna_C_file"), type="character", default=NULL,
            help="",
            dest="canopy_overlapping_cna_C_file"),
    make_option(c("-f","--canopy_snv_affected_cna_Y_file"), type="character", default=NULL,
            help="",
            dest="canopy_snv_affected_cna_Y_file"),

    make_option(c("-g","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file"),
    make_option(c("-i","--tree_figure_file"), type="character", default=NULL,
            help="",
            dest="tree_figure_file"),
    make_option(c("-j","--figure_directory"), type="character", default=NULL,
            help="",
            dest="figure_directory"),
    
    make_option(c("-k","--r_data_file"), type="character", default=NULL,
            help="",
            dest="r_data_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)



# set working dir
setwd(opt$figure_directory)


# calculate patient_id
patient_id <- strsplit(opt$figure_file, "/")[[1]]
patient_id <- strsplit(patient_id[length(patient_id)], "_")[[1]]
patient_id <- strsplit(patient_id[1], "-")[[1]][2]

# load dfs
X = as.matrix(read.csv(opt$canopy_total_depth_X_file, sep='\t', row.names=1)) ## mutant allele read depth (for SNAs)
R = as.matrix(read.csv(opt$canopy_mutant_allele_depth_R_file, sep='\t', row.names=1)) ## total depth (for SNAs)

# Create a mask that keeps only rows where X does not have a 0
mask <- apply(X, 1, function(row) all(row != 0))

# Apply the mask to both R and X DataFrames
R <- R[mask, ]
X <- X[mask, ]


set.seed(123)


# create pdf
pdf(opt$figure_file, width=10, height=10)

# ##################
# # clustering mutations first time to determine number of clusters
# ##################
# num_cluster=2:15 # Range of number of clusters to run
# num_run=10 # How many EM runs per clustering step for each mutation cluster wave

# # cluster mutations
# canopy.cluster = canopy.cluster(R = R, X = X, 
# num_cluster = num_cluster, num_run = num_run)


# # BIC to determine the optimal number of mutation clusters
# bic_output=canopy.cluster$bic_output
# bic_error=canopy.cluster$variance
# plot(num_cluster, bic_output, xlab='Number of mutation clusters', ylab='BIC', type='b', main='BIC for model selection')
# abline(v=num_cluster[which.max(bic_output)],lty=2)

# # Add error bars
# arrows(num_cluster, bic_output - bic_error, num_cluster, bic_output + bic_error, 
#        angle = 90, code = 3, length = 0.1) 

##################
# visualize clustering results
##################


# color and shape for each cluster
colc <- c("#FF0000", # red
  "#0000FF", # blue
  "#008000", # green
  "#FFFF00", # yellow
  "#FFA500", # orange
  "#800080", # purple
  "#A52A2A", # brown
  "#FFC0CB", # pink
  "#00FFFF", # cyan
  "#FF00FF", # magenta
  "#008080", # teal
  "#FFD700", # gold
  "#006400", # darkgreen
  "#000080", # navy
  "#808080"  # gray
)
              
pchc = c(1:15)

# # Visualization of clustering results
# Mu=canopy.cluster$Mu # VAF centroid for each cluster
# Tau=canopy.cluster$Tau  # Prior for mutation cluster, with a K+1 component
# sna_cluster=canopy.cluster$sna_cluster # cluster identity for each mutation

# plot((R/X)[,1],(R/X)[,2],xlab='Sample1 VAF',ylab='Sample2 VAF',col=colc[sna_cluster],pch=pchc[sna_cluster],ylim=c(0,max(R/X)),xlim=c(0,max(R/X)))


# dev.off()


##################
# cluster while removing noise and visualize
##################

# MANUALLY DETERMINED CLUSTERS from BIC visual
clusters_list = setNames(list(8, 6, 5, 6, 8, 9, 6, 6, 7, 6, 8), c('RLGS1', 'RLGS2', 'RLGS3', 'RLGS4', 'RLGS5', 'RLGS6', 'RLGS7', 'RLGS9', 'RLGS10', 'RLGS11', 'RLGS12'))
# num_cluster = clusters_list[[patient_id]]
num_cluster = 8

num_run=10 # How many EM runs per clustering step for each mutation cluster wave
# Tau_Kplus1=0.05
# Mu.init=cbind(c(0.01,0.15,0.25,0.45),c(0.2,0.2,0.01,0.2))

canopy.cluster=canopy.cluster(R = R,
                              X = X,
                              num_cluster = num_cluster,
                              num_run = num_run)


# Visualization of clustering result
Mu=canopy.cluster$Mu # VAF centroid for each cluster
Tau=canopy.cluster$Tau  # Prior for mutation cluster, with a K+1 component
sna_cluster=canopy.cluster$sna_cluster # cluster identity for each mutation

plot((R/X)[,1],(R/X)[,2],xlab='Sample1 VAF',ylab='Sample2 VAF',col=colc[sna_cluster],pch=pchc[sna_cluster],ylim=c(0,max(R/X)),xlim=c(0,max(R/X)))

table(sna_cluster) # the 5th cluster corresponds to the noise component

# mask for the noise cluster
mask = sna_cluster <= num_cluster

R=R[mask,] # exclude mutations in the noise cluster
X=X[mask,]
sna_cluster=sna_cluster[mask]

R.cluster=round(Mu*100)  # Generate pseudo-SNAs correponding to each cluster. 
X.cluster=pmax(R.cluster,100)   # Total depth is set at 100 here but can be obtained as median across mutations in the cluster.
rownames(R.cluster)=rownames(X.cluster)=paste('SNA.cluster',1:num_cluster,sep='')



dev.off()

print("Mutation clustering complete")



##################
# MCMC sampling
##################

projectname = patient_id ## name of project

# load the rest of the dfs
WM = as.matrix(read.csv(opt$canopy_major_cna_WM_file, sep='\t', row.names=1))
Wm = as.matrix(read.csv(opt$canopy_minor_cna_Wm_file, sep='\t', row.names=1))

epsilonM = 0.01 ## standard deviation of WM, pre-fixed here
epsilonm = 0.01 ## standard deviation of Wm, pre-fixed here

## whether CNA regions harbor specific CNAs (only needed for overlapping CNAs)
C = as.matrix(read.csv(opt$canopy_overlapping_cna_C_file, sep='\t', row.names=1))
Y = as.matrix(read.csv(opt$canopy_snv_affected_cna_Y_file, sep='\t', row.names=1))

# mask for the noise cluster
Y = Y[mask,]


K_by_sample = setNames(list(c(7:9), c(5:7), c(6:8), 
                            c(5:7), c(7:9), c(8:10), 
                            c(5:7), c(5:7), c(6:8), 
                            c(5:7), c(7:9)), 
                            c('RLGS1', 'RLGS2', 'RLGS3', 
                              'RLGS4', 'RLGS5', 'RLGS6', 
                              'RLGS7', 'RLGS9', 'RLGS10', 
                              'RLGS11', 'RLGS12'))
                       
# K_by_sample = setNames(list(c(8, 6, 5, 
#                             6, 8, 9, 
#                             6, 6, 7, 
#                             6, 8)), c('RLGS1', 'RLGS2', 'RLGS3', 'RLGS4', 'RLGS5', 'RLGS6', 'RLGS7', 'RLGS9', 'RLGS10', 'RLGS11', 'RLGS12'))


# number of subclones
K = K_by_sample[[patient_id]]
numchain = 10 # number of chains with random initiations



sampchain = canopy.sample.cluster(R = R, X = X, sna_cluster = sna_cluster, 
                          WM = WM, Wm = Wm, epsilonM = epsilonM, 
                          epsilonm = epsilonm, C = C, Y = Y, K = K, 
                          numchain = numchain, max.simrun = 5000,
                          min.simrun = 500, writeskip = 50,
                          projectname = projectname, cell.line = TRUE,
                          plot.likelihood = TRUE)


# sampchain = canopy.sample.cluster(R = R, X = X, sna_cluster = sna_cluster, 
#                           WM = WM, Wm = Wm, epsilonM = epsilonM, 
#                           epsilonm = epsilonm, C = C, Y = Y, K = K, 
#                           numchain = numchain, max.simrun = 100000,
#                           min.simrun = 20000, writeskip = 200,
#                           projectname = projectname, cell.line = TRUE,
#                           plot.likelihood = TRUE)



save.image(file = opt$r_data_file, compress ='xz')

print("MCMC complete")

##################
# BIC for model selection
##################

burnin = 15

thin = 3 # If there is error in the bic and canopy.post step below, make sure burnin and thinning parameters are wisely selected so that there are posterior trees left.

bic = canopy.BIC(sampchain = sampchain, projectname = projectname, K = K, numchain = numchain, burnin = burnin, thin = thin, pdf = FALSE)

optK = K[which.max(bic)]

save.image(file = opt$r_data_file, compress ='xz')

print("BIC complete")

##################
# posterior tree evaluation
##################

post = canopy.post(sampchain = sampchain, projectname = projectname, K = K,
                   numchain = numchain, burnin = burnin, thin = thin, 
                   optK = optK, post.config.cutoff = 0.05)

samptreethin = post[[1]]   # list of all post-burnin and thinning trees
samptreethin.lik = post[[2]]   # likelihoods of trees in samptree
config = post[[3]]
config.summary = post[[4]]

print(config.summary)

save.image(file = opt$r_data_file, compress ='xz')

print("Posterior tree complete")


##################
# Tree output and plot
##################

# choose the configuration with the highest posterior likelihood
config.i = config.summary[which.max(config.summary[,3]),1]
cat('Configuration', config.i, 'has the highest posterior likelihood.\n')
output.tree = canopy.output(post, config.i, C=NULL)

canopy.plottree(output.tree, pdf = TRUE, pdf.name = opt$tree_figure_file)


print("Printing tree complete")









