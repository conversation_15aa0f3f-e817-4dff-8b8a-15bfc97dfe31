# Alec <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''


def load_and_combine_dfs(file_list):
    # load each df and add to res_df
    res_df = []

    for file in file_list:
        df = pd.read_csv(file, sep='\t')

        # add data source
        patient = file.split('/')[-1].split('-')[-1].split(".")[0]

        # add data source to df
        df['patient'] = patient

        # add chromosome and position columns
        df['chr'] = df['mutation_id'].str.split('_').str[0]
        df['pos'] = df['mutation_id'].str.split('_').str[1]

        res_df.append(df)

    # combine dfs
    res_df = pd.concat(res_df)

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # list of files
    regex = f'{results_dir}/analysis_sarek/pyclone/output/pyclone_vi_results-*tsv'
    files_list = glob.glob(regex)

    # process and combine all dfs
    df = load_and_combine_dfs(files_list)

    # save to files
    df.to_csv(combined_pyclone_results_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "combined_pyclone_results_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--combined_pyclone_results_file"):
            combined_pyclone_results_file = str(arg)
        

    main()




