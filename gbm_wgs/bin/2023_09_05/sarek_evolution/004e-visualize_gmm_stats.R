# Alec <PERSON>
library(optparse)
library(forcats)
library(ggplot2)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt sample
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



plot_stats = function(sample, stats_df){

# subset to sample
input_df = stats_df[stats_df$sample == sample,]

# plot BIC
p = ggplot(input_df, aes(x=n_components, y=-(bic))) + plot_theme() +
geom_point(pch=21, size=3) +

# add standard deviation bars from value +/- "bic_standard_deviation"
geom_errorbar(aes(ymin=-(bic+bic_standard_deviation), ymax=-(bic-bic_standard_deviation)), width=0.2) +

# add vertical line at max value
geom_vline(xintercept = input_df$n_components[which.min(input_df$bic)], linetype="dashed") +

# set x axis to be 1:10 whole
scale_x_continuous(breaks = seq(1, 10, 1)) +
    
# hide legend
theme(legend.position="none") +

ggtitle(sample) +
xlab("GMM n_components") + ylab('BIC (negative)')


print(p)


return ()
}


sort_patients = function(df){
# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# order samples
sorted_vector <- unique(df$sample)[order(sapply(unique(df$sample), extract_numeric))]
df$sample = factor(df$sample, levels = sorted_vector)

return(df)
}


pdf(opt$figure_file)

# load df
stats_df = read.csv(opt$figure_stats_file, sep='\t')

# sort patients
stats_df = sort_patients(stats_df)

# visualize stats
lapply(levels(stats_df$sample), plot_stats, stats_df)


dev.off()


print(opt$figure_file)






