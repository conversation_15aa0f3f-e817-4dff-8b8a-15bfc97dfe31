# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def separate_vcf_into_sample_files(rlgs_mutation_snv_file, output_dir):
    # load df
    vcf_df = pd.read_csv(rlgs_mutation_snv_file, sep='\t')

    # for each sample, subset to and save to new file
    for sample in vcf_df['sample'].unique():
        sample_df = vcf_df[vcf_df['sample'] == sample]

        # subset to the first 11 columns
        sample_df = sample_df.iloc[:, :11]

        # define new file name
        new_file = os.path.join(output_dir, f"{sample}-consensus.tsv")
        sample_df.to_csv(new_file, sep='\t', index=False)



def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # separate vcf into each sample
    separate_vcf_into_sample_files(rlgs_mutation_snv_file, output_dir)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["rlgs_mutation_snv_file=", "output_dir="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--rlgs_mutation_snv_file"):
            rlgs_mutation_snv_file = str(arg)

        if opt in ("--output_dir"):
            output_dir = str(arg)

            
    main()




