# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def process_df(sigprofiler_assignment_file, min_fraction=0.05):
    # load df
    df = pd.read_csv(sigprofiler_assignment_file, sep='\t', index_col=0)

    # remove artifact signatures
    artifact_signatures = ["SBS27", "SBS43", "SBS45", "SBS46", "SBS47", "SBS48", "SBS49", "SBS50", "SBS51", "SBS52", "SBS53", "SBS54", "SBS55", "SBS56", "SBS57", "SBS58", "SBS59", "SBS60", "SBS95"]
    df = df.loc[:, ~df.columns.isin(artifact_signatures)]

    # Calculate the sum of each row (excluding the 'Samples' column)
    row_sums = df.sum(axis=1)
    
    # Divide each value in the DataFrame (excluding the 'Samples' column) by the sum of its row
    fraction_df = df.div(row_sums, axis=0)

    # # if the fraction is less than 5%, set it to 0 and recalculate fraction
    # fraction_df = fraction_df.map(lambda x: 0 if x < min_fraction else x)
    # row_sums = fraction_df.sum(axis=1)
    # fraction_df = fraction_df.div(row_sums, axis=0)

    # calcualte top 10 signatures, set rest to "other"
    top_signatures = fraction_df.sum(axis=0).sort_values(ascending=False).index[:10]
    other_signatures = fraction_df.columns[~fraction_df.columns.isin(top_signatures)]

    # combine the "Other" columns
    fraction_df['Other'] = fraction_df[other_signatures].sum(axis=1)

    # drop other signatures
    fraction_df = fraction_df.drop(columns=other_signatures)
    

    # add sample column
    fraction_df['Samples'] = df.index.to_numpy()
    
    # Melt the DataFrame to reshape it to have three columns: 'Samples', 'SBS signature', and 'Fraction'
    melted_df = pd.melt(fraction_df, id_vars=['Samples'], var_name='SBS_signature', value_name='Fraction')

    # remove rows with 0 fraction
    melted_df = melted_df[melted_df['Fraction'] > 0]

    # add sample number
    melted_df['sample_number'] = melted_df['Samples'].str.split("_").str[0]
    
    # Calculate the total contribution of each sample to each signature
    signature_totals = melted_df.groupby('SBS_signature')['Fraction'].sum().reset_index(name='Total_Signature_Fraction')
    merged_df = melted_df.merge(signature_totals, on='SBS_signature')

    # Calculate the contribution fraction of each sample to the total signature fraction
    merged_df['Contribution_Fraction'] = merged_df['Fraction'] / merged_df['Total_Signature_Fraction']

    # Create a contributions DataFrame
    contributions_df = merged_df[['sample_number', 'SBS_signature', 'Contribution_Fraction']].copy()

    return melted_df, contributions_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load dfs, process, and melt
    df, stats_df = process_df(sigprofiler_assignment_file)

    # save to files
    df.to_csv(figure_data_file, sep='\t', index=False)
    stats_df.to_csv(figure_stats_file, sep='\t', index=False)

    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["sigprofiler_assignment_file=", "figure_data_file=", "figure_stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--sigprofiler_assignment_file"):
            sigprofiler_assignment_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)

    main()




