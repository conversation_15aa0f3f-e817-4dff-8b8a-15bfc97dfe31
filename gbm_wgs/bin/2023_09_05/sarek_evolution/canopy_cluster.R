# Alec <PERSON>

canopy.cluster=function(R, X, num_cluster, num_run, Mu.init = NULL,
                        Tau_Kplus1 = NULL){
  if(is.null(Tau_Kplus1)){
    Tau_Kplus1=0    # proportion of noise, uniformly distributed between 0 and 1
  }
  
  #remove rows with zero or NA reference alleles for any sample
  zeroNARef = apply(X, 1, function(x) any(x==0 | is.na(x)))
  if (any(zeroNARef)){
    cat("Removing variants with NA or zero total allele depth for any sample \n")
    R = R[!zeroNARef, ]
    X = X[!zeroNARef, ]
  }
  
  VAF=R/X
  
  s=nrow(R)
  r=pmax(R,1);x=pmax(X,1) # for log()
  Mu_output=Tau_output=pGrank_output=bic_output=bic_variance_ouput=vector('list',length(num_cluster))
  for(K in num_cluster){
    cat('Running EM with',K,'clusters...\t')
    Mu_run=Tau_run=pGrank_run=bic_run=vector('list',num_run)
    for(run in 1:num_run){
      cat(run,'  ')
      bic.temp=0
      bic_variance=0
      Tau=rep(NA,K+1)
      Tau[K+1]=Tau_Kplus1 
      Tau[1:K]=(1-Tau_Kplus1)/K
      
      if(K==1){
        Mu=t(as.matrix(apply(R/X,2,mean)))
      } else{
        if (run==1 & (!is.null(Mu.init))){
          Mu=Mu.init
        } else if(run<=(num_run/2)){
          # using hierarchical clustering to find initial values of centers
          VAF.pheat=pheatmap(VAF,cluster_rows = TRUE,cluster_cols = FALSE,kmeans_k=K,silent=TRUE, clustering_distance_rows = "euclidean")
          Mu=pmax(VAF.pheat$kmeans$centers,0.001)
        } else{
          if(ncol(R)>1){
            VAF.pheat=pheatmap(VAF,cluster_rows = TRUE,cluster_cols = FALSE,kmeans_k=K,silent=TRUE, clustering_distance_rows = "correlation")
            Mu=pmax(VAF.pheat$kmeans$centers,0.001) 
          } else{
            VAF.pheat=pheatmap(VAF,cluster_rows = TRUE,cluster_cols = FALSE,kmeans_k=K,silent=TRUE, clustering_distance_rows = "euclidean")
            Mu=pmax(VAF.pheat$kmeans$centers,0.001)
          }
        }
      }
      diff=1
      numiters=1
      while( (diff>0.001 & !is.na(diff) & numiters <= 300) || numiters <= 30){ 
        numiters=numiters+1
        pG=canopy.cluster.Estep(Tau,Mu,r,x)
        curM=canopy.cluster.Mstep(pG,R,X,Tau_Kplus1)
        curTau=curM$Tau
        curMu=curM$Mu
        
        diff=max(max(abs(Tau-curTau)),max(abs(Mu-curMu)))
        Mu=curMu
        Tau=curTau
        #cat('Iteration:',numiters-1,'\t','diff =',diff,'\n')
      }
      if (!is.na(diff) & numiters < 300 ){
        dim(pG)
        pGrank=apply(pG,2,which.max)
        for (i in 1:s){
          if(pGrank[i]<=K){
            muk=Mu[pGrank[i],]
            for(j in 1:ncol(R)){
              bic.temp=bic.temp+log(Tau[pGrank[i]])+r[i,j]*log(muk[j])+(x[i,j]-r[i,j])*log(1-muk[j])
            }
          }
          if(pGrank[i]==(K+1)){
            for(j in 1:ncol(R)){
              bic.temp=bic.temp+log(Tau[pGrank[i]])+lbeta(r[i,j]+1,x[i,j]-r[i,j]+1)
            }
          }
        }
        bic.temp=2*bic.temp-3*(length(Tau)-2+length(Mu))*log(length(R)+length(X))
        Mu_run[[run]]=Mu
        Tau_run[[run]]=Tau
        pGrank_run[[run]]=pGrank
        bic_run[[run]]=bic.temp
      } else {
        bic_run[[run]] = NA
      }
    }
    cat('\n')
    if ( all(sapply(bic_run, is.na)) ) {
      Mu_output[[which(num_cluster==K)]]=NA
      Tau_output[[which(num_cluster==K)]]=NA
      pGrank_output[[which(num_cluster==K)]]=NA
      bic_output[[which(num_cluster==K)]]=NA
      bic_variance_ouput[[which(num_cluster==K)]]=NA
      cat ( "EM did not converge in any runs with", K , "clusters \n" )
    } else {
      Mu_output[[which(num_cluster==K)]]=Mu_run[[which.max(bic_run)]]
      Tau_output[[which(num_cluster==K)]]=Tau_run[[which.max(bic_run)]]
      pGrank_output[[which(num_cluster==K)]]=pGrank_run[[which.max(bic_run)]]
      bic_output[[which(num_cluster==K)]]=bic_run[[which.max(bic_run)]]
      bic_variance_ouput[[which(num_cluster==K)]]=sd(unlist(bic_run), na.rm = TRUE)
      nConverged = sum(!sapply(bic_run, is.na))
      cat ( "EM converged in", nConverged,  "out of", num_run  ,"runs with", K , "clusters \n" )
    }
  }
  bic_output=as.numeric(bic_output)
  Mu=round(Mu_output[[which.max(bic_output)]],3)
  Tau=round(Tau_output[[which.max(bic_output)]],3)
  pGrank=pGrank_output[[which.max(bic_output)]]
  sna_cluster=pGrank
  variance=unlist(bic_variance_ouput)
  return(list(bic_output=bic_output,Mu=Mu,Tau=Tau,sna_cluster=sna_cluster,variance=variance))
}




canopy.plottree = function(tree, pdf = NULL, pdf.name = NULL, txt = NULL, 
                           txt.name = NULL) {
    if (is.null(pdf)) {
        pdf = FALSE
    }
    if (is.null(txt)){
        txt = FALSE
    }
    if (pdf & is.null(pdf.name)) {
        stop("pdf.name has to be provided if pdf = TRUE!")
    }
    if (txt & is.null(txt.name)){
        stop("txt.name has to be provided if txt = TRUE")
    }
    if (!is.null(pdf.name)) {
        pdf.split = strsplit(pdf.name, "\\.")[[1]]
        if (length(pdf.split) < 2 | pdf.split[length(pdf.split)] != "pdf") {
            stop("pdf.name has to end with .pdf!")
        }
    }
    if (pdf) {
        pdf(file = pdf.name, height = 6, width = 6)
    }
    nf <- layout(matrix(c(1, 2, 3), 3, 1, byrow = TRUE), widths = c(3, 
        3, 3), heights = c(1.3, 1, 1), respect = TRUE)
    par(mar = c(1, 7, 1, 10))
    # plot tree
    K = ncol(tree$Z)
    plot(tree, label.offset = 0.1, type = "cladogram", direction = "d", 
        show.tip.label = FALSE)
    nodelabels()
    tiplabels()
    snaedge = rep(NA, nrow(tree$sna))
    for (k in 1:nrow(tree$sna)) {
        snaedge[k] = intersect(which(tree$edge[, 1] == tree$sna[k, 2]), 
            which(tree$edge[, 2] == tree$sna[k, 3]))
    }
    if(!is.null(tree$cna)){
        cnaedge = rep(NA, nrow(tree$cna))
        for (k in 1:nrow(tree$cna)) {
            cnaedge[k] = intersect(which(tree$edge[, 1] == tree$cna[k, 2]), 
                                   which(tree$edge[, 2] == tree$cna[k, 3]))
        }
    } else{
        cnaedge=NULL
    }
    edge.label = sort(unique(c(snaedge, cnaedge)))
    edgelabels(paste("mut", 1:length(edge.label), sep = ""), edge.label, 
        frame = "n", col = 2, cex = 1.2)
    tiplabels("Normal", 1, adj = c(0.2, 1.5), frame = "n", cex = 1.2, 
        col = 4)
    tiplabels(paste("Clone", 1:(K - 2), sep = ""), 2:(K - 1), adj = c(0.5, 
        1.5), frame = "n", cex = 1.2, col = 4)
    tiplabels(paste("Clone", (K - 1), sep = ""), K, adj = c(0.8, 1.5), 
        frame = "n", cex = 1.2, col = 4)
    # plot clonal frequencies
    par(mar = c(1, 7, 0.5, 9.5))
    P = tree$P
    image(1:nrow(P), 1:ncol(P), axes = FALSE, ylab = "", xlab = "", 
        P, breaks = 0:100/100, col = tim.colors(100))
    axis(4, at = 1:ncol(P), colnames(P), cex.axis = 1.2, las = 1, tick = FALSE)
    abline(h = seq(0.5, ncol(P) + 0.5, 1), v = seq(0.5, nrow(P) + 0.5, 
        1), col = "grey")
    for (i in 1:nrow(P)) {
        for (j in 1:ncol(P)) {
            txt.temp <- sprintf("%0.3f", P[i, j])
            if (P[i, j] <= 0.05 | P[i, j] >= 0.95) {
                text(i, j, txt.temp, cex = 0.7, col = "white")
            } else {
                text(i, j, txt.temp, cex = 0.7)
            }
        }
    }
    sna.name = rownames(tree$sna)
    cna.name = rownames(tree$cna)
    # plot mutations
    plot(c(0, 1), c(0, 1), ann = FALSE, bty = "n", type = "n", xaxt = "n", 
        yaxt = "n")
    txt.output=matrix(nrow=length(edge.label),ncol=1)
    for (i in 1:length(edge.label)) {
        txt.temp = paste("mut", i, ": ", paste(c(sna.name[which(snaedge == 
            edge.label[i])], cna.name[which(cnaedge == edge.label[i])]), 
            collapse = ", "), sep = "")
        text(x = 0, y = 0.95 - 0.1 * (i - 1), txt.temp, pos = 4, cex = 1.2)
        txt.output[i,1]=txt.temp
    }
    
    if (txt){
        write.table(txt.output, file = txt.name, col.names = FALSE,
                    row.names = FALSE, quote = FALSE, sep = '\t')
    }
    
    if (!is.null(pdf.name)) {
        text(x = 0.5, y = 0.1, pdf.split[1], font = 2, cex = 1.2)
    }
    if (pdf) {
        dev.off()
    }
    par(mfrow=c(1,1))
} 



canopy.plottree = function(tree, pdf = NULL, pdf.name = NULL, txt = NULL, 
                           txt.name = NULL) {
    if (is.null(pdf)) {
        pdf = FALSE
    }
    if (is.null(txt)){
        txt = FALSE
    }
    if (pdf & is.null(pdf.name)) {
        stop("pdf.name has to be provided if pdf = TRUE!")
    }
    if (txt & is.null(txt.name)){
        stop("txt.name has to be provided if txt = TRUE")
    }
    if (!is.null(pdf.name)) {
        pdf.split = strsplit(pdf.name, "\\.")[[1]]
    }
    if (pdf) {
        pdf(file = pdf.name, height = 6, width = 6)
    }
    nf <- layout(matrix(c(1, 2, 3), 3, 1, byrow = TRUE), widths = c(3, 
        3, 3), heights = c(1.3, 1, 1), respect = TRUE)
    par(mar = c(1, 7, 1, 10))
    # plot tree
    K = ncol(tree$Z)
    plot(tree, label.offset = 0.1, type = "cladogram", direction = "d", 
        show.tip.label = FALSE)
    nodelabels()
    tiplabels()
    snaedge = rep(NA, nrow(tree$sna))
    for (k in 1:nrow(tree$sna)) {
        snaedge[k] = intersect(which(tree$edge[, 1] == tree$sna[k, 2]), 
            which(tree$edge[, 2] == tree$sna[k, 3]))
    }
    if(!is.null(tree$cna)){
        cnaedge = rep(NA, nrow(tree$cna))
        for (k in 1:nrow(tree$cna)) {
            cnaedge[k] = intersect(which(tree$edge[, 1] == tree$cna[k, 2]), 
                                   which(tree$edge[, 2] == tree$cna[k, 3]))
        }
    } else{
        cnaedge=NULL
    }
    edge.label = sort(unique(c(snaedge, cnaedge)))
    edgelabels(paste("mut", 1:length(edge.label), sep = ""), edge.label, 
        frame = "n", col = 2, cex = 1.2)
    tiplabels("Normal", 1, adj = c(0.2, 1.5), frame = "n", cex = 1.2, 
        col = 4)
    tiplabels(paste("Clone", 1:(K - 2), sep = ""), 2:(K - 1), adj = c(0.5, 
        1.5), frame = "n", cex = 1.2, col = 4)
    tiplabels(paste("Clone", (K - 1), sep = ""), K, adj = c(0.8, 1.5), 
        frame = "n", cex = 1.2, col = 4)
    # plot clonal frequencies
    par(mar = c(1, 7, 0.5, 9.5))
    P = tree$P
    image(1:nrow(P), 1:ncol(P), axes = FALSE, ylab = "", xlab = "", 
        P, breaks = 0:100/100, col = tim.colors(100))
    axis(4, at = 1:ncol(P), colnames(P), cex.axis = 1.2, las = 1, tick = FALSE)
    abline(h = seq(0.5, ncol(P) + 0.5, 1), v = seq(0.5, nrow(P) + 0.5, 
        1), col = "grey")
    for (i in 1:nrow(P)) {
        for (j in 1:ncol(P)) {
            txt.temp <- sprintf("%0.3f", P[i, j])
            if (P[i, j] <= 0.05 | P[i, j] >= 0.95) {
                text(i, j, txt.temp, cex = 0.7, col = "white")
            } else {
                text(i, j, txt.temp, cex = 0.7)
            }
        }
    }
    sna.name = rownames(tree$sna)
    cna.name = rownames(tree$cna)
    
    if (!is.null(pdf.name)) {
        text(x = 0.5, y = 0.1, pdf.split[1], font = 2, cex = 1.2)
    }
    if (pdf) {
        dev.off()
    }
    par(mfrow=c(1,1))
} 

                    