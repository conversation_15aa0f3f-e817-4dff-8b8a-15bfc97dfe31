# <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from scipy.spatial.distance import jense<PERSON>hannon
import statsmodels.stats.multitest as multitest

help_message = '''
Failed
'''

def permutation_test(primary, recurrent, n_permutations=10000, random_state=None):
    rng = np.random.default_rng(random_state)
    observed_distance = jensenshannon(primary, recurrent)

    combined = np.concatenate([primary, recurrent])
    n = len(primary)
    count = 0
    for _ in range(n_permutations):
        rng.shuffle(combined)
        perm_primary = combined[:n]
        perm_recurrent = combined[n:]
        dist = jensenshannon(perm_primary, perm_recurrent)
        if dist >= observed_distance:
            count += 1

    p_value = count / n_permutations
    return observed_distance, p_value


def run_similarity_stats(signature_proportions_file):
    # load dfs
    df = pd.read_csv(signature_proportions_file, sep='\t')

    # Add patient and tumor type columns
    df['patient'] = df['sample_number'].str.split('-').str[0]
    df['tumor_type'] = df['sample_number'].str.split('-').str[1]

    res_df = []

    # For each patient, run stats
    for patient in df['patient'].unique():
        
        patient_df = df[df['patient'] == patient]

        # Separate into primary and recurrent
        primary_df = patient_df[patient_df['tumor_type'] == 'primary'].copy()
        recurrent_df = patient_df[patient_df['tumor_type'] == 'recurrent'].copy()

        if primary_df.empty or recurrent_df.empty:
            continue

        # Union of all signatures
        all_sigs = np.union1d(primary_df['SBS_signature'], recurrent_df['SBS_signature'])

        # Ensure all signatures are present in both dfs
        for sig in all_sigs:
            if sig not in primary_df['SBS_signature'].values:
                new_row = pd.DataFrame([{'SBS_signature': sig, 'Contribution_Fraction': 0, 'tumor_type': 'primary'}])
                primary_df = pd.concat([primary_df, new_row], ignore_index=True)
            if sig not in recurrent_df['SBS_signature'].values:
                new_row = pd.DataFrame([{'SBS_signature': sig, 'Contribution_Fraction': 0, 'tumor_type': 'recurrent'}])
                recurrent_df = pd.concat([recurrent_df, new_row], ignore_index=True)

        # Sort for alignment
        primary_df = primary_df.sort_values('SBS_signature')
        recurrent_df = recurrent_df.sort_values('SBS_signature')

        # Get contribution lists
        primary_vals = primary_df['Contribution_Fraction'].to_numpy()
        recurrent_vals = recurrent_df['Contribution_Fraction'].to_numpy()

        # Run permutation test
        dist, p = permutation_test(primary_vals, recurrent_vals, n_permutations=10000, random_state=42)

        # Append results
        res_df.append([patient, dist, p])

    # Create a new DataFrame from the results
    res_df = pd.DataFrame(res_df, columns=['patient', 'js_distance', 'p_value'])

    # run fdr
    res_df['fdr'] = multitest.fdrcorrection(res_df['p_value'])[1]

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # summarize maf df
    df = run_similarity_stats(signature_proportions_file)

    # save to files
    df.to_csv(stats_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["signature_proportions_file=", "stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--signature_proportions_file"):
            signature_proportions_file = str(arg)

        if opt in ("--stats_file"):
            stats_file = str(arg)

    main()




