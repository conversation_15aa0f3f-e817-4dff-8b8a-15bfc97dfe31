# Alec <PERSON>
library(optparse)
library(forcats)
library(ggplot2)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt sample
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




create_histogram = function(sample, raw_df){

# subset to sample
input_df = raw_df[raw_df$sample == sample,]

p = ggplot(input_df, aes(x = vaf)) + plot_theme() +
geom_histogram(bins=60) +

ggtitle(sample) +
xlab("") + ylab('VAF')


print(p)

return()
}



sort_patients = function(df){
# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# order samples
sorted_vector <- unique(df$sample)[order(sapply(unique(df$sample), extract_numeric))]
df$sample = factor(df$sample, levels = sorted_vector)

return(df)
}


pdf(opt$figure_file)

# load dfs
df = read.csv(opt$figure_data_file, sep='\t')

# sort patients
df = sort_patients(df)


# create boxplots for each sample
lapply(levels(df$sample), create_histogram, df)


dev.off()


print(opt$figure_file)






