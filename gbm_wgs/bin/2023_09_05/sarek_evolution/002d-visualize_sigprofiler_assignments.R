# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}






# plot venn diagram
barplot = function(input_df){


# my_colors <- c(
#     "#E41A1C", "#377EB8", "#4DAF4A", "#984EA3", "#FF7F00", 
#     "#FFFF33", "#A65628", "#F781BF", "#999999", "#8DD3C7", 
#     "#BEBADA", "#FB8072", "#80B1D3", "#FDB462", "#B3DE69", 
#     "#FCCDE5", "#D9D9D9", "#BC80BD", "#CCEBC5", "#FFED6F", 
#     "#FF5733", "#33FF57", "#3357FF"
# )

sbs_colors <- c(
  "SBS88" = "#E41A1C",  # Red
  "SBS12" = "#377EB8",  # Blue
  "SBS39" = "#4DAF4A",  # Green
  "SBS37" = "#984EA3",  # Purple
  "SBS46" = "#FF7F00",  # Orange
  "SBS40b" = "#FFFF33", # Yellow
  "SBS14" = "#A65628",  # Brown
  "SBS26" = "#F781BF",  # Pink
  "SBS21" = "#999999",  # Grey
  "SBS44" = "#66C2A5",  # Teal
  "SBS96" = "#FC8D62",  # Coral
  "SBS7b" = "#8DA0CB",  # Periwinkle
  "SBS58" = "#E78AC3",  # Pale Pink
  "SBS32" = "#A6D854",  # Lime Green
  "SBS89" = "#FFD92F",  # Gold
  "SBS57" = "#E5C494",  # Sand
  "SBS40a" = "#B3B3B3", # Light Grey
  "SBS90" = "#B15928",  # Dark Brown
  "SBS15" = "#CAB2D6",  # Lavender
  "SBS8" = "#FF69B4",   # Hot Pink
  "SBS1" = "#1B9E77",   # Forest Green
  "SBS5" = "#D95F02",    # Burnt Orange
  "Other" = "grey"
)

    
p = ggplot(input_df, aes(x = sample_number, y = Fraction, fill = SBS_signature)) + plot_theme() +
geom_bar(stat = "identity") +

scale_fill_manual(values = sbs_colors) +

ggtitle('') + ylab('Fraction of SNVs') + xlab('')

print(p)



sample_colors <- c(
  "RLGS1-primary" = "#E41A1C",   # Red
  "RLGS1-recurrent" = "#377EB8", # Blue
  "RLGS2-primary" = "#4DAF4A",   # Green
  "RLGS2-recurrent" = "#984EA3", # Purple
  "RLGS3-primary" = "#FF7F00",   # Orange
  "RLGS3-recurrent" = "#FFFF33", # Yellow
  "RLGS4-primary" = "#A65628",   # Brown
  "RLGS4-recurrent" = "#F781BF", # Pink
  "RLGS5-primary" = "#999999",   # Grey
  "RLGS5-recurrent" = "#66C2A5", # Teal
  "RLGS6-primary" = "#FC8D62",   # Coral
  "RLGS6-recurrent" = "#8DA0CB", # Periwinkle
  "RLGS7-primary" = "#E78AC3",   # Pale Pink
  "RLGS7-recurrent" = "#A6D854", # Lime Green
  "RLGS8-primary" = "#FFD92F",   # Gold
  "RLGS8-recurrent" = "#E5C494", # Sand
  "RLGS9-primary" = "#B3B3B3",   # Light Grey
  "RLGS9-recurrent" = "#B15928", # Dark Brown
  "RLGS10-primary" = "#CAB2D6",  # Lavender
  "RLGS10-recurrent" = "#FF69B4",# Hot Pink
  "RLGS11-primary" = "#1B9E77",  # Forest Green
  "RLGS11-recurrent" = "#D95F02",# Burnt Orange
  "RLGS12-primary" = "#7570B3",  # Indigo
  "RLGS12-recurrent" = "#E7298A" # Magenta
)
    
# plot the signature and contribution by each sample
p = ggplot(stats_df, aes(x = SBS_signature, y = Contribution_Fraction, fill = sample_number)) + plot_theme() +
geom_bar(stat = "identity") +

scale_fill_manual(values = sample_colors) +

ggtitle('') + ylab('Fraction of sample') + xlab('')

print(p)



return()
}





sort_df = function(input_df){
    # Function to extract numeric parts from each element
    extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))
    
    # Extract and sort by numeric parts
    sorted_vector <- unique(input_df$sample_number)[order(sapply(unique(input_df$sample_number), extract_numeric))]

    # order samples
    input_df$sample_number = factor(input_df$sample_number, levels = sorted_vector)
    
    return(input_df)
}




pdf(opt$figure_file, width=12)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')

# sort df
input_df = sort_df(input_df)
stats_df = sort_df(stats_df)

# order signatures
input_df$SBS_signature <- fct_reorder(input_df$SBS_signature, input_df$Fraction, .fun = sum, .desc = TRUE)

# reorder making sure the "other" is the first signature
input_df$SBS_signature <- fct_relevel(input_df$SBS_signature, "Other", after = Inf)
input_df$SBS_signature = factor(input_df$SBS_signature, levels = rev(levels(input_df$SBS_signature)))

# create plot
barplot(input_df)

dev.off()

print(opt$figure_file)





