# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''

def subset_mutations(annotated_mutations_file, cgc_file, patient):
    # load dfs
    df = pd.read_csv(annotated_mutations_file, sep='\t')
    cgc_df = pd.read_csv(cgc_file, sep='\t')

    # add patient column
    df['patient'] = df['sample'].str.split('-').str[0]

    # subset to patient
    df = df[df['patient'] == patient]

    # subset to cgc genes
    df = df[df['gene'].isin(cgc_df['GENE_SYMBOL'])]

    # subset to unique mutations
    df = df[~df['variant_id'].duplicated()]

    # only keep the columns we need
    df = df[['variant_id', 'gene']]

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # extract patient
    patient = subset_annotated_mutations_file.split('/')[-1].split('-')[0]

    # generate maf format df
    df = subset_mutations(annotated_mutations_file, cgc_file, patient)

    # save to files
    df.to_csv(subset_annotated_mutations_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["annotated_mutations_file=", "cgc_file=", "subset_annotated_mutations_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--annotated_mutations_file"):
            annotated_mutations_file = str(arg)
        if opt in ("--cgc_file"):
            cgc_file = str(arg)

        if opt in ("--subset_annotated_mutations_file"):
            subset_annotated_mutations_file = str(arg)


    main()




