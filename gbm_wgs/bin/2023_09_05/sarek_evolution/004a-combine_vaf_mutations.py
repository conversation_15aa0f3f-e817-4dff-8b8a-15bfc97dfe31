# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''


def process_mutations_to_vaf(files):
    # list unique patient ids
    patient_ids = np.unique([file.split('/')[-1].split('-')[1].split("_")[0] for file in files])

    # for each patient, load the two files and calculate the vaf
    res_df = []

    for patient_id in patient_ids:
        # load files
        variant_depth_file = [file for file in files if patient_id in file and 'R.tsv' in file][0]
        total_depth_file = [file for file in files if patient_id in file and 'X.tsv' in file][0]

        # load dfs
        R_df = pd.read_csv(variant_depth_file, sep='\t', index_col=0)
        X_df = pd.read_csv(total_depth_file, sep='\t', index_col=0)

        # divide R by X
        vaf = R_df / X_df

        # add patient id and mutation_id
        vaf['patient_id'] = patient_id
        vaf['mutation_id'] = R_df.index.to_numpy()

        # melt
        vaf = vaf.melt(id_vars=['patient_id', 'mutation_id'], value_name='vaf')

        # append to list
        res_df.append(vaf)

    # combine
    res_df = pd.concat(res_df)

    # remove vaf = 0 or NA
    res_df = res_df.loc[res_df['vaf'] != 0, :]
    res_df = res_df.dropna(subset=['vaf'])

    # rename "variable" to "sample"
    res_df = res_df.rename(columns={'variable': 'sample'})

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # list files
    file_regex = f'{results_dir}/analysis_sarek/canopy/subset_mutations_input/*depth*'
    files = glob.glob(file_regex)

    # load data and process
    df = process_mutations_to_vaf(files)

    # save to file
    df.to_csv(combined_mutation_vaf_file, sep="\t", index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "combined_mutation_vaf_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)
        
        if opt in ("--combined_mutation_vaf_file"):
            combined_mutation_vaf_file = str(arg)
            

    main()



