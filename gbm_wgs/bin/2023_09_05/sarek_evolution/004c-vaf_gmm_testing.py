# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from sklearn.mixture import GaussianMixture
from concurrent.futures import ProcessPoolExecutor

help_message = '''
Failed
'''


def process_vafs(args, n_components = list(range(1, 11)), n_tests = 10):
    sample, df = args

    # best fit assignment
    assignments_df = None

    # fit GMMs
    bic = []
    aic = []
    bic_standard_deviation = []

    for n in n_components:
        gmm = GaussianMixture(n_components=n, random_state=0)
        gmm.fit(df['vaf'].values.reshape(-1, 1))

        # bic and aic
        bic_val = gmm.bic(df['vaf'].values.reshape(-1, 1))
        aic_val = gmm.aic(df['vaf'].values.reshape(-1, 1))

        # if the bic is the best so far, save the assignments
        if assignments_df is None or np.all(bic_val < bic):
            best_fit = gmm.predict(df['vaf'].values.reshape(-1, 1))

            # add the "mutation_id" and the original vaf to the assignments
            assignments_df = pd.DataFrame({'assignment': best_fit, 'mutation_id': df['mutation_id'].values, 'vaf': df['vaf'].values})
            best_fit_param = n

        # append
        bic.append(bic_val)
        aic.append(aic_val)

        # bic std_dev
        bic_std_dev = []

        # repeat n_tests times for standard deviation
        for i in range(n_tests):
            gmm = GaussianMixture(n_components=n, random_state=i)
            gmm.fit(df['vaf'].values.reshape(-1, 1))

            bic_val = gmm.bic(df['vaf'].values.reshape(-1, 1))

            bic_std_dev.append(bic_val)

        # append 
        bic_standard_deviation.append(np.std(bic_std_dev))

    # create dfs and merge columns
    bic_df = pd.DataFrame({'n_components': n_components, 'bic': bic})
    aic_df = pd.DataFrame({'n_components': n_components, 'aic': aic})

    # merge and add sample
    res_df = bic_df.merge(aic_df, on='n_components')
    res_df['bic_standard_deviation'] = bic_standard_deviation
    res_df['sample'] = sample

    # add sample and best_fit_param to assignments_df
    assignments_df['sample'] = sample
    assignments_df['n_components'] = best_fit_param

    # return
    return res_df, assignments_df


def load_and_process_vafs(combined_mutation_vaf_file, threads):
    # load
    df = pd.read_csv(combined_mutation_vaf_file, sep="\t")

    # arguments for processing
    args = [(sample, df[df['sample'] == sample]) for sample in np.unique(df['sample'])]

    # process
    with ProcessPoolExecutor(max_workers=threads) as executor:
        res = list(executor.map(process_vafs, args))

    # separate and combine
    stats_df = pd.concat([r[0] for r in res])
    assignments_df = pd.concat([r[1] for r in res])

    return assignments_df, stats_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load and combine dfs
    assignments_df, stats_df = load_and_process_vafs(combined_mutation_vaf_file, threads)

    # save to files
    assignments_df.to_csv(gmm_cluster_assignments_file, sep="\t", index=False)
    stats_df.to_csv(gmm_stats_file, sep="\t", index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["combined_mutation_vaf_file=", "gmm_cluster_assignments_file=", "gmm_stats_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--combined_mutation_vaf_file"):
            combined_mutation_vaf_file = str(arg)

        if opt in ("--gmm_cluster_assignments_file"):
            gmm_cluster_assignments_file = str(arg)
        if opt in ("--gmm_stats_file"):
            gmm_stats_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)
            
    main()





