# <PERSON>
library(optparse)
library(forcats)
library(ggplot2)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt sample
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}


colc <- c("#FF0000", # red
  "#0000FF", # blue
  "#008000", # green
  "#FFFF00", # yellow
  "#FFA500", # orange
  "#800080", # purple
  "#A52A2A", # brown
  "#FFC0CB", # pink
  "#00FFFF", # cyan
  "#FF00FF", # magenta
  "#008080", # teal
  "#FFD700", # gold
  "#006400", # darkgreen
  "#000080", # navy
  "#808080"  # gray
)

pchc = c(1:10)


create_dotplot = function(sample, raw_df){

# subset to sample
input_df = raw_df[raw_df$sample == sample,]

# factors not numbers
input_df$assignment = factor(input_df$assignment)

p = ggplot(input_df, aes(x=vaf, y=0)) + plot_theme() +
geom_point(aes(color=assignment, pch=assignment), size=1) +

# add colors and shapes
scale_color_manual(values = colc) +
scale_shape_manual(values = pchc) +

theme(legend.position="none") +
    
ggtitle(sample) +
xlab("VAF") + ylab('')


print(p)

return()
}



sort_patients = function(df){
# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# order samples
sorted_vector <- unique(df$sample)[order(sapply(unique(df$sample), extract_numeric))]
df$sample = factor(df$sample, levels = sorted_vector)

return(df)
}


pdf(opt$figure_file)

# load dfs
df = read.csv(opt$figure_data_file, sep='\t')

# sort patients
df = sort_patients(df)


# visualize assignments
lapply(levels(df$sample), create_dotplot, df)


dev.off()


print(opt$figure_file)






