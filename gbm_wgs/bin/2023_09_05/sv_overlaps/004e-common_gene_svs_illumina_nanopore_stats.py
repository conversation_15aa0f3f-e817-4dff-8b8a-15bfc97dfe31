# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from scipy.spatial.distance import jense<PERSON><PERSON>non
import statsmodels.stats.multitest as multitest

help_message = '''
Failed
'''

def permutation_test(primary, recurrent, n_permutations, random_state=None):
    rng = np.random.default_rng(random_state)
    observed_distance = jensenshannon(primary, recurrent)

    combined = np.concatenate([primary, recurrent])
    n = len(primary)
    count = 0
    for _ in range(n_permutations):
        rng.shuffle(combined)
        perm_primary = combined[:n]
        perm_recurrent = combined[n:]
        dist = jensenshannon(perm_primary, perm_recurrent)
        if dist >= observed_distance:
            count += 1

    p_value = count / n_permutations
    return observed_distance, p_value


def run_similarity_stats(fractions_file):
    # load dfs
    df = pd.read_csv(fractions_file, sep='\t')

    res_df = []

    # For each patient, run stats
    for patient in df['patient'].unique():
        
        patient_df = df[df['patient'] == patient]

        # Separate into primary and recurrent
        primary_df = patient_df[patient_df['tumor'] == 'primary'].copy()
        recurrent_df = patient_df[patient_df['tumor'] == 'recurrent'].copy()

        # Sort for alignment
        primary_df = primary_df.sort_values('sv_type')
        recurrent_df = recurrent_df.sort_values('sv_type')

        # Get contribution lists
        primary_vals = primary_df['fraction'].to_numpy()
        recurrent_vals = recurrent_df['fraction'].to_numpy()

        # Run permutation test
        dist, p = permutation_test(primary_vals, recurrent_vals, n_permutations=10_000, random_state=42)

        # Append results
        res_df.append([patient, dist, p])

    # Create a new DataFrame from the results
    res_df = pd.DataFrame(res_df, columns=['patient', 'js_distance', 'p_value'])

    # run fdr
    res_df['fdr'] = multitest.fdrcorrection(res_df['p_value'])[1]

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # summarize maf df
    df = run_similarity_stats(fractions_file)

    # save to files
    df.to_csv(stats_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["fractions_file=", "stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--fractions_file"):
            fractions_file = str(arg)

        if opt in ("--stats_file"):
            stats_file = str(arg)

    main()




