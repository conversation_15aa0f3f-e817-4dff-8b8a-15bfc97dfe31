# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''


def count_specific_svs(df):
    res_df = []

    # iterate through primary, recurrent, both sv dfs to get counts of each sv
    for tumor_type in df['primary_recurrent_both'].unique():
        # filter df
        df_filtered = df[df['primary_recurrent_both'] == tumor_type]

        # count the number of unique svs
        counts = df_filtered['sv_type'].value_counts()

        # create a df from it
        df_counts = pd.DataFrame(counts).reset_index()

        # rename columns
        df_counts.columns = ['variable', 'count']
        df_counts['tumor'] = tumor_type

        # append to res_df
        res_df.append(df_counts)

    return pd.concat(res_df)


def identify_common_svs(file_list):
    # create a df to store the results
    res_df = []
    spec_sv_df = []

    for file in file_list:
        # load df
        df = pd.read_csv(file, sep='\t')
        
        # count the number of primary, recurrent and both svs and create a df from it
        values = df['primary_recurrent_both'].value_counts()
        df_counts = pd.DataFrame(values).reset_index()

        # rename columns
        df_counts.columns = ['variable', 'count']
        df_counts['sample'] = "-".join(file.split("/")[-1].split("_")[:2])

        # repeat but for the specific svs
        df_counts_specific = count_specific_svs(df)

        # add sample
        df_counts_specific['sample'] = "-".join(file.split("/")[-1].split("_")[:2])

        # append to dfs
        res_df.append(df_counts)
        spec_sv_df.append(df_counts_specific)

    # concatenate all dfs
    res_df = pd.concat(res_df)
    spec_sv_df = pd.concat(spec_sv_df)

    # add patient_id ids
    res_df['patient_id'] = res_df['sample'].str.split("-").str[0]
    spec_sv_df['patient_id'] = spec_sv_df['sample'].str.split("-").str[0]

    # add a fraction column based on the counts relative to that patient
    res_df['fraction'] = res_df['count'] / res_df.groupby('patient_id')['count'].transform('sum')
    spec_sv_df['fraction'] = spec_sv_df['count'] / spec_sv_df.groupby('patient_id')['count'].transform('sum')

    return res_df, spec_sv_df

def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # list files of interest
    regex = f'{results_dir}/analysis_nanopore/sv_overlaps/processed/*_primary_vs_recurrent-intercepted_processed.tsv'
    files = glob.glob(regex)
    
    # generate maf format df
    df, specific_sv_df = identify_common_svs(files)

    # save to files
    df.to_csv(nanopore_sv_counts_file, sep='\t', index=False)
    specific_sv_df.to_csv(specific_sv_counts_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "nanopore_sv_counts_file=", "specific_sv_counts_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--nanopore_sv_counts_file"):
            nanopore_sv_counts_file = str(arg)
        if opt in ("--specific_sv_counts_file"):
            specific_sv_counts_file = str(arg)

    main()




