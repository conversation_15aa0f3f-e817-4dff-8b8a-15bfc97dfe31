# Alec Bahcheli

import sys, getopt, time, os

import pandas as pd 
import numpy as np

import scipy.stats as stats

help_message = '''
Failed
'''

def compare_sv_lengths(illumina_file, nanopore_file):
    # load dfs
    illumina_df = pd.read_csv(illumina_file, sep='\t')
    nanopore_df = pd.read_csv(nanopore_file, sep='\t')

    # for each sv_type, extract lengths and run stats
    res_df = []
    stats_df = []

    for sv_type in nanopore_df['sv_type'].unique():
        # subset to sv_type
        illumina_subset = illumina_df[illumina_df['sv_type'] == sv_type]
        nanopore_subset = nanopore_df[nanopore_df['sv_type'] == sv_type]

        # subset to sample, patient, tumor, and length
        illumina_subset = illumina_subset[['sample', 'patient', 'tumor', 'size']]
        nanopore_subset = nanopore_subset[['sample', 'patient', 'tumor', 'size']]

        # add identifier
        illumina_subset['source'] = 'illumina'
        nanopore_subset['source'] = 'nanopore'

        # run ttests and utests
        illumina_length = illumina_subset['size']
        nanopore_length = nanopore_subset['size']

        # ttest
        pval_ttest = stats.ttest_ind(illumina_length, nanopore_length, equal_var=False)[1]

        # mannwhitney
        pval_utest = stats.mannwhitneyu(illumina_length, nanopore_length)[1]

        # combine the dataframes
        combined_df = pd.concat([illumina_subset, nanopore_subset], ignore_index=True)
        combined_df['sv_type'] = sv_type

        # append to dfs
        res_df.append(combined_df)
        stats_df.append([sv_type, pval_ttest, pval_utest])

    # combine the dataframes
    res_df = pd.concat(res_df, ignore_index=True)
    stats_df = pd.DataFrame(stats_df, columns=['sv_type', 'pval_ttest', 'pval_utest'])

    return res_df, stats_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    df, stats_df = compare_sv_lengths(illumina_gene_svs_file, nanopore_gene_svs_file)

    # save to files
    df.to_csv(sv_lengths_file, sep='\t', index=False)
    stats_df.to_csv(stats_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["illumina_gene_svs_file=", "nanopore_gene_svs_file=", "sv_lengths_file=", "stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--illumina_gene_svs_file"):
            illumina_gene_svs_file = str(arg)
        if opt in ("--nanopore_gene_svs_file"):
            nanopore_gene_svs_file = str(arg)

        if opt in ("--sv_lengths_file"):
            sv_lengths_file = str(arg)
        if opt in ("--stats_file"):
            stats_file = str(arg)

    main()




