# Alec Bahcheli

import sys, getopt, time, os

import pandas as pd 
import numpy as np

import scipy.stats as stats

help_message = '''
Failed
'''

def compare_sv_lengths(illumina_file, nanopore_file):
    # load dfs
    illumina_df = pd.read_csv(illumina_file, sep='\t')
    nanopore_df = pd.read_csv(nanopore_file, sep='\t')

    # for each sv_type, extract lengths and run stats
    lengths_df = []
    stats_df = []
    count_df = []

    for sv_type in illumina_df['sv_type'].unique():
        # subset to sv_type
        illumina_subset = illumina_df[illumina_df['sv_type'] == sv_type]
        nanopore_subset = nanopore_df[nanopore_df['sv_type'] == sv_type]

        # subset to sample, patient, tumor, and length
        illumina_subset = illumina_subset[['sample', 'patient', 'tumor', 'size']]
        nanopore_subset = nanopore_subset[['sample', 'patient', 'tumor', 'size']]

        # add identifier
        illumina_subset['source'] = 'illumina'
        nanopore_subset['source'] = 'nanopore'

        # run ttests and utests
        illumina_length = illumina_subset['size']
        nanopore_length = nanopore_subset['size']

        # ttest and mannwhitneyu
        pval_ttest = stats.ttest_ind(illumina_length, nanopore_length, equal_var=False)[1]
        pval_utest = stats.mannwhitneyu(illumina_length, nanopore_length)[1]

        # append to stats df
        stats_df.append([sv_type, pval_ttest, pval_utest, 'length'])

        # repeat for number of svs per sample
        illumina_counts = illumina_subset.groupby(['sample']).size().reset_index(name='sv_count')
        nanopore_counts = nanopore_subset.groupby(['sample']).size().reset_index(name='sv_count')

        # add identifier
        illumina_counts['source'] = 'illumina'
        nanopore_counts['source'] = 'nanopore'

        # run ttests and utests
        pval_ttest_counts = stats.ttest_ind(illumina_counts['sv_count'], nanopore_counts['sv_count'], equal_var=False)[1]
        pval_utest_counts = stats.mannwhitneyu(illumina_counts['sv_count'], nanopore_counts['sv_count'])[1]

        # append to stats df
        stats_df.append([sv_type, pval_ttest_counts, pval_utest_counts, 'sv_count'])

        # combine the dataframes
        lengths_combined_df = pd.concat([illumina_subset, nanopore_subset], ignore_index=True)
        lengths_combined_df['sv_type'] = sv_type

        counts_combined_df = pd.concat([illumina_counts, nanopore_counts], ignore_index=True)
        counts_combined_df['sv_type'] = sv_type

        # append to dfs
        lengths_df.append(lengths_combined_df)
        count_df.append(counts_combined_df)

    # combine the dataframes
    lengths_df = pd.concat(lengths_df, ignore_index=True)
    count_df = pd.concat(count_df, ignore_index=True)

    # create df
    stats_df = pd.DataFrame(stats_df, columns=['sv_type', 'pval_ttest', 'pval_utest', 'comparison'])

    return lengths_df, count_df, stats_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    df, count_df, stats_df = compare_sv_lengths(illumina_gene_svs_file, nanopore_gene_svs_file)

    # save to files
    df.to_csv(all_sv_details_file, sep='\t', index=False)
    count_df.to_csv(counts_file, sep='\t', index=False)
    stats_df.to_csv(stats_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["illumina_gene_svs_file=", "nanopore_gene_svs_file=", "all_sv_details_file=", "counts_file=", "stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--illumina_gene_svs_file"):
            illumina_gene_svs_file = str(arg)
        if opt in ("--nanopore_gene_svs_file"):
            nanopore_gene_svs_file = str(arg)

        if opt in ("--all_sv_details_file"):
            all_sv_details_file = str(arg)
        if opt in ("--counts_file"):
            counts_file = str(arg)
        if opt in ("--stats_file"):
            stats_file = str(arg)

    main()




