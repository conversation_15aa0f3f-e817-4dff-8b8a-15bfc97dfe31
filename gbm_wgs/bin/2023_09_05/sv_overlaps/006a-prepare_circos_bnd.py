# Alec Bahcheli

import sys, getopt, time

import pandas as pd 
import numpy as np

import scipy.stats as stats

help_message = '''
Failed
'''



def filter_df(df, sv_type='break_end', sample='RLGS9-primary'):
    # filter the df to only include break_end sv_type
    df = df[df['sv_type'] == sv_type]

    # filter the df to only include sample of interest
    df = df[df['sample'] == sample]

    # remove rows where breakpoint chromosomes are non-canonical
    chr_list = ['chr' + str(i) for i in range(23)][1:]
    chr_list.extend(['chrX', 'chrY'])

    mask1 = df['breakpoint1'].str.split(":").str[0].isin(chr_list)
    mask2 = df['breakpoint2'].str.split(":").str[0].isin(chr_list)
    df = df[mask1 & mask2]

    # remove breakpoint variants smaller than 1kb
    b1 = df['breakpoint1'].str.split(":").str[1].astype(int)
    b2 = df['breakpoint2'].str.split(":").str[1].astype(int)

    df = df[(b2 - b1).abs() > 1000]

    # remove duplicate rows based on breakpoint1 and 2
    df.drop_duplicates(subset=['breakpoint1', 'breakpoint2'], inplace=True)

    return df


def prepare_circos_plots(illumina_file, nanopore_file):
    # load dfs
    illumina_df = pd.read_csv(illumina_file, sep='\t')
    nanopore_df = pd.read_csv(nanopore_file, sep='\t')

    # filter the dfs
    illumina_df = filter_df(illumina_df)
    nanopore_df = filter_df(nanopore_df)

    # split breakpoint columns
    illumina_df[['chr1', 'start1']] = illumina_df['breakpoint1'].str.split(':', expand=True)
    illumina_df[['chr2', 'start2']] = illumina_df['breakpoint2'].str.split(':', expand=True)

    nanopore_df[['chr1', 'start1']] = nanopore_df['breakpoint1'].str.split(':', expand=True)
    nanopore_df[['chr2', 'start2']] = nanopore_df['breakpoint2'].str.split(':', expand=True)

    # add end coordinates that's the same as start
    illumina_df['end1'] = illumina_df['start1']
    illumina_df['end2'] = illumina_df['start2']
    nanopore_df['end1'] = nanopore_df['start1']
    nanopore_df['end2'] = nanopore_df['start2']

    # add data source
    illumina_df['source'] = 'illumina'
    nanopore_df['source'] = 'nanopore'

    # combine and return
    df = pd.concat([illumina_df, nanopore_df], ignore_index=True)

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    df = prepare_circos_plots(illumina_gene_svs_file, nanopore_gene_svs_file)

    # save to files
    df.to_csv(circos_bnd_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["illumina_gene_svs_file=", "nanopore_gene_svs_file=", "circos_bnd_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--illumina_gene_svs_file"):
            illumina_gene_svs_file = str(arg)
        if opt in ("--nanopore_gene_svs_file"):
            nanopore_gene_svs_file = str(arg)

        if opt in ("--circos_bnd_file"):
            circos_bnd_file = str(arg)

    main()




