# Alec <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}





stacked_barplot = function(input_df){

# plot
p = ggplot(input_df, aes(x = sample, y = fraction, fill = sv_type)) + plot_theme() +
geom_bar(stat = "identity", position = "stack") +

# Set colors for the bars
scale_fill_manual(values = c("n_nanopore" = "#df7b20", "n_illumina" = "#229a32", "n_common" = "#b98acd")) +

ggtitle('') +
xlab('') + ylab('Proportion of mutations')

print(p)

return()
}




sort_samples = function(df) {
# Function to extract numeric parts from each sample name
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# Extract the sample type (primary or recurrent)
extract_type <- function(x) {
ifelse(grepl("primary", x), "primary", "recurrent")
}

# Sort the samples: first by numeric part, then by type (primary before recurrent)
sorted_vector <- unique(df$sample)[order(sapply(unique(df$sample), extract_numeric), sapply(unique(df$sample), extract_type))]

# Assign the sorted factor levels to the sample column
df$sample = factor(df$sample, levels = sorted_vector)

return(df)
}



pdf(opt$figure_file)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')

# sort samples
input_df = sort_samples(input_df)

# sort the levels of variables
input_df$sv_type = factor(input_df$sv_type, levels=(c('n_common','n_illumina','n_nanopore')))

# plot stacked barplots
stacked_barplot(input_df)




dev.off()


print(opt$figure_file)




