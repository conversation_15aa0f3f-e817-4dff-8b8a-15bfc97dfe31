# Alec <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




lengths_boxplots = function(input_df){

# plot the number of SVs
p = ggplot(input_df, aes(x = source, y = log10(size))) + plot_theme() +
geom_boxplot(outlier.shape = NA) +

# facet
facet_wrap(~ sv_type, nrow = 1) +

ggtitle('') +
xlab('') + ylab('SV sizez (log10)')

print(p)

# plot the number of SVs
p = ggplot(input_df, aes(x = source, y = log10(size))) + plot_theme() +
geom_violin() +

# facet
facet_wrap(~ sv_type, nrow = 1) +

ggtitle('') +
xlab('') + ylab('SV sizez (log10)')

print(p)


return()
}


counts_boxplots = function(input_df){

# plot the number of SVs
p = ggplot(input_df, aes(x = source, y = sv_count)) + plot_theme() +
geom_boxplot(outlier.shape = NA) +
geom_jitter(aes(color = source), alpha = 0.5) +

# facet
facet_wrap(~ sv_type, nrow = 1) +

ggtitle('') +
xlab('') + ylab('N. events')

print(p)


return()
}




pdf(opt$figure_file, width=10, height=6)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')

# plot
lengths_boxplots(input_df)
counts_boxplots(stats_df)

dev.off()


print(opt$figure_file)




