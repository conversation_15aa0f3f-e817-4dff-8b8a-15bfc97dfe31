library(optparse)
library(circlize)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




circos_plot = function(data_source, input_df){

# subset to data_source
df = input_df[input_df$source == data_source,]


# create a new plot
circos.clear()
    
# start with chr1 at the top; make each track 10% of the total height; reduce margins between tracks
circos.par("start.degree" = 90, track.height = 0.1, cell.padding = c(0, 0, 0, 0))

# Initialize the circos plot
circos.initializeWithIdeogram(species = "hg38")

# plot the segments
circos.genomicLink(df[,c('chr1', 'start1', 'end1')],
                   df[,c('chr2', 'start2', 'end2')],
                   col = "#1f77b4")

# # Customize the plot if needed, e.g., add legend
# legend("topright", legend = names(cna_colors), fill = cna_colors, border = NA, bty = "n", ncol = length(cna_colors), inset = 0.05)
title(data_source, cex = 1.5)

}




pdf(opt$figure_file)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')

df1 = input_df[input_df$source == 'illumina',]
df2 = input_df[input_df$source == 'nanopore',]

print(df2)

# create circos plots
lapply(unique(input_df$source), circos_plot, input_df)


dev.off()


print(opt$figure_file)






