# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''

t_vs_c_dict = {'10':'tumor', 
                  '01':'control', 
                  '11':'both'}


def count_svs(files):
    counts_df = []
    spec_sv_df = []

    # for each file, count the number of specific SVs
    for file in files:
        df = pd.read_csv(file, sep='\t', comment="#", header=None)
        df.columns = ['CHROM', 'POS', 'ID', 'REF', 'ALT', 'QUAL', 'FILTER', 'INFO', 'FORMAT', 'SAMPLE']

        # Keep only normal chromosomes
        valid_chroms = ['chr' + str(i) for i in range(1, 23)] + ['chrX', 'chrY']
        df = df[df['CHROM'].isin(valid_chroms)]

        # parse variant type
        df['variant_type'] = df['INFO'].str.extract(r"SVTYPE=([^;]+)")

        # Extract tumor vs class
        df['variant_classification'] = df['INFO'].str.extract(r"SUPP_VEC=([01]{2})")[0].map(t_vs_c_dict).fillna('unknown')

        # subset to variation_type and variant_classification
        df = df[['variant_type', 'variant_classification']]

        # count the number of each variant type
        df = df.groupby(['variant_type', 'variant_classification']).size().reset_index(name='count')

        # create a second df just counting the number of variant_classification
        df2 = df.groupby(['variant_classification']).sum().reset_index()

        # add sample_id, patient_id, and tumor
        sample = file.split("/")[-1].split("_")[0]
        df['sample'] = sample
        df['patient_id'] = sample.split("-")[0]
        df['tumor'] = sample.split("-")[1]

        # add sample_id, patient_id, and tumor to df2
        df2['sample'] = sample
        df2['patient_id'] = sample.split("-")[0]
        df2['tumor'] = sample.split("-")[1]

        # add to counts_df
        counts_df.append(df)
        spec_sv_df.append(df2)


    # concatenate all the dataframes
    counts_df = pd.concat(counts_df, ignore_index=True)
    spec_sv_df = pd.concat(spec_sv_df, ignore_index=True)

    return counts_df, spec_sv_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # list files of interest
    regex = f"{results_dir}/analysis_nanopore/processing_non_somatic_svs/*_vs_blood-intercepted.vcf"
    files = glob.glob(regex)

    # process the df
    counts_df, spec_counts_df = count_svs(files)

    # save to files
    counts_df.to_csv(sv_counts_file, sep='\t', index=False)
    spec_counts_df.to_csv(spec_sv_counts_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "sv_counts_file=", "spec_sv_counts_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--sv_counts_file"):
            sv_counts_file = str(arg)
        if opt in ("--spec_sv_counts_file"):
            spec_sv_counts_file = str(arg)

    main()




