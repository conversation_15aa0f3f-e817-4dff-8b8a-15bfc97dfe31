# Alec Bahcheli

import sys, getopt, time, os

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''

def find_common_svs(illumina_file, nanopore_file):
    # load dfs
    illumina_df = pd.read_csv(illumina_file, sep='\t')
    nanopore_df = pd.read_csv(nanopore_file, sep='\t')

    # set the variant id for each as the sample, gene, variant_type
    illumina_df['variant_id'] = illumina_df['sample'] + '_' + illumina_df['gene'] + '_' + illumina_df['sv_type']
    nanopore_df['variant_id'] = nanopore_df['sample'] + '_' + nanopore_df['gene'] + '_' + nanopore_df['sv_type']

    # if each id is not uniq, add a number to the end to count
    illumina_df['variant_id'] = illumina_df.groupby('variant_id').cumcount().astype(str) + '_' + illumina_df['variant_id']
    nanopore_df['variant_id'] = nanopore_df.groupby('variant_id').cumcount().astype(str) + '_' + nanopore_df['variant_id']

    # results df
    res_df = []
    spec_df = []

    # identify number of SVS in common and unique per patient
    for patient in nanopore_df['patient'].unique():
        # subset to patient
        nanopore_patient_df = nanopore_df[nanopore_df['patient'] == patient]
        illumina_patient_df = illumina_df[illumina_df['patient'] == patient]

        # for primary and recurrent tumors, calculate the number of unique and common SVs
        for tumor in nanopore_patient_df['tumor'].unique():
            # subset to tumor
            nanopore_tumor_df = nanopore_patient_df[nanopore_patient_df['tumor'] == tumor]
            illumina_tumor_df = illumina_patient_df[illumina_patient_df['tumor'] == tumor]

            # intersect the variant ids
            common_svs = np.intersect1d(nanopore_tumor_df['variant_id'].astype('str'), illumina_tumor_df['variant_id'].astype('str'))

            # number of uniq SVs
            n_uniq_nanopore = np.sum(~np.isin(nanopore_tumor_df['variant_id'], common_svs))
            n_uniq_illumina = np.sum(~np.isin(illumina_tumor_df['variant_id'], common_svs))

            # convert to fractions
            total_svs = len(common_svs) + n_uniq_nanopore + n_uniq_illumina

            n_commons = len(common_svs) / total_svs
            n_nanopore = n_uniq_nanopore / total_svs
            n_illumina = n_uniq_illumina / total_svs

            # append to results df
            res_df.append([f'{patient}-{tumor}', patient, tumor, n_commons, n_nanopore, n_illumina])

            # repeat for each specific variant type
            for variant_type in nanopore_tumor_df['sv_type'].unique():
                # subset to variant type
                nanopore_variant_df = nanopore_tumor_df[nanopore_tumor_df['sv_type'] == variant_type]
                illumina_variant_df = illumina_tumor_df[illumina_tumor_df['sv_type'] == variant_type]

                # intersect the variant ids
                common_svs = np.intersect1d(nanopore_variant_df['variant_id'].astype('str'), illumina_variant_df['variant_id'].astype('str'))

                # number of uniq SVs
                n_uniq_nanopore = np.sum(~np.isin(nanopore_variant_df['variant_id'], common_svs))
                n_uniq_illumina = np.sum(~np.isin(illumina_variant_df['variant_id'], common_svs))

                # convert into fractions
                total_svs = len(common_svs) + n_uniq_nanopore + n_uniq_illumina
                
                n_commons = len(common_svs) / total_svs
                n_nanopore = n_uniq_nanopore / total_svs
                n_illumina = n_uniq_illumina / total_svs

                # append to results df
                spec_df.append([f'{patient}-{tumor}', patient, tumor, variant_type, n_commons, n_nanopore, n_illumina])
                # spec_df.append([f'{patient}-{tumor}', patient, tumor, variant_type, n_commons, n_nanopore, n_illumina, len(common_svs), n_uniq_nanopore, n_uniq_illumina])

    # convert to df
    res_df = pd.DataFrame(res_df, columns=['sample', 'patient', 'tumor', 'n_common', 'n_nanopore', 'n_illumina'])
    spec_df = pd.DataFrame(spec_df, columns=['sample', 'patient', 'tumor', 'variant_type', 'n_common', 'n_nanopore', 'n_illumina'])
    # spec_df = pd.DataFrame(spec_df, columns=['sample', 'patient', 'tumor', 'variant_type', 'n_common', 'n_nanopore', 'n_illumina', 'n_common_count', 'n_nanopore_count', 'n_illumina_count'])

    # melt the dfs for a single "counts" column
    res_df = pd.melt(res_df, id_vars=['sample', 'patient', 'tumor'], value_vars=['n_common', 'n_nanopore', 'n_illumina'], var_name='sv_type', value_name='fraction')
    spec_df = pd.melt(spec_df, id_vars=['sample', 'patient', 'tumor', 'variant_type'], value_vars=['n_common', 'n_nanopore', 'n_illumina'], var_name='sv_type', value_name='fraction')

    return res_df, spec_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    df, spec_df = find_common_svs(illumina_gene_svs_file, nanopore_gene_svs_file)

    # save to files
    df.to_csv(overlapping_svs_file, sep='\t', index=False)
    spec_df.to_csv(spec_overlapping_svs_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["illumina_gene_svs_file=", "nanopore_gene_svs_file=", "overlapping_svs_file=", "spec_overlapping_svs_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--illumina_gene_svs_file"):
            illumina_gene_svs_file = str(arg)
        if opt in ("--nanopore_gene_svs_file"):
            nanopore_gene_svs_file = str(arg)

        if opt in ("--overlapping_svs_file"):
            overlapping_svs_file = str(arg)
        if opt in ("--spec_overlapping_svs_file"):
            spec_overlapping_svs_file = str(arg)

    main()




