# Alec Bahcheli - <EMAIL>


###################################
# alignment and processing with epi2me
###################################

rule setup_wf_basecalling:
    input:
        script = BIN_DIR + "/nanopore/001a-setup_wf_basecalling.py"

    output:
        wf_execution_file = BIN_DIR + "/epi2me_nextflow/basecalling/{run_type}-wf_basecalling-{sample}",

    params:
        processing_dir = RES_DIR + "/{run_type}/{sample}",

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        "{PYTHON} {input.script} --wf_execution_file {output.wf_execution_file} --processing_dir {params.processing_dir}"


rule setup_wf_somatic_variation:
    input:
        tumor_bam_file = RES_DIR + "/nanopore/{sample_code}-{tumor}/{sample_code}-{tumor}.pass.bam",
        germline_bam_file = RES_DIR + "/nanopore/{sample_code}-blood/{sample_code}-blood.pass.bam",

        genome_fasta = genome_fasta,

        script = BIN_DIR + "/nanopore/001b-setup_wf_somatic_variation.py"

    output:
        wf_execution_file = BIN_DIR + "/epi2me_nextflow/somatic_variation/{run_type}-wf_somatic_variation-{sample_code}-{tumor}"

    params:
        processing_dir = RES_DIR + "/{run_type}/{sample_code}-{tumor}"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        "{PYTHON} {input.script} --tumor_bam_file {input.tumor_bam_file} --germline_bam_file {input.germline_bam_file} --processing_dir {params.processing_dir} --genome_fasta {input.genome_fasta} --wf_execution_file {output.wf_execution_file}"


rule setup_wf_somatic_variation_primary_recurrent:
    input:
        primary_tumor_bam_file = RES_DIR + "/nanopore/{sample_code}-primary/{sample_code}-primary.pass.bam",
        recurrent_tumor_bam_file = RES_DIR + "/nanopore/{sample_code}-recurrent/{sample_code}-recurrent.pass.bam",

        genome_fasta = genome_fasta,

        script = BIN_DIR + "/nanopore/001c-setup_wf_primary_recurrent_methylation.py"

    output:
        wf_execution_file = BIN_DIR + "/epi2me_nextflow/primary_recurrent_somatic_variation/{run_type}-wf_primary_recurrent_somatic_variation-{sample_code}"

    params:
        processing_dir = RES_DIR + "/{run_type}/{sample_code}"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        "{PYTHON} {input.script} --primary_tumor_bam_file {input.primary_tumor_bam_file} --recurrent_tumor_bam_file {input.recurrent_tumor_bam_file} --genome_fasta {input.genome_fasta} --processing_dir {params.processing_dir} --wf_execution_file {output.wf_execution_file}"


rule setup_wf_human_variation:
    input:
        bam_file = RES_DIR + "/nanopore/{sample_code}-{tumor}/{sample_code}-{tumor}.pass.bam",

        genome_fasta = genome_fasta,

        script = BIN_DIR + "/nanopore/001d-setup_wf_human_variation.py"

    output:
        wf_execution_file = BIN_DIR + "/epi2me_nextflow/human_variation/{run_type}-wf_human_variation-{sample_code}-{tumor}"

    params:
        processing_dir = RES_DIR + "/{run_type}/{sample_code}-{tumor}",

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        "{PYTHON} {input.script} --bam_file {input.bam_file} --processing_dir {params.processing_dir} --genome_fasta {input.genome_fasta} --wf_execution_file {output.wf_execution_file}"


rule setup_wf_human_variation_blood_control:
    input:
        bam_file = RES_DIR + "/nanopore/{sample_code}-{tumor}/{sample_code}-{tumor}.pass.bam",

        genome_fasta = genome_fasta,

        script = BIN_DIR + "/nanopore/001e-setup_wf_human_variation_blood_control.py"

    output:
        wf_execution_file = BIN_DIR + "/epi2me_nextflow/human_variation/{run_type}-wf_human_variation_blood_control-{sample_code}-{tumor}"

    params:
        processing_dir = RES_DIR + "/{run_type}/{sample_code}-{tumor}",

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        "{PYTHON} {input.script} --bam_file {input.bam_file} --processing_dir {params.processing_dir} --genome_fasta {input.genome_fasta} --wf_execution_file {output.wf_execution_file}"













