# Alec Ba<PERSON>cheli - <EMAIL>


#####################
# summarize tumor purity and ploidy
#####################

rule summarize_tumor_ploidy_purity:
    input:
        expand("{res_dir}/sarek_wgs/{sample}-{tumor}/variant_calling/ascat/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood.purityploidy.txt", res_dir = RES_DIR, sample = sample_codes_list, tumor = tumor_types),
        script = BIN_DIR + "/sarek_cnas/000a-summarize_tumor_ploidy_purity.py"

    output:
        ploidy_purity_file = RES_DIR + "/analysis_sarek/consensus_cnas/111-summarize_tumor_ploidy_purity.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --ploidy_purity_file {output.ploidy_purity_file}"



#####################
# process segment CNAs
#####################

# create a unique directory for each sample
rule create_res_dir_wgs_cnas:
    output:
        null_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/null.txt"

    params:
        output_dir = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'
        
    shell:
        "mkdir -p {params.output_dir} && touch {output.null_file}"


rule process_cnvkit_cnas:
    input:
        RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/null.txt",
        raw_cnv_file = RES_DIR + "/sarek_wgs/{sample}-{tumor}/variant_calling/cnvkit/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood/{sample}-{tumor}-{tumor}.call.cns",

        script = BIN_DIR + "/sarek_cnas/001a-process_cnvkit_cnas.py"

    output:
        processed_gain_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/001-{sample}-{tumor}_cnvkit_gain.tsv",
        processed_loss_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/001-{sample}-{tumor}_cnvkit_loss.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTHON} {input.script} --raw_cnv_file {input.raw_cnv_file} --processed_gain_file {output.processed_gain_file} --processed_loss_file {output.processed_loss_file}"

rule process_ascat_cnas:
    input:
        RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/null.txt",
        raw_cnv_file = RES_DIR + "/sarek_wgs/{sample}-{tumor}/variant_calling/ascat/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood.cnvs.txt",

        script = BIN_DIR + "/sarek_cnas/001b-process_ascat_cnas.py"

    output:
        processed_gain_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/001-{sample}-{tumor}_ascat_gain.tsv",
        processed_loss_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/001-{sample}-{tumor}_ascat_loss.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTHON} {input.script} --raw_cnv_file {input.raw_cnv_file} --processed_gain_file {output.processed_gain_file} --processed_loss_file {output.processed_loss_file}"

rule process_controlfreec_cnas:
    input:
        RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/null.txt",
        raw_cnv_file = RES_DIR + "/sarek_wgs/{sample}-{tumor}/variant_calling/controlfreec/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood.p.value.txt",

        script = BIN_DIR + "/sarek_cnas/001c-process_controlfreec_cnas.py"

    output:
        processed_gain_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/001-{sample}-{tumor}_controlfreec_gain.tsv",
        processed_loss_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/001-{sample}-{tumor}_controlfreec_loss.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTHON} {input.script} --raw_cnv_file {input.raw_cnv_file} --processed_gain_file {output.processed_gain_file} --processed_loss_file {output.processed_loss_file}"




#####################
# find common CNAs
#####################

# intersection of CNAs from different callers
rule intersect_bed_files_ascat_cnvkit:
    input:
        file1 = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/001-{sample}-{tumor}_cnvkit_{classification}.tsv",
        file2 = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/001-{sample}-{tumor}_ascat_{classification}.tsv"

    output:
        intersected_bed_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/002-{sample}-{tumor}_ascat_cnvkit_{classification}.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.file1} -b {input.file2} -wo | awk '{{start = ($2 > $7 ? $2 : $7); end = ($3 < $8 ? $3 : $8); print $1, start, end, $4, $5}}' OFS="\t" > {output.intersected_bed_file}"""

rule intersect_bed_files_ascat_controlfreec:
    input:
        file1 = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/001-{sample}-{tumor}_ascat_{classification}.tsv",
        file2 = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/001-{sample}-{tumor}_controlfreec_{classification}.tsv"

    output:
        intersected_bed_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/002-{sample}-{tumor}_ascat_controlfreec_{classification}.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.file1} -b {input.file2} -wo | awk '{{start = ($2 > $7 ? $2 : $7); end = ($3 < $8 ? $3 : $8); print $1, start, end, $4, $5}}' OFS="\t" > {output.intersected_bed_file}"""

rule intersect_bed_files_cnvkit_controlfreec:
    input:
        file1 = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/001-{sample}-{tumor}_controlfreec_{classification}.tsv",
        file2 = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/001-{sample}-{tumor}_cnvkit_{classification}.tsv"

    output:
        intersected_bed_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/002-{sample}-{tumor}_cnvkit_controlfreec_{classification}.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.file1} -b {input.file2} -wo | awk '{{start = ($2 > $7 ? $2 : $7); end = ($3 < $8 ? $3 : $8); print $1, start, end, $4, $5}}' OFS="\t" > {output.intersected_bed_file}"""


# combine intersected results
rule combine_intersected_results_cnas:
    input:
        intersected_results1 = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/002-{sample}-{tumor}_ascat_cnvkit_{classification}.tsv",
        intersected_results2 = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/002-{sample}-{tumor}_ascat_controlfreec_{classification}.tsv",
        intersected_results3 = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/002-{sample}-{tumor}_cnvkit_controlfreec_{classification}.tsv"

    output:
        combined_bed_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/002-{sample}-{tumor}_combine_intersected_results_{classification}.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'

    # first should be ASCAT, then controlfreec because the copy numbers from these software is more specific / accurate
    shell:
        "cat {input.intersected_results1} {input.intersected_results2} {input.intersected_results3} > {output.combined_bed_file}"

# merge overlaps
rule merge_regions_cnas:
    input:
        combined_bed_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/002-{sample}-{tumor}_combine_intersected_results_{classification}.tsv"

    output:
        merged_uniq_bed_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/{sample}-{tumor}_cnas_merged_unique_{classification}.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """sort -k1,1 -k2,2n {input.combined_bed_file} | /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools merge -i - -c 4,5 -o collapse > {output.merged_uniq_bed_file}"""


# merge overlaps
rule combine_merged_regions_cnas:
    input:
        gain_bed_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/{sample}-{tumor}_cnas_merged_unique_gain.tsv",
        loss_bed_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/{sample}-{tumor}_cnas_merged_unique_loss.tsv"

    output:
        merged_uniq_bed_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}-{tumor}_cnas_merged_unique.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """cat {input.gain_bed_file} {input.loss_bed_file} | sort -k1,1 -k2,2n - > {output.merged_uniq_bed_file}"""






