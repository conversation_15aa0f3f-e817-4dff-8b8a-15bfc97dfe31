# Alec <PERSON>


#####################
# visualize the impacts of WGS SVs
#####################

rule process_illumina_svs:
    input:
        expand("{res_dir}/analysis_nanopore/sv_overlaps/nanopore_vs_illumina/{sample}-manta_passing_svs.vcf", res_dir = RES_DIR, sample = sample_list),

        script = BIN_DIR + "/sarek_svs/001a-process_illumina_svs.py"

    output:
        combined_sv_vcf_file = RES_DIR + "/analysis_sarek/svs/processed_illumina_svs.tsv"

    resources:
        threads = 10,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --combined_sv_vcf_file {output.combined_sv_vcf_file} --threads {resources.threads}"


rule convert_illumina_sv_to_bed:
    input:
        combined_sv_vcf_file = RES_DIR + "/analysis_sarek/svs/processed_illumina_svs.tsv",

        script = BIN_DIR + "/sarek_svs/001b-convert_illumina_sv_to_bed.py"

    output:
        sv_bed_file = RES_DIR + "/analysis_sarek/svs/processed_illumina_svs.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --combined_sv_vcf_file {input.combined_sv_vcf_file} --sv_bed_file {output.sv_bed_file}"

rule intersect_illumina_svs_genes:
    input:
        protein_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions.bed",
        sv_bed_file = RES_DIR + "/analysis_sarek/svs/processed_illumina_svs.bed"

    output:
        intersected_bed_file = RES_DIR + "/analysis_sarek/svs/sv_protein_coding_genes_intersected.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.protein_bed_file} -b <(tail -n +2 {input.sv_bed_file}) -wa -wb > {output.intersected_bed_file}"""


rule subset_to_gene_svs_illumina:
    input:
        intersected_bed_file = RES_DIR + "/analysis_sarek/svs/sv_protein_coding_genes_intersected.tsv",

        script = BIN_DIR + "/sarek_svs/001c-subset_to_gene_svs_illumina.py"

    output:
        gene_svs_file = RES_DIR + "/analysis_sarek/svs/subset_to_gene_svs.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --intersected_bed_file {input.intersected_bed_file} --gene_svs_file {output.gene_svs_file}"


###################################
# visualize key gene SVs
###################################

# gene-specific events
rule gene_illunia_sv_examples:
    input:
        gene_svs_file = RES_DIR + "/analysis_sarek/svs/subset_to_gene_svs.tsv",
        gene_loci_bed = RES_DIR + "/analysis_sarek/gene_impacts/modified_protein_gene_regions.bed",

        script = BIN_DIR + "/sarek_svs/002a-subset_to_gene_svs.py"

    output:
        figure_data_file = RES_DIR + "/analysis_sarek/svs/sub_genic_illumina_sv_examples.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --gene_svs_file {input.gene_svs_file} --gene_loci_bed {input.gene_loci_bed} --figure_data_file {output.figure_data_file}"

rule visualize_illumina_sv_examples:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/svs/sub_genic_illumina_sv_examples.tsv",
        figure_stats_file = RES_DIR + "/analysis_sarek/gene_impacts/modified_protein_gene_regions.bed",

        r_script = BIN_DIR + "/sarek_svs/002b-visualize_illumina_sv_examples.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/sv_figures/visualize_illumina_sv_examples.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"


# exon-specific events
rule exon_illumina_sv_examples:
    input:
        gene_svs_file = RES_DIR + "/analysis_sarek/svs/subset_to_gene_svs.tsv",
        exon_loci_bed = RES_DIR + "/analysis_sarek/gene_impacts/spec_gene_exons.bed",

        script = BIN_DIR + "/sarek_svs/002c-exon_nanopore_sv_examples.py"

    output:
        figure_data_file = RES_DIR + "/analysis_sarek/svs/exon_nanopore_sv_examples.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --gene_svs_file {input.gene_svs_file} --exon_loci_bed {input.exon_loci_bed} --figure_data_file {output.figure_data_file}"

rule visualize_exon_illumina_sv_examples:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/svs/exon_nanopore_sv_examples.tsv",
        figure_stats_file = RES_DIR + "/analysis_sarek/gene_impacts/spec_gene_exons.bed",

        r_script = BIN_DIR + "/sarek_svs/002d-visualize_exon_nanopore_sv_examples.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/sv_figures/visualize_exon_nanopore_sv_examples.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"








