# Alec <PERSON>cheli - <EMAIL>


#####################
# identify the SVs in control that are not in tumor 
# process for gene impacts
#####################

# # filter the jasmine results
# rule filter_jasmine_for_control:
#     input:
#         processed_vcf_file = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/{patient}-{tumor}_vs_blood-intercepted.vcf",

#         script = BIN_DIR + "/nanopore_svs_cnas/003b-filter_jasmine_for_control.py"

#     output:
#         nanopore_somatic_svs_file = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/somatic_svs/{patient}-{tumor}_blood_only-somatic.vcf"
        
#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --processed_vcf_file {input.processed_vcf_file} --nanopore_somatic_svs_file {output.nanopore_somatic_svs_file}"


rule process_control_svs:
    input:
        # expand("{res_dir}/analysis_nanopore/processing_non_somatic_svs/somatic_svs/{patient}-{tumor}_blood_only-somatic.vcf", res_dir = RES_DIR, patient = sample_codes_list, tumor = tumor_types),
        expand("{res_dir}/nanopore_human_variation/{patient}-blood/{patient}-blood.wf_sv.vcf.gz", res_dir = RES_DIR, patient = sample_codes_list),

        # script = BIN_DIR + "/nanopore_svs_cnas/004a-process_control_svs.py"
        script = BIN_DIR + "/nanopore_svs_cnas/004a-process_control_svs_from_unfiltered_vcf.py"

    output:
        combined_sv_vcf_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/control_svs.tsv"

    resources:
        threads = 10,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --combined_sv_vcf_file {output.combined_sv_vcf_file} --threads {resources.threads}"

rule convert_control_sv_to_bed:
    input:
        combined_sv_vcf_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/control_svs.tsv",

        script = BIN_DIR + "/nanopore_svs_cnas/001b-convert_nanopore_sv_to_bed.py"

    output:
        sv_bed_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/control_svs.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --combined_sv_vcf_file {input.combined_sv_vcf_file} --sv_bed_file {output.sv_bed_file}"

rule intersect_control_svs_genes:
    input:
        protein_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions.bed",
        sv_bed_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/control_svs.bed"

    output:
        intersected_bed_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/control_sv_protein_coding_genes_intersected.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.protein_bed_file} -b <(tail -n +2 {input.sv_bed_file}) -wa -wb > {output.intersected_bed_file}"""


rule subset_controls_to_gene_svs:
    input:
        intersected_bed_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/control_sv_protein_coding_genes_intersected.tsv",

        script = BIN_DIR + "/nanopore_svs_cnas/001c-subset_to_gene_svs.py"

    output:
        gene_svs_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/control_gene_svs.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --intersected_bed_file {input.intersected_bed_file} --gene_svs_file {output.gene_svs_file}"



###################################
# visualize key gene SVs
###################################

# gene-specific events
rule gene_nanopore_sv_examples_control:
    input:
        gene_svs_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/control_gene_svs.tsv",
        gene_loci_bed = RES_DIR + "/analysis_sarek/gene_impacts/modified_protein_gene_regions.bed",

        script = BIN_DIR + "/nanopore_svs_cnas/002a-subset_to_gene_svs.py"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/control_gene_svs_data.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --gene_svs_file {input.gene_svs_file} --gene_loci_bed {input.gene_loci_bed} --figure_data_file {output.figure_data_file}"

rule visualize_nanopore_sv_examples_control:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/control_gene_svs_data.tsv",
        figure_stats_file =RES_DIR + "/analysis_sarek/gene_impacts/modified_protein_gene_regions.bed",

        r_script = BIN_DIR + "/nanopore_svs_cnas/002b-visualize_nanopore_sv_examples.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/svs_figures/control_nanopore_sv_examples.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"


# exon-specific events
rule exon_nanopore_sv_examples_control:
    input:
        gene_svs_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/control_gene_svs.tsv",
        exon_loci_bed = RES_DIR + "/analysis_sarek/gene_impacts/spec_gene_exons.bed",

        script = BIN_DIR + "/nanopore_svs_cnas/002c-exon_nanopore_sv_examples.py"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/control_exon_nanopore_sv_examples.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --gene_svs_file {input.gene_svs_file} --exon_loci_bed {input.exon_loci_bed} --figure_data_file {output.figure_data_file}"

rule visualize_exon_nanopore_sv_examples_control:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/control_exon_nanopore_sv_examples.tsv",
        figure_stats_file = RES_DIR + "/analysis_sarek/gene_impacts/spec_gene_exons.bed",

        r_script = BIN_DIR + "/nanopore_svs_cnas/002d-visualize_exon_nanopore_sv_examples.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/svs_figures/control_exon_nanopore_sv_examples.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"








