# Alec <PERSON>


###################################
# whole genome CpG correlation overview
###################################

rule correlate_methylation_primary_recurrent:
    input:
        primary_methylation_file = RES_DIR + "/nanopore_evolution/{sample}/{sample}-primary/mod/5mC/bedMethyl/5mC.{sample}-primary_normal.bed.gz",
        recurrent_methylation_file = RES_DIR + "/nanopore_evolution/{sample}/{sample}-primary/mod/5mC/bedMethyl/5mC.{sample}-primary_tumor.bed.gz",

        script = BIN_DIR + "/nanopore_methylation_landscape/001a-correlate_methylation_primary_recurrent.py"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/methyl_overview/correlated_methylation_primary_recurrent_{sample}.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '60G'
        
    shell:
        "{PYTHON} {input.script} --primary_methylation_file {input.primary_methylation_file} --recurrent_methylation_file {input.recurrent_methylation_file} --figure_data_file {output.figure_data_file}"


# faceted methylation correlation plots
rule combine_methylation_correlation_faceted:
    input:
        expand("{res_dir}/analysis_nanopore/methyl_overview/correlated_methylation_primary_recurrent_{sample}.tsv", res_dir = RES_DIR, sample = sample_codes_list),

        script = BIN_DIR + "/nanopore_methylation_landscape/001b-combine_methylation_correlation_faceted.py"

    output:
        figure_stats_file = RES_DIR + "/analysis_nanopore/methyl_overview/combined_methylation_correlation_stats.tsv",
        figure_data_file = RES_DIR + "/analysis_nanopore/methyl_overview/combined_methylation_correlation.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --res_dir {RES_DIR} --figure_stats_file {output.figure_stats_file} --figure_data_file {output.figure_data_file}"

rule visualize_methylation_correlation_faceted_hexbin:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/methyl_overview/combined_methylation_correlation.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/methyl_overview/combined_methylation_correlation_stats.tsv",

        r_script = BIN_DIR + "/nanopore_methylation_landscape/001c-visualize_methylation_correlation_hexbin.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures_methyl_landscape/correlated_methylation_hexbin.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '60G'
        
    shell:
        "{RDEV_SCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"



###################################
# histogram of promoter and CpG methylation
###################################

# histrogram of promoter methylation
rule mean_promoter_methylation:
    input:
        combined_promoter_methylation_file = RES_DIR + "/analysis_nanopore/methyl_overview/promoter_methylation_data.tsv",

        script = BIN_DIR + "/nanopore_methylation_landscape/002a-promoter_methylation_histogram.py"

    output:
        mean_promoter_methylation_by_gene_sample_file = RES_DIR + "/analysis_nanopore/methyl_overview/mean_promoter_methylation_by_gene_sample.tsv",
        primary_recurrent_gene_promoter_methylation_stats_file = RES_DIR + "/analysis_nanopore/methyl_overview/mean_gene_promoter_methylation_stats.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --combined_promoter_methylation_file {input.combined_promoter_methylation_file} --mean_promoter_methylation_by_gene_sample_file {output.mean_promoter_methylation_by_gene_sample_file} --primary_recurrent_gene_promoter_methylation_stats_file {output.primary_recurrent_gene_promoter_methylation_stats_file}"

rule visualize_mean_promoter_methylation:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/methyl_overview/mean_promoter_methylation_by_gene_sample.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/methyl_overview/mean_gene_promoter_methylation_stats.tsv",

        script = BIN_DIR + "/nanopore_methylation_landscape/002b-visualize_mean_promoter_methylation.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures_methyl_landscape/mean_promoter_methylation_by_gene_sample.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"

rule visualize_cpg_promoter_methylation:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/methyl_overview/promoter_methylation_data.tsv",

        script = BIN_DIR + "/nanopore_methylation_landscape/002c-visualize_cpg_promoter_methylation.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures_methyl_landscape/cpg_promoter_methylation_by_sample.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"



###################################
# correlate mean coverage with methylation correlation
###################################

rule associate_coverage_with_correlation:
    input:
        expand("{res_dir}/nanopore_somatic/{sample}/{sample}/qc/coverage/{sample}_tumor.mosdepth.summary.txt", res_dir = RES_DIR, sample = sample_list),
        methylation_correlation_file = RES_DIR + "/analysis_nanopore/methyl_overview/combined_methylation_correlation_stats.tsv",

        script = BIN_DIR + "/nanopore_methylation_landscape/003a-associate_coverage_with_correlation.py"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/methyl_overview/correlation_coverage_association.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/methyl_overview/correlation_coverage_association_stats.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --methylation_correlation_file {input.methylation_correlation_file} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file}"

rule visualize_coverage_correlation_associations:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/methyl_overview/correlation_coverage_association.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/methyl_overview/correlation_coverage_association_stats.tsv",

        r_script = BIN_DIR + "/nanopore_methylation_landscape/003b-visualize_coverage_correlation_associations.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures_methyl_landscape/visualize_coverage_correlation_associations.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{RDEV_SCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"




###################################
# UMAP and PCA of PCG promoter methylation sites
###################################

rule pcg_promoter_umap:
    input:
        combined_promoter_methylation_file = RES_DIR + "/analysis_nanopore/methyl_overview/promoter_methylation_data.tsv",

        script = BIN_DIR + "/nanopore_methylation_landscape/004a-pcg_promoter_umap.py"

    output:
        pcg_promoter_umap_pca_file = RES_DIR + "/analysis_nanopore/methyl_overview/pcg_promoter_umap_pca.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --combined_promoter_methylation_file {input.combined_promoter_methylation_file} --pcg_promoter_umap_pca_file {output.pcg_promoter_umap_pca_file}"

rule visualize_pcg_promoter_umap:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/methyl_overview/pcg_promoter_umap_pca.tsv",

        script = BIN_DIR + "/nanopore_methylation_landscape/004b-visualize_pcg_promoter_umap.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures_methyl_landscape/pcg_promoter_umap.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"









