# Alec Ba<PERSON>cheli - <EMAIL>


#####################
# compare mutations to GLASS
#####################

rule compare_mutation_counts_glass:
    input:
        glass_mutation_summary_stats_file = RAW_DATA_DIR + "/glass/glass_mutation_freqs.tsv",
        glass_clinical_summary_stats_file = RAW_DATA_DIR + "/glass/glass_clinical_data.tsv",
        rlgs_mutation_snv_file = RES_DIR + "/analysis_sarek/_figure_data/001-combined_vcfs_VEP.tsv",

        script = BIN_DIR + "/sarek_wgs/003a-compare_mutation_counts_glass.py"

    output:
        figure_data_file = RES_DIR + "/analysis_sarek/glass_mutations_comparison/003-compare_mutation_counts_glass.tsv",
        figure_stats_file = RES_DIR + "/analysis_sarek/glass_mutations_comparison/003-compare_mutation_counts_glass_stats.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --glass_mutation_summary_stats_file {input.glass_mutation_summary_stats_file} --glass_clinical_summary_stats_file {input.glass_clinical_summary_stats_file} --rlgs_mutation_summary_stats_file {input.rlgs_mutation_snv_file} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file}"

rule compare_mutation_counts_glass_visualize:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/glass_mutations_comparison/003-compare_mutation_counts_glass.tsv",
        figure_stats_file = RES_DIR + "/analysis_sarek/glass_mutations_comparison/003-compare_mutation_counts_glass_stats.tsv",

        r_script = BIN_DIR + "/sarek_wgs/003b-compare_mutation_counts_glass_visualize.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/003-compare_mutation_counts_glass_visualize.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"

rule compare_mutation_freqs_paired_glass_visualize:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/glass_mutations_comparison/003-compare_mutation_counts_glass.tsv",

        r_script = BIN_DIR + "/sarek_wgs/003c-compare_mutation_freqs_paired_glass_visualize.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/003-compare_mutation_freqs_paired_glass_visualize.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"



#####################
# summarize cgc variants
#####################

rule summarize_cgc_snvs_indels:
    input:
        vcf_expanded_file = RES_DIR + "/analysis_sarek/_figure_data/001-combined_vcfs_VEP_expanded.tsv",
        ensembl_gff3_file = REF_DATA_DIR + "/ensembl_Homo_sapiens.GRCh38.113.chr.gff3",

        script = BIN_DIR + "/sarek_wgs/001b-summarize_cgc_snvs_indels.py"

    output:
        vcf_translated_subset_file = RES_DIR + "/analysis_sarek/vcf_figure_data/001-vcf_vep_translated_subset.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --vcf_expanded_file {input.vcf_expanded_file} --ensembl_gff3_file {input.ensembl_gff3_file} --vcf_translated_subset_file {output.vcf_translated_subset_file}"


# visualize the impacts on cgc genes
rule oncoprint_cgc_genes:
    input:
        vcf_translated_subset_file = RES_DIR + "/analysis_sarek/vcf_figure_data/001-vcf_vep_translated_subset.tsv",

        script = BIN_DIR + "/sarek_wgs/001c-oncoprint_cgc_genes.py"

    output:
        cgc_gene_muations_file = RES_DIR + "/analysis_sarek/vcf_figure_data/oncoprint_cgc_genes.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --vcf_translated_subset_file {input.vcf_translated_subset_file} --cgc_gene_muations_file {output.cgc_gene_muations_file}"

rule visualize_oncoprint_cgc_genes:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/vcf_figure_data/oncoprint_cgc_genes.tsv",

        script = BIN_DIR + "/sarek_wgs/001d-visualize_oncoprint_cgc_genes.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/oncoprint_cgc_genes.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"


# visualize automatic oncoprint on all genes
rule oncoprint_all_genes:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/_figure_data/001-combined_vcfs_VEP.tsv",

        r_script = BIN_DIR + "/sarek_wgs/001e-oncoprint_cgc_genes.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/001-oncoprint_all_genes.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"




#####################
# summarize nucleotide variants overview
#####################

rule summarize_nucleotide_mutations:
    input:
        maf_file = RES_DIR + "/analysis_sarek/_figure_data/001-combined_vcfs_VEP.tsv",

        script = BIN_DIR + "/sarek_wgs/002a-summarize_nucleotide_mutations.py"

    output:
        mutation_summary_file = RES_DIR + "/analysis_sarek/vcf_figure_data/002-summarize_nucleotide_mutations.tsv",
        mutation_summary_file_stats = RES_DIR + "/analysis_sarek/vcf_figure_data/002-summarize_nucleotide_mutations_stats.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --maf_file {input.maf_file} --mutation_summary_file {output.mutation_summary_file} --mutation_summary_file_stats {output.mutation_summary_file_stats}"

rule summarize_nucleotide_mutations_visualize:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/vcf_figure_data/002-summarize_nucleotide_mutations.tsv",
        figure_stats_file = RES_DIR + "/analysis_sarek/vcf_figure_data/002-summarize_nucleotide_mutations_stats.tsv",

        r_script = BIN_DIR + "/sarek_wgs/002b-summarize_nucleotide_mutations.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/002-summarize_nucleotide_mutations.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"



# rule summarize_nucleotide_indels:
#     input:
#         figure_data_file = RES_DIR + "/analysis_wgs/vcf_figure_data/002-summarize_nucleotide_mutations.tsv",
#         figure_stats_file = RES_DIR + "/analysis_wgs/vcf_figure_data/002-summarize_nucleotide_mutations_stats.tsv",

#         script = BIN_DIR + "/sarek_wgs/002c-summarize_nucleotide_mutations.py"

#     output:
#         figure_file = RES_DIR + "/analysis_sarek/_figures/002-summarize_nucleotide_mutations.pdf"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"


#####################
# COSMIC CGC overview
#####################

# visualize amino-acid level modifications
rule generate_maf_format:
    input:
        expanded_vcf_file = RES_DIR + "/analysis_sarek/_figure_data/001-combined_vcfs_VEP_expanded.tsv",

        script = BIN_DIR + "/sarek_wgs/002c-generate_maf_format.py"

    output:
        maf_format_file = RES_DIR + "/analysis_sarek/vcf_figure_data/002-generate_maf_format.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --expanded_vcf_file {input.expanded_vcf_file} --maf_format_file {output.maf_format_file}"

rule visualize_maf:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/vcf_figure_data/002-generate_maf_format.tsv",

        r_script = BIN_DIR + "/sarek_wgs/002d-visualize_maf.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/002-visualize_maf.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r_dev/bin/Rscript {input.r_script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"


# summarize CGC mutations by amino acid position
rule overlapping_cgc_mutations:
    input:
        maf_summary_file = RES_DIR + "/analysis_sarek/vcf_figure_data/002-generate_maf_format.tsv",
        cosmic_snv_file = REF_DATA_DIR + "/cosmic_cgc/Cosmic_MutantCensus_v100_GRCh38.tsv.gz",

        script = BIN_DIR + "/sarek_wgs/002e-overlapping_cgc_mutations.py"

    output:
        cosmic_stats_file = RES_DIR + "/analysis_sarek/vcf_figure_data/002-overlapping_cgc_mutations.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --maf_summary_file {input.maf_summary_file} --cosmic_snv_file {input.cosmic_snv_file} --cosmic_stats_file {output.cosmic_stats_file}"

rule visualize_overlapping_cgc_mutations:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/vcf_figure_data/002-overlapping_cgc_mutations.tsv",

        script = BIN_DIR + "/sarek_wgs/002f-visualize_overlapping_cgc_mutations.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/002-visualize_overlapping_cgc_mutations.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"


# summarize coding mutations not using MAF tools
rule summarize_coding_impacts_of_snvs_indels:
    input:
        maf_summary_file = RES_DIR + "/analysis_sarek/vcf_figure_data/002-generate_maf_format.tsv",

        script = BIN_DIR + "/sarek_wgs/002g-summarize_protein_impacts_of_snvs_indels.py"

    output:
        protein_impact_summary_file = RES_DIR + "/analysis_sarek/vcf_figure_data/protein_impact_summary.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --maf_summary_file {input.maf_summary_file} --protein_impact_summary_file {output.protein_impact_summary_file}"

rule visualize_coding_impacts_of_snvs_indels:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/vcf_figure_data/protein_impact_summary.tsv",

        script = BIN_DIR + "/sarek_wgs/002h-visualize_coding_impacts_of_snvs_indels.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/coding_impacts_of_snvs_indels.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"








#####################
# differences in mutational processes between primary-recurrent tumors
#####################

# calculate nucleotide mutation differences stats
rule paired_nucleotide_mutation_stats:
    input:
        mutation_summary_file = RES_DIR + "/analysis_sarek/vcf_figure_data/002-summarize_nucleotide_mutations.tsv",

        script = BIN_DIR + "/sarek_wgs/006a-paired_nucleotide_mutation_stats.py"

    output:
        nucleotide_paired_mutation_file = RES_DIR + "/analysis_sarek/sigprofiler/paired_nucleotide_mutation.tsv",
        nucleotide_paired_mutation_stats_file = RES_DIR + "/analysis_sarek/sigprofiler/paired_nucleotide_mutation_stats.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --mutation_summary_file {input.mutation_summary_file} --nucleotide_paired_mutation_file {output.nucleotide_paired_mutation_file} --nucleotide_paired_mutation_stats_file {output.nucleotide_paired_mutation_stats_file}"

rule visualize_paired_nucleotide_mutation_stats:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/sigprofiler/paired_nucleotide_mutation.tsv",

        script = BIN_DIR + "/sarek_wgs/006b-visualize_paired_nucleotide_mutation_stats.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/visualize_paired_nucleotide_mutation_stats.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"






