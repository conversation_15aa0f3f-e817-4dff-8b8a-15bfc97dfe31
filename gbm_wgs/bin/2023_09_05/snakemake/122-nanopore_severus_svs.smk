# Alec <PERSON>cheli - <EMAIL>


###################################
# SV processing and gene impacts
###################################

rule severus_process_nanopore_svs:
    input:
        expand("{res_dir}/nanopore_somatic/{sample}/{sample}.wf-somatic-sv.vcf.gz", res_dir = RES_DIR, sample = sample_list, tumor = tumor_types),

        script = BIN_DIR + "/nanopore_svs_cnas/005a-process_severus_nanopore_svs.py"

    output:
        combined_sv_vcf_file = RES_DIR + "/analysis_nanopore/svs/processed_nanopore_svs.tsv"

    resources:
        threads = 10,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --combined_sv_vcf_file {output.combined_sv_vcf_file} --threads {resources.threads}"


rule severus_convert_nanopore_sv_to_bed:
    input:
        combined_sv_vcf_file = RES_DIR + "/analysis_nanopore/svs/processed_nanopore_svs.tsv",

        script = BIN_DIR + "/nanopore_svs_cnas/001b-convert_nanopore_sv_to_bed.py"

    output:
        sv_bed_file = RES_DIR + "/analysis_nanopore/svs/processed_nanopore_svs.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --combined_sv_vcf_file {input.combined_sv_vcf_file} --sv_bed_file {output.sv_bed_file}"


rule severus_intersect_svs_genes:
    input:
        protein_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions.bed",
        sv_bed_file = RES_DIR + "/analysis_nanopore/svs/processed_nanopore_svs.bed"

    output:
        intersected_bed_file = RES_DIR + "/analysis_nanopore/svs/sv_protein_coding_genes_intersected.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.protein_bed_file} -b <(tail -n +2 {input.sv_bed_file}) -wa -wb > {output.intersected_bed_file}"""




###################################
# visualize SV summaries
###################################

rule severus_subset_to_gene_svs:
    input:
        intersected_bed_file = RES_DIR + "/analysis_nanopore/svs/sv_protein_coding_genes_intersected.tsv",

        script = BIN_DIR + "/nanopore_svs_cnas/001c-subset_to_gene_svs.py"

    output:
        gene_svs_file = RES_DIR + "/analysis_nanopore/svs/subset_to_gene_svs.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --intersected_bed_file {input.intersected_bed_file} --gene_svs_file {output.gene_svs_file}"

rule severus_visualize_svs_oncoprint:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/svs/subset_to_gene_svs.tsv",

        script = BIN_DIR + "/nanopore_svs_cnas/001d-svs_oncoprint.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/svs_figures/svs_oncoprint.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"




###################################
# visualize key gene SVs
###################################

# gene-specific events
rule severus_gene_nanopore_sv_examples:
    input:
        gene_svs_file = RES_DIR + "/analysis_nanopore/svs/subset_to_gene_svs.tsv",
        gene_loci_bed = RES_DIR + "/analysis_sarek/gene_impacts/modified_protein_gene_regions.bed",

        script = BIN_DIR + "/nanopore_svs_cnas/002a-subset_to_gene_svs.py"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/svs/sub_genic_nanopore_sv_examples.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --gene_svs_file {input.gene_svs_file} --gene_loci_bed {input.gene_loci_bed} --figure_data_file {output.figure_data_file}"

rule severus_visualize_nanopore_sv_examples:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/svs/sub_genic_nanopore_sv_examples.tsv",
        figure_stats_file =RES_DIR + "/analysis_sarek/gene_impacts/modified_protein_gene_regions.bed",

        r_script = BIN_DIR + "/nanopore_svs_cnas/002b-visualize_nanopore_sv_examples.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/svs_figures/visualize_nanopore_sv_examples_severus.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"


# exon-specific events
rule severus_exon_nanopore_sv_examples:
    input:
        gene_svs_file = RES_DIR + "/analysis_nanopore/svs/subset_to_gene_svs.tsv",
        exon_loci_bed = RES_DIR + "/analysis_sarek/gene_impacts/spec_gene_exons.bed",

        script = BIN_DIR + "/nanopore_svs_cnas/002c-exon_nanopore_sv_examples.py"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/svs/exon_nanopore_sv_examples.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --gene_svs_file {input.gene_svs_file} --exon_loci_bed {input.exon_loci_bed} --figure_data_file {output.figure_data_file}"

rule severus_visualize_exon_nanopore_sv_examples:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/svs/exon_nanopore_sv_examples.tsv",
        figure_stats_file =RES_DIR + "/analysis_sarek/gene_impacts/spec_gene_exons.bed",

        r_script = BIN_DIR + "/nanopore_svs_cnas/002d-visualize_exon_nanopore_sv_examples.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/svs_figures/visualize_exon_nanopore_sv_examples_severus.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"








