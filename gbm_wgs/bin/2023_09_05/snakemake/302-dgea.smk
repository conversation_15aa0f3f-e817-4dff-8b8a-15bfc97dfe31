# Alec Ba<PERSON>cheli


###################################
# pre-process rna-seq data
###################################

# subset to protein-coding genes
rule rna_subset_protein_coding:
    input:
        counts_file = RES_DIR + "/analysis_rna/star_salmon/salmon.merged.{data_type}.tsv",
        differential_methylation_file = RES_DIR + "/analysis_nanopore/methylation/dpm_merged_pvalues-paired_ttest_p_value.tsv",

        script = BIN_DIR + "/rnaseq_interpretation/002a-rna_subset_protein_coding.py"

    output:
        pcg_counts_file = RES_DIR + "/analysis_rna/star_salmon/pcg_salmon.merged.{data_type}.tsv",

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{PYTHON} {input.script} --counts_file {input.counts_file} --differential_methylation_file {input.differential_methylation_file} --pcg_counts_file {output.pcg_counts_file}"



###################################
# differential expression analysis
###################################

# dgea
rule paired_edger_dgea:
    input:
        counts_file = RES_DIR + "/analysis_rna/star_salmon/pcg_salmon.merged.{data_type}.tsv",

        rscript = BIN_DIR + "/rnaseq_interpretation/002b-edger_paired.R"

    output:
        dgea_file = RES_DIR + "/analysis_rna/dgea/{data_type}-edger_paired.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{DGEA_RSCRIPT} {input.rscript} --counts_file {input.counts_file} --dgea_file {output.dgea_file}"

rule paired_deseq2_dgea:
    input:
        counts_file = RES_DIR + "/analysis_rna/star_salmon/pcg_salmon.merged.{data_type}.tsv",

        rscript = BIN_DIR + "/rnaseq_interpretation/002c-deseq2_paired.R"

    output:
        dgea_file = RES_DIR + "/analysis_rna/dgea/{data_type}-deseq2_paired.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{DGEA_RSCRIPT} {input.rscript} --counts_file {input.counts_file} --dgea_file {output.dgea_file}"



###################################
# visualize dgea results
###################################

# visualize dgea
rule volcano_plot_rnaseq:
    input:
        expand("{res_dir}/analysis_rna/dgea/{data_type}-{dgea_analysis_types}.tsv", res_dir = RES_DIR, data_type = rnaseq_data_types, dgea_analysis_types = dgea_analysis_types),

        script = BIN_DIR + "/rnaseq_interpretation/003a-volcano_plot.py"

    output:
        figure_data_file = RES_DIR + "/analysis_rna/_figure_data/002-volcano_plot_rnaseq.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --figure_data_file {output.figure_data_file}"

rule visualize_volcano_plot_rnaseq:
    input:
        figure_data_file = RES_DIR + "/analysis_rna/_figure_data/002-volcano_plot_rnaseq.tsv",

        script = BIN_DIR + "/rnaseq_interpretation/003b-visualize_volcano_plot_rnaseq.R"

    output:
        figure_file = RES_DIR + "/analysis_rna/_figures/002-visualize_volcano_plot_rnaseq.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"


# viualize sample-specific differences
rule dgea_barplots:
    input:
        dgea_file = RES_DIR + "/analysis_rna/_figure_data/002-volcano_plot_rnaseq.tsv",
        gene_expression_file = RES_DIR + "/analysis_rna/star_salmon/pcg_salmon.merged.gene_counts_scaled.tsv",

        script = BIN_DIR + "/rnaseq_interpretation/003c-dgea_barplots.py"

    output:
        figure_data_file = RES_DIR + "/analysis_rna/dgea/dgea_barplots_expression_data.tsv",
        figure_stats_file = RES_DIR + "/analysis_rna/dgea/dgea_barplots_stats.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{PYTHON} {input.script} --dgea_file {input.dgea_file} --gene_expression_file {input.gene_expression_file} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file}"

rule visualize_dgea_barplots:
    input:
        figure_data_file = RES_DIR + "/analysis_rna/dgea/dgea_barplots_expression_data.tsv",
        figure_stats_file = RES_DIR + "/analysis_rna/dgea/dgea_barplots_stats.tsv",

        script = BIN_DIR + "/rnaseq_interpretation/003d-visualize_dgea_barplots.R"

    output:
        figure_file = RES_DIR + "/analysis_rna/_figures/002-visualize_dgea_barplots.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"









