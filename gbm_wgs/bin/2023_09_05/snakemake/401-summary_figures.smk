# Alec <PERSON>cheli - <EMAIL>


###################################
# project summary figures
###################################

rule clinical_sequencing_summary_figure:
    input:
        clinical_file = REF_DATA_DIR + "/clinical_data/clinical_data.tsv",
        sequencing_details_file = REF_DATA_DIR + "/clinical_data/sequencing_data.tsv",
        tumor_data_file = REF_DATA_DIR + "/clinical_data/tumor_data.tsv",
        ploidy_purity_file = RES_DIR + "/analysis_sarek/consensus_cnas/111-summarize_tumor_ploidy_purity.tsv",
        transcription_subtype_file = RES_DIR + "/analysis_rna/_figure_data/010-process_molecular_subtypes_stats.tsv",

        script = BIN_DIR + "/overview_analysis/001a-clinical_sequencing_summary_figure.py",
        r_script = BIN_DIR + "/overview_analysis/001b-clinical_sequencing_summary_figure.R"

    output:
        figure_data_file = RES_DIR + "/analysis_overview/_figure_data/001-clinical_sequencing_summary_figure.tsv",

        figure_file = RES_DIR + "/analysis_overview/_figures/001-clinical_sequencing_summary_figure.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --clinical_file {input.clinical_file} --sequencing_details_file {input.sequencing_details_file} --tumor_data_file {input.tumor_data_file} --ploidy_purity_file {input.ploidy_purity_file} --transcription_subtype_file {input.transcription_subtype_file} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_file {output.figure_file}"



rule patient_treatment_setup:
    input:
        treatment_file = REF_DATA_DIR + "/clinical_data/rlgs_treatments.tsv",

        script = BIN_DIR + "/overview_analysis/002a-clinical_sequencing_summary_figure.py"

    output:
        figure_data_file = REF_DATA_DIR + "/clinical_data/rlgs_treatments_modified.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --treatment_file {input.treatment_file} --figure_data_file {output.figure_data_file}"

rule patient_treatment_summary_figure:
    input:
        figure_data_file = REF_DATA_DIR + "/clinical_data/rlgs_treatments_modified.tsv",

        script = BIN_DIR + "/overview_analysis/002b-clinical_sequencing_summary_figure.R"

    output:
        figure_file = RES_DIR + "/analysis_overview/_figures/patient_treatment_summary_figure.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"




###################################
# combined mutational information
###################################


rule combine_mutational_information:
    input:
        nanopore_svs_pcg_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/subset_to_gene_svs.tsv",
        sarek_cnas_pcg_file = RES_DIR + "/analysis_sarek/gene_impacts/combined_pcg_cnas.bed",
        sarek_snvs_pcg_file = RES_DIR + "/analysis_sarek/vcf_figure_data/oncoprint_cgc_genes.tsv",

        script = BIN_DIR + "/overview_analysis/003a-combine_mutational_information.py"

    output:
        figure_data_file = RES_DIR + "/analysis_overview/_figure_data/combined_mutational_information.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --nanopore_svs_pcg_file {input.nanopore_svs_pcg_file} --sarek_cnas_pcg_file {input.sarek_cnas_pcg_file} --sarek_snvs_pcg_file {input.sarek_snvs_pcg_file} --figure_data_file {output.figure_data_file}"

rule combined_oncoprint:
    input:
        figure_data_file = RES_DIR + "/analysis_overview/_figure_data/combined_mutational_information.tsv",

        script = BIN_DIR + "/overview_analysis/003b-combined_oncoprint.R"

    output:
        figure_file = RES_DIR + "/analysis_overview/_figures/combined_oncoprint.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"









