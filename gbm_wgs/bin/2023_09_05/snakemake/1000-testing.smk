# Alec Ba<PERSON>cheli - <EMAIL>


#####################
# look for ecDNA
#####################

rule extract_chr7_region:
    input:
        regions_bed_gz = RES_DIR + "/sarek_wgs/{patient}-{tumor}/reports/mosdepth/{patient}-{tumor}/{patient}-{tumor}-{tumor}.recal.regions.bed.gz"

    output:
        region_bed = RES_DIR + "/analysis_sarek/coverage_analysis/{patient}-{tumor}.bed"

    params:
        region = "chr7:54000000-56000000"

    resources:
        threads = 3,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'

    shell:
        """
        zcat {input.regions_bed_gz} | awk '$1 == "chr7" && $2 >= 54000000 && $3 <= 56000000' > {output.region_bed}
        """

# rule test_process_tumor_svs:
#     input:
#         expand("{res_dir}/nanopore_human_variation/{sample}/{sample}.wf_sv.vcf.gz", res_dir = RES_DIR, sample = ['RLGS2-primary', 'RLGS2-recurrent']),

#         script = BIN_DIR + "/nanopore_svs_cnas/020a-test_process_tumor_svs.py"

#     output:
#         combined_sv_vcf_file = RES_DIR + "/analysis_nanopore/tmp/tumor_only_svs.tsv"

#     resources:
#         threads = 3,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '10G'
        
#     shell:
#         "{PYTHON} {input.script} --results_dir {RES_DIR} --combined_sv_vcf_file {output.combined_sv_vcf_file} --threads {resources.threads}"

# rule test_convert_tumor_sv_to_bed:
#     input:
#         combined_sv_vcf_file = RES_DIR + "/analysis_nanopore/tmp/tumor_only_svs.tsv",

#         script = BIN_DIR + "/nanopore_svs_cnas/001b-convert_nanopore_sv_to_bed.py"

#     output:
#         sv_bed_file = RES_DIR + "/analysis_nanopore/tmp/tumor_only_svs.bed"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --combined_sv_vcf_file {input.combined_sv_vcf_file} --sv_bed_file {output.sv_bed_file}"






