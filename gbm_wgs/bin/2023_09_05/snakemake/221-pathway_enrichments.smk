# Alec Ba<PERSON>cheli - <EMAIL>




###################################
# SNVs, CNAs, DGEA, methlyation
###################################

rule activepathway_snv_cna_rna_methylation_preparation:
    input:
        dgea_file = RES_DIR + "/analysis_rna/_figure_data/002-volcano_plot_rnaseq.tsv",
        # differential_methylation_file = RES_DIR + "/analysis_nanopore/methylation/dpm_merged_pvalues-paired_ttest_p_value.tsv",
        differential_methylation_file = RES_DIR + "/analysis_nanopore/diff_promoter_methylation/finalized_pvalues_fcs-wilcoxon_p_value.tsv",
        differential_sv_file = RES_DIR + "/analysis_nanopore/svs/nanopore_wgs_cnas_svs_combined.tsv",
        # differential_sv_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/nanopore_sv_gene_stats.tsv",
        # differential_cna_file = RES_DIR + "/analysis_sarek/consensus_cnas/cna_gene_continuous_stats.tsv",
        differential_snv_file = RES_DIR + "/analysis_sarek/vcf_figure_data/snv_nb_differentially_mutated.tsv",

        script = BIN_DIR + "/pathway_enrichments/006a-activepathway_snv_cna_rna_methylation_preparation.py"

    output:
        pvalue_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna_snv/input_pvalues.tsv",
        fc_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna_snv/input_fcs.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{PYTHON} {input.script} --dgea_file {input.dgea_file} --differential_methylation_file {input.differential_methylation_file} --differential_snv_file {input.differential_snv_file} --differential_sv_file {input.differential_sv_file} --pvalue_file {output.pvalue_file} --fc_file {output.fc_file}"
        # "{PYTHON} {input.script} --dgea_file {input.dgea_file} --differential_methylation_file {input.differential_methylation_file} --differential_cna_file {input.differential_cna_file} --differential_snv_file {input.differential_snv_file} --differential_sv_file {input.differential_sv_file} --pvalue_file {output.pvalue_file} --fc_file {output.fc_file}"


rule ActivePathways_snv_cna_rna_methylation:
    input:
        pvalue_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna_snv/input_pvalues.tsv",
        fc_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna_snv/input_fcs.tsv",

        gmt_file = RAW_DATA_DIR + "/networks/gprofiler_gobp_reactome-06_06_2023.gmt",

        script = BIN_DIR + "/pathway_enrichments/006b-ActivePathways_snv_cna_rna_methylation.R"

    output:
        enriched_pathways_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna_snv/enriched_pathways.csv",
        pathways_text_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna_snv/ap_pathways.txt"

    params:
        cytoscape_file_prefix = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna_snv/ap_"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{AP_RSCRIPT} {input.script} --pvalue_file {input.pvalue_file} --fc_file {input.fc_file} --gmt_file {input.gmt_file} --cytoscape_file_prefix {params.cytoscape_file_prefix} --enriched_pathways_file {output.enriched_pathways_file}"



rule pathway_gene_contributions_snv_cna_rna_methylation:
    input:
        pvalue_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna_snv/input_pvalues.tsv",
        fc_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna_snv/input_fcs.tsv",

        enriched_pathways_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna_snv/enriched_pathways.csv",
        pathways_text_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna_snv/ap_pathways.txt",

        cgc_file = REF_DATA_DIR + "/cgc_v100_17102024.tsv",

        script = BIN_DIR + "/pathway_enrichments/007a-pathway_gene_contributions_snv_cna_rna_methylation.py"

    output:
        enriched_pathway_gene_contributions_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna_snv/pathway_contributions.tsv",
        gene_contributions_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna_snv/gene_contributions.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{PYTHON} {input.script} --pvalue_file {input.pvalue_file} --fc_file {input.fc_file} --enriched_pathways_file {input.enriched_pathways_file} --pathways_text_file {input.pathways_text_file} --cgc_file {input.cgc_file} --enriched_pathway_gene_contributions_file {output.enriched_pathway_gene_contributions_file} --gene_contributions_file {output.gene_contributions_file}"

rule visualize_pathway_contributions_snv_cna_rna_methylation:
    input:
        enriched_pathway_gene_contributions_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna_snv/pathway_contributions.tsv",
        gene_contributions_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna_snv/gene_contributions.tsv",

        script = BIN_DIR + "/pathway_enrichments/007b-visualize_pathway_contributions_snv_cna_rna_methylation.R"

    output:
        figure_file = RES_DIR + "/analysis_rna/_figures/visualize_pathway_contributions_snv_cna_rna_methylation.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{RSCRIPT} {input.script} --enriched_pathway_gene_contributions_file {input.enriched_pathway_gene_contributions_file} --gene_contributions_file {input.gene_contributions_file} --figure_file {output.figure_file}"






# ###################################
# # CNAs, DGEA, methylation
# ###################################

# rule activepathway_rna_cna_methylation_preparation:
#     input:
#         dgea_file = RES_DIR + "/analysis_rna/_figure_data/002-volcano_plot_rnaseq.tsv",
#         differential_methylation_file = RES_DIR + "/analysis_nanopore/diff_promoter_methylation/finalized_pvalues_fcs-wilcoxon_p_value.tsv",
#         differential_cna_file = RES_DIR + "/analysis_nanopore/svs/nanopore_wgs_cnas_svs_merged.tsv",

#         script = BIN_DIR + "/pathway_enrichments/005a-activepathway_rna_cna_methylation_preparation.py"

#     output:
#         pvalue_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna/input_pvalues.tsv",
#         fc_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna/input_fcs.tsv"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'

#     shell:
#         "{PYTHON} {input.script} --dgea_file {input.dgea_file} --differential_methylation_file {input.differential_methylation_file} --differential_cna_file {input.differential_cna_file} --pvalue_file {output.pvalue_file} --fc_file {output.fc_file}"


# rule ActivePathways_rna_cna_methylation:
#     input:
#         pvalue_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna/input_pvalues.tsv",
#         fc_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna/input_fcs.tsv",

#         gmt_file = RAW_DATA_DIR + "/networks/gprofiler_gobp_reactome-06_06_2023.gmt",

#         script = BIN_DIR + "/pathway_enrichments/005b-ActivePathways_rna_cna_methylation.R"

#     output:
#         enriched_pathways_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna/enriched_pathways.csv"

#     params:
#         cytoscape_file_prefix = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna/ap_"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'

#     shell:
#         "{AP_RSCRIPT} {input.script} --pvalue_file {input.pvalue_file} --fc_file {input.fc_file} --gmt_file {input.gmt_file} --cytoscape_file_prefix {params.cytoscape_file_prefix} --enriched_pathways_file {output.enriched_pathways_file}"




# ###################################
# # DGEA, methylation
# ###################################

# rule activepathway_rna_methylation_preparation:
#     input:
#         dgea_file = RES_DIR + "/analysis_rna/_figure_data/002-volcano_plot_rnaseq.tsv",
#         differential_methylation_file = RES_DIR + "/analysis_nanopore/diff_promoter_methylation/finalized_pvalues_fcs-wilcoxon_p_value.tsv",

#         script = BIN_DIR + "/pathway_enrichments/004a-activepathway_rna_methylation_preparation.py"

#     output:
#         pvalue_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation/input_pvalues.tsv",
#         fc_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation/input_fcs.tsv"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'

#     shell:
#         "{PYTHON} {input.script} --dgea_file {input.dgea_file} --differential_methylation_file {input.differential_methylation_file} --pvalue_file {output.pvalue_file} --fc_file {output.fc_file}"


# rule ActivePathways_rna_methylation:
#     input:
#         pvalue_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation/input_pvalues.tsv",
#         fc_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation/input_fcs.tsv",

#         gmt_file = RAW_DATA_DIR + "/networks/gprofiler_gobp_reactome-06_06_2023.gmt",

#         script = BIN_DIR + "/pathway_enrichments/004b-ActivePathways_rna_methylation.R"

#     output:
#         enriched_pathways_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation/enriched_pathways.csv"

#     params:
#         cytoscape_file_prefix = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation/ap_"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'

#     shell:
#         "{AP_RSCRIPT} {input.script} --pvalue_file {input.pvalue_file} --fc_file {input.fc_file} --gmt_file {input.gmt_file} --cytoscape_file_prefix {params.cytoscape_file_prefix} --enriched_pathways_file {output.enriched_pathways_file}"





