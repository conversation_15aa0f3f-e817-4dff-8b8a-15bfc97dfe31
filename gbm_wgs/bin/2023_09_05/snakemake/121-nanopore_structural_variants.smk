# Alec Ba<PERSON>cheli - <EMAIL>


#####################
# calculate SVs in common between tumors and control
#####################

rule extract_passing_svs_vcf_human_variation:
    input:
        sv_vcf_file = RES_DIR + "/nanopore_human_variation/{sample}/{sample}.wf_sv.vcf.gz"

    output:
        filtered_sv_vcf_file = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/{sample}-passing_svs.vcf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'

    # we want to allow for PASS and GT (genotype) because valid genotypes will be removed using Jasmine with the blood control, but we want to include all variants
    shell:
        """zcat {input.sv_vcf_file} | grep "#" > {output.filtered_sv_vcf_file} && zcat {input.sv_vcf_file} | awk '($7 == "PASS" || $7 == "GT") && $6 > 50' >> {output.filtered_sv_vcf_file}"""


# setup tumor vs blood jasmine parameters
rule setup_jasmine_input_files_primary_vs_control:
    input:
        primary_filtered_sv_vcf_file = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/{patient}-primary-passing_svs.vcf",
        blood_filtered_sv_vcf_file = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/{patient}-blood-passing_svs.vcf"

    output:
        jasmine_p_vs_b_txt = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/{patient}-primary_vs_blood.txt"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """echo {input.primary_filtered_sv_vcf_file} > {output.jasmine_p_vs_b_txt} && echo {input.blood_filtered_sv_vcf_file} >> {output.jasmine_p_vs_b_txt}"""

rule setup_jasmine_input_files_recurrent_vs_control:
    input:
        recurrent_filtered_sv_vcf_file = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/{patient}-recurrent-passing_svs.vcf",
        blood_filtered_sv_vcf_file = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/{patient}-blood-passing_svs.vcf"

    output:
        jasmine_r_vs_b_txt = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/{patient}-recurrent_vs_blood.txt"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """echo {input.recurrent_filtered_sv_vcf_file} > {output.jasmine_r_vs_b_txt} && echo {input.blood_filtered_sv_vcf_file} >> {output.jasmine_r_vs_b_txt}"""


# find the common SVs in between the tumors and the blood controls
rule run_jasmine_primary_vs_control:
    input:
        jasmine_p_vs_b_txt = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/{patient}-primary_vs_blood.txt"

    output:
        jasmine_p_vs_b_vcf = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/{patient}-primary_vs_blood-intercepted.vcf"

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/jasmine file_list={input.jasmine_p_vs_b_txt} out_file={output.jasmine_p_vs_b_vcf} --max_dist_linear 0.7"""

rule run_jasmine_recurrent_vs_control:
    input:
        jasmine_r_vs_b_txt = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/{patient}-recurrent_vs_blood.txt"

    output:
        jasmine_r_vs_b_vcf = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/{patient}-recurrent_vs_blood-intercepted.vcf"

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/jasmine file_list={input.jasmine_r_vs_b_txt} out_file={output.jasmine_r_vs_b_vcf} --max_dist_linear 0.7"""


# filter the jasmine results
rule filter_jasmine_merged_results:
    input:
        processed_vcf_file = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/{patient}-{tumor}_vs_blood-intercepted.vcf",
        severus_vcf_file = RES_DIR + "/nanopore_somatic/{patient}-{tumor}/{patient}-{tumor}.wf-somatic-sv.vcf.gz",

        script = BIN_DIR + "/nanopore_svs_cnas/003a-filter_jasmine_merged_results.py"

    output:
        nanopore_somatic_svs_file = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/somatic_svs/{patient}-{tumor}_vs_blood-somatic.vcf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --processed_vcf_file {input.processed_vcf_file} --severus_vcf_file {input.severus_vcf_file} --nanopore_somatic_svs_file {output.nanopore_somatic_svs_file}"


###################################
# SV processing and gene impacts
###################################
rule process_nanopore_svs:
    input:
        expand("{res_dir}/analysis_nanopore/processing_non_somatic_svs/somatic_svs/{patient}-{tumor}_vs_blood-somatic.vcf", res_dir = RES_DIR, patient = sample_codes_list, tumor = tumor_types),

        script = BIN_DIR + "/nanopore_svs_cnas/001a-process_nanopore_svs.py"

    output:
        combined_sv_vcf_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/processed_nanopore_svs.tsv"

    resources:
        threads = 10,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --combined_sv_vcf_file {output.combined_sv_vcf_file} --threads {resources.threads}"

rule convert_nanopore_sv_to_bed:
    input:
        combined_sv_vcf_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/processed_nanopore_svs.tsv",

        script = BIN_DIR + "/nanopore_svs_cnas/001b-convert_nanopore_sv_to_bed.py"

    output:
        sv_bed_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/processed_nanopore_svs.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --combined_sv_vcf_file {input.combined_sv_vcf_file} --sv_bed_file {output.sv_bed_file}"


rule intersect_svs_genes:
    input:
        protein_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions.bed",
        sv_bed_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/processed_nanopore_svs.bed"

    output:
        intersected_bed_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/sv_protein_coding_genes_intersected.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.protein_bed_file} -b <(tail -n +2 {input.sv_bed_file}) -wa -wb > {output.intersected_bed_file}"""




###################################
# visualize SV summaries
###################################

rule subset_to_gene_svs:
    input:
        intersected_bed_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/sv_protein_coding_genes_intersected.tsv",

        script = BIN_DIR + "/nanopore_svs_cnas/001c-subset_to_gene_svs.py"

    output:
        gene_svs_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/subset_to_gene_svs.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --intersected_bed_file {input.intersected_bed_file} --gene_svs_file {output.gene_svs_file}"

rule visualize_svs_oncoprint:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/subset_to_gene_svs.tsv",

        script = BIN_DIR + "/nanopore_svs_cnas/001d-svs_oncoprint.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/svs_figures/svs_oncoprint.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"




###################################
# visualize key gene SVs
###################################

# gene-specific events
rule gene_nanopore_sv_examples:
    input:
        gene_svs_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/subset_to_gene_svs.tsv",
        gene_loci_bed = RES_DIR + "/analysis_sarek/gene_impacts/modified_protein_gene_regions.bed",

        script = BIN_DIR + "/nanopore_svs_cnas/002a-subset_to_gene_svs.py"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/sub_genic_nanopore_sv_examples.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --gene_svs_file {input.gene_svs_file} --gene_loci_bed {input.gene_loci_bed} --figure_data_file {output.figure_data_file}"

rule visualize_nanopore_sv_examples:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/sub_genic_nanopore_sv_examples.tsv",
        figure_stats_file =RES_DIR + "/analysis_sarek/gene_impacts/modified_protein_gene_regions.bed",

        r_script = BIN_DIR + "/nanopore_svs_cnas/002b-visualize_nanopore_sv_examples.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/svs_figures/visualize_nanopore_sv_examples.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"


# exon-specific events
rule exon_nanopore_sv_examples:
    input:
        gene_svs_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/subset_to_gene_svs.tsv",
        exon_loci_bed = RES_DIR + "/analysis_sarek/gene_impacts/spec_gene_exons.bed",

        script = BIN_DIR + "/nanopore_svs_cnas/002c-exon_nanopore_sv_examples.py"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/exon_nanopore_sv_examples.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --gene_svs_file {input.gene_svs_file} --exon_loci_bed {input.exon_loci_bed} --figure_data_file {output.figure_data_file}"

rule visualize_exon_nanopore_sv_examples:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/non_somatic_svs/exon_nanopore_sv_examples.tsv",
        figure_stats_file = RES_DIR + "/analysis_sarek/gene_impacts/spec_gene_exons.bed",

        r_script = BIN_DIR + "/nanopore_svs_cnas/002d-visualize_exon_nanopore_sv_examples.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/svs_figures/visualize_exon_nanopore_sv_examples.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"








