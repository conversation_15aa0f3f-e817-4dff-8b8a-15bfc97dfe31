# Alec Ba<PERSON>cheli - <EMAIL>



#####################
# canopy
#####################

# prepare copy number data for major and minor CNs
rule prepare_cna_intersection_files:
    input:
        sample_1_file = RES_DIR + '/sarek_wgs/{patient}-primary/variant_calling/ascat/{patient}-primary-primary_vs_{patient}-primary-blood/{patient}-primary-primary_vs_{patient}-primary-blood.cnvs.txt',
        sample_2_file = RES_DIR + '/sarek_wgs/{patient}-recurrent/variant_calling/ascat/{patient}-recurrent-recurrent_vs_{patient}-recurrent-blood/{patient}-recurrent-recurrent_vs_{patient}-recurrent-blood.cnvs.txt',

        script = BIN_DIR + "/sarek_evolution/003a-prepare_cna_intersection_files.py"

    output:
        primary_cna_file = RES_DIR + "/analysis_sarek/canopy/patient_cnas_merged/{patient}-primary_ascat_cnas.tsv",
        recurrent_cna_file = RES_DIR + "/analysis_sarek/canopy/patient_cnas_merged/{patient}-recurrent_ascat_cnas.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --sample_1_file {input.sample_1_file} --sample_2_file {input.sample_2_file} --primary_cna_file {output.primary_cna_file} --recurrent_cna_file {output.recurrent_cna_file}"

rule unique_cnas_per_patient:
    input:
        primary_cna_file = RES_DIR + "/analysis_sarek/canopy/patient_cnas_merged/{patient}-primary_ascat_cnas.tsv",
        recurrent_cna_file = RES_DIR + "/analysis_sarek/canopy/patient_cnas_merged/{patient}-recurrent_ascat_cnas.tsv"

    output:
        ascat_cna_merged = RES_DIR + "/analysis_sarek/canopy/patient_cnas_merged/{patient}-ascat_cnas.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.primary_cna_file} -b {input.recurrent_cna_file} -wa -wb > {output.ascat_cna_merged}"


# prepare canopy data for ALL MUTATIONS
rule canopy_input_prep:
    input:
        input_vcf = RES_DIR + "/analysis_sarek/_figure_data/001-combined_vcfs_VEP.tsv",
        cna_file = RES_DIR + "/analysis_sarek/canopy/patient_cnas_merged/{patient}-ascat_cnas.tsv",

        script = BIN_DIR + "/sarek_evolution/003b-canopy_input_prep.py"

    output:
        canopy_total_depth_X_file = RES_DIR + "/analysis_sarek/canopy/input/canopy-{patient}_total_depth_X.tsv",
        canopy_mutant_allele_depth_R_file = RES_DIR + "/analysis_sarek/canopy/input/canopy-{patient}_mutant_allele_depth_R.tsv",

        canopy_major_cna_WM_file = RES_DIR + "/analysis_sarek/canopy/input/canopy-{patient}_major_cna_WM.tsv",
        canopy_minor_cna_Wm_file = RES_DIR + "/analysis_sarek/canopy/input/canopy-{patient}_minor_cna_Wm.tsv",

        canopy_overlapping_cna_C_file = RES_DIR + "/analysis_sarek/canopy/input/canopy-{patient}_overlapping_cna_C.tsv",
        canopy_snv_affected_cna_Y_file = RES_DIR + "/analysis_sarek/canopy/input/canopy-{patient}_snv_affected_cna_Y.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --input_vcf {input.input_vcf} --cna_file {input.cna_file} --results_dir {RES_DIR} --canopy_total_depth_X_file {output.canopy_total_depth_X_file} --canopy_mutant_allele_depth_R_file {output.canopy_mutant_allele_depth_R_file} --canopy_major_cna_WM_file {output.canopy_major_cna_WM_file} --canopy_minor_cna_Wm_file {output.canopy_minor_cna_Wm_file} --canopy_overlapping_cna_C_file {output.canopy_overlapping_cna_C_file} --canopy_snv_affected_cna_Y_file {output.canopy_snv_affected_cna_Y_file}"

rule canopy:
    input:
        canopy_total_depth_X_file = RES_DIR + "/analysis_sarek/canopy/input/canopy-{patient}_total_depth_X.tsv",
        canopy_mutant_allele_depth_R_file = RES_DIR + "/analysis_sarek/canopy/input/canopy-{patient}_mutant_allele_depth_R.tsv",

        canopy_major_cna_WM_file = RES_DIR + "/analysis_sarek/canopy/input/canopy-{patient}_major_cna_WM.tsv",
        canopy_minor_cna_Wm_file = RES_DIR + "/analysis_sarek/canopy/input/canopy-{patient}_minor_cna_Wm.tsv",

        canopy_overlapping_cna_C_file = RES_DIR + "/analysis_sarek/canopy/input/canopy-{patient}_overlapping_cna_C.tsv",
        canopy_snv_affected_cna_Y_file = RES_DIR + "/analysis_sarek/canopy/input/canopy-{patient}_snv_affected_cna_Y.tsv",

        script = BIN_DIR + "/sarek_evolution/003c-canopy.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/canopy/canopy_figures/canopy-{patient}_canopy_figures.pdf",
        r_data_file = RES_DIR + "/analysis_sarek/canopy/canopy_r_results/canopy-{patient}.Rdata"

    params:
        tree_figure_file = RES_DIR + "/analysis_sarek/canopy/canopy_figures/canopy-{patient}_canopy_tree_figure.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '21:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/canopy2/bin/Rscript {input.script} --canopy_total_depth_X_file {input.canopy_total_depth_X_file} --canopy_mutant_allele_depth_R_file {input.canopy_mutant_allele_depth_R_file} --canopy_major_cna_WM_file {input.canopy_major_cna_WM_file} --canopy_minor_cna_Wm_file {input.canopy_minor_cna_Wm_file} --canopy_overlapping_cna_C_file {input.canopy_overlapping_cna_C_file} --canopy_snv_affected_cna_Y_file {input.canopy_snv_affected_cna_Y_file} --figure_file {output.figure_file} --tree_figure_file {params.tree_figure_file} --r_data_file {output.r_data_file}"



# prepare canopy data for SUBSET MUTATIONS
rule canopy_input_prep_subset_mutations:
    input:
        input_vcf = RES_DIR + "/analysis_sarek/_figure_data/001-combined_vcfs_VEP.tsv",
        cna_file = RES_DIR + "/analysis_sarek/canopy/patient_cnas_merged/{patient}-ascat_cnas.tsv",

        script = BIN_DIR + "/sarek_evolution/003d-canopy_input_prep_subset_mutations.py"

    output:
        canopy_total_depth_X_file = RES_DIR + "/analysis_sarek/canopy/subset_mutations_input/canopy-{patient}_total_depth_X.tsv",
        canopy_mutant_allele_depth_R_file = RES_DIR + "/analysis_sarek/canopy/subset_mutations_input/canopy-{patient}_mutant_allele_depth_R.tsv",

        canopy_major_cna_WM_file = RES_DIR + "/analysis_sarek/canopy/subset_mutations_input/canopy-{patient}_major_cna_WM.tsv",
        canopy_minor_cna_Wm_file = RES_DIR + "/analysis_sarek/canopy/subset_mutations_input/canopy-{patient}_minor_cna_Wm.tsv",

        canopy_overlapping_cna_C_file = RES_DIR + "/analysis_sarek/canopy/subset_mutations_input/canopy-{patient}_overlapping_cna_C.tsv",
        canopy_snv_affected_cna_Y_file = RES_DIR + "/analysis_sarek/canopy/subset_mutations_input/canopy-{patient}_snv_affected_cna_Y.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --input_vcf {input.input_vcf} --cna_file {input.cna_file} --results_dir {RES_DIR} --canopy_total_depth_X_file {output.canopy_total_depth_X_file} --canopy_mutant_allele_depth_R_file {output.canopy_mutant_allele_depth_R_file} --canopy_major_cna_WM_file {output.canopy_major_cna_WM_file} --canopy_minor_cna_Wm_file {output.canopy_minor_cna_Wm_file} --canopy_overlapping_cna_C_file {output.canopy_overlapping_cna_C_file} --canopy_snv_affected_cna_Y_file {output.canopy_snv_affected_cna_Y_file}"

# prepare annotations for mutations for visualization in Canopy
rule mutation_annotations_for_canopy:
    input:
        annotated_mutations_file = RES_DIR + "/analysis_sarek/vcf_figure_data/001-vcf_vep_translated_subset.tsv",
        cgc_file = REF_DATA_DIR + "/cgc_v100_17102024.tsv",

        script = BIN_DIR + "/sarek_evolution/003e-mutation_annotations_for_canopy.py"

    output:
        subset_annotated_mutations_file = RES_DIR + "/analysis_sarek/canopy/subset_mutations_input/{patient}-mutations_annotated.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --annotated_mutations_file {input.annotated_mutations_file} --cgc_file {input.cgc_file} --subset_annotated_mutations_file {output.subset_annotated_mutations_file}"


rule canopy_subset_mutations:
    input:
        canopy_total_depth_X_file = RES_DIR + "/analysis_sarek/canopy/subset_mutations_input/canopy-{patient}_total_depth_X.tsv",
        canopy_mutant_allele_depth_R_file = RES_DIR + "/analysis_sarek/canopy/subset_mutations_input/canopy-{patient}_mutant_allele_depth_R.tsv",

        canopy_major_cna_WM_file = RES_DIR + "/analysis_sarek/canopy/subset_mutations_input/canopy-{patient}_major_cna_WM.tsv",
        canopy_minor_cna_Wm_file = RES_DIR + "/analysis_sarek/canopy/subset_mutations_input/canopy-{patient}_minor_cna_Wm.tsv",

        canopy_overlapping_cna_C_file = RES_DIR + "/analysis_sarek/canopy/subset_mutations_input/canopy-{patient}_overlapping_cna_C.tsv",
        canopy_snv_affected_cna_Y_file = RES_DIR + "/analysis_sarek/canopy/subset_mutations_input/canopy-{patient}_snv_affected_cna_Y.tsv",

        subset_annotated_mutations_file = RES_DIR + "/analysis_sarek/canopy/subset_mutations_input/{patient}-mutations_annotated.tsv",

        script = BIN_DIR + "/sarek_evolution/003f-canopy_subset_mutations.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/canopy/canopy_figures/canopy-{patient}-subset_mutations-canopy_figures.pdf"

    params:
        tree_figure_file = RES_DIR + "/analysis_sarek/canopy/canopy_figures/canopy-{patient}-subset_mutations-canopy_tree_figure.pdf",
        r_data_file = RES_DIR + "/analysis_sarek/canopy/canopy_r_results/canopy-{patient}-subset_mutations.Rdata",

        figure_directory = RES_DIR + "/analysis_sarek/canopy/canopy_figures"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '21:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/canopy2/bin/Rscript {input.script} \
--canopy_total_depth_X_file {input.canopy_total_depth_X_file} --canopy_mutant_allele_depth_R_file {input.canopy_mutant_allele_depth_R_file} \
--canopy_major_cna_WM_file {input.canopy_major_cna_WM_file} --canopy_minor_cna_Wm_file {input.canopy_minor_cna_Wm_file} \
--canopy_overlapping_cna_C_file {input.canopy_overlapping_cna_C_file} --canopy_snv_affected_cna_Y_file {input.canopy_snv_affected_cna_Y_file} \
--subset_annotated_mutations_file {input.subset_annotated_mutations_file} \
--figure_file {output.figure_file} --tree_figure_file {params.tree_figure_file} --figure_directory {params.figure_directory} --r_data_file {params.r_data_file}"





#####################
# pyclone-vi
#####################

# use ASCAT CNA estimates based on major and minor copy number
rule combine_ascat_CNAs:
    input:
        # expand("{res_dir}/analysis_sarek/consensus_cnas/{sample}/001-{sample}-{tumor}_ascat_cnas.tsv", res_dir = RES_DIR, sample = sample_codes_list, tumor = tumor_types),
        expand("{res_dir}/sarek_wgs/{sample}-{tumor}/variant_calling/ascat/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood.cnvs.txt", res_dir = RES_DIR, sample = sample_codes_list, tumor = tumor_types),

        script = BIN_DIR + "/sarek_evolution/001a-combine_ascat_CNAs.py"

    output:
        cna_file = RES_DIR + "/analysis_sarek/pyclone/input/ascat_cnas_combined.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --cna_file {output.cna_file}"


rule pyclone_vi_input_prep:
    input:
        input_vcf = RES_DIR + "/analysis_sarek/_figure_data/001-combined_vcfs_VEP.tsv",
        cna_file = RES_DIR + "/analysis_sarek/pyclone/input/ascat_cnas_combined.tsv",
        ploidy_purity_file = RES_DIR + "/analysis_sarek/consensus_cnas/111-summarize_tumor_ploidy_purity.tsv",

        script = BIN_DIR + "/sarek_evolution/001b-pyclone_vi_preparation.py"

    output:
        pyclone_input_file = RES_DIR + "/analysis_sarek/pyclone/input/pyclone_vi_input-{patient}.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --input_vcf {input.input_vcf} --cna_file {input.cna_file} --ploidy_purity_file {input.ploidy_purity_file} --pyclone_input_file {output.pyclone_input_file}"


rule pyclone_vi_analysis_part1:
    input:
        pyclone_input_file = RES_DIR + "/analysis_sarek/pyclone/input/pyclone_vi_input-{patient}.tsv"

    output:
        pyclone_results_file = RES_DIR + "/analysis_sarek/pyclone/output/pyclone_vi-{patient}.h5"

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/pyclone-vi"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "pyclone-vi fit -i {input.pyclone_input_file} -o {output.pyclone_results_file}"


rule pyclone_vi_analysis_part2:
    input:
        pyclone_results_file = RES_DIR + "/analysis_sarek/pyclone/output/pyclone_vi-{patient}.h5"

    output:
        pyclone_results_final_file = RES_DIR + "/analysis_sarek/pyclone/output/pyclone_vi_results-{patient}.tsv"

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/pyclone-vi"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '100G'
        
    shell:
        "pyclone-vi write-results-file -i {input.pyclone_results_file} -o {output.pyclone_results_final_file}"


rule combine_pyclone_results:
    input:
        expand("{res_dir}/analysis_sarek/pyclone/output/pyclone_vi_results-{patient}.tsv", res_dir = RES_DIR, patient = sample_codes_list),

        script = BIN_DIR + "/sarek_evolution/001c-combine_pyclone_results.py"

    output:
        combined_pyclone_results_file = RES_DIR + "/analysis_sarek/pyclone/output/pyclone_vi-combined_results.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --combined_pyclone_results_file {output.combined_pyclone_results_file}"



# rule pyclone_vi_visualize_results:
#     input:
#         expand("{res_dir}/analysis_wgs/pyclone_vi/{sample}_mutect2-pyclone_vi.tsv", res_dir = RES_DIR, sample = wgs_sample_list),

#         script = BIN_DIR + "/wgs_interpretations/004c-pyclone_visualization.py",
#         r_script = BIN_DIR + "/wgs_interpretations/004d-pyclone_visualization.R"

#     output:
#         figure_data_file = RES_DIR + "/analysis_wgs/_figure_data/004-pyclone_visualization.tsv",
#         figure_stats_file = RES_DIR + "/analysis_wgs/_figure_data/004-pyclone_visualization_stats.tsv",

#         figure_file = RES_DIR + "/analysis_wgs/_figures/004-pyclone_visualization.pdf"

#     params:
#         pyclone_dir = RES_DIR + "/analysis_wgs/pyclone_vi"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --pyclone_dir {params.pyclone_dir} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file} --r_script {input.r_script} --figure_file {output.figure_file}"




