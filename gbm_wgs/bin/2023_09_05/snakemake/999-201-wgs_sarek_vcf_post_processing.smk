# Alec Bahcheli - <EMAIL>

#####################
# consensus variants pre-processing and general variant processing tools
#####################

# create a unique directory for each sample
rule create_res_dir_wgs_vcfs:
    output:
        null_file = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/null.txt"

    params:
        output_dir = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'
        
    shell:
        "mkdir -p {params.output_dir} && touch {output.null_file}"

# merge strelka indels and snvs
rule merge_strelka_vcfs:
    input:
        RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}-{tumor}_{annotation_tool}/null.txt",

        snv_vcf_index = RES_DIR + "/sarek_wgs/{sample}-{tumor}/annotation/strelka/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood.strelka.somatic_snvs_{annotation_tool}.ann.vcf.gz.tbi",
        indel_vcf_index = RES_DIR + "/sarek_wgs/{sample}-{tumor}/annotation/strelka/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood.strelka.somatic_indels_{annotation_tool}.ann.vcf.gz.tbi",

        snv_vcf = RES_DIR + "/sarek_wgs/{sample}-{tumor}/annotation/strelka/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood.strelka.somatic_snvs_{annotation_tool}.ann.vcf.gz",
        indel_vcf = RES_DIR + "/sarek_wgs/{sample}-{tumor}/annotation/strelka/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood.strelka.somatic_indels_{annotation_tool}.ann.vcf.gz"

    output:
        merged_vcf = temp(RES_DIR + "/sarek_wgs/{sample}-{tumor}/annotation/strelka/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood.strelka.somatic_snvs_indels_{annotation_tool}.ann.vcf.gz")

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools concat --allow-overlaps -O v {input.snv_vcf} {input.indel_vcf} | /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bgzip -c > {output.merged_vcf}"

# index vcfs
rule index_vcf:
    input:
        input_vcf = RES_DIR + "/analysis_sarek/{variant_dir}/{variant_file}.vcf.gz"

    output:
        vcf_index = temp(RES_DIR + "/analysis_sarek/{variant_dir}/{variant_file}.vcf.gz.tbi")

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools index -t {input.input_vcf}"""

#####################
# variant filtering
#####################

# filter freebayes
rule filter_freebayes:
    input:
        input_vcf = RES_DIR + "/sarek_wgs/{sample}-{tumor}/annotation/freebayes/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood.freebayes_{annotation_tool}.ann.vcf.gz"

    output:
        filtered_vcf = temp(RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}-{tumor}_{annotation_tool}/{sample}-{tumor}.freebayes_{annotation_tool}.ann.filtered.vcf.gz")

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    params:
        minimum_quality_score = 80
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools view -O z -i 'QUAL > {params.minimum_quality_score}' {input.input_vcf} -o {output.filtered_vcf}"

# filter strelka 
rule filter_strelka:
    input:
        input_vcf = RES_DIR + "/sarek_wgs/{sample}-{tumor}/annotation/strelka/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood.strelka.somatic_snvs_indels_{annotation_tool}.ann.vcf.gz"

    output:
        filtered_vcf = temp(RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}-{tumor}_{annotation_tool}/{sample}-{tumor}.strelka_{annotation_tool}.ann.filtered.vcf.gz")

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools view -O z -i 'FILTER="PASS"' {input.input_vcf} -o {output.filtered_vcf}"""

# filter mutect2 
rule filter_mutect2:
    input:
        input_vcf = RES_DIR + "/sarek_wgs/{sample}-{tumor}/annotation/mutect2/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood.mutect2.filtered_{annotation_tool}.ann.vcf.gz"

    output:
        filtered_vcf = temp(RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}-{tumor}_{annotation_tool}/{sample}-{tumor}.mutect2_{annotation_tool}.ann.filtered.vcf.gz")

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools view -O z -i 'FILTER="PASS"' {input.input_vcf} -o {output.filtered_vcf}"""


# normalize variant calls
rule normalize_vcfs:
    input:
        genome_fasta = RES_DIR + "/analysis_sarek/consensus_vcfs/Homo_sapiens_assembly38.fasta",

        input_vcf_index = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/{sample}.{source}_{annotation_tool}.ann.filtered.vcf.gz.tbi",
        input_vcf = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/{sample}.{source}_{annotation_tool}.ann.filtered.vcf.gz"

    output:
        normalized_vcf = temp(RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/{sample}.{source}_{annotation_tool}.ann.filtered.normalized.vcf.gz")

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools norm -O z -f {input.genome_fasta} -o {output.normalized_vcf} {input.input_vcf}"


#####################
# merge VCFs
#####################

# obtain consensus variants
rule variant_consensus:
    input:
        RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/null.txt",

        mutect2_vcf_index = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/{sample}.mutect2_{annotation_tool}.ann.filtered.normalized.vcf.gz.tbi",
        strelka_vcf_index = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/{sample}.strelka_{annotation_tool}.ann.filtered.normalized.vcf.gz.tbi",
        freebayes_vcf_index = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/{sample}.freebayes_{annotation_tool}.ann.filtered.normalized.vcf.gz.tbi",

        mutect2_vcf = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/{sample}.mutect2_{annotation_tool}.ann.filtered.normalized.vcf.gz",
        strelka_vcf = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/{sample}.strelka_{annotation_tool}.ann.filtered.normalized.vcf.gz",
        freebayes_vcf = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/{sample}.freebayes_{annotation_tool}.ann.filtered.normalized.vcf.gz"

    output:
        temp(RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/0000.vcf.gz"),
        temp(RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/0001.vcf.gz"),
        temp(RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/0002.vcf.gz")

    params:
        outdir = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools isec -O z -n =2 -p {params.outdir} {input.mutect2_vcf} {input.strelka_vcf} {input.freebayes_vcf}"""

# rename freebayes samples
rule rename_freebayes_samples:
    input:
        freebayes_vcf = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}-{tumor}_{annotation_tool}/0001.vcf.gz",

    output:
        renamed_vcf = temp(RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}-{tumor}_{annotation_tool}/0003.vcf.gz")

    params:
        sample = "{sample}",
        tumor = "{tumor}"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "zcat {input.freebayes_vcf} | sed 's/^#CHROM\tPOS\tID\tREF\tALT\tQUAL\tFILTER\tINFO\tFORMAT\tNORMAL\tTUMOR/#CHROM\tPOS\tID\tREF\tALT\tQUAL\tFILTER\tINFO\tFORMAT\t{params.sample}-{params.tumor}_{params.sample}-{params.tumor}-blood\t{params.sample}-{params.tumor}_{params.sample}-{params.tumor}-{params.tumor}/' | /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bgzip > {output.renamed_vcf}"

# rename strelka samples
rule rename_strelka_samples:
    input:
        freebayes_vcf = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}-{tumor}_{annotation_tool}/0002.vcf.gz",

    output:
        renamed_vcf = temp(RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}-{tumor}_{annotation_tool}/0004.vcf.gz")

    params:
        sample = "{sample}",
        tumor = "{tumor}"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "zcat {input.freebayes_vcf} | sed 's/^#CHROM\tPOS\tID\tREF\tALT\tQUAL\tFILTER\tINFO\tFORMAT\tNORMAL\tTUMOR/#CHROM\tPOS\tID\tREF\tALT\tQUAL\tFILTER\tINFO\tFORMAT\t{params.sample}-{params.tumor}_{params.sample}-{params.tumor}-blood\t{params.sample}-{params.tumor}_{params.sample}-{params.tumor}-{params.tumor}/' | /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bgzip > {output.renamed_vcf}"

# merge vcf
rule merge_vcfs:
    input:
        mutect2_vcf_index = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/0000.vcf.gz.tbi",
        strelka_vcf_index = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/0003.vcf.gz.tbi",
        freebayes_vcf_index = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/0002.vcf.gz.tbi",

        mutect2_vcf = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/0000.vcf.gz",
        strelka_vcf = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/0003.vcf.gz",
        freebayes_vcf = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}/0002.vcf.gz"

    output:
        merged_vcf = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}_combined.vcf.gz"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bcftools concat -o {output.merged_vcf} --allow-overlaps -O z {input.mutect2_vcf} {input.strelka_vcf} {input.freebayes_vcf}"


#####################
# process merged vcfs
#####################

# process vcfs into a single df
rule combine_final_vcfs:
    input:
        expand("{res_dir}/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}_combined.vcf.gz", res_dir = RES_DIR, annotation_tool = 'VEP', sample = sample_list),

        cgc_file = REF_DATA_DIR + "/cgc_v100_17102024.tsv",

        script = BIN_DIR + "/sarek_wgs/001a-process_and_combine_vcfs.py"

    output:
        expanded_vcf_file = RES_DIR + "/analysis_sarek/_figure_data/001-combined_vcfs_{annotation_tool}_expanded.tsv",
        figure_data_file = RES_DIR + "/analysis_sarek/_figure_data/001-combined_vcfs_{annotation_tool}.tsv"

    params:
        vcf_csv = ",".join(expand("{res_dir}/analysis_sarek/consensus_vcfs/{sample}_{annotation_tool}_combined.vcf.gz", res_dir = RES_DIR, annotation_tool = 'VEP', sample = sample_list))

    resources:
        threads = 12,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'
        
    shell:
        "{PYTHON} {input.script} --vcf_csv {params.vcf_csv} --cgc_file {input.cgc_file} --expanded_vcf_file {output.expanded_vcf_file} --figure_data_file {output.figure_data_file} --threads {resources.threads}"





