# <PERSON>
# Generate 1kb-level coverage maps for DNA-seq, RNA-seq, and methylation

###################################
# Coverage bed file generation
###################################

# Process DNA-seq coverage to 1kb resolution and calculate log2(tumor/normal)
rule process_dna_coverage:
    input:
        normal_bed = RES_DIR + "sarek_wgs/{sample}/reports/mosdepth/{sample}-blood/{sample}-blood.md.regions.bed.gz",
        tumor_bed = RES_DIR + "sarek_wgs/{sample}/reports/mosdepth/{sample}-primary/{sample}-primary.md.regions.bed.gz",
        script = BIN_DIR + "/coverage_to_bed/501a-process_dna_coverage.py"
    
    output:
        coverage_bed = RES_DIR + "/coverage_maps/dna/{sample}_dna_coverage_1kb.bed"
    
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'
    
    shell:
        "{PYTHON} {input.script} --normal_bed {input.normal_bed} --tumor_bed {input.tumor_bed} --output_bed {output.coverage_bed}"

# Process methylation data to 1kb resolution
rule process_methylation_coverage:
    input:
        primary_bed = RES_DIR + "/nanopore_evolution/{sample}/{sample}-primary/mod/5mC/bedMethyl/5mC.{sample}-primary_normal.bed.gz",
        recurrent_bed = RES_DIR + "/nanopore_evolution/{sample}/{sample}-primary/mod/5mC/bedMethyl/5mC.{sample}-primary_tumor.bed.gz",
        script = BIN_DIR + "/coverage_to_bed/501b-process_methylation_coverage.py"
    
    output:
        methylation_bed = RES_DIR + "/coverage_maps/methylation/{sample}_methylation_coverage_1kb.bed"
    
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'
    
    shell:
        "{PYTHON} {input.script} --primary_bed {input.primary_bed} --recurrent_bed {input.recurrent_bed} --output_bed {output.methylation_bed}"

# Convert RNA-seq bigWig to 1kb resolution bed files
rule process_rnaseq_coverage:
    input:
        forward_bw = RES_DIR + "/rnaseq/star_salmon/bigwig/{sample}.forward.bigWig",
        reverse_bw = RES_DIR + "/rnaseq/star_salmon/bigwig/{sample}.reverse.bigWig",
        script = BIN_DIR + "/coverage_to_bed/501c-process_rnaseq_coverage.py"
    
    output:
        rnaseq_bed = RES_DIR + "/coverage_maps/rnaseq/{sample}_rnaseq_coverage_1kb.bed"
    
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'
    
    shell:
        "{PYTHON} {input.script} --forward_bw {input.forward_bw} --reverse_bw {input.reverse_bw} --output_bed {output.rnaseq_bed}"

# Combine all coverage maps for a sample
rule combine_coverage_maps:
    input:
        dna_bed = RES_DIR + "/coverage_maps/dna/{sample}_dna_coverage_1kb.bed",
        methylation_bed = RES_DIR + "/coverage_maps/methylation/{sample}_methylation_coverage_1kb.bed",
        rnaseq_bed = RES_DIR + "/coverage_maps/rnaseq/{sample}_rnaseq_coverage_1kb.bed",
        script = BIN_DIR + "/coverage_to_bed/501d-combine_coverage_maps.py"
    
    output:
        combined_bed = RES_DIR + "/coverage_maps/combined/{sample}_combined_coverage_1kb.bed"
    
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'
    
    shell:
        "{PYTHON} {input.script} --dna_bed {input.dna_bed} --methylation_bed {input.methylation_bed} --rnaseq_bed {input.rnaseq_bed} --output_bed {output.combined_bed}"

# Visualize coverage maps
rule visualize_coverage_maps:
    input:
        combined_bed = RES_DIR + "/coverage_maps/combined/{sample}_combined_coverage_1kb.bed",
        script = BIN_DIR + "/coverage_to_bed/501e-visualize_coverage_maps.R"
    
    output:
        coverage_plot = RES_DIR + "/coverage_maps/_figures/{sample}_coverage_maps.pdf"
    
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'
    
    shell:
        "{RSCRIPT} {input.script} --combined_bed {input.combined_bed} --output_plot {output.coverage_plot}"
