# Alec Ba<PERSON>cheli
# Generate 1kb-level coverage maps for DNA-seq, RNA-seq, and methylation

###################################
# Coverage bed file generation
###################################

# Process DNA-seq coverage to 1kb resolution and calculate log2(tumor/normal)
rule process_dna_coverage:
    input:
        normal_bed = RES_DIR + "/sarek_wgs/{sample_code}-{tumor}/reports/mosdepth/{sample_code}-{tumor}-blood/{sample_code}-{tumor}-blood.md.regions.bed.gz",
        tumor_bed = RES_DIR + "/sarek_wgs/{sample_code}-{tumor}/reports/mosdepth/{sample_code}-{tumor}-{tumor}/{sample_code}-{tumor}-{tumor}.md.regions.bed.gz",
        script = BIN_DIR + "/coverage_to_bed/501a-process_dna_coverage.py"
    
    output:
        coverage_bed = RES_DIR + "/coverage_maps/dna/{sample_code}-{tumor}_dna_coverage_1kb.bed"
    
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'
    
    shell:
        "{PYTHON} {input.script} --normal_bed {input.normal_bed} --tumor_bed {input.tumor_bed} --output_bed {output.coverage_bed}"

# Process methylation data to 1kb resolution
rule process_methylation_coverage:
    input:
        primary_bed = RES_DIR + "/nanopore_evolution/{sample_code}/{sample_code}-primary/mod/5mC/bedMethyl/5mC.{sample_code}-primary_normal.bed.gz",
        recurrent_bed = RES_DIR + "/nanopore_evolution/{sample_code}/{sample_code}-primary/mod/5mC/bedMethyl/5mC.{sample_code}-primary_tumor.bed.gz",
        script = BIN_DIR + "/coverage_to_bed/501b-process_methylation_coverage.py"
    
    output:
        primary_output_bed = RES_DIR + "/coverage_maps/methylation/{sample_code}-primary_methylation_coverage_1kb.bed",
        recurrent_output_bed = RES_DIR + "/coverage_maps/methylation/{sample_code}-recurrent_methylation_coverage_1kb.bed"
    
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'
    
    shell:
        "{PYTHON} {input.script} --primary_bed {input.primary_bed} --recurrent_bed {input.recurrent_bed} --primary_output_bed {output.primary_output_bed} --recurrent_output_bed {output.recurrent_output_bed}"

# Convert RNA-seq bigWig to 1kb resolution bed files
rule process_rnaseq_coverage:
    input:
        forward_bw = RES_DIR + "/rnaseq/star_salmon/bigwig/{sample_code}_{tumor}.forward.bigWig",
        reverse_bw = RES_DIR + "/rnaseq/star_salmon/bigwig/{sample_code}_{tumor}.reverse.bigWig",
        script = BIN_DIR + "/coverage_to_bed/501c-process_rnaseq_coverage.py"
    
    output:
        rnaseq_bed = RES_DIR + "/coverage_maps/rnaseq/{sample_code}-{tumor}_rnaseq_coverage_1kb.bed"
    
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
    
    shell:
        "{PYTHON} {input.script} --forward_bw {input.forward_bw} --reverse_bw {input.reverse_bw} --output_bed {output.rnaseq_bed}"





