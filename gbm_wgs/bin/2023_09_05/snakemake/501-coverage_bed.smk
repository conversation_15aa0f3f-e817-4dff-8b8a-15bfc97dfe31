# Alec Ba<PERSON>cheli
# Generate 1kb-level coverage maps for DNA-seq, RNA-seq, and methylation

###################################
# Coverage bed file generation
###################################

# Process DNA-seq coverage to 1kb resolution and calculate log2(tumor/normal)
rule process_dna_coverage:
    input:
        normal_bed = RES_DIR + "/sarek_wgs/{sample_code}-{tumor}/reports/mosdepth/{sample_code}-{tumor}-blood/{sample_code}-{tumor}-blood.md.regions.bed.gz",
        tumor_bed = RES_DIR + "/sarek_wgs/{sample_code}-{tumor}/reports/mosdepth/{sample_code}-{tumor}-{tumor}/{sample_code}-{tumor}-{tumor}.md.regions.bed.gz",
        script = BIN_DIR + "/coverage_to_bed/501a-process_dna_coverage.py"
    
    output:
        coverage_bed = RES_DIR + "/coverage_maps/dna/{sample_code}-{tumor}_dna_coverage_1kb.bed"
    
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '30G'
    
    shell:
        "{PYTHON} {input.script} --normal_bed {input.normal_bed} --tumor_bed {input.tumor_bed} --output_bed {output.coverage_bed}"

# Process methylation data to 1kb resolution
rule process_methylation_coverage:
    input:
        primary_bed = RES_DIR + "/nanopore_evolution/{sample_code}/{sample_code}-primary/mod/5mC/bedMethyl/5mC.{sample_code}-primary_normal.bed.gz",
        recurrent_bed = RES_DIR + "/nanopore_evolution/{sample_code}/{sample_code}-primary/mod/5mC/bedMethyl/5mC.{sample_code}-primary_tumor.bed.gz",
        script = BIN_DIR + "/coverage_to_bed/501b-process_methylation_coverage.py"
    
    output:
        primary_output_bed = RES_DIR + "/coverage_maps/methylation/{sample_code}-primary_methylation_coverage_1kb.bed",
        recurrent_output_bed = RES_DIR + "/coverage_maps/methylation/{sample_code}-recurrent_methylation_coverage_1kb.bed"
    
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '50G'
    
    shell:
        "{PYTHON} {input.script} --primary_bed {input.primary_bed} --recurrent_bed {input.recurrent_bed} --primary_output_bed {output.primary_output_bed} --recurrent_output_bed {output.recurrent_output_bed}"

# Generate RNA-seq coverage using mosdepth
rule generate_rnaseq_coverage_mosdepth:
    input:
        bam = RES_DIR + "/rnaseq/star_salmon/{sample_code}_{tumor}.markdup.sorted.bam"

    output:
        regions_bed = RES_DIR + "/coverage_maps/rnaseq/{sample_code}_{tumor}_mosdepth.regions.bed.gz"

    params:
        output_prefix = RES_DIR + "/coverage_maps/rnaseq/{sample_code}_{tumor}_mosdepth",
        window_size = 1000

    resources:
        threads = 4,
        queue = "all.q",
        jobtime = '2:0:0:0',
        individual_core_memory = '10G'

    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/mosdepth {params.output_prefix} {input.bam} --by {params.window_size} --threads {resources.threads}"

# Convert chromosome names from NC_ to chr format
rule convert_rnaseq_chromosome_names:
    input:
        regions_bed = RES_DIR + "/coverage_maps/rnaseq/{sample_code}_{tumor}_mosdepth.regions.bed.gz",
        script = BIN_DIR + "/coverage_to_bed/501c-convert_chromosome_names.py"

    output:
        rnaseq_bed = RES_DIR + "/coverage_maps/rnaseq/{sample_code}-{tumor}_rnaseq_coverage_1kb.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{PYTHON} {input.script} --input_bed {input.regions_bed} --output_bed {output.rnaseq_bed}"

# Subset coverage files to standard chromosomes and compress
rule subset_and_compress_dna_coverage:
    input:
        coverage_bed = RES_DIR + "/coverage_maps/dna/{sample_code}-{tumor}_dna_coverage_1kb.bed",
        script = BIN_DIR + "/coverage_to_bed/501f-subset_and_compress_coverage.py"

    output:
        compressed_bed = RES_DIR + "/coverage_maps/all_coverage_tracks/{sample_code}-{tumor}_dna_coverage_1kb.bed.gz"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{PYTHON} {input.script} --input_bed {input.coverage_bed} --output_bed {output.compressed_bed}"

# Subset methylation coverage files to standard chromosomes and compress
rule subset_and_compress_methylation_coverage:
    input:
        primary_bed = RES_DIR + "/coverage_maps/methylation/{sample_code}-primary_methylation_coverage_1kb.bed",
        recurrent_bed = RES_DIR + "/coverage_maps/methylation/{sample_code}-recurrent_methylation_coverage_1kb.bed",
        script = BIN_DIR + "/coverage_to_bed/501f-subset_and_compress_coverage.py"

    output:
        primary_compressed = RES_DIR + "/coverage_maps/all_coverage_tracks/{sample_code}-primary_methylation_coverage_1kb.bed.gz",
        recurrent_compressed = RES_DIR + "/coverage_maps/all_coverage_tracks/{sample_code}-recurrent_methylation_coverage_1kb.bed.gz"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{PYTHON} {input.script} --input_bed {input.primary_bed} --output_bed {output.primary_compressed} && "
        "{PYTHON} {input.script} --input_bed {input.recurrent_bed} --output_bed {output.recurrent_compressed}"

# Subset RNA-seq coverage files to standard chromosomes and compress
rule subset_and_compress_rnaseq_coverage:
    input:
        rnaseq_bed = RES_DIR + "/coverage_maps/rnaseq/{sample_code}-{tumor}_rnaseq_coverage_1kb.bed",
        script = BIN_DIR + "/coverage_to_bed/501f-subset_and_compress_coverage.py"

    output:
        compressed_bed = RES_DIR + "/coverage_maps/all_coverage_tracks/{sample_code}-{tumor}_rnaseq_coverage_1kb.bed.gz"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{PYTHON} {input.script} --input_bed {input.rnaseq_bed} --output_bed {output.compressed_bed}"

