# Alec Ba<PERSON>cheli


###################################
# visualize genome coverage data genome-wide
###################################

# Process coverage data to calculate log2(tumor/normal) ratios
rule process_coverage_ratios:
    input:
        tumor_coverage = RES_DIR + "/sarek_wgs/{patient}-{tumor}/reports/mosdepth/{patient}-{tumor}-{tumor}/{patient}-{tumor}-{tumor}.md.regions.bed.gz",
        normal_coverage = RES_DIR + "/sarek_wgs/{patient}-{tumor}/reports/mosdepth/{patient}-{tumor}-blood/{patient}-{tumor}-blood.md.regions.bed.gz",
        script = BIN_DIR + "/coverage_analysis/001a-process_coverage_ratios.py"

    output:
        processed_coverage = RES_DIR + "/coverage_analysis/{patient}-{tumor}_coverage_ratios.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '40G'

    shell:
        "{PYTHON} {input.script} --tumor_coverage {input.tumor_coverage} --normal_coverage {input.normal_coverage} --output_file {output.processed_coverage} --sample_name {wildcards.patient}-{wildcards.tumor}"


# Visualize coverage using KaryoploteR for individual samples
rule visualize_coverage_karyoplot:
    input:
        sample_coverage = RES_DIR + "/coverage_analysis/{patient}-{tumor}_coverage_ratios.tsv",
        script = BIN_DIR + "/coverage_analysis/001b-visualize_coverage_karyoplot.R"

    output:
        figure_file = RES_DIR + "/coverage_analysis/_figures/{patient}-{tumor}_coverage_karyoplot.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '2:0:0:0',
        individual_core_memory = '20G'

    shell:
        "{RSCRIPT} {input.script} --sample_coverage {input.sample_coverage} --figure_file {output.figure_file}"


