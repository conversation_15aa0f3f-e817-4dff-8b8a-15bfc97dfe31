# Alec Ba<PERSON>cheli


###################################
# visualize genome coverage data genome-wide
###################################

# Process coverage data to calculate log2(tumor/normal) ratios
rule process_coverage_ratios:
    input:
        tumor_coverage = RES_DIR + "/sarek_wgs/{patient}-{tumor}/reports/mosdepth/{patient}-{tumor}-{tumor}/{patient}-{tumor}-{tumor}.md.regions.bed.gz",
        normal_coverage = RES_DIR + "/sarek_wgs/{patient}-{tumor}/reports/mosdepth/{patient}-{tumor}-blood/{patient}-{tumor}-blood.md.regions.bed.gz",
        script = BIN_DIR + "/coverage_analysis/001a-process_coverage_ratios.py"

    output:
        processed_coverage = RES_DIR + "/coverage_analysis/{patient}-{tumor}_coverage_ratios.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '40G'

    shell:
        "{PYTHON} {input.script} --tumor_coverage {input.tumor_coverage} --normal_coverage {input.normal_coverage} --output_file {output.processed_coverage} --sample_name {wildcards.patient}-{wildcards.tumor}"


# Visualize coverage using KaryoploteR for individual samples
rule visualize_coverage_karyoplot:
    input:
        sample_coverage = RES_DIR + "/coverage_analysis/{sample}_coverage_ratios.tsv",
        script = BIN_DIR + "/coverage_analysis/001b-visualize_coverage_karyoplot.R"

    output:
        figure_file = RES_DIR + "/coverage_analysis/figures/{sample}_coverage_karyoplot.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '2:0:0:0',
        individual_core_memory = '20G'

    shell:
        "{RSCRIPT} {input.script} --sample_coverage {input.sample_coverage} --figure_file {output.figure_file}"


# Combine all coverage data into a single file
rule combine_coverage_data:
    input:
        expand(RES_DIR + "/coverage_analysis/{sample}_coverage_ratios.tsv", sample=sample_list),
        script = BIN_DIR + "/coverage_analysis/002a-combine_coverage_data.py"

    output:
        combined_coverage = RES_DIR + "/coverage_analysis/all_samples_combined_coverage.tsv"

    params:
        coverage_dir = RES_DIR + "/coverage_analysis"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'

    shell:
        "{PYTHON} {input.script} --coverage_dir {params.coverage_dir} --output_file {output.combined_coverage}"


# Visualize combined coverage data as continuous line plots
rule visualize_combined_coverage_lines:
    input:
        combined_coverage = RES_DIR + "/coverage_analysis/all_samples_combined_coverage.tsv",
        chr_mids = RES_DIR + "/coverage_analysis/all_samples_combined_coverage_chr_mids.tsv",
        script = BIN_DIR + "/coverage_analysis/002b-visualize_coverage_lines.R"

    output:
        figure_file = RES_DIR + "/coverage_analysis/figures/all_samples_coverage_lines.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '2:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{RSCRIPT} {input.script} --combined_coverage {input.combined_coverage} --chr_mids {input.chr_mids} --figure_file {output.figure_file}"


