# Alec Ba<PERSON>cheli - <EMAIL>
# this workflow is based on GATK's best practices: https://gatk.broadinstitute.org/hc/en-us/articles/360035535912-Data-pre-processing-for-variant-discovery
# uses Sarek: https://github.com/nf-core/sarek


###################################
# Create project directories
###################################
# create directory if it does not already exist
rule setup_sarek_wgs:
    input:
        script = BIN_DIR + "/wgs_processing/001a-setup_sarek_wgs.py"

    output:
        expand("{bin_dir}/sarek/{patient}", bin_dir = BIN_DIR, patient = sample_list),
        expand("{bin_dir}/sarek/{patient}.csv", bin_dir = BIN_DIR, patient = sample_list)

    params:
        bin_dir = BIN_DIR + "/sarek",
        results_dir = RES_DIR + "/sarek_wgs"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        "{PYTHON} {input.script} --fastq_dir {FASTQ_DIR} --bin_dir {params.bin_dir} --results_dir {params.results_dir}"






