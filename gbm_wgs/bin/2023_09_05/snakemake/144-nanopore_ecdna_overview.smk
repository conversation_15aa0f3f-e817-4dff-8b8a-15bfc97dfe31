# Alec Bahcheli
# Nanopore ecDNA interpretation pipeline

###################################
# Summarize and visualize Decoil results
###################################

# combine decoil results from all samples
rule combine_decoil_results:
    input:
        summary_files = expand(RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/summary_unique.txt", sample=sample_list),
        bed_files = expand(RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/reconstruct.ecDNA.filtered.unique.bed", sample=sample_list),

        script = BIN_DIR + "/circ_dna_scripts/002a-combine_decoil_results.py"

    output:
        combined_results = RES_DIR + "/analysis_nanopore/ecdna/decoil/combined_decoil_results.tsv",
        summary_results = RES_DIR + "/analysis_nanopore/ecdna/decoil/combined_decoil_results_summary.tsv",
        ecdna_counts = RES_DIR + "/analysis_nanopore/ecdna/decoil/ecdna_counts.tsv"

    params:
        sample_list = ",".join(sample_list)

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --sample_list {params.sample_list} --combined_results {output.combined_results} --summary_results {output.summary_results} --ecdna_counts {output.ecdna_counts}"


# visualize decoil results
rule visualize_decoil_results:
    input:
        summary_results = RES_DIR + "/analysis_nanopore/ecdna/decoil/combined_decoil_results_summary.tsv",
        ecdna_counts = RES_DIR + "/analysis_nanopore/ecdna/decoil/ecdna_counts.tsv",

        script = BIN_DIR + "/circ_dna_scripts/002b-visualize_decoil_results.R"

    output:
        summary_plot = RES_DIR + "/analysis_nanopore/ecdna/_figures/decoil_summary.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '15G'

    shell:
        "{RSCRIPT} {input.script} --summary_results {input.summary_results} --ecdna_counts {input.ecdna_counts}  --summary_plot {output.summary_plot}"


###################################
# ecDNA exon analysis
###################################

# Extract exons from GFF file and convert to BED format
rule extract_exons_from_gff:
    input:
        protein_coding_genes_gff_file = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.gff",
        script = BIN_DIR + "/circ_dna_scripts/003a-process_coding_exons.py"

    output:
        exon_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_exons.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'

    shell:
        "{PYTHON} {input.script} --protein_coding_genes_gff_file {input.protein_coding_genes_gff_file} --protein_coding_bed_file {output.exon_bed_file}"


# Intersect exon BED file with each sample's ecDNA regions using bedtools
rule intersect_ecdna_exons:
    input:
        exon_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_exons.bed",
        ecdna_bed_file = RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/reconstruct.ecDNA.filtered.unique.bed"

    output:
        intersected_file = RES_DIR + "/analysis_nanopore/ecdna/exon_intersections/{sample}_exon_intersections.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'

    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.exon_bed_file} -b {input.ecdna_bed_file} -wa -wb > {output.intersected_file}"""


# Create relative coordinate BED files for each sample
rule create_relative_coordinate_exon_beds:
    input:
        intersection_file = RES_DIR + "/analysis_nanopore/ecdna/exon_intersections/{sample}_exon_intersections.bed",
        ecdna_bed_file = RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/reconstruct.ecDNA.filtered.unique.bed",
        script = BIN_DIR + "/circ_dna_scripts/003c-create_relative_coordinate_beds.py"

    output:
        summary_file = RES_DIR + "/analysis_nanopore/ecdna/exon_intersections/{sample}_exon_relative_coordinates.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'

    shell:
        "{PYTHON} {input.script} --intersection_bed {input.intersection_file} --ecdna_bed {input.ecdna_bed_file} --output_file {output.summary_file}"




###################################
# ecDNA full gene analysis
###################################

# prepare gene bed file
rule add_chr_to_gene_bed_file:
    input:
        gene_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions.bed",
        script = BIN_DIR + "/circ_dna_scripts/003b-process_gene_bed_file.py"

    output:
        gene_bed_file_processed = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions_renamed_chr.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'

    shell:
        "{PYTHON} {input.script} --gene_bed_file {input.gene_bed_file} --gene_bed_file_processed {output.gene_bed_file_processed}"


# Intersect gene BED file with each sample's ecDNA regions using bedtools
rule intersect_ecdna_genes:
    input:
        gene_bed_file_processed = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions_renamed_chr.bed",
        ecdna_bed_file = RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/reconstruct.ecDNA.filtered.unique.bed"

    output:
        intersected_file = RES_DIR + "/analysis_nanopore/ecdna/gene_intersections/{sample}_gene_intersections.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'

    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.gene_bed_file_processed} -b {input.ecdna_bed_file} -wa -wb > {output.intersected_file}"""


# Create relative coordinate BED files for each sample
rule create_relative_coordinate_gene_beds:
    input:
        intersection_file = RES_DIR + "/analysis_nanopore/ecdna/gene_intersections/{sample}_gene_intersections.bed",
        ecdna_bed_file = RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/reconstruct.ecDNA.filtered.unique.bed",
        script = BIN_DIR + "/circ_dna_scripts/003c-create_relative_coordinate_beds.py"

    output:
        summary_file = RES_DIR + "/analysis_nanopore/ecdna/gene_intersections/{sample}_gene_relative_coordinates.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'

    shell:
        "{PYTHON} {input.script} --intersection_bed {input.intersection_file} --ecdna_bed {input.ecdna_bed_file} --output_file {output.summary_file}"


###################################
# Enhancer analysis
###################################

# # Extract enhancers from GFF
# rule extract_enhancers_from_gff:
#     input:
#         gff_file = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.gff",
#         script = BIN_DIR + "/circ_dna_scripts/003d-process_enhancers.py"

#     output:
#         enhancer_bed = RES_DIR + "/analysis_sarek/gene_impacts/enhancers.bed"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '1:0:0:0',
#         individual_core_memory = '10G'

#     shell:
#         "{PYTHON} {input.script} --gff_file {input.gff_file} --enhancer_bed_file {output.enhancer_bed}"


# Intersect enhancer BED file with each sample's ecDNA regions using bedtools
rule intersect_ecdna_enhancers:
    input:
        enhancer_bed_file = REF_DATA_DIR + "/glioblastoma_enhancers.bed",
        ecdna_bed_file = RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/reconstruct.ecDNA.filtered.unique.bed"

    output:
        intersected_file = RES_DIR + "/analysis_nanopore/ecdna/enhancer_intersections/{sample}/enhancer_intersections.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.enhancer_bed_file} -b {input.ecdna_bed_file} -wa -wb > {output.intersected_file}"""


# Create relative coordinate enhancer beds
rule create_relative_coordinate_enhancer_beds:
    input:
        intersection_bed = RES_DIR + "/analysis_nanopore/ecdna/enhancer_intersections/{sample}/enhancer_intersections.bed",
        ecdna_bed = RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/reconstruct.ecDNA.filtered.unique.bed",
        script = BIN_DIR + "/circ_dna_scripts/003f-enhancers_relative_coordinate_beds.py"

    output:
        enhancer_coords_summary = RES_DIR + "/analysis_nanopore/ecdna/enhancer_intersections/{sample}/enhancer_relative_coordinates.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{PYTHON} {input.script} --intersection_bed {input.intersection_bed} --ecdna_bed {input.ecdna_bed} --output_file {output.enhancer_coords_summary}"







###################################
# combine annotations for visualization in snapgene
###################################

# Convert relative coordinate BED files to BED12 format for visualization (combine exons and genes) and create complete annotation file
rule create_complete_annotation_file:
    input:
        exon_summary_file = RES_DIR + "/analysis_nanopore/ecdna/exon_intersections/{sample}_exon_relative_coordinates.tsv",
        gene_summary_file = RES_DIR + "/analysis_nanopore/ecdna/gene_intersections/{sample}_gene_relative_coordinates.tsv",
        enhancer_coords_summary = RES_DIR + "/analysis_nanopore/ecdna/enhancer_intersections/{sample}/enhancer_relative_coordinates.tsv",
        script = BIN_DIR + "/circ_dna_scripts/003e-convert_to_bed12.py"

    output:
        RES_DIR + "/analysis_nanopore/ecdna/annotations/{sample}_complete.txt"

    params:
        sample_name = "{sample}",
        output_dir = RES_DIR + "/analysis_nanopore/ecdna/annotations/"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'

    shell:
        """{PYTHON} {input.script} --exon_coords_summary {input.exon_summary_file} --gene_coords_summary {input.gene_summary_file} --enhancer_coords_summary {input.enhancer_coords_summary} --sample_name {params.sample_name} --output_dir {params.output_dir}"""







###################################
# Canonical oncogene analysis
###################################

# Analyze canonical oncogenes and enhancers in ecDNA regions
rule analyze_ecdna_oncogenes:
    input:
        gene_intersections = expand(RES_DIR + "/analysis_nanopore/ecdna/gene_intersections/{sample}_gene_relative_coordinates.tsv", sample=sample_list),
        enhancer_intersections = expand(RES_DIR + "/analysis_nanopore/ecdna/enhancer_intersections/{sample}/enhancer_relative_coordinates.tsv", sample=sample_list),
        cgc_file = REF_DATA_DIR + "/cgc_v100_17102024.tsv",
        script = BIN_DIR + "/circ_dna_scripts/005a-analyze_ecdna_oncogenes.py"

    output:
        oncogene_analysis = RES_DIR + "/analysis_nanopore/ecdna/gene_intersections/ecdna_oncogene_analysis.tsv"

    params:
        sample_list = ",".join(sample_list),
        gene_intersections_dir = RES_DIR + "/analysis_nanopore/ecdna/gene_intersections/",
        enhancer_intersections_dir = RES_DIR + "/analysis_nanopore/ecdna/enhancer_intersections/"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{PYTHON} {input.script} --gene_intersections_dir {params.gene_intersections_dir} --enhancer_intersections_dir {params.enhancer_intersections_dir} --cgc_file {input.cgc_file} --sample_list {params.sample_list} --output_file {output.oncogene_analysis}"


# Visualize canonical oncogenes in ecDNA
rule visualize_ecdna_oncogenes:
    input:
        oncogene_analysis = RES_DIR + "/analysis_nanopore/ecdna/gene_intersections/ecdna_oncogene_analysis.tsv",
        script = BIN_DIR + "/circ_dna_scripts/005b-visualize_ecdna_oncogenes.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/ecdna/_figures/ecdna_oncogenes.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'

    shell:
        "{RSCRIPT} {input.script} --input_file {input.oncogene_analysis} --figure_file {output.figure_file}"






###################################
# Sample-level oncogene analysis
###################################

# Analyze oncogenes per sample
rule analyze_sample_oncogenes:
    input:
        oncogene_analysis = RES_DIR + "/analysis_nanopore/ecdna/gene_intersections/ecdna_oncogene_analysis.tsv",
        script = BIN_DIR + "/circ_dna_scripts/005c-analyze_sample_oncogenes.py"

    output:
        sample_oncogene_analysis = RES_DIR + "/analysis_nanopore/ecdna/gene_intersections/sample_oncogene_analysis.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{PYTHON} {input.script} --ecdna_oncogene_analysis {input.oncogene_analysis} --output_file {output.sample_oncogene_analysis}"


# Visualize sample oncogenes
rule visualize_sample_oncogenes:
    input:
        sample_oncogene_analysis = RES_DIR + "/analysis_nanopore/ecdna/gene_intersections/sample_oncogene_analysis.tsv",
        script = BIN_DIR + "/circ_dna_scripts/005d-visualize_sample_oncogenes.R"

    output:
        sample_oncogene_figure = RES_DIR + "/analysis_nanopore/ecdna/_figures/sample_oncogenes.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '15G'

    shell:
        "{RSCRIPT} {input.script} --sample_oncogene_analysis {input.sample_oncogene_analysis} --figure_file {output.sample_oncogene_figure}"


