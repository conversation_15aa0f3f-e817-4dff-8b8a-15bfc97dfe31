# Alec Bahcheli

###################################
# preprocess amplicon results
###################################

# Intersect all amplicon BED files with enhancers using Python script
rule combine_amplicon_ecdna_coordinates:
    input:
        expand("{res_dir}/circ_dna/{sample}/ampliconsuite/ampliconclassifier/{sample}_feature_basic_properties.tsv", res_dir = RES_DIR, sample = sample_list),

        script = BIN_DIR + "/circ_dna_scripts/006a-combine_ecdna_coordinates.py"

    output:
        combined_ecdna_coordinates = RES_DIR + "/circ_dna/combined_ecdna_coordinates.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --output_file {output.combined_ecdna_coordinates}"


###################################
# exon analysis
###################################

# Intersect exon BED file with each sample's ecDNA regions using bedtools
rule amplicon_architect_intersect_exons:
    input:
        exon_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_exons.bed",
        ecdna_bed_file = RES_DIR + "/circ_dna/combined_ecdna_coordinates.bed"

    output:
        intersected_file = RES_DIR + "/circ_dna/exon_intersections.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'

    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.exon_bed_file} -b {input.ecdna_bed_file} -wa -wb > {output.intersected_file}"""


# Create relative coordinate BED files for each sample
rule amplicon_architect_create_relative_coordinate_exon_beds:
    input:
        intersection_file = RES_DIR + "/circ_dna/exon_intersections.bed",
        ecdna_bed_file = RES_DIR + "/circ_dna/combined_ecdna_coordinates.bed",
        script = BIN_DIR + "/circ_dna_scripts/006b-create_relative_coordinate_beds.py"

    output:
        summary_file = RES_DIR + "/circ_dna/exon_relative_coordinates.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'

    shell:
        "{PYTHON} {input.script} --intersection_bed {input.intersection_file} --ecdna_bed {input.ecdna_bed_file} --output_file {output.summary_file}"


###################################
# full gene analysis
###################################

# Intersect gene BED file with each sample's ecDNA regions using bedtools
rule amplicon_architect_intersect_ecdna_genes:
    input:
        gene_bed_file_processed = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions_renamed_chr.bed",
        ecdna_bed_file = RES_DIR + "/circ_dna/combined_ecdna_coordinates.bed"

    output:
        intersected_file = RES_DIR + "/circ_dna/gene_intersections.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'

    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.gene_bed_file_processed} -b {input.ecdna_bed_file} -wa -wb > {output.intersected_file}"""


# Create relative coordinate BED files for each sample
rule amplicon_architect_create_relative_coordinate_gene_beds:
    input:
        intersection_file = RES_DIR + "/circ_dna/gene_intersections.bed",
        ecdna_bed_file = RES_DIR + "/circ_dna/combined_ecdna_coordinates.bed",
        script = BIN_DIR + "/circ_dna_scripts/006b-create_relative_coordinate_beds.py"

    output:
        summary_file = RES_DIR + "/circ_dna/gene_relative_coordinates.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'

    shell:
        "{PYTHON} {input.script} --intersection_bed {input.intersection_file} --ecdna_bed {input.ecdna_bed_file} --output_file {output.summary_file}"


###################################
# Enhancer analysis
###################################

# Intersect enhancer BED file with each sample's ecDNA regions using bedtools
rule amplicon_architect_intersect_ecdna_enhancers:
    input:
        enhancer_bed_file = REF_DATA_DIR + "/glioblastoma_enhancers.bed",
        ecdna_bed_file = RES_DIR + "/circ_dna/combined_ecdna_coordinates.bed"

    output:
        intersected_file = RES_DIR + "/circ_dna/enhancer_intersections.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.enhancer_bed_file} -b {input.ecdna_bed_file} -wa -wb > {output.intersected_file}"""


# Create relative coordinate enhancer beds
rule amplicon_architect_create_relative_coordinate_enhancer_beds:
    input:
        intersection_bed = RES_DIR + "/circ_dna/enhancer_intersections.bed",
        ecdna_bed = RES_DIR + "/circ_dna/combined_ecdna_coordinates.bed",
        script = BIN_DIR + "/circ_dna_scripts/006c-enhancers_relative_coordinate_beds.py"

    output:
        enhancer_coords_summary = RES_DIR + "/circ_dna/enhancer_relative_coordinates.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{PYTHON} {input.script} --intersection_bed {input.intersection_bed} --ecdna_bed {input.ecdna_bed} --output_file {output.enhancer_coords_summary}"


###################################
# Analyze canonical oncogenes and enhancers in ecDNA regions
###################################

# Analyze canonical oncogenes and enhancers in ecDNA regions
rule amplicon_architect_analyze_ecdna_oncogenes:
    input:
        gene_intersections = RES_DIR + "/circ_dna/gene_relative_coordinates.tsv",
        enhancer_intersections = RES_DIR + "/circ_dna/enhancer_relative_coordinates.tsv",

        cgc_file = REF_DATA_DIR + "/cgc_v100_17102024.tsv",

        script = BIN_DIR + "/circ_dna_scripts/006d-analyze_ecdna_oncogenes.py"

    output:
        oncogene_analysis = RES_DIR + "/circ_dna/combined_oncogene_enhancer_results.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{PYTHON} {input.script} --gene_intersections {input.gene_intersections} --enhancer_intersections {input.enhancer_intersections} --cgc_file {input.cgc_file} --output_file {output.oncogene_analysis}"










































# ###################################
# # enhancer analysis
# ###################################

# # Intersect all amplicon BED files with enhancers using Python script
# rule intersect_amplicon_enhancers:
#     input:
#         # expand(RES_DIR + "/circ_dna/{sample}/ampliconsuite/ampliconclassifier/amplicon_information/{sample}_amplicon1_ecDNA_1_intervals.bed", sample=sample_list),
#         enhancer_bed = RES_DIR + "/analysis_sarek/gene_impacts/enhancers.bed",
#         script = BIN_DIR + "/circ_dna_scripts/006f-intersect_amplicon_enhancers.py"

#     output:
#         amplicon_enhancer_intersections = RES_DIR + "/circ_dna/{sample}/amplicon_enhancer_intersections.bed"

#     params:
#         amplicon_dir = RES_DIR + "/circ_dna/{sample}/ampliconsuite/ampliconclassifier/amplicon_information/",
#         bedtools_path = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '1:0:0:0',
#         individual_core_memory = '10G'

#     shell:
#         "{PYTHON} {input.script} --sample_name {wildcards.sample} --amplicon_dir {params.amplicon_dir} --enhancer_bed {input.enhancer_bed} --bedtools_path {params.bedtools_path} --output_file {output.amplicon_enhancer_intersections}"

# rule post_process_enhancers:
#     input:
#         intersection_bed = RES_DIR + "/circ_dna/{sample}/amplicon_enhancer_intersections.bed",
#         script = BIN_DIR + "/circ_dna_scripts/006c-post_process_enhancers.py"

#     output:
#         processed_enhancer_intersections = RES_DIR + "/circ_dna/{sample}/amplicon_enhancer_intersections.bed"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '1:0:0:0',
#         individual_core_memory = '10G'

#     shell:
#         "{PYTHON} {input.script} --intersection_bed {input.intersection_bed} --output_file {output.processed_enhancer_intersections}"


# ###################################
# # Amplicon annotation analysis
# ###################################

# # Intersect all amplicon BED files with enhancers using Python script
# rule intersect_amplicon_enhancers:
#     input:
#         enhancer_bed = RES_DIR + "/analysis_sarek/gene_impacts/enhancers.bed",
#         script = BIN_DIR + "/circ_dna_scripts/006f-intersect_amplicon_enhancers.py"

#     output:
#         amplicon_enhancer_intersections = RES_DIR + "/circ_dna/{sample}/amplicon_enhancer_intersections.bed"

#     params:
#         amplicon_dir = RES_DIR + "/circ_dna/{sample}/ampliconsuite/ampliconclassifier/amplicon_information/",
#         bedtools_path = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '1:0:0:0',
#         individual_core_memory = '10G'

#     shell:
#         "{PYTHON} {input.script} --sample_name {wildcards.sample} --amplicon_dir {params.amplicon_dir} --enhancer_bed {input.enhancer_bed} --bedtools_path {params.bedtools_path} --output_file {output.amplicon_enhancer_intersections}"


# # Combine amplicon oncogene counts in LR/SR comparison format
# rule combine_amplicon_oncogene_enhancers:
#     input:
#         enhancer_intersections = expand(RES_DIR + "/circ_dna/{sample}/amplicon_enhancer_intersections.bed", sample=SAMPLES),
#         combined_amplicon_results = RES_DIR + "/circ_dna/combined_amplicon_results.tsv",
#         cgc_file = REF_DATA_DIR + "/cgc_v100_17102024.tsv",
#         script = BIN_DIR + "/circ_dna_scripts/006e-combine_amplicon_oncogene_counts.py"

#     output:
#         combined_oncogene_counts = RES_DIR + "/analysis_nanopore/ecdna/method_comparison/combined_oncogene_enhancers.tsv"

#     params:
#         sample_list = ",".join(SAMPLES),
#         enhancer_intersections_dir = RES_DIR + "/circ_dna/"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '1:0:0:0',
#         individual_core_memory = '10G'

#     shell:
#         "{PYTHON} {input.script} --combined_amplicon_results {input.combined_amplicon_results} --enhancer_intersections_dir {params.enhancer_intersections_dir} --cgc_file {input.cgc_file} --sample_list {params.sample_list} --output_file {output.combined_oncogene_counts}"

