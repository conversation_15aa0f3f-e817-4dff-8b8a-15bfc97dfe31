# Alec Ba<PERSON>cheli - <EMAIL>



#####################
# differential methylation of promoters
#####################

# differential methylation testing
rule primary_recurrent_differential_promoter_methylation_beta_value:
    input:
        promoter_methylation_combined_file = RES_DIR + "/analysis_nanopore/methylation/promoter_methylation_data.tsv",
        protein_coding_genes_gff_file = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.protein_coding_genes.tsv",

        script = BIN_DIR + "/nanopore_methylation/002a-primary_recurrent_differential_promoter_methylation_beta_value.py"

    output:
        promoter_methlyation_results_file = RES_DIR + "/analysis_nanopore/methylation/primary_recurrent_differential_promoter_methylation_beta_value.tsv"
        
    resources:
        threads = 30,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '3G'
        
    shell:
        "{PYTHON} {input.script} --promoter_methylation_combined_file {input.promoter_methylation_combined_file} --protein_coding_genes_gff_file {input.protein_coding_genes_gff_file} --promoter_methlyation_results_file {output.promoter_methlyation_results_file} --threads {resources.threads}"


rule prioritize_differential_promoter_methylation_pvalues:
    input:
        promoter_methlyation_results_file = RES_DIR + "/analysis_nanopore/methylation/primary_recurrent_differential_promoter_methylation_beta_value.tsv",

        script = BIN_DIR + "/nanopore_methylation/002b-prioritize_differential_promoter_methylation_pvalues.py"

    output:
        promoter_prioritized_stats_file = RES_DIR + "/analysis_nanopore/methylation/primary_recurrent_differential_promoter_methylation_beta_value_stats-{model}.tsv",
        gene_sites_file = RES_DIR + "/analysis_nanopore/methylation/selected_methylation_sites-{model}.tsv"

    params:
        model = '{model}'

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --promoter_methlyation_results_file {input.promoter_methlyation_results_file} --model {params.model} --promoter_prioritized_stats_file {output.promoter_prioritized_stats_file} --gene_sites_file {output.gene_sites_file}"



# extracting and visualizing using DPM
rule setup_df_for_dpm:
    input:
        promoter_methlyation_results_file = RES_DIR + "/analysis_nanopore/methylation/primary_recurrent_differential_promoter_methylation_beta_value.tsv",

        script = BIN_DIR + "/nanopore_methylation/002c-setup_df_for_dpm.py"

    output:
        pval_file = RES_DIR + "/analysis_nanopore/methylation/pvals_for_dpm-{model}.tsv",
        fc_file = RES_DIR + "/analysis_nanopore/methylation/fcs_for_dpm-{model}.tsv"

    params:
        model = '{model}'
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --promoter_methlyation_results_file {input.promoter_methlyation_results_file} --pval_file {output.pval_file} --fc_file {output.fc_file} --model {params.model}"

rule dpm_merge_methylation_pvalues:
    input:
        pval_file = RES_DIR + "/analysis_nanopore/methylation/pvals_for_dpm-{model}.tsv",
        fc_file = RES_DIR + "/analysis_nanopore/methylation/fcs_for_dpm-{model}.tsv",
        
        script = BIN_DIR + "/nanopore_methylation/002d-dpm_merge_methylation_pvalues.R"

    output:
        merged_pval_file = RES_DIR + "/analysis_nanopore/methylation/dpm_merged_pvalues-{model}.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{AP_RSCRIPT} {input.script} --pval_file {input.pval_file} --fc_file {input.fc_file} --merged_pval_file {output.merged_pval_file}"


rule non_directional_merge_methylation_pvalues:
    input:
        pval_file = RES_DIR + "/analysis_nanopore/methylation/pvals_for_dpm-{model}.tsv",
        
        script = BIN_DIR + "/nanopore_methylation/002e-non_directional_merge_methylation_pvalues.R"

    output:
        merged_pval_file = RES_DIR + "/analysis_nanopore/methylation/non_directional_merged_pvalues-{model}.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{AP_RSCRIPT} {input.script} --pval_file {input.pval_file} --merged_pval_file {output.merged_pval_file}"




#####################
# comparing p-value distributions based on stats model
#####################

rule methylation_pvalues_different_distributions:
    input:
        expand("{res_dir}/analysis_nanopore/methylation/dpm_merged_pvalues-{model}.tsv", res_dir = RES_DIR, model = ['paired_ttest_p_value', 'paired_log_ttest_p_value', 'wilcoxon_p_value']),

        script = BIN_DIR + "/nanopore_methylation/004a-methylation_pvalues_different_distributions.py"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/methylation_pvalues_different_distributions.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --figure_data_file {output.figure_data_file}"

rule visualize_methylation_pvalues_different_distributions:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/methylation_pvalues_different_distributions.tsv",

        script = BIN_DIR + "/nanopore_methylation/004b-visualize_methylation_pvalues_different_distributions.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures/methylation_pvalues_different_distributions.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"



#####################
# visualizing p-value distributions
#####################

# visualize methylation summary stats 
rule qqplot_beta_methylation_pvalues:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/methylation/primary_recurrent_differential_promoter_methylation_beta_value.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/methylation/primary_recurrent_differential_promoter_methylation_beta_value_stats-wilcoxon_p_value.tsv",
        
        script = BIN_DIR + "/nanopore_methylation/003a-qqplot_beta_methylation_pvalues.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures_dml/qqplot_beta_methylation_pvalues.pdf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"

rule volcano_plot_methylation_pvalues:
    input:
        figure_stats_file = RES_DIR + "/analysis_nanopore/methylation/primary_recurrent_differential_promoter_methylation_beta_value_stats-wilcoxon_p_value.tsv",
        
        script = BIN_DIR + "/nanopore_methylation/003b-volcano_plot_methylation_pvalues.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures_dml/volcano_plot_methylation_pvalues.pdf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"


# visualize the mean promoter methylation differences
rule paired_test_methylation_boxplots:
    input:
        promoter_methylation_combined_file = RES_DIR + "/analysis_nanopore/methylation/promoter_methylation_data.tsv",
        gene_sites_file = RES_DIR + "/analysis_nanopore/methylation/selected_methylation_sites-wilcoxon_p_value.tsv",

        script = BIN_DIR + "/nanopore_methylation/003c-paired_test_methylation_boxplots.py"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/methylation/003-paired_test_methylation_boxplots.tsv"

    resources:
        threads = 30,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '3G'
        
    shell:
        "{PYTHON} {input.script} --promoter_methylation_combined_file {input.promoter_methylation_combined_file} --gene_sites_file {input.gene_sites_file} --figure_data_file {output.figure_data_file} --threads {resources.threads}"

rule visualize_paired_test_methylation_boxplots:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/methylation/003-paired_test_methylation_boxplots.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/methylation/primary_recurrent_differential_promoter_methylation_beta_value_stats-wilcoxon_p_value.tsv",

        script = BIN_DIR + "/nanopore_methylation/003d-visualize_paired_test_methylation_boxplots.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures_dml/003-visualize_paired_test_methylation_boxplots.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"


# visualize the primary-recurrent paired mean differences for each gene
rule visualize_paired_dpm_methylation_boxplots:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/methylation/003-paired_test_methylation_boxplots.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/methylation/dpm_merged_pvalues-wilcoxon_p_value.tsv",

        script = BIN_DIR + "/nanopore_methylation/003e-visualize_paired_dpm_methylation_boxplots.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures_dml/003-visualize_paired_dpm_methylation_boxplots-paired_ttest_p_value.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"




