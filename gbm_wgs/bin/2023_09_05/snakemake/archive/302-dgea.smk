# Alec Ba<PERSON>cheli - <EMAIL>
# this workflow is based on the Reimand Lab best practices (University of Toronto, OICR)


DGEA_RSCRIPT = '/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/dgea/bin/Rscript'

counts_file = RES_DIR + "/rnaseq_star/raw_counts.tsv"

###################################
# data preprocessing
###################################

rule counts_processing:
    input:
        expand("{res_dir}/rnaseq_star/tpm_counts/{sample}_gene_counts.txt", res_dir = RES_DIR, sample = rna_sample_list),
        expand("{res_dir}/rnaseq_star/tpm_counts/{sample}_exon_counts.txt", res_dir = RES_DIR, sample = rna_sample_list),
        expand("{res_dir}/rnaseq_star/tpm_counts/{sample}_transcript_counts.txt", res_dir = RES_DIR, sample = rna_sample_list),

        hgnc_file = REF_DATA_DIR + "/hgnc_gene_annotations.tsv",

        script = BIN_DIR + "/rna_interpretation/000b-subset_counts_to_protein_coding.py"

    output:
        raw_counts = RES_DIR + "/rnaseq_star/raw_counts.tsv",
        raw_protein_coding_counts = RES_DIR + "/rnaseq_star/raw_protein_coding_counts.tsv",

        raw_exon_counts = RES_DIR + "/rnaseq_star/raw_exon_counts.tsv",

        raw_transcript_counts = RES_DIR + "/rnaseq_star/raw_transcript_counts.tsv"

    params:
        counts_dir = RES_DIR + "/rnaseq_star/tpm_counts",
        samples_csv = ",".join(rna_sample_list)

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '30G'

    shell:
        "{PYTHON} {input.script} --counts_dir {params.counts_dir} --samples_csv {params.samples_csv} --hgnc_file {input.hgnc_file} --all_counts_file {output.raw_counts} --protein_coding_counts_file {output.raw_protein_coding_counts} --all_exons_file {output.raw_exon_counts} --all_transcript_file {output.raw_transcript_counts}"


rule combine_tpm_counts:
    input:
        expand("{res_dir}/rnaseq_star/tpm_counts/{sample}.Aligned.out_sorted_genes.out", res_dir = RES_DIR, sample = rna_sample_list),
        gtf_file = GTF_FILE,
        
        script = BIN_DIR + "/rna_interpretation/000a-tpm_processing.py"
        
    output:
        tpm_outfile = RES_DIR + "/rnaseq_star/tpm.tsv"
        
    params:
        tpm_counts_directory = RES_DIR + "/rnaseq_star/tpm_counts"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTHON} {input.script} --tpm_counts_directory {params.tpm_counts_directory} --gtf_file {input.gtf_file} --tpm_outfile {output.tpm_outfile}"


###################################
# differential expression analysis
###################################

rule paired_edger_dgea:
    input:
        counts_file = RES_DIR + "/rnaseq_star/{data_type}.tsv",

        rscript = BIN_DIR + "/rna_interpretation/001a-edger_paired.R"

    output:
        dgea_file = RES_DIR + "/analysis_rna/_figure_data/{data_type}-edger_paired.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '30G'

    shell:
        "{DGEA_RSCRIPT} {input.rscript} --counts_file {input.counts_file} --dgea_file {output.dgea_file}"

rule unpaired_edger_dgea:
    input:
        counts_file = RES_DIR + "/rnaseq_star/{data_type}.tsv",

        rscript = BIN_DIR + "/rna_interpretation/001b-edger_unpaired.R"

    output:
        dgea_file = RES_DIR + "/analysis_rna/_figure_data/{data_type}-edger_unpaired.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '30G'

    shell:
        "{DGEA_RSCRIPT} {input.rscript} --counts_file {input.counts_file} --dgea_file {output.dgea_file}"


rule paired_deseq2_dgea:
    input:
        counts_file = RES_DIR + "/rnaseq_star/{data_type}.tsv",

        rscript = BIN_DIR + "/rna_interpretation/001c-deseq2_paired.R"

    output:
        dgea_file = RES_DIR + "/analysis_rna/_figure_data/{data_type}-deseq2_paired.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '30G'

    shell:
        "{DGEA_RSCRIPT} {input.rscript} --counts_file {input.counts_file} --dgea_file {output.dgea_file}"

rule unpaired_deseq2_dgea:
    input:
        counts_file = RES_DIR + "/rnaseq_star/{data_type}.tsv",

        rscript = BIN_DIR + "/rna_interpretation/001d-deseq2_unpaired.R"

    output:
        dgea_file = RES_DIR + "/analysis_rna/_figure_data/{data_type}-deseq2_unpaired.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '30G'

    shell:
        "{DGEA_RSCRIPT} {input.rscript} --counts_file {input.counts_file} --dgea_file {output.dgea_file}"




###################################
# visualizing dgea
###################################

rule volcano_plot_rnaseq:
    input:
        expand("{res_dir}/analysis_rna/_figure_data/{{data_type}}-{analysis}.tsv", res_dir = RES_DIR, analysis = dgea_analysis_types),
        gff = GFF_FILE,

        script = BIN_DIR + "/rna_interpretation/001e-volcano_plot.py",
        r_script = BIN_DIR + "/rna_interpretation/001f-volcano_plot.R"

    output:
        figure_data_file = RES_DIR + "/analysis_rna/_figure_data/001-dgea_data_{data_type}.tsv",

        figure_file = RES_DIR + "/analysis_rna/_figures/001-dgea_{data_type}.pdf"

    params:
        input_data_dir = RES_DIR + "/analysis_rna/_figure_data",
        data_type = "{data_type}"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '30G'

    shell:
        "{PYTHON} {input.script} --input_data_dir {params.input_data_dir} --gff3_file {input.gff} --data_type {params.data_type} --figure_data_file {output.figure_data_file} --r_script {input.r_script} --figure_file {output.figure_file}"


rule dgea_barplots:
    input:
        input_data_file = RES_DIR + "/rnaseq_star/{data_type}.tsv",
        input_stats_file = RES_DIR + "/analysis_rna/_figure_data/001-dgea_data_{data_type}.tsv",

        script = BIN_DIR + "/rna_interpretation/001g-dgea_barplots.py",
        r_script = BIN_DIR + "/rna_interpretation/001h-dgea_barplots.R"

    output:
        figure_data_file = RES_DIR + "/analysis_rna/_figure_data/001-dgea_barplots_data_{data_type}.tsv",

        figure_file = RES_DIR + "/analysis_rna/_figures/001-dgea_barplots_{data_type}.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '30G'

    shell:
        "{PYTHON} {input.script} --input_data_file {input.input_data_file} --input_stats_file {input.input_stats_file} --figure_data_file {output.figure_data_file} --r_script {input.r_script} --figure_file {output.figure_file}"




###################################
# gene fusion circos plots
###################################

rule gene_fusion_circos:
    input:
        expand("{res_dir}/rnaseq_star/arriba_fusions/{sample}_arriba_fusions.tsv", res_dir = RES_DIR, sample = rna_sample_list),

        script = BIN_DIR + "/rna_interpretation/002a-rna_fusion_circos.py",
        r_script = BIN_DIR + "/rna_interpretation/002b-rna_fusion_circos.R"

    output:
        figure_data_file = RES_DIR + "/analysis_rna/_figure_data/002-rna_gene_fusions.tsv",

        figure_file = RES_DIR + "/analysis_rna/_figures/002-rna_gene_fusions.pdf"

    params:
        aribba_fusions_directory = RES_DIR + "/rnaseq_star/arriba_fusions"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '30G'

    shell:
        "{PYTHON} {input.script} --aribba_fusions_directory {params.aribba_fusions_directory} --figure_data_file {output.figure_data_file} --r_script {input.r_script} --figure_file {output.figure_file}"




###################################
# pathway enrichment analysis
###################################

rule pathway_enrichment_primary_current_proteins:
    input:
        dgea_results_file = RES_DIR + "/analysis_rna/_figure_data/raw_protein_coding_counts-deseq2_paired.tsv",
        gmt_infile = RAW_DATA_DIR + "/networks/go_bp_reactome_v7.5.1.symbols.gmt",

        gene_translation_file = REF_DATA_DIR + "/ensembl_canonical_hg38.tsv",
        
        script = BIN_DIR + "/rna_interpretation/003a-ap_pathway_enrichment.py",
        r_script = BIN_DIR + "/rna_interpretation/003b-ap_pathway_enrichment.R"
        
    output:
        RES_DIR + "/analysis_rna/pathway_enrichment/pathways.txt",

        pval_file = RES_DIR + "/analysis_rna/pathway_enrichment/ap_input_pvalues.tsv",
        enriched_pathways_file = RES_DIR + "/analysis_rna/pathway_enrichment/enriched_pathways.csv",

    params:
        output_dir = RES_DIR + "/analysis_rna/pathway_enrichment/"
    
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --dgea_results_file {input.dgea_results_file} --gene_translation_file {input.gene_translation_file} --gmt_infile {input.gmt_infile} --r_script {input.r_script} --pval_file {output.pval_file} --enriched_pathways_file {output.enriched_pathways_file} --output_dir {params.output_dir}"









