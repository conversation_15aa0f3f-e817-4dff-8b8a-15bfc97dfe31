# Alec Ba<PERSON>cheli


#####################
# calculate SVs in common between primary-recurrent pairs
#####################

# setup jasmine input files
rule setup_jasmine_input_files_p_r_comparison:
    input:
        primary_filtered_sv_vcf_file = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/somatic_svs/{patient}-primary_vs_blood-somatic.vcf",
        recurrent_filtered_sv_vcf_file = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/somatic_svs/{patient}-recurrent_vs_blood-somatic.vcf"

    output:
        jasmine_txt = RES_DIR + "/analysis_nanopore/sv_overlaps/primary_recurrent_raw/{patient}_primary_vs_recurrent.txt"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """echo {input.primary_filtered_sv_vcf_file} > {output.jasmine_txt} && echo {input.recurrent_filtered_sv_vcf_file} >> {output.jasmine_txt}"""


# run jasmine to combine SVs and identify common ones
rule run_jasmine_p_r:
    input:
        jasmine_txt = RES_DIR + "/analysis_nanopore/sv_overlaps/primary_recurrent_raw/{patient}_primary_vs_recurrent.txt",

    output:
        jasmine_vcf = RES_DIR + "/analysis_nanopore/sv_overlaps/primary_recurrent_raw/{patient}_primary_vs_recurrent-intercepted.vcf"

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/jasmine file_list={input.jasmine_txt} out_file={output.jasmine_vcf}"""


# process the variants as before for consistency
rule process_jasmine_p_r_results:
    input:
        vcf_file = RES_DIR + "/analysis_nanopore/sv_overlaps/primary_recurrent_raw/{patient}_primary_vs_recurrent-intercepted.vcf",

        script = BIN_DIR + "/sv_overlaps/001a-process_jasmine_results.py"

    output:
        processed_vcf_file = RES_DIR + "/analysis_nanopore/sv_overlaps/processed/{patient}_primary_vs_recurrent-intercepted_processed.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --vcf_file {input.vcf_file} --processed_vcf_file {output.processed_vcf_file}"

# interpret jasmine results
rule interpret_jasmine_results_p_r:
    input:
        expand("{res_dir}/analysis_nanopore/sv_overlaps/processed/{patient}_primary_vs_recurrent-intercepted_processed.tsv", res_dir = RES_DIR, patient = sample_codes_list),

        script = BIN_DIR + "/sv_overlaps/001b-interpret_jasmine_results.py"

    output:
        nanopore_sv_counts_file = RES_DIR + "/analysis_nanopore/sv_overlaps/processed/sv_counts.tsv",
        specific_sv_counts_file = RES_DIR + "/analysis_nanopore/sv_overlaps/processed/specific_sv_counts.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --nanopore_sv_counts_file {output.nanopore_sv_counts_file} --specific_sv_counts_file {output.specific_sv_counts_file}"

rule visualize_jasmine_results:
    input:
        figure_stats_file = RES_DIR + "/analysis_nanopore/sv_overlaps/processed/sv_counts.tsv",

        script = BIN_DIR + "/sv_overlaps/001c-interpret_jasmine_results.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/svs_figures/jasmine_overlaps_proportions.pdf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"



#####################
# calculate SVs in common between Illumina and Nanopore
#####################

# extract passing manta variants
rule extract_passing_manta_svs_vcf_human_variation:
    input:
        raw_manta_vcf = RES_DIR + "/sarek_wgs/{patient}-{tumor}/variant_calling/manta/{patient}-{tumor}-{tumor}_vs_{patient}-{tumor}-blood/{patient}-{tumor}-{tumor}_vs_{patient}-{tumor}-blood.manta.somatic_sv.vcf.gz"

    output:
        manta_vcf = RES_DIR + "/analysis_nanopore/sv_overlaps/nanopore_vs_illumina/{patient}-{tumor}-manta_passing_svs.vcf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """zcat {input.raw_manta_vcf} | grep "#" > {output.manta_vcf} && zcat {input.raw_manta_vcf} | awk '$7 == "PASS"' >> {output.manta_vcf} """


# setup jasmine input files, one with the primary file first, another with the primary file second
rule setup_jasmine_input_files_illumina:
    input:
        filtered_sv_vcf_file = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/somatic_svs/{sample}_vs_blood-somatic.vcf",
        manta_vcf = RES_DIR + "/analysis_nanopore/sv_overlaps/nanopore_vs_illumina/{sample}-manta_passing_svs.vcf"

    output:
        jasmine_txt = RES_DIR + "/analysis_nanopore/sv_overlaps/nanopore_vs_illumina/{sample}_nanopore_vs_illumina.txt"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """echo {input.filtered_sv_vcf_file} > {output.jasmine_txt} && echo {input.manta_vcf} >> {output.jasmine_txt}"""

# run jasmine twice to count the number of common SVs
rule run_jasmine_illumina:
    input:
        jasmine_txt = RES_DIR + "/analysis_nanopore/sv_overlaps/nanopore_vs_illumina/{sample}_nanopore_vs_illumina.txt"

    output:
        jasmine_vcf = RES_DIR + "/analysis_nanopore/sv_overlaps/nanopore_vs_illumina/{sample}_nanopore_vs_illumina-intercepted.vcf"

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/jasmine file_list={input.jasmine_txt} out_file={output.jasmine_vcf}"""


# process the variants as before for consistency
rule process_jasmine_results_illumina:
    input:
        vcf_file = RES_DIR + "/analysis_nanopore/sv_overlaps/nanopore_vs_illumina/{sample}_nanopore_vs_illumina-intercepted.vcf",

        script = BIN_DIR + "/sv_overlaps/002a-process_jasmine_results_illumina.py"

    output:
        processed_vcf_file = RES_DIR + "/analysis_nanopore/sv_overlaps/nanopore_vs_illumina_processed/{sample}_nanopore_vs_illumina-intercepted_processed.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --vcf_file {input.vcf_file} --processed_vcf_file {output.processed_vcf_file}"

# interpret jasmine results
rule interpret_jasmine_results_illumina:
    input:
        expand("{res_dir}/analysis_nanopore/sv_overlaps/nanopore_vs_illumina_processed/{sample}_nanopore_vs_illumina-intercepted_processed.tsv", res_dir = RES_DIR, sample = sample_list),

        script = BIN_DIR + "/sv_overlaps/002b-interpret_jasmine_results_illumina.py"

    output:
        nanopore_sv_counts_file = RES_DIR + "/analysis_nanopore/sv_overlaps/nanopore_vs_illumina_processed/sv_counts.tsv",
        specific_sv_counts_file = RES_DIR + "/analysis_nanopore/sv_overlaps/nanopore_vs_illumina_processed/specific_sv_counts.tsv",
        stats_file = RES_DIR + "/analysis_nanopore/sv_overlaps/nanopore_vs_illumina_processed/specific_sv_counts_stats.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --nanopore_sv_counts_file {output.nanopore_sv_counts_file} --specific_sv_counts_file {output.specific_sv_counts_file} --stats_file {output.stats_file}"

rule visualize_jasmine_results_illumina:
    input:
        figure_stats_file = RES_DIR + "/analysis_nanopore/sv_overlaps/nanopore_vs_illumina_processed/sv_counts.tsv",

        script = BIN_DIR + "/sv_overlaps/002c-visualize_jasmine_results_illumina.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/svs_figures/jasmine_results_illumina_overlap_proportions.pdf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"


#####################
# calculate SVs in common between tumor and blood control
#####################

# process the variants as before for consistency
rule process_jasmine_results_tumor_vs_control:
    input:
        expand("{res_dir}/analysis_nanopore/processing_non_somatic_svs/{sample}_vs_blood-intercepted.vcf", res_dir = RES_DIR, sample = sample_list),

        script = BIN_DIR + "/sv_overlaps/003a-interpret_jasmine_results_tumor_vs_control.py"

    output:
        sv_counts_file = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/sv_counts.tsv",
        spec_sv_counts_file = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/spec_sv_counts.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --sv_counts_file {output.sv_counts_file} --spec_sv_counts_file {output.spec_sv_counts_file}"

rule visualize_jasmine_results_tumor_vs_control:
    input:
        figure_stats_file = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/sv_counts.tsv",

        script = BIN_DIR + "/sv_overlaps/003b-visualize_jasmine_results_tumor_vs_control.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/svs_figures/jasmine_results_tumor_vs_control.pdf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"










