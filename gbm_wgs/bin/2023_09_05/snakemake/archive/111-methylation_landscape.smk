# Alec Ba<PERSON>cheli - <EMAIL>


###################################
# summary of methylation landscape
###################################

# circos plots of methylation
rule methyl_nanopore_recurrent_minus_control:
    input:
        primary_methylation_bed_file = RES_DIR + "/nanopore_evolution/{sample}/{sample}-primary/mod/5mC/bedMethyl/5mC.{sample}-primary_normal.bed.gz",
        recurrent_methylation_bed_file = RES_DIR + "/nanopore_evolution/{sample}/{sample}-primary/mod/5mC/bedMethyl/5mC.{sample}-primary_tumor.bed.gz",

        script = BIN_DIR + "/nanopore_methylation/001a-methylation_summarize.py"

    output:
        methlyation_results_file = RES_DIR + "/analysis_nanopore/methylation/{sample}-5mC_binned_{value}kbp.tsv"

    params:
        window_size = '{value}'
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "{PYTHON} {input.script} --primary_methylation_bed_file {input.primary_methylation_bed_file} --recurrent_methylation_bed_file {input.recurrent_methylation_bed_file} --methlyation_results_file {output.methlyation_results_file} --window_size {params.window_size}"

rule methyl_nanopore_whole_genome_circos:
    input:
        expand("{res_dir}/analysis_nanopore/methylation/{sample}-5mC_binned_{value}kbp.tsv", res_dir = RES_DIR, sample = sample_codes_list, value = '200'),

        figure_stats_file = RES_DIR + "/analysis_nanopore/_figure_data/001-methylation_genome_overview_stats.tsv",

        script = BIN_DIR + "/nanopore_methylation/001b-methylation_summarize.py",
        r_script = BIN_DIR + "/nanopore_methylation/001c-methylation_summarize.R"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/001-methylation_genome_overview.tsv",

        figure_file =  RES_DIR + "/analysis_nanopore/_figures/001-methylation_genome_overview.pdf"

    params:
        methylation_res_dir = RES_DIR + "/analysis_nanopore/methylation/"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --methylation_res_dir {params.methylation_res_dir} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"


###################################
# heatmaps
###################################

# binned average methylation heatmap
rule whole_genome_binned_methylation:
    input:
        methylation_bed_file = RES_DIR + "/nanopore_evolution/{sample}/{sample}-primary/mod/5mC/bedMethyl/5mC.{sample}-primary_{condition}.bed.gz",

        script = BIN_DIR + "/nanopore_methylation/002a-methylation_summarize.py"

    output:
        methlyation_results_file = RES_DIR + "/analysis_nanopore/methylation/{sample}_{condition}-5mC_binned_{value}kbp.tsv"

    params:
        window_size = '{value}'
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "{PYTHON} {input.script} --methylation_bed_file {input.methylation_bed_file} --methlyation_results_file {output.methlyation_results_file} --window_size {params.window_size}"

rule whole_genome_methylation_heatmap_prep:
    input:
        expand("{res_dir}/analysis_nanopore/methylation/{sample}_{condition}-5mC_binned_{value}kbp.tsv", res_dir = RES_DIR, sample = sample_codes_list, condition = ['normal', 'tumor'], value = '10'),

        script = BIN_DIR + "/nanopore_methylation/002b-binned_methylation_heatmap.py"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/002-binned_methylation_heatmap.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/_figure_data/002-binned_methylation_heatmap_stats.tsv",

        figure_file = RES_DIR + "/analysis_nanopore/_figures/002-binned_methylation_heatmap_dendrogram.pdf"

    params:
        methylation_res_dir = RES_DIR + "/analysis_nanopore/methylation/"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "{PYTHON} {input.script} --methylation_res_dir {params.methylation_res_dir} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file} --figure_file {output.figure_file}"

rule whole_genome_methylation_heatmap:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/002-binned_methylation_heatmap.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/_figure_data/002-binned_methylation_heatmap_stats.tsv",

        r_script = BIN_DIR + "/nanopore_methylation/002c-binned_methylation_heatmap.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures/002-binned_methylation_heatmap.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"



# create a heatmap of promoter methylation
rule promoter_methylation_heatmap:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/003-promoter_methylation_histogram.tsv",

        r_script = BIN_DIR + "/nanopore_methylation/003i-promoter_methylation_heatmap.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures/003-promoter_methylation_heatmap.png"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/ggdendro/bin/Rscript {input.r_script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"





# ###################################
# # histogram of promoter methylation
# ###################################

# # histrogram of promoter methylation
# rule promoter_methylation_histogram:
#     input:
#         combined_promoter_methylation_file = RES_DIR + "/analysis_nanopore/methylation/combined_mean_promoter_methylation.tsv",

#         script = BIN_DIR + "/nanopore_methylation/003g-promoter_methylation_histogram.py",
#         r_script = BIN_DIR + "/nanopore_methylation/003h-promoter_methylation_histogram.R"

#     output:
#         figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/003-promoter_methylation_histogram.tsv",
#         figure_stats_file = RES_DIR + "/analysis_nanopore/_figure_data/003-promoter_methylation_histogram_stats.tsv",

#         figure_file = RES_DIR + "/analysis_nanopore/_figures/003-promoter_methylation_histogram.pdf"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --combined_promoter_methylation_file {input.combined_promoter_methylation_file} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file} --figure_file {output.figure_file}"





