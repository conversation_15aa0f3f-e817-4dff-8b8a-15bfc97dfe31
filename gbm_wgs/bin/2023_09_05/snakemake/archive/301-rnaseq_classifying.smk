# Alec Ba<PERSON>cheli - <EMAIL>


###################################
# pre-processing rnaseq data
###################################

rule convert_gene_ids_to_names:
    input:
        tpm_file = RES_DIR + "/rnaseq_star/tpm.tsv",
        gff_file = GFF_FILE,

        script = BIN_DIR + "/rnaseq_interpretation/000a-convert_gene_ids_to_names.py"

    output:
        gene_name_tpm_file = RES_DIR + "/analysis_rna/tpm_gene_names.tsv",
        protein_tpm_file = RES_DIR + "/analysis_rna/protein_tpm_gene_names.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --tpm_file {input.tpm_file} --gff_file {input.gff_file} --gene_name_tpm_file {output.gene_name_tpm_file} --protein_tpm_file {output.protein_tpm_file}"


###################################
# visualizing transcriptomic differences 
###################################

rule umap_tpm:
    input:
        tpm_file = RES_DIR + "/analysis_rna/protein_tpm_gene_names.tsv",

        script = BIN_DIR + "/rnaseq_interpretation/001a-umap_tpm.py",
        r_script = BIN_DIR + "/rnaseq_interpretation/001b-umap_tpm.R"

    output:
        figure_data_file = RES_DIR + "/analysis_rna/_figure_data/001-umap_tpm.tsv",

        figure_file = RES_DIR + "/analysis_rna/_figures/001-umap_tpm.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --tpm_file {input.tpm_file} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_file {output.figure_file}"



rule rna_correlation:
    input:
        tpm_file = RES_DIR + "/analysis_rna/protein_tpm_gene_names.tsv",

        script = BIN_DIR + "/rnaseq_interpretation/001c-rna_correlation.py"

    output:
        figure_stats_file = RES_DIR + "/analysis_rna/_figure_data/001-rna_correlation_stats.tsv",
        figure_data_file = RES_DIR + "/analysis_rna/_figure_data/001-rna_correlation.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --tpm_file {input.tpm_file} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file}"

rule rna_correlation_visualize_faceted:
    input:
        figure_stats_file = RES_DIR + "/analysis_rna/_figure_data/001-rna_correlation_stats.tsv",
        figure_data_file = RES_DIR + "/analysis_rna/_figure_data/001-rna_correlation.tsv",

        r_script = BIN_DIR + "/rnaseq_interpretation/001d-rna_correlation_visualize_faceted.R"

    output:
        figure_file = RES_DIR + "/analysis_rna/_figures/001-rna_correlation_visualize_faceted.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_stats_file {input.figure_stats_file} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"





