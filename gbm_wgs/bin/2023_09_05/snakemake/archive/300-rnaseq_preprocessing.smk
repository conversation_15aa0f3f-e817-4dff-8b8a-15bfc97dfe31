# Alec <PERSON>i - <EMAIL>
# this workflow is based on the Reimand Lab best practices (University of Toronto, OICR)

###################################
# Create project directories
###################################
rule create_rna_results_dir:
    input:
        null = RES_DIR + "/null.txt"
        
    output:
        working_file = RES_DIR + "/rnaseq_star/null.txt"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '10G'
        
    shell:
        "mkdir -p {RES_DIR}/rnaseq_star && touch {output.working_file}"


rule create_rna_spec_results_dirs:
    input:
        null = RES_DIR + "/null.txt"
        
    output:
        working_file_pass1 = RES_DIR + "/rnaseq_star/pass1/null.txt",
        working_file_pass2 = RES_DIR + "/rnaseq_star/pass2/null.txt",

        working_file_tpm = RES_DIR + "/rnaseq_star/tpm_counts/null.txt",
        working_file_arriba = RES_DIR + "/rnaseq_star/arriba_fusions/null.txt"

    params:
        sample_dir1 = RES_DIR + "/rnaseq_star/pass1",
        sample_dir2 = RES_DIR + "/rnaseq_star/pass2",

        sample_dir3 = RES_DIR + "/rnaseq_star/tpm_counts",
        sample_dir4 = RES_DIR + "/rnaseq_star/arrarriba_fusionsiba"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '10G'
        
    shell:
        "mkdir -p {params.sample_dir1} {params.sample_dir2} {params.sample_dir3} {params.sample_dir4} && touch {output.working_file_pass1} {output.working_file_pass2} {output.working_file_tpm} {output.working_file_arriba}"


rule create_rna_spec_dirs:
    input:
        working_file_arriba = RES_DIR + "/rnaseq_star/arriba_fusions/null.txt",

        working_file_pass1 = RES_DIR + "/rnaseq_star/pass1/null.txt",
        working_file_pass2 = RES_DIR + "/rnaseq_star/pass2/null.txt"
        
    output:
        working_file_pass1 = RES_DIR + "/rnaseq_star/pass1/{sample}/null.txt",
        working_file_pass2 = RES_DIR + "/rnaseq_star/pass2/{sample}/null.txt",

        working_sample_file_arriba = RES_DIR + "/rnaseq_star/arriba_fusions/{sample}/null.txt"

    params:
        sample_dir1 = RES_DIR + "/rnaseq_star/pass1/{sample}",
        sample_dir2 = RES_DIR + "/rnaseq_star/pass2/{sample}",

        sample_dir3 = RES_DIR + "/rnaseq_star/arriba_fusions/{sample}"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '10G'
        
    shell:
        "mkdir -p {params.sample_dir1} {params.sample_dir2} {params.sample_dir3} && touch {output.working_file_pass1} {output.working_file_pass2} {output.working_sample_file_arriba}"



###################################
# STAR genome indexing and mapping 
###################################
rule star_index:
    input:
        RES_DIR + "/rnaseq_star/null.txt",

        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa", # provide your reference FASTA file
        gtf = GTF_FILE # provide your GTF file

    output:
        results = RES_DIR + "/results.tsv"

    params:
        star_dir = RES_DIR + "/rnaseq_star/", 
        star_tmp_dir = RES_DIR + "/rnaseq_star/tmp", # output tmp dir
        overhang = '150' # sjdbOverhang is read length -1

    resources:
        threads = RNA_THREADS,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '5G'

    shell:
        "cd {params.star_dir} && /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/STAR --runMode genomeGenerate --genomeDir {params.star_dir} --genomeFastaFiles {input.genome_fasta} --sjdbGTFfile {input.gtf} --sjdbOverhang {params.overhang} --outTmpDir {params.star_tmp_dir} --runThreadN {resources.threads}" 


rule star_pass1:
    input:
        RES_DIR + "/rnaseq_star/pass1/{sample}/null.txt",
        RES_DIR + "/rnaseq_star/SAindex",

        fastq_forward = FASTQ_DIR + "/{sample}_R1.fastq.gz",
        fastq_reverse = FASTQ_DIR + "/{sample}_R2.fastq.gz"

    output:
        bam = RES_DIR + "/rnaseq_star/pass1/{sample}/{sample}.Aligned.out.bam",
        sjdb_file = RES_DIR + "/rnaseq_star/pass1/{sample}/{sample}.SJ.out.tab"

    params:
        output_prefix = RES_DIR + "/rnaseq_star/pass1/{sample}/{sample}.",

        star_dir = RES_DIR + "/rnaseq_star",
        star_tmp_dir = RES_DIR + "/rnaseq_star/pass1/{sample}/tmp", # output tmp dir

        overhang = '150' # sjdbOverhang is read length -1

    resources:
        threads = RNA_THREADS,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '5G'

    shell:
        "cd {params.star_dir} && /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/STAR --readFilesIn {input.fastq_forward},{input.fastq_reverse} --genomeDir {params.star_dir} --readFilesCommand zcat --outSAMtype BAM Unsorted --sjdbOverhang {params.overhang} --outFileNamePrefix {params.output_prefix} --outTmpDir {params.star_tmp_dir} --runThreadN {resources.threads}" 


rule star_pass2:
    input:
        RES_DIR + "/rnaseq_star/pass2/{sample}/null.txt",
        RES_DIR + "/rnaseq_star/SAindex",

        fastq_forward = FASTQ_DIR + "/{sample}_R1.fastq.gz",
        fastq_reverse = FASTQ_DIR + "/{sample}_R2.fastq.gz",

        sjdb_file = RES_DIR + "/rnaseq_star/pass1/{sample}/{sample}.SJ.out.tab"

    output:
        bam = RES_DIR + "/rnaseq_star/pass2/{sample}/{sample}.Aligned.out.bam"

    params:
        output_prefix = RES_DIR + "/rnaseq_star/pass2/{sample}/{sample}.",

        star_dir = RES_DIR + "/rnaseq_star",
        star_tmp_dir = RES_DIR + "/rnaseq_star/pass2/{sample}/tmp",

        overhang = '150'

    resources:
        threads = RNA_THREADS,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '5G'

    shell:
        "cd {params.star_dir} && /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/STAR --readFilesIn {input.fastq_forward},{input.fastq_reverse} --genomeDir {params.star_dir} --readFilesCommand zcat --outSAMtype BAM Unsorted --sjdbOverhang {params.overhang} --outFileNamePrefix {params.output_prefix} --outTmpDir {params.star_tmp_dir} --runThreadN {resources.threads} --sjdbFileChrStartEnd {input.sjdb_file}"




###################################
# gene fusion detection
###################################

rule arriba_gene_fusions:
    input:
        RES_DIR + "/rnaseq_star/arriba_fusions/null.txt",
        RES_DIR + "/rnaseq_star/SAindex",
        RES_DIR + "/rnaseq_star/arriba_fusions/{sample}/null.txt",


        genome_fasta = genome_fasta,
        gtf_file = GTF_FILE,

        fastq_forward = FASTQ_DIR + "/{sample}_R1.fastq.gz",
        fastq_reverse = FASTQ_DIR + "/{sample}_R2.fastq.gz",

        blacklist_fusions = REF_DATA_DIR + "/gene_fusions/blacklist_hg38_GRCh38_v2.4.0.tsv.gz",
        known_fusions = REF_DATA_DIR + "/gene_fusions/known_fusions_hg38_GRCh38_v2.4.0.tsv.gz",
        known_fusion_regions = REF_DATA_DIR + "/gene_fusions/cytobands_hg38_GRCh38_v2.4.0.tsv",
        protein_domains = REF_DATA_DIR + "/gene_fusions/protein_domains_hg38_GRCh38_v2.4.0.gff3"

    output:
        fusions_file = RES_DIR + "/rnaseq_star/arriba_fusions/{sample}_arriba_fusions.tsv",
        discarded_fusions_file = RES_DIR + "/rnaseq_star/arriba_fusions/{sample}_arriba_discarded.tsv"

    params:
        star_dir = RES_DIR + "/rnaseq_star",

        sample_dir = RES_DIR + "/rnaseq_star/arriba_fusions/{sample}",
        star_tmp_dir = RES_DIR + "/rnaseq_star/arriba_fusions/{sample}/tmp",

        overhang = '150'

    resources:
        threads = 4,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '50G'

    shell:
        "cd {params.sample_dir} && \
/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/STAR --genomeDir {params.star_dir} --genomeLoad NoSharedMemory \
--readFilesIn {input.fastq_forward} {input.fastq_reverse} --readFilesCommand zcat \
--outStd BAM_Unsorted --outSAMtype BAM Unsorted --outSAMunmapped Within --outBAMcompression 0 \
--outFilterMultimapNmax 50 --peOverlapNbasesMin 10 --alignSplicedMateMapLminOverLmate 0.5 --alignSJstitchMismatchNmax 5 -1 5 5 \
--chimSegmentMin 10 --chimOutType WithinBAM HardClip --chimJunctionOverhangMin 10 --chimScoreDropMax 30 \
--chimScoreJunctionNonGTAG 0 --chimScoreSeparation 1 --chimSegmentReadGapMax 3 --chimMultimapNmax 50 --runThreadN {resources.threads} | \
/.mounts/labs/reimandlab/private/users/abahcheli/software/arriba_v2.4.0/arriba \
-x /dev/stdin \
-o {output.fusions_file} -O {output.discarded_fusions_file} \
-a {input.genome_fasta} -g {input.gtf_file} \
-b {input.blacklist_fusions} -k {input.known_fusions} -t {input.known_fusion_regions} -p {input.protein_domains}"


# rule STAR_Fusion:
#     input:
#         RES_DIR + "/rnaseq_star/arriba_fusions/null.txt",
#         RES_DIR + "/rnaseq_star/SAindex",

#         fastq_forward = FASTQ_DIR + "/{sample}_R1.fastq.gz",
#         fastq_reverse = FASTQ_DIR + "/{sample}_R2.fastq.gz"

#     output:
#         fusions_file = RES_DIR + "/rnaseq_star/arriba_fusions/{sample}_arriba_fusions.tsv",
#         discarded_fusions_file = RES_DIR + "/rnaseq_star/arriba_fusions/{sample}_arriba_discarded.tsv"

#     params:
#         star_dir = RES_DIR + "/rnaseq_star",
#         star_tmp_dir = RES_DIR + "/rnaseq_star/arriba_fusions/{sample}/tmp",

#         overhang = '150'

#     resources:
#         threads = 8,
#         queue = "all.q",
#         jobtime = '0:112:0:0',
#         individual_core_memory = '12G'

#     shell:
#         ""



###################################
# read counts and TPM
###################################
# index alignment
rule sorted_bam_wgs:
    input:
        bam = RES_DIR + "/rnaseq_star/pass2/{sample}/{sample}.Aligned.out.bam"
        
    output:
        sorted_bam = RES_DIR + "/rnaseq_star/pass2/{sample}/{sample}.Aligned.out_sorted.bam"
    
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        jobtime = '14:0:0:0',
        individual_core_memory = '7G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools sort {input.bam} -o {output.sorted_bam}"



# index alignment
rule index_bam_wgs:
    input:
        sorted_bam = RES_DIR + "/rnaseq_star/pass2/{sample}/{sample}.Aligned.out_sorted.bam"
        
    output:
        sorted_index = RES_DIR + "/rnaseq_star/pass2/{sample}/{sample}.Aligned.out_sorted.bam.bai"
    
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        jobtime = '14:0:0:0',
        individual_core_memory = '7G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools index -@ {resources.threads} {input.sorted_bam}"


rule htseq:
    input:
        RES_DIR + "/rnaseq_star/tpm_counts/null.txt",

        bam = RES_DIR + "/rnaseq_star/pass2/{sample}/{sample}.Aligned.out_sorted.bam",
        sorted_index = RES_DIR + "/rnaseq_star/pass2/{sample}/{sample}.Aligned.out_sorted.bam.bai",

        gff = GFF_FILE

    output:
        counts_file = RES_DIR + "/rnaseq_star/tpm_counts/{sample}_{element_type}_counts.txt"

    params:
        element_type = "{element_type}"

    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        jobtime = '14:0:0:0',
        individual_core_memory = '7G'

    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/htseq-count -m union -s no -t {params.element_type} -i ID -r pos -f bam {input.bam} {input.gff} > {output.counts_file}"



# calculate tpm
# https://github.com/ncbi/TPMCalculator
rule TPMcalculator:
    input:
        RES_DIR + "/rnaseq_star/tpm_counts/null.txt",

        sorted_bam = RES_DIR + "/rnaseq_star/pass2/{sample}/{sample}.Aligned.out_sorted.bam",
        sorted_index = RES_DIR + "/rnaseq_star/pass2/{sample}/{sample}.Aligned.out_sorted.bam.bai",

        gtf_file = GTF_FILE
                
    output:
        RES_DIR + "/rnaseq_star/tpm_counts/{sample}.Aligned.out_sorted_genes.out"

    params:
        tpm_dir = RES_DIR + "/rnaseq_star/tpm_counts"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '80G'

    shell:
        "cd {params.tpm_dir} && /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/TPMCalculator -g {input.gtf_file} -b {input.sorted_bam}"






