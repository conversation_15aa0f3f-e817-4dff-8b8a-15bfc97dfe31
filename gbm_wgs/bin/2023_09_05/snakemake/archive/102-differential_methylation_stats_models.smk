# Alec Ba<PERSON>cheli - <EMAIL>


###################################
# prepare bed files
###################################

# define the promoters of each gene as 2kb upstream
rule define_protein_gene_promoters:
    input:
        protein_coding_genes_gff_file = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.protein_coding_genes.gff",

        script = BIN_DIR + "/nanopore_methylation/051a-protein_gene_promoters.py"

    output:
        protein_coding_promoter_bed_file = RES_DIR + "/analysis_nanopore/methylation/protein_promoter_regions.bed"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --protein_coding_genes_gff_file {input.protein_coding_genes_gff_file} --protein_coding_promoter_bed_file {output.protein_coding_promoter_bed_file}"



#####################
# differential methylation of promoters
#####################

# prepare methylation data
rule prepare_methylation_data:
    input:
        methyl_file = RES_DIR + "/nanopore_evolution/{sample}/{sample}-primary/mod/5mC/bedMethyl/5mC.{sample}-primary_{tumor}.bed.gz",

        script = BIN_DIR + "/nanopore_methylation/051b-prepare_methylation_data.py"

    output:
        methyl_outfile = RES_DIR + "/analysis_nanopore/dml_methylation/{sample}-{tumor}_methylation_details.bed"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --methyl_file {input.methyl_file} --methyl_outfile {output.methyl_outfile}"

rule intersect_promoters_methylation:
    input:
        mgmt_protein_bed_file = RES_DIR + "/analysis_nanopore/methylation/protein_promoter_regions.bed",
        methyl_outfile = RES_DIR + "/analysis_nanopore/dml_methylation/{sample}-{tumor}_methylation_details.bed"

    output:
        intersected_bed_file = RES_DIR + "/analysis_nanopore/methylation/{sample}-{tumor}_promoters_methylation.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a <(tail -n +2 {input.mgmt_protein_bed_file}) -b <(tail -n +2 {input.methyl_outfile}) -wa -wb > {output.intersected_bed_file}"""

# merge methylation data
rule merge_methylation_data:
    input:
        expand("{res_dir}/analysis_nanopore/methylation/{sample_code}-{tumor}_promoters_methylation.bed", res_dir = RES_DIR, sample_code = sample_codes_list, tumor = ['normal', 'tumor']),
        gene_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions.bed",

        script = BIN_DIR + "/nanopore_methylation/051c-merge_methylation_data.py"

    output:
        combined_methylation_file = RES_DIR + "/analysis_nanopore/methylation/promoter_methylation_data.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --gene_bed_file {input.gene_bed_file} --combined_methylation_file {output.combined_methylation_file}"

# differential methylation testing
rule primary_recurrent_differential_promoter_methylation_beta_value:
    input:
        promoter_methylation_combined_file = RES_DIR + "/analysis_nanopore/methylation/promoter_methylation_data.tsv",
        protein_coding_genes_gff_file = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.protein_coding_genes.gff",

        script = BIN_DIR + "/nanopore_methylation/051e-primary_recurrent_differential_promoter_methylation_beta_value.py"

    output:
        promoter_methlyation_results_file = RES_DIR + "/analysis_nanopore/methylation/primary_recurrent_differential_promoter_methylation_beta_value.tsv"
        
    resources:
        threads = 30,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '3G'
        
    shell:
        "{PYTHON} {input.script} --promoter_methylation_combined_file {input.promoter_methylation_combined_file} --protein_coding_genes_gff_file {input.protein_coding_genes_gff_file} --promoter_methlyation_results_file {output.promoter_methlyation_results_file} --threads {resources.threads}"



#####################
# visualizing p-value distributions
#####################

rule prioritize_differential_promoter_methylation_pvalues:
    input:
        promoter_methlyation_results_file = RES_DIR + "/analysis_nanopore/methylation/primary_recurrent_differential_promoter_methylation_beta_value.tsv",

        script = BIN_DIR + "/nanopore_methylation/051f-prioritize_differential_promoter_methylation_pvalues.py"

    output:
        promoter_prioritized_stats_file = RES_DIR + "/analysis_nanopore/methylation/primary_recurrent_differential_promoter_methylation_beta_value_stats.tsv",
        gene_sites_file = RES_DIR + "/analysis_nanopore/methylation/selected_methylation_sites.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --promoter_methlyation_results_file {input.promoter_methlyation_results_file} --promoter_prioritized_stats_file {output.promoter_prioritized_stats_file} --gene_sites_file {output.gene_sites_file}"


# visualize methylation summary stats 
rule qqplot_beta_methylation_pvalues:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/methylation/primary_recurrent_differential_promoter_methylation_beta_value.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/methylation/primary_recurrent_differential_promoter_methylation_beta_value_stats.tsv",
        
        script = BIN_DIR + "/nanopore_methylation/051g-qqplot_beta_methylation_pvalues.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures_dml/qqplot_beta_methylation_pvalues.pdf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"

rule volcano_plot_methylation_pvalues:
    input:
        figure_stats_file = RES_DIR + "/analysis_nanopore/methylation/primary_recurrent_differential_promoter_methylation_beta_value_stats.tsv",
        
        script = BIN_DIR + "/nanopore_methylation/051h-volcano_plot_methylation_pvalues.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures_dml/volcano_plot_methylation_pvalues.pdf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"


# visualize the mean promoter methylation differences
rule paired_test_methylation_boxplots:
    input:
        promoter_methylation_combined_file = RES_DIR + "/analysis_nanopore/methylation/promoter_methylation_data.tsv",
        gene_sites_file = RES_DIR + "/analysis_nanopore/methylation/selected_methylation_sites.tsv",

        script = BIN_DIR + "/nanopore_methylation/052a-paired_test_methylation_boxplots.py"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/methylation/052-paired_test_methylation_boxplots.tsv"

    resources:
        threads = 30,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '3G'
        
    shell:
        "{PYTHON} {input.script} --promoter_methylation_combined_file {input.promoter_methylation_combined_file} --gene_sites_file {input.gene_sites_file} --figure_data_file {output.figure_data_file} --threads {resources.threads}"

rule visualize_paired_test_methylation_boxplots:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/methylation/052-paired_test_methylation_boxplots.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/methylation/primary_recurrent_differential_promoter_methylation_beta_value_stats.tsv",

        script = BIN_DIR + "/nanopore_methylation/052b-visualize_paired_test_methylation_boxplots.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures_dml/052-visualize_paired_test_methylation_boxplots.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"



#####################
# extracting and visualizing using DPM
#####################

rule setup_df_for_dpm:
    input:
        promoter_methlyation_results_file = RES_DIR + "/analysis_nanopore/methylation/primary_recurrent_differential_promoter_methylation_beta_value.tsv",

        script = BIN_DIR + "/nanopore_methylation/051i-setup_df_for_dpm.py"

    output:
        pval_file = RES_DIR + "/analysis_nanopore/methylation/pvals_for_dpm.tsv",
        fc_file = RES_DIR + "/analysis_nanopore/methylation/fcs_for_dpm.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --promoter_methlyation_results_file {input.promoter_methlyation_results_file} --pval_file {output.pval_file} --fc_file {output.fc_file}"

rule dpm_merge_methylation_pvalues:
    input:
        pval_file = RES_DIR + "/analysis_nanopore/methylation/pvals_for_dpm.tsv",
        fc_file = RES_DIR + "/analysis_nanopore/methylation/fcs_for_dpm.tsv",
        
        script = BIN_DIR + "/nanopore_methylation/051j-dpm_merge_methylation_pvalues.R"

    output:
        merged_pval_file = RES_DIR + "/analysis_nanopore/methylation/dpm_merged_pvalues.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{AP_RSCRIPT} {input.script} --pval_file {input.pval_file} --fc_file {input.fc_file} --merged_pval_file {output.merged_pval_file}"

rule visualize_paired_dpm_methylation_boxplots:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/methylation/052-paired_test_methylation_boxplots.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/methylation/dpm_merged_pvalues.tsv",

        script = BIN_DIR + "/nanopore_methylation/052c-visualize_paired_dpm_methylation_boxplots.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures_dml/052-visualize_paired_dpm_methylation_boxplots.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"









