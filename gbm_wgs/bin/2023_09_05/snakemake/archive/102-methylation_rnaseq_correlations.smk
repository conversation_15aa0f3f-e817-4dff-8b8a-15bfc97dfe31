# Alec <PERSON>i - <EMAIL>



###################################
# compare p-values between the two data sources
###################################

rule rna_methylation_pvalues:
    input:
        pvalue_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation/input_pvalues.tsv",

        script = BIN_DIR + "/rnaseq_interpretation/004c-rna_methylation_pvalues.py"

    output:
        figure_data_file = RES_DIR + "/analysis_rna/_figure_data/rna_methyl_pvalues.tsv",

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{PYTHON} {input.script} --pvalue_file {input.pvalue_file} --figure_data_file {output.figure_data_file}"

rule visualize_rna_methylation_pvalues:
    input:
        figure_data_file = RES_DIR + "/analysis_rna/_figure_data/rna_methyl_pvalues.tsv",

        script = BIN_DIR + "/rnaseq_interpretation/004d-visualize_rna_methylation_pvalues.R"

    output:
        figure_file = RES_DIR + "/analysis_rna/_figures/004-pvalue_distributions.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"





# ###################################
# # methylation and expression comparison
# ###################################

# # visualize methylation correlation with expression
# rule methylation_expression_correlation:
#     input:
#         gene_expression_file = RES_DIR + "/rnaseq_star/tpm.tsv",
#         gene_translation_file = REF_DATA_DIR + "/ensembl_canonical_hg38.tsv",
#         combined_promoter_methylation_file = RES_DIR + "/analysis_nanopore/methylation/combined_mean_promoter_methylation.tsv",

#         script = BIN_DIR + "/nanopore_methylation/003e-methylation_expression_correlations.py",
#         r_script = BIN_DIR + "/nanopore_methylation/003f-methylation_expression_correlations.R"

#     output:
#         figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/003-methylation_expression_correlations.tsv",
#         figure_stats_file = RES_DIR + "/analysis_nanopore/_figure_data/003-methylation_expression_correlations_stats.tsv",

#         figure_file = RES_DIR + "/analysis_nanopore/_figures/003-methylation_expression_correlations.pdf"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --gene_expression_file {input.gene_expression_file} --gene_translation_file {input.gene_translation_file} --combined_promoter_methylation_file {input.combined_promoter_methylation_file} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file} --figure_file {output.figure_file}"















###################################
# methylation and expression comparison
###################################

# visualize methylation correlation with expression
rule methylation_expression_correlation:
    input:
        gene_expression_file = RES_DIR + "/rnaseq_star/tpm.tsv",
        gene_translation_file = REF_DATA_DIR + "/ensembl_canonical_hg38.tsv",
        combined_promoter_methylation_file = RES_DIR + "/analysis_nanopore/methylation/combined_mean_promoter_methylation.tsv",

        script = BIN_DIR + "/nanopore_methylation/003e-methylation_expression_correlations.py",
        r_script = BIN_DIR + "/nanopore_methylation/003f-methylation_expression_correlations.R"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/003-methylation_expression_correlations.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/_figure_data/003-methylation_expression_correlations_stats.tsv",

        figure_file = RES_DIR + "/analysis_nanopore/_figures/003-methylation_expression_correlations.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --gene_expression_file {input.gene_expression_file} --gene_translation_file {input.gene_translation_file} --combined_promoter_methylation_file {input.combined_promoter_methylation_file} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file} --figure_file {output.figure_file}"





###################################
# methylation and rna-seq pathway enrichment
###################################
# volcano plots
rule methylation_rna_volcano_plots:
    input:
        methylation_file = RES_DIR + "/analysis_nanopore/methylation/009-paired_test_methylation_downsampled_part2.tsv",
        rna_seq_file = RES_DIR + "/analysis_rna/_figure_data/001-dgea_data_raw_protein_coding_counts.tsv",

        script = BIN_DIR + "/nanopore_methylation/006g-volcano_plots.py",
        r_script = BIN_DIR + "/nanopore_methylation/006h-volcano_plots.R"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/006-volcano_plot.tsv",

        figure_file = RES_DIR + "/analysis_nanopore/_figures/006-volcano_plot.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --methylation_file {input.methylation_file} --rna_seq_file {input.rna_seq_file} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_file {output.figure_file}"

# DPM of methylation and rna-seq
rule ActivePathways_DPM_ttest_methylation_rnaseq:
    input:
        rnaseq_methylation_merged_file = RES_DIR + "/analysis_nanopore/_figure_data/006-volcano_plot.tsv",

        gmt_file = REF_DATA_DIR + "/hsapiens_gobp_reactome_2024_05_17.gmt",

        script = BIN_DIR + "/nanopore_methylation/006k-ActivePathways_DPM.py",
        r_script = BIN_DIR + "/nanopore_methylation/006f-ActivePathways_DPM.R"

    output:
        pval_file = RES_DIR + "/analysis_nanopore/ActivePathways/006-dpm_input_pvals.tsv",
        fc_file = RES_DIR + "/analysis_nanopore/ActivePathways/006-dpm_input_fcs.tsv",

        output_file = RES_DIR + "/analysis_nanopore/ActivePathways/ap_dpm_methylation_rnaseq.csv"

    params:
        cytoscape_file_prefix = RES_DIR + '/analysis_nanopore/ActivePathways/ap_dpm_methylation_rnaseq-',
        max_min_genes = '750,50'

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --rnaseq_methylation_merged_file {input.rnaseq_methylation_merged_file} --gmt_file {input.gmt_file} --r_script {input.r_script} --pval_file {output.pval_file} --fc_file {output.fc_file} --output_file {output.output_file} --cytoscape_file_prefix {params.cytoscape_file_prefix} --max_min_genes {params.max_min_genes}"


# ActivePathways of methylation alone
rule ActivePathways_ttest_methylation:
    input:
        methylation_file = RES_DIR + "/analysis_nanopore/methylation/009-paired_test_methylation_downsampled_part2.tsv",

        gmt_file = REF_DATA_DIR + "/hsapiens_gobp_reactome_2024_05_17.gmt",

        script = BIN_DIR + "/nanopore_methylation/011a-ActivePathways_DPM.py",
        r_script = BIN_DIR + "/nanopore_methylation/011b-ActivePathways_DPM.R"

    output:
        pval_file = RES_DIR + "/analysis_nanopore/ActivePathways_methyl/011-dpm_input_pvals.tsv",

        output_file = RES_DIR + "/analysis_nanopore/ActivePathways_methyl/ap_dpm_methylation_rnaseq.csv"

    params:
        cytoscape_file_prefix = RES_DIR + '/analysis_nanopore/ActivePathways_methyl/ap_dpm_methylation_rnaseq-',
        max_min_genes = '750,50'

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --methylation_file {input.methylation_file} --gmt_file {input.gmt_file} --r_script {input.r_script} --pval_file {output.pval_file} --output_file {output.output_file} --cytoscape_file_prefix {params.cytoscape_file_prefix} --max_min_genes {params.max_min_genes}"






