# Alec <PERSON>i - <EMAIL>


###################################
# nanopore basecalling with dorado
###################################

rule split_fast5s:
    output:
        RAW_DATA_DIR + "/fast5s/null.txt",
        RAW_DATA_DIR + "/pod5s/null.txt",

        expand("{raw_data_dir}/fast5s/{sample}", raw_data_dir = RAW_DATA_DIR, sample = nanopore_sample_list)

    params:
        script = BIN_DIR + "/nanopore/000-split_fast5s.py",

        production_dir = "/.mounts/labs/reimandlab/production",

        fast5_link_path = RAW_DATA_DIR + "/fast5s",
        pod5_directory = RAW_DATA_DIR + "/pod5s"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTHON} {params.script} --production_dir {params.production_dir} --fast5_link_path {params.fast5_link_path} --pod5_directory {params.pod5_directory}"



rule pod5_conversion:
    input:
        RAW_DATA_DIR + "/fast5s/null.txt",
        RAW_DATA_DIR + "/pod5s/null.txt",
        RAW_DATA_DIR + "/fast5s/{sample}"

    output:
        pod5_file =  RAW_DATA_DIR + "/pod5s/{sample}/{number}/{sample}_{number}.pod5"

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/pod5"

    params:
        fast5_input_regex = RAW_DATA_DIR + "/fast5s/{sample}/{number}/*.fast5"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "pod5 convert fast5 {params.fast5_input_regex} --output {output.pod5_file}"


rule dorado_modified_basecalling:
    input:
        tmp = RAW_DATA_DIR + "/pod5s/{sample}/{number}/{sample}_{number}.pod5"

    output:
        modified_bam_file = temp(RAW_DATA_DIR + "/nanopore_ubams/{sample}_{number}-modified_bases.bam")

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/nvidia_cuda"

    params:
        pod5_folder =  RAW_DATA_DIR + "/pod5s/{sample}/{number}/"

    resources:
        threads = 1,
        queue = "gpu.q -l gpu=1",
        jobtime = '7:0:0:0',
        individual_core_memory = '60G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/dorado_compiled/dorado-0.6.1-linux-x64/bin/dorado basecaller --device cuda:0 /.mounts/labs/reimandlab/private/users/abahcheli/software/dorado/dna_r9.4.1_e8_sup@v3.3 {params.pod5_folder} --modified-bases-models /.mounts/labs/reimandlab/private/users/abahcheli/software/dorado/dna_r9.4.1_e8_sup@v3.3_5mCG_5hmCG@v0 > {output.modified_bam_file}"


# rule dorado_modified_5mCG_basecalling:
#     input:
#         tmp = RAW_DATA_DIR + "/pod5s/{sample}/{number}/{sample}_{number}.pod5"

#     output:
#         modified_bam_file = temp(RAW_DATA_DIR + "/nanopore_ubams/{sample}_{number}-modified_bases.bam")

#     conda:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/nvidia_cuda"

#     params:
#         pod5_folder =  RAW_DATA_DIR + "/pod5s/{sample}/{number}/"

#     resources:
#         threads = 1,
#         queue = "gpu.q -l gpu=1",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '60G'
        
#     shell:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/software/dorado_raw/dorado/cmake-build/bin/dorado basecaller --recursive --device cuda:0 /.mounts/labs/reimandlab/private/users/abahcheli/software/dorado/dna_r9.4.1_e8_sup@v3.3 {params.pod5_folder} --modified-bases-models /.mounts/labs/reimandlab/private/users/abahcheli/software/dorado/dna_r9.4.1_e8_sup@v3.3_5mCG@v0.1 > {output.modified_bam_file}"


rule dorado_sup_basecalling:
    input:
        tmp = RAW_DATA_DIR + "/pod5s/{sample}/{number}/{sample}_{number}.pod5"

    output:
        modified_bam_file = temp(RAW_DATA_DIR + "/nanopore_sup_ubams/{sample}_{number}-modified_bases.bam")

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/nvidia_cuda"

    params:
        pod5_folder =  RAW_DATA_DIR + "/pod5s/{sample}/{number}/"

    resources:
        threads = 1,
        queue = "gpu.q -l gpu=1",
        jobtime = '7:0:0:0',
        individual_core_memory = '60G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/dorado_raw/dorado/cmake-build/bin/dorado basecaller --device cuda:0 /.mounts/labs/reimandlab/private/users/abahcheli/software/dorado/dna_r9.4.1_e8_sup@v3.6 {params.pod5_folder} > {output.modified_bam_file}"




rule list_ubams:
    input:
        expand("{raw_data_dir}/{{run_type}}_ubams/{sample}_{number}-modified_bases.bam", raw_data_dir = RAW_DATA_DIR, sample = nanopore_sample_list, number = multi_processing_number_list)

    output:
        list_of_ubam_files = RAW_DATA_DIR + "/{run_type}_ubams/{sample}_ubams.txt"

    params:
        ubam_dir = RAW_DATA_DIR + "/{run_type}_ubams/",
        sample = "{sample}"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "ls {params.ubam_dir}/*{params.sample}* | grep -v combined > {output.list_of_ubam_files}"


rule merge_ubams:
    input:
        expand("{raw_data_dir}/{{run_type}}_ubams/{sample}_{number}-modified_bases.bam", raw_data_dir = RAW_DATA_DIR, sample = nanopore_sample_list, number = multi_processing_number_list), 

        list_of_ubam_files = RAW_DATA_DIR + "/{run_type}_ubams/{sample}_ubams.txt"

    output:
        merged_ubam_file = RAW_DATA_DIR + "/{run_type}_ubams/{sample}-combined_modified_bases.bam"

    resources:
        threads = 5,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '8G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools cat -b {input.list_of_ubam_files} --no-PG -@ {resources.threads} -o {output.merged_ubam_file}"



rule link_ubams:
    input:
        merged_ubam_file = RAW_DATA_DIR + "/{run_type}_ubams/{sample}-combined_modified_bases.bam"

    output:
        linked_ubam_file = RES_DIR + "/{run_type}/input_ubams/{sample}-combined_modified_bases.bam"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        "ln -s {input.merged_ubam_file} {output.linked_ubam_file}"




###################################
# alignment and processing with epi2me
###################################

rule setup_wf_basecalling:
    input:
        linked_ubam_file = RES_DIR + "/{run_type}/input_ubams/{sample}-combined_modified_bases.bam",

        script = BIN_DIR + "/nanopore/001a-setup_wf_alignment.py"

    output:
        wf_execution_file = BIN_DIR + "/nanopore/{run_type}_wf_alignment-{sample}"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        "{PYTHON} {input.script} --linked_ubam_file {input.linked_ubam_file} --wf_execution_file {output.wf_execution_file}"


rule setup_wf_alignment:
    input:
        linked_ubam_file = RES_DIR + "/{run_type}/input_ubams/{sample}-combined_modified_bases.bam",

        script = BIN_DIR + "/epi2me_nextflow/alignment/001a-setup_wf_alignment.py"

    output:
        wf_execution_file = BIN_DIR + "/nanopore/{run_type}_wf_alignment-{sample}"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        "{PYTHON} {input.script} --linked_ubam_file {input.linked_ubam_file} --wf_execution_file {output.wf_execution_file}"


rule setup_wf_somatic_variation:
    input:
        tumor_bam_file = RES_DIR + "/{run_type}/{sample_code}-{tumor}/{sample_code}-{tumor}-combined_modified_bases.sorted.aligned.bam",
        germline_bam_file = RES_DIR + "/{run_type}/{sample_code}-blood/{sample_code}-blood-combined_modified_bases.sorted.aligned.bam",

        genome_fasta = RES_DIR + "/{run_type}/hg38_chr.fa",

        script = BIN_DIR + "/epi2me_nextflow/somatic_variation/001b-setup_wf_somatic_variation.py"

    output:
        wf_execution_file = BIN_DIR + "/nanopore/{run_type}_wf_somatic_variation-{sample_code}-{tumor}"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        "{PYTHON} {input.script} --tumor_bam_file {input.tumor_bam_file} --germline_bam_file {input.germline_bam_file} --genome_fasta {input.genome_fasta} --wf_execution_file {output.wf_execution_file}"

















