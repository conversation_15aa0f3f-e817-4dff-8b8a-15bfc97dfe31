# Alec <PERSON>i - <EMAIL>

#####################
# summarize cgc variants
#####################

rule visualize_cgc_impact:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/_figure_data/001-combined_vcfs_VEP.tsv",

        script = BIN_DIR + "/sarek_wgs/001b-visualize_cgc_impact.py",
        r_script = BIN_DIR + "/sarek_wgs/001c-visualize_cgc_impact.R"

    output:
        figure_stats_file = RES_DIR + "/analysis_sarek/vcf_figure_data/001-visualize_cgc_impact_stats.tsv",
        figure_file = RES_DIR + "/analysis_sarek/_figures/001-visualize_cgc_impact.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTH<PERSON>} {input.script} --figure_data_file {input.figure_data_file} --r_script {input.r_script} --figure_stats_file {output.figure_stats_file} --figure_file {output.figure_file}"

