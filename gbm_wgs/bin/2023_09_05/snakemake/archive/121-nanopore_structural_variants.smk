# Alec <PERSON>cheli - <EMAIL>


###################################
# SVs and CNAs
###################################

# SVs circos plots
rule process_nanopore_svs_cnas:
    input:
        expand("{res_dir}/nanopore_somatic/{sample}/{sample}.wf-somatic-sv.vcf.gz", res_dir = RES_DIR, sample = sample_list, tumor = tumor_types),

        script = BIN_DIR + "/nanopore_svs_cnas/001a-preprocess_nanopore_structural_variants.py"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/svs_cnas/001-nanopore_sv_cnas.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/svs_cnas/001-nanopore_sv_cnas_stats.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file}"

rule process_nanopore_coverage:
    input:
        expand("{res_dir}/nanopore_somatic/{sample}/{sample}/qc/coverage/{sample}_tumor.regions.bed.gz", res_dir = RES_DIR, sample = sample_list),

        script = BIN_DIR + "/nanopore_svs_cnas/001c-process_nanopore_coverage.py"

    output:
        processed_coverage_file = RES_DIR + "/analysis_nanopore/svs_cnas/001-process_nanopore_coverage.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --processed_coverage_file {output.processed_coverage_file}"



###################################
# visualize SVs and CNAs
###################################

rule circos_nanopore_structural_variants_visualize:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/svs_cnas/001-nanopore_sv_cnas.tsv",

        r_script = BIN_DIR + "/nanopore_svs_cnas/001b-circos_nanopore_structural_variants.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures_sv_cnas/001-circos_nanopore_structural_variants.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"

# summarize numbers of structural variants
rule summarize_nanopore_svs_cnas_visualize:
    input:
        sv_cna_file = RES_DIR + "/analysis_nanopore/svs_cnas/001-nanopore_sv_cnas.tsv",

        script = BIN_DIR + "/nanopore_svs_cnas/002a-summarize_nanopore_svs_cnas.py",
        r_script = BIN_DIR + "/nanopore_svs_cnas/002b-summarize_nanopore_svs_cnas.R"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/svs_cnas/002-summarize_nanopore_svs_cnas.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/svs_cnas/002-summarize_nanopore_svs_cnas_stats.tsv",

        figure_file = RES_DIR + "/analysis_nanopore/_figures_sv_cnas/002-summarize_nanopore_svs_cnas.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --sv_cna_file {input.sv_cna_file} --r_script {input.r_script} --figure_stats_file {output.figure_stats_file} --figure_data_file {output.figure_data_file} --figure_file {output.figure_file}"

# summarize numbers of structural variants
rule circos_coverage_visualize:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/svs_cnas/001-process_nanopore_coverage.tsv",
        cna_summary_file = RES_DIR + "/analysis_nanopore/svs_cnas/002-summarize_nanopore_svs_cnas.tsv",

        script = BIN_DIR + "/nanopore_svs_cnas/002c-circos_coverage_visualize.py",
        r_script = BIN_DIR + "/nanopore_svs_cnas/002d-circos_coverage_visualize.R"

    output:
        figure_stats_file = RES_DIR + "/analysis_nanopore/svs_cnas/002-circos_coverage_visualize_cnas.tsv",

        figure_file = RES_DIR + "/analysis_nanopore/_figures_sv_cnas/002-circos_coverage_visualize.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --figure_data_file {input.figure_data_file} --cna_summary_file {input.cna_summary_file} --r_script {input.r_script} --figure_stats_file {output.figure_stats_file} --figure_file {output.figure_file}"



###################################
# visualize SVs and CNAs
###################################






# rule svs_to_genes_nanopore:
#     input:
#         cna_combined_file = RES_DIR + "/analysis_nanopore/_figure_data/001-cna_circos_stats.tsv",
#         ensembl_gene_bed_file = REF_DATA_DIR + "/ensembl_hg38_27_10_2022.bed",

#         script = BIN_DIR + "/nanopore_interpretations/001e-cnas_to_genes.py",
#         r_script = BIN_DIR + "/nanopore_interpretations/001f-cnas_to_genes.R"

#     output:
#         figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/002-gene_alterations_by_cna.tsv",

#         figure_file = RES_DIR + "/analysis_nanopore/_figures/002-gene_alterations_by_cna.pdf"
    
#     conda:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --cna_combined_file {input.cna_combined_file} --ensembl_gene_bed_file {input.ensembl_gene_bed_file} --figure_data_file {output.figure_data_file} --r_script {input.r_script} --figure_file {output.figure_file}"



###################################
# haplotype-specific mutations
###################################

# rule methyl_nanopore_by_gene:
#     input:
#         # expand("{res_dir}/nanopore/{sample}/{sample}/mod/5mC/DMR/{sample}.5mC.dmr.tsv", res_dir = RES_DIR, sample = new_nanopore_list, tumor = tumor_types),
#         methylation_file = RES_DIR + "/nanopore/RLGS11-primary/RLGS11-primary/mod/5mC/DMR/RLGS11-primary.5mC.dmr.tsv",
#         # methylation_file2 = RES_DIR + "/nanopore/RLGS11-recurrent/RLGS11-recurrent/mod/5mC/DMR/RLGS11-recurrent.5mC.dmr.tsv",

#         ensembl_gene_bed_file = REF_DATA_DIR + "/ensembl_hg38_27_10_2022.bed",

#         script = BIN_DIR + "/nanopore_interpretations/002a-interpret_methyl_nanopore.py",
#         r_script = BIN_DIR + "/nanopore_interpretations/002b-interpret_methyl_nanopore.R"

#     output:
#         figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/002-methyl_circos.tsv",

#         figure_file = RES_DIR + "/analysis_nanopore/_figures/002-methyl_circos.pdf"

#     params:
#         results_dir = RES_DIR + "/analysis_nanopore"
    
#     conda:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --methylation_file {input.methylation_file} --ensembl_gene_bed_file {input.ensembl_gene_bed_file} --figure_data_file {output.figure_data_file} --r_script {input.r_script} --figure_file {output.figure_file}"





# ###################################
# # mutation descriptions - nanopore and WGS
# ###################################

# rule maf_cna_summary:
#     input:
#         snv_file = RES_DIR + "/analysis_nanopore/_figure_data/001-{software}-combined.maf",
#         cna_combined_file = RES_DIR + "/analysis_nanopore/_figure_data/002-gene_alterations_by_cna.tsv",

#         script = BIN_DIR + "/nanopore_interpretations/001c-maf_cna_summary.py",
#         r_script = BIN_DIR + "/nanopore_interpretations/001d-maf_cna_summary.R"

#     output:
#         figure_data_file = RES_DIR + "/analysis_nanopore/{software}-maf_cna.tsv",

#         figure_file = RES_DIR + "/analysis_nanopore/_figures/001-{software}-maf_cna.pdf"

#     params:
#         wgs_results_directory = RES_DIR + "/wgs_processing"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '40G'
        
#     shell:
#         "{PYTHON} {input.script} --snv_file {input.snv_file} --cna_combined_file {input.cna_combined_file} --figure_data_file {output.figure_data_file} --r_script {input.r_script} --figure_file {output.figure_file}"



# ###################################
# # pyclone
# ###################################
# rule vcf_pyclone_prep:
#     input:
#         input_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-final_{software}.vcf.gz",

#         script = BIN_DIR + "/nanopore_interpretations/004a-vcf_preparation.py"

#     output:
#         output_vcf = RES_DIR + "/analysis_nanopore/pyclone_vi/{sample}-final_modified_{software}.vcf"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --input_vcf {input.input_vcf} --output_vcf {output.output_vcf}"


# rule pyclone_vi_input_prep:
#     input:
#         input_vcf = RES_DIR + "/analysis_nanopore/pyclone_vi/{sample}-final_modified_{software}.vcf",
#         cna_file = RES_DIR + "/analysis_nanopore/_figure_data/002-cnas_primary_recurrent.tsv",

#         script = BIN_DIR + "/nanopore_interpretations/004b-pyclone_vi_preparation.py"

#     output:
#         output_tsv = RES_DIR + "/analysis_nanopore/pyclone_vi/{sample}_{software}-pyclone_vi_input.tsv"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --input_vcf {input.input_vcf} --cna_file {input.cna_file} --output_tsv {output.output_tsv} --threads {resources.threads}"


# rule pyclone_vi_analysis_part1:
#     input:
#         input_tsv = RES_DIR + "/analysis_nanopore/pyclone_vi/{sample}_{software}-pyclone_vi_input.tsv"

#     output:
#         pyclone_results = RES_DIR + "/analysis_nanopore/pyclone_vi/{sample}_{software}-pyclone_vi.h5"

#     conda:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/pyclone-vi"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '50G'
        
#     shell:
#         "pyclone-vi fit -i {input.input_tsv} -o {output.pyclone_results}"
#         # "pyclone-vi fit -i {input.input_tsv} -o {output.pyclone_results} -c 40 -d beta-binomial -r 10"


# rule pyclone_vi_analysis_part2:
#     input:
#         pyclone_results = RES_DIR + "/analysis_nanopore/pyclone_vi/{sample}_{software}-pyclone_vi.h5"

#     output:
#         pyclone_results_final = RES_DIR + "/analysis_nanopore/pyclone_vi/{sample}_{software}-pyclone_vi.tsv"

#     conda:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/pyclone-vi"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '100G'
        
#     shell:
#         "pyclone-vi write-results-file -i {input.pyclone_results} -o {output.pyclone_results_final}"


# rule pyclone_vi_visualize_results:
#     input:
#         expand("{res_dir}/analysis_nanopore/pyclone_vi/{sample}_mutect2-pyclone_vi.tsv", res_dir = RES_DIR, sample = wgs_sample_list),

#         script = BIN_DIR + "/nanopore_interpretations/004c-pyclone_visualization.py",
#         r_script = BIN_DIR + "/nanopore_interpretations/004d-pyclone_visualization.R"

#     output:
#         figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/004-pyclone_visualization.tsv",
#         figure_stats_file = RES_DIR + "/analysis_nanopore/_figure_data/004-pyclone_visualization_stats.tsv",

#         figure_file = RES_DIR + "/analysis_nanopore/_figures/004-pyclone_visualization.pdf"

#     params:
#         pyclone_dir = RES_DIR + "/analysis_nanopore/pyclone_vi"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --pyclone_dir {params.pyclone_dir} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file} --r_script {input.r_script} --figure_file {output.figure_file}"



# ###################################
# # CNA summaries
# ###################################

# rule cnas_primary_recurrent_comparison:
#     input:
#         cna_file = RES_DIR + "/analysis_nanopore/_figure_data/002-combined_cnas.tsv",

#         script = BIN_DIR + "/nanopore_interpretations/002c-cnas_primary_recurrent_comparison.py",
#         r_script = BIN_DIR + "/nanopore_interpretations/002d-cnas_primary_recurrent_comparison.R"

#     output:
#         figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/002-cnas_primary_recurrent.tsv",
#         figure_stats_file = RES_DIR + "/analysis_nanopore/_figure_data/002-cnas_primary_recurrent_stats.tsv",

#         figure_file = RES_DIR + "/analysis_nanopore/_figures/002-cnas_primary_recurrent.pdf"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --cna_file {input.cna_file} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file} --r_script {input.r_script} --figure_file {output.figure_file}"


# rule cnas_primary_recurrent_sorted_heatmap:
#     input:
#         input_stats_file = RES_DIR + "/analysis_nanopore/_figure_data/002-cnas_primary_recurrent_stats.tsv",

#         script = BIN_DIR + "/nanopore_interpretations/002g-cnas_primary_recurrent_sorted_heatmap.py",
#         r_script = BIN_DIR + "/nanopore_interpretations/002h-cnas_primary_recurrent_sorted_heatmap.R"

#     output:
#         figure_stats_file = RES_DIR + "/analysis_nanopore/_figure_data/002-cnas_primary_recurrent_sorted_heatmap.tsv",

#         figure_file = RES_DIR + "/analysis_nanopore/_figures/002-cnas_primary_recurrent_sorted_heatmap.pdf"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --input_stats_file {input.input_stats_file} --figure_stats_file {output.figure_stats_file} --r_script {input.r_script} --figure_file {output.figure_file}"








