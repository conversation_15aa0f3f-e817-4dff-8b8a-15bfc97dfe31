# Alec <PERSON>i - <EMAIL>



###################################
# compare p-values between the two data sources
###################################

rule rna_methylation_pvalues:
    input:
        pvalue_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation/input_pvalues.tsv",

        script = BIN_DIR + "/rnaseq_interpretation/004c-rna_methylation_pvalues.py"

    output:
        figure_data_file = RES_DIR + "/analysis_rna/_figure_data/rna_methyl_pvalues.tsv",

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{PYTHON} {input.script} --pvalue_file {input.pvalue_file} --figure_data_file {output.figure_data_file}"

rule visualize_rna_methylation_pvalues:
    input:
        figure_data_file = RES_DIR + "/analysis_rna/_figure_data/rna_methyl_pvalues.tsv",

        script = BIN_DIR + "/rnaseq_interpretation/004d-visualize_rna_methylation_pvalues.R"

    output:
        figure_file = RES_DIR + "/analysis_rna/_figures/004-pvalue_distributions.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"


#####################
# compare merged p-values between rna and methlyation
#####################

# create scatterplot comparing directional and non-directional methods
rule pvalue_merging_comparison_with_activepathways:
    input:
        pvalue_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation/input_pvalues.tsv",
        fc_file = RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation/input_fcs.tsv",

        script = BIN_DIR + "/rnaseq_interpretation/004e-pvalue_merging_comparison_with_activepathways.R"

    output:
        comparing_pvalues_file = RES_DIR + "/analysis_rna/rna_methylation/comparing_pvalues.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{AP_RSCRIPT} {input.script} --pvalue_file {input.pvalue_file} --fc_file {input.fc_file} --comparing_pvalues_file {output.comparing_pvalues_file}"

rule scatterplot_rna_methylation_pvalues:
    input:
        figure_data_file = RES_DIR + "/analysis_rna/rna_methylation/comparing_pvalues.tsv",

        script = BIN_DIR + "/rnaseq_interpretation/004f-scatterplot_rna_methylation_pvalues.R"

    output:
        figure_file = RES_DIR + "/analysis_rna/_figures/004-scatterplot_rna_methylation_pvalues.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"




###################################
# correlating gene expression vs. methylation
###################################

# only the protein-coding gene promoters
rule calculate_mean_methylation_per_gene_sample:
    input:
        methylation_by_site_file = RES_DIR + "/analysis_nanopore/methylation/promoter_methylation_data.tsv",
        gene_sites_file = RES_DIR + "/analysis_nanopore/methylation/selected_methylation_sites-wilcoxon_p_value.tsv",

        script = BIN_DIR + "/nanopore_methylation/006a-calculate_mean_methylation_per_gene_sample.py"

    output:
        mean_methylation_per_gene_sample = RES_DIR + "/analysis_nanopore/methylation/006-calculate_mean_methylation_per_gene_sample.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --methylation_by_site_file {input.methylation_by_site_file} --gene_sites_file {input.gene_sites_file} --mean_methylation_per_gene_sample {output.mean_methylation_per_gene_sample}"

# correlate methylation and transcript expression
rule methylation_transcript_expression_correlation:
    input:
        gene_expression_file = RES_DIR + "/analysis_rna/star_salmon/salmon.merged.gene_tpm.tsv",
        mean_methylation_per_gene_sample = RES_DIR + "/analysis_nanopore/methylation/006-calculate_mean_methylation_per_gene_sample.tsv",

        script = BIN_DIR + "/nanopore_methylation/006b-methylation_transcript_expression_correlation.py"

    output:
        correlation_data_file = RES_DIR + "/analysis_nanopore/methylation/006-methylation_transcript_expression_correlation.tsv",
        correlation_stats_file = RES_DIR + "/analysis_nanopore/methylation/006-methylation_transcript_expression_correlation_stats.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --gene_expression_file {input.gene_expression_file} --mean_methylation_per_gene_sample {input.mean_methylation_per_gene_sample} --correlation_data_file {output.correlation_data_file} --correlation_stats_file {output.correlation_stats_file}"

rule visualize_methylation_transcript_correlation:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/methylation/006-methylation_transcript_expression_correlation.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/methylation/006-methylation_transcript_expression_correlation_stats.tsv",

        script = BIN_DIR + "/nanopore_methylation/006c-visualize_methylation_transcript_correlation.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures/006-methylation_transcript_correlations.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"







# ###################################
# # methylation and expression comparison
# ###################################

# # visualize methylation correlation with expression
# rule methylation_expression_correlation:
#     input:
#         gene_expression_file = RES_DIR + "/rnaseq_star/tpm.tsv",
#         gene_translation_file = REF_DATA_DIR + "/ensembl_canonical_hg38.tsv",
#         combined_promoter_methylation_file = RES_DIR + "/analysis_nanopore/methylation/combined_mean_promoter_methylation.tsv",

#         script = BIN_DIR + "/nanopore_methylation/003e-methylation_expression_correlations.py",
#         r_script = BIN_DIR + "/nanopore_methylation/003f-methylation_expression_correlations.R"

#     output:
#         figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/003-methylation_expression_correlations.tsv",
#         figure_stats_file = RES_DIR + "/analysis_nanopore/_figure_data/003-methylation_expression_correlations_stats.tsv",

#         figure_file = RES_DIR + "/analysis_nanopore/_figures/003-methylation_expression_correlations.pdf"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --gene_expression_file {input.gene_expression_file} --gene_translation_file {input.gene_translation_file} --combined_promoter_methylation_file {input.combined_promoter_methylation_file} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file} --figure_file {output.figure_file}"











