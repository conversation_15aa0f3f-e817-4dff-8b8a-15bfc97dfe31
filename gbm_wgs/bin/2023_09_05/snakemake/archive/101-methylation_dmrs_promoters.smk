# Alec <PERSON>i - <EMAIL>


###################################
# summary of DMRs 
###################################

rule dmr_nanopore_by_sample:
    input:
        expand("{res_dir}/nanopore_evolution/{sample}/{sample}-primary/mod/5mC/DMR/{sample}-primary.5mC.dmr.tsv", res_dir = RES_DIR, sample = sample_codes_list),

        script = BIN_DIR + "/nanopore_methylation/001d-dmr_summarize.py"

    output:
        dmr_results_file = RES_DIR + "/analysis_nanopore/_figure_data/001-methylation_genome_overview_stats.tsv"

    params:
        methylation_res_dir = RES_DIR + "/nanopore_evolution/"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTHON} {input.script} --methylation_res_dir {params.methylation_res_dir} --dmr_results_file {output.dmr_results_file}"

rule summarize_number_of_dmr:
    input:
        methylation_stats_file = RES_DIR + "/analysis_nanopore/_figure_data/001-methylation_genome_overview_stats.tsv",

        script = BIN_DIR + "/nanopore_methylation/001e-dmr_summary.py",
        r_script = BIN_DIR + "/nanopore_methylation/001f-dmr_summary.R"

    output:
        figure_stats_file = RES_DIR + "/analysis_nanopore/_figure_data/001-dmr_summary.tsv",

        figure_file = RES_DIR + "/analysis_nanopore/_figures/001-dmr_summary.pdf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTHON} {input.script} --methylation_stats_file {input.methylation_stats_file} --r_script {input.r_script} --figure_stats_file {output.figure_stats_file} --figure_file {output.figure_file}"



###################################
# prepare bed files
###################################

# define the promoters and enhancers in a single file
rule define_protein_gene_promoters_enhancers:
    input:
        protein_coding_genes_gff_file = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.protein_coding_genes.gff",

        script = BIN_DIR + "/nanopore_methylation/005a-protein_gene_promoters_enhancers.py"

    output:
        protein_coding_promoter_bed_file = RES_DIR + "/analysis_nanopore/methylation/protein_gene_promoter_enhancer_regions.bed"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --protein_coding_genes_gff_file {input.protein_coding_genes_gff_file} --protein_coding_promoter_bed_file {output.protein_coding_promoter_bed_file}"

# gzip file 
rule bgzip_file:
    input:
        input_file = RES_DIR + "/analysis_nanopore/{base_filename}"

    output:
        file_gzipped = temp(RES_DIR + "/analysis_nanopore/{base_filename}.gz")

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bgzip -k {input.input_file}"



###################################
# methylation of multiple promoters and enhancers 
###################################

# find promoters and enhancers that belong to multiple genes
rule intersect_promoters_enhancers_self:
    input:
        promoter_enhancer_bed_file = RES_DIR + "/analysis_nanopore/methylation/protein_gene_promoter_enhancer_regions.bed.gz"

    output:
        intersected_bed_file = RES_DIR + "/analysis_nanopore/methylation/promoter_enhancer_self_intersect.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.promoter_enhancer_bed_file} -b {input.promoter_enhancer_bed_file} -wa -wb | awk -F '\t' '$1 != $7 || $2 != $8 || $3 != $9' > {output.intersected_bed_file}"""

rule process_promoter_self_overlaps:
    input:
        intersected_bed_file = RES_DIR + "/analysis_nanopore/methylation/promoter_enhancer_self_intersect.bed",

        script = BIN_DIR + "/nanopore_methylation/005b-process_promoter_self_overlaps.py"

    output:
        common_promoter_file = RES_DIR + "/analysis_nanopore/methylation/promoter_self_overlaps_processed.bed"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --intersected_bed_file {input.intersected_bed_file} --common_promoter_file {output.common_promoter_file}"

# intersect self-promoters with DMRs
rule intersect_promoters_enhancers_dmrs:
    input:
        common_promoter_file = RES_DIR + "/analysis_nanopore/methylation/promoter_self_overlaps_processed.bed",
        dmr_bed_file = RES_DIR + "/analysis_nanopore/methylation/methylation_genome_overview_stats_sorted.bed.gz"

    output:
        intersected_bed_file = RES_DIR + "/analysis_nanopore/methylation/promoter_enhancer_self_dmr_intersected.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.common_promoter_file} -b {input.dmr_bed_file} -wa -wb > {output.intersected_bed_file}"""

# statistically analyze differentially methylated promoters of multiple genes
rule multiple_promoters_enhancers_dmrs_methylated:
    input:
        intersected_bed_file = RES_DIR + "/analysis_nanopore/methylation/promoter_enhancer_self_dmr_intersected.bed",
        single_promoter_file = RES_DIR + "/analysis_nanopore/_figure_data/004-dmr_cgc_summary.tsv",
        double_promoter_file = RES_DIR + "/analysis_nanopore/methylation/promoter_self_overlaps_processed.bed",
        promoter_file = RES_DIR + "/analysis_nanopore/methylation/protein_promoter_regions.bed.gz",

        script = BIN_DIR + "/nanopore_methylation/005c-multiple_promoters_enhancers_dmrs_methylated.py",
        r_script = BIN_DIR + "/nanopore_methylation/005d-multiple_promoters_enhancers_dmrs_methylated.R"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/005-multiple_promoters_enhancers_dmrs_methylated.tsv",

        figure_file = RES_DIR + "/analysis_nanopore/_figures/005-multiple_promoters_enhancers_dmrs_methylated.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --intersected_bed_file {input.intersected_bed_file} --single_promoter_file {input.single_promoter_file} --double_promoter_file {input.double_promoter_file} --promoter_file {input.promoter_file} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_file {output.figure_file}"


# visualize the differentially methylated double promoters
rule visualize_multiple_promoter_dmr:
    input:
        intersected_bed_file = RES_DIR + "/analysis_nanopore/methylation/promoter_enhancer_self_dmr_intersected.bed",

        script = BIN_DIR + "/nanopore_methylation/005e-visualize_multiple_promoter_dmr.py",
        r_script = BIN_DIR + "/nanopore_methylation/005f-visualize_multiple_promoter_dmr.R"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/005-visualize_multiple_promoter_dmr.tsv",

        figure_file = RES_DIR + "/analysis_nanopore/_figures/005-visualize_multiple_promoter_dmr.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --intersected_bed_file {input.intersected_bed_file} --single_promoter_file {input.single_promoter_file} --double_promoter_file {input.double_promoter_file} --promoter_file {input.promoter_file} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_file {output.figure_file}"



###################################
# find differentially methylated promoters from MODKIT
###################################

# find differentially methylated promoters
rule promoter_dmr_prep:
    input:
        figure_stats_file = RES_DIR + "/analysis_nanopore/_figure_data/001-methylation_genome_overview_stats.tsv",

        script = BIN_DIR + "/nanopore_methylation/004a-dmr_bed_prep.py"

    output:
        dmr_bed_file = RES_DIR + "/analysis_nanopore/methylation/methylation_genome_overview_stats_sorted.bed.gz"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --figure_stats_file {input.figure_stats_file} --dmr_bed_file {output.dmr_bed_file}"

rule bed_dmr_intersect_files:
    input:
        promoter_bed_file = RES_DIR + "/analysis_nanopore/methylation/protein_promoter_regions.bed",
        dmr_bed_file = RES_DIR + "/analysis_nanopore/methylation/methylation_genome_overview_stats_sorted.bed.gz"

    output:
        intersected_bed_file = RES_DIR + "/analysis_nanopore/methylation/dmr_promoters.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.promoter_bed_file} -b {input.dmr_bed_file} -wa -wb > {output.intersected_bed_file}"""


# associate differentially methylated promoters with gene expression
rule promoter_dmr_expression_correlations:
    input:
        gene_expression_file = RES_DIR + "/rnaseq_star/tpm.tsv",
        gene_translation_file = REF_DATA_DIR + "/ensembl_canonical_hg38.tsv",
        intersected_bed_file = RES_DIR + "/analysis_nanopore/methylation/dmr_promoters.bed",

        script = BIN_DIR + "/nanopore_methylation/004b-promoter_dmr_expression_correlations.py",
        r_script = BIN_DIR + "/nanopore_methylation/004c-promoter_dmr_expression_correlations.R"

    output:
        dmr_promoter_results_file = RES_DIR + "/analysis_nanopore/methylation/promoter_dmr_expression.tsv",

        figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/004-promoter_dmr_expression_correlations.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/_figure_data/004-promoter_dmr_expression_correlations_stats.tsv",

        figure_file = RES_DIR + "/analysis_nanopore/_figures/004-promoter_dmr_expression_correlations.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --gene_expression_file {input.gene_expression_file} --gene_translation_file {input.gene_translation_file} --intersected_bed_file {input.intersected_bed_file} --r_script {input.r_script} --dmr_promoter_results_file {output.dmr_promoter_results_file} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file} --figure_file {output.figure_file}"


# find cgc genes in DMRs
rule dmr_cgc_summary:
    input:
        dmr_promoter_results_file = RES_DIR + "/analysis_nanopore/methylation/promoter_dmr_expression.tsv",
        cgc_file = REF_DATA_DIR + "/cgc_v100_16072024.tsv",

        script = BIN_DIR + "/nanopore_methylation/004d-dmr_cgc_summary.py",
        r_script = BIN_DIR + "/nanopore_methylation/004e-dmr_cgc_summary.R"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/004-dmr_cgc_summary.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/_figure_data/004-dmr_cgc_summary_stats.tsv",

        figure_file = RES_DIR + "/analysis_nanopore/_figures/004-dmr_cgc_summary.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --dmr_promoter_results_file {input.dmr_promoter_results_file} --cgc_file {input.cgc_file} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file} --figure_file {output.figure_file}"




# intersect the DMRs with raw methylation
rule prepare_dmr_file_intersect:
    input:
        dmr_file = RES_DIR + "/analysis_nanopore/methylation/promoter_dmr_expression.tsv",

        script = BIN_DIR + "/nanopore_methylation/004f-prepare_dmr_file_intersect.py"

    output:
        expand("{res_dir}/analysis_nanopore/methylation/{sample}-dmr_sorted.bed", res_dir = RES_DIR, sample = sample_codes_list)

    params:
        methylation_res_dir = RES_DIR + "/analysis_nanopore/methylation/"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --dmr_file {input.dmr_file} --methylation_res_dir {params.methylation_res_dir}"

rule prepare_methylation_file_intersect:
    input:
        methyl_file = RES_DIR + "/nanopore_evolution/{sample}/{sample}-primary/mod/5mC/bedMethyl/5mC.{sample}-primary_{condition}.bed.gz",

        script = BIN_DIR + "/nanopore_methylation/004g-prepare_methylation_file_intersect.py"

    output:
        methyl_outfile = RES_DIR + "/analysis_nanopore/methylation/{sample}_{condition}-methyl_sorted.bed"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --methyl_file {input.methyl_file} --methyl_outfile {output.methyl_outfile}"

rule intersect_dmr_methyl:
    input:
        dmr_outfile = RES_DIR + "/analysis_nanopore/methylation/{sample}-dmr_sorted.bed",
        methyl_outfile = RES_DIR + "/analysis_nanopore/methylation/{sample}_{condition}-methyl_sorted.bed.gz"

    output:
        intersected_bed_file = RES_DIR + "/analysis_nanopore/methylation/{sample}_{condition}-dmr_raw_methylation.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.dmr_outfile} -b {input.methyl_outfile} -wa -wb > {output.intersected_bed_file}"""


# visualize the DMR differences at the nucleotide level
rule visualize_raw_methyl_dmr:
    input:
        expand("{res_dir}/analysis_nanopore/methylation/{sample}_{condition}-dmr_raw_methylation.tsv", res_dir = RES_DIR, sample = sample_codes_list, condition = ['normal', 'tumor']),

        cgc_file = REF_DATA_DIR + "/cgc_v100_16072024.tsv",

        script = BIN_DIR + "/nanopore_methylation/004h-visualize_raw_methyl_dmr.py",
        r_script = BIN_DIR + "/nanopore_methylation/004i-visualize_raw_methyl_dmr.R"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/004-visualize_raw_methyl_dmr.tsv",
        figure_file = RES_DIR + "/analysis_nanopore/_figures/004-visualize_raw_methyl_dmr.pdf"

    params:
        methylation_res_dir = RES_DIR + "/analysis_nanopore/methylation/"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --methylation_res_dir {params.methylation_res_dir} --cgc_file {input.cgc_file} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_file {output.figure_file}"












