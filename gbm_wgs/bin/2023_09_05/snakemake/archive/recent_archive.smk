# Alec Ba<PERSON>cheli - <EMAIL>




#####################
# visualize chromosome arm CNAs
#####################

rule process_single_cna_file:
    input:
        expand("{res_dir}/analysis_sarek/consensus_cnas/{sample}-{tumor}_cnas_merged_unique.tsv", res_dir = RES_DIR, sample = sample_codes_list, tumor = tumor_types),

        script = BIN_DIR + "/sarek_cnas/005a-process_single_cna_file.py"

    output:
        cna_file = RES_DIR + "/analysis_sarek/_figure_data/process_single_cna_file.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --cna_file {output.cna_file}"


rule intersect_cnas_with_chromosome_arms:
    input:
        cna_gene_loci_file = REF_DATA_DIR + "/genomics/cna_gene_loci.tsv",
        cna_file = RES_DIR + "/analysis_sarek/_figure_data/process_single_cna_file.tsv"

    output:
        intersected_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/intersect_cnas_with_chromosome_arms.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a <(tail -n +2 {input.cna_gene_loci_file}) -b <(tail -n +2 {input.cna_file}) -wa -wb > {output.intersected_bed_file}"""


# subset CNAs with chromosome arms and genes of interest
rule subset_cnas_chr_arms:
    input:
        cna_file_intersected = RES_DIR + "/analysis_sarek/gene_impacts/intersect_cnas_with_chromosome_arms.tsv",
        pcg_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions.bed",

        script = BIN_DIR + "/sarek_cnas/006a-subset_cnas_chr_arms.py"

    output:
        figure_data_file = RES_DIR + "/analysis_sarek/_figure_data/subset_cnas_chr_arms.tsv",
        figure_stats_file = RES_DIR + "/analysis_sarek/_figure_data/subset_cnas_chr_arms_stats.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --cna_file_intersected {input.cna_file_intersected} --pcg_bed_file {input.pcg_bed_file} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file}"

rule visualize_subset_cnas_chr_arms:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/_figure_data/subset_cnas_chr_arms.tsv",
        figure_stats_file = RES_DIR + "/analysis_sarek/_figure_data/subset_cnas_chr_arms_stats.tsv",
        
        r_script = BIN_DIR + "/sarek_cnas/006b-visualize_subset_cnas_chr_arms.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/visualize_subset_cnas_chr_arms.pdf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"






#####################
# visualize CNAs
#####################

rule process_wgs_coverage:
    input:
        expand("{res_dir}/analysis_sarek/consensus_cnas/{sample}-{tumor}_cnas_merged_unique.tsv", res_dir = RES_DIR, sample = sample_codes_list, tumor = tumor_types),

        script = BIN_DIR + "/sarek_wgs/102a-process_wgs_coverage.py"

    output:
        processed_coverage_file = RES_DIR + "/analysis_sarek/_figure_data/102-process_wgs_coverage.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --processed_coverage_file {output.processed_coverage_file}"

rule wgs_cnas_circos:
    input:
        expand("{res_dir}/analysis_sarek/consensus_cnas/{sample}-{tumor}_cnas_merged_unique.tsv", res_dir = RES_DIR, sample = sample_codes_list, tumor = tumor_types),

        figure_data_file = RES_DIR + "/analysis_sarek/_figure_data/102-process_wgs_coverage.tsv",

        script = BIN_DIR + "/sarek_wgs/102b-wgs_cnas_circos.py",
        r_script = BIN_DIR + "/sarek_wgs/102c-wgs_cnas_circos.R"

    output:
        figure_stats_file = RES_DIR + "/analysis_sarek/_figure_data/102-wgs_cnas_circos.tsv",
        figure_file = RES_DIR + "/analysis_sarek/_figures/102-wgs_cnas_circos.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --r_script {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {output.figure_stats_file} --figure_file {output.figure_file}"












