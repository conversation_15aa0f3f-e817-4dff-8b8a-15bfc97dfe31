# Alec Ba<PERSON>cheli - <EMAIL>


#####################
# test differential methylation of MGMT
#####################

rule subset_promoter_test_mgmt:
    input:
        gene_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions.bed",

        script = BIN_DIR + "/nanopore_methylation/041b-subset_promoter_test_mgmt.py"

    output:
        mgmt_protein_bed_file = RES_DIR + "/analysis_nanopore/methylation/mgmt_protein_promoter_region.bed"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --gene_bed_file {input.gene_bed_file} --mgmt_protein_bed_file {output.mgmt_protein_bed_file}"


rule intersect_mgmt_methylation:
    input:
        mgmt_protein_bed_file = RES_DIR + "/analysis_nanopore/methylation/mgmt_protein_promoter_region.bed",
        methyl_outfile = RES_DIR + "/analysis_nanopore/dml_methylation/{sample}-{tumor}_methylation_details.bed"

    output:
        intersected_bed_file = RES_DIR + "/analysis_nanopore/methylation/{sample}-{tumor}_mgmt_methylation.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a <(tail -n +2 {input.mgmt_protein_bed_file}) -b <(tail -n +2 {input.methyl_outfile}) -wa -wb > {output.intersected_bed_file}"""

# merge methylation data
rule merge_test_methylation_data:
    input:
        expand("{res_dir}/analysis_nanopore/methylation/{samples}_mgmt_methylation.bed", res_dir = RES_DIR, samples = ['RLGS2-normal', 'RLGS5-normal', 'RLGS5-tumor', 'RLGS7-tumor', 'RLGS8-normal', 'RLGS9-normal', 'RLGS11-normal', 'RLGS12-tumor']),
        gene_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions.bed",

        script = BIN_DIR + "/nanopore_methylation/041c-merge_test_methylation_data.py"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/methylation/mgmt_promoter_methylation_data.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/methylation/mgmt_promoter_methylation_data_stats.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --gene_bed_file {input.gene_bed_file} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file}"

rule visualize_merge_test_methylation_data:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/methylation/mgmt_promoter_methylation_data.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/methylation/mgmt_promoter_methylation_data_stats.tsv",
        
        script = BIN_DIR + "/nanopore_methylation/041d-visualize_merge_test_methylation_data.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures_dml/visualize_merge_test_methylation_data.pdf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"


# test differential promoter methylation
rule test_differential_promoter_methylation:
    input:
        mgmt_promoter_methylation_combined_file = RES_DIR + "/analysis_nanopore/methylation/mgmt_promoter_methylation_data.tsv",
        purity_ploidy_file = RES_DIR + "/analysis_sarek/consensus_cnas/111-summarize_tumor_ploidy_purity.tsv",

        script = BIN_DIR + "/nanopore_methylation/041e-test_differential_promoter_methylation.py"

    output:
        mgmt_promoter_methylation_stats_file = RES_DIR + "/analysis_nanopore/methylation/test_differential_promoter_methylation.tsv",
        prioritized_stats_file = RES_DIR + "/analysis_nanopore/methylation/test_differential_promoter_methylation_stats.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --mgmt_promoter_methylation_combined_file {input.mgmt_promoter_methylation_combined_file} --purity_ploidy_file {input.purity_ploidy_file} --mgmt_promoter_methylation_stats_file {output.mgmt_promoter_methylation_stats_file} --prioritized_stats_file {output.prioritized_stats_file}"











