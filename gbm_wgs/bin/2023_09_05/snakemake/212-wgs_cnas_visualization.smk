# Alec Ba<PERSON>cheli - <EMAIL>


#####################
# intersect CNAs with PCGs and combine
#####################

# define PCG coordinates
rule define_protein_genes:
    input:
        protein_coding_genes_gff_file = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.protein_coding_genes.gff",

        script = BIN_DIR + "/sarek_cnas/004a-define_protein_genes.py"

    output:
        protein_coding_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions.bed"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --protein_coding_genes_gff_file {input.protein_coding_genes_gff_file} --protein_coding_bed_file {output.protein_coding_bed_file}"

rule intersect_cnas_with_pcgs:
    input:
        protein_coding_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions.bed",
        cna_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}-{tumor}_cnas_merged_unique.tsv"

    output:
        intersected_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/{sample}-{tumor}_cnas_pcgs.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.protein_coding_bed_file} -b {input.cna_file} -wa -wb > {output.intersected_bed_file}"""

# combine pcg-cnas into a single file
rule combine_pcg_cnas:
    input:
        expand("{res_dir}/analysis_sarek/gene_impacts/{sample}-{tumor}_cnas_pcgs.tsv", res_dir = RES_DIR, sample = sample_codes_list, tumor = tumor_types),

        script = BIN_DIR + "/sarek_cnas/004b-combine_pcg_cnas.py"

    output:
        combined_pcg_cna_file = RES_DIR + "/analysis_sarek/gene_impacts/combined_pcg_cnas.bed"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --combined_pcg_cna_file {output.combined_pcg_cna_file}"

rule visualize_pcg_cnas_oncoprint:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/gene_impacts/combined_pcg_cnas.bed",

        r_script = BIN_DIR + "/sarek_cnas/004c-visualize_pcg_cnas_oncoprint.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/visualize_pcg_cnas_oncoprint.pdf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"





#####################
# visualize CNAs based on logR of ascat
#####################

rule create_bed_format_from_ascat_ratio:
    input:
        ascat_tumor_ratio_file = RES_DIR + "/sarek_wgs/{sample}-{tumor}/variant_calling/ascat/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood/{sample}-{tumor}-{tumor}_vs_{sample}-{tumor}-blood.tumour_tumourLogR.txt"

    output:
        ascat_bed_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/{sample}-{tumor}_ascat_tumorLogR.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """awk 'BEGIN {{print "chromosome\\tstart\\tend\\tratio"}} NR > 1 {{print $2 "\\t" $3 "\\t" $3 "\\t" $4}}' {input.ascat_tumor_ratio_file} > {output.ascat_bed_file}"""

# modify the loci of genes of interest to include 100kb on either side
rule subset_region_coverage:
    input:
        pcg_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions.bed",

        script = BIN_DIR + "/sarek_cnas/002a-subset_region_coverage.py"

    output:
        modified_gene_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/modified_protein_gene_regions.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --pcg_bed_file {input.pcg_bed_file} --modified_gene_bed_file {output.modified_gene_bed_file}"
 
rule intersect_coverage_with_cna_genes:
    input:
        modified_gene_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/modified_protein_gene_regions.bed",
        ascat_bed_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/{sample}-{tumor}_ascat_tumorLogR.bed"

    output:
        intersected_bed_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/{sample}-{tumor}-ascat_coverage_modified_gene_regions.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a <(tail -n +2 {input.modified_gene_bed_file}) -b <(tail -n +2 {input.ascat_bed_file}) -wa -wb > {output.intersected_bed_file}"""

# combine coverage and visualize
rule combine_ascat_cna_genes:
    input:
        expand("{res_dir}/analysis_sarek/consensus_cnas/{sample}/{sample}-{tumor}-ascat_coverage_modified_gene_regions.tsv", res_dir = RES_DIR, sample = sample_codes_list, tumor = tumor_types),

        script = BIN_DIR + "/sarek_cnas/002b-combine_ascat_cna_genes.py"

    output:
        cna_coverage_file = RES_DIR + "/analysis_sarek/_figure_data/combine_ascat_cna_genes.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --cna_coverage_file {output.cna_coverage_file}"

rule visualize_coverage_at_cna_genes:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/_figure_data/combine_ascat_cna_genes.tsv",
        figure_stats_file = RES_DIR + "/analysis_sarek/gene_impacts/modified_protein_gene_regions.bed",

        r_script = BIN_DIR + "/sarek_cnas/002c-visualize_coverage_at_cna_genes.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/visualize_coverage_at_cna_genes_ascat.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"








#####################
# visualize CNAs based on logR of ascat of exons to understand exon-level CNAs
#####################

# extract exons from GTF
rule subset_exon_coverage:
    input:
        gtf_file = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.gtf",

        script = BIN_DIR + "/sarek_cnas/003a-subset_exon_coverage.py"

    output:
        bed_file = RES_DIR + "/analysis_sarek/gene_impacts/spec_gene_exons.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --gtf_file {input.gtf_file} --bed_file {output.bed_file}"

# use the original coordinates not the expanded coordinates
rule subset_protein_gene_regions_original:
    input:
        modified_gene_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/modified_protein_gene_regions.bed",

        script = BIN_DIR + "/sarek_cnas/003b-subset_protein_gene_regions_original.py"

    output:
        original_gene_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/subset_protein_gene_regions.bed",

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        "{PYTHON} {input.script} --modified_gene_bed_file {input.modified_gene_bed_file} --original_gene_bed_file {output.original_gene_bed_file}"

# intersect with just the gene loci to look for exonic CNAs (the second last and last columns are the original coordinates
rule intersect_coverage_with_cna_genes_exons:
    input:
        original_gene_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/subset_protein_gene_regions.bed",
        ascat_bed_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/{sample}-{tumor}_ascat_tumorLogR.bed"

    output:
        intersected_bed_file = RES_DIR + "/analysis_sarek/consensus_cnas/{sample}/{sample}-{tumor}-ascat_coverage_modified_gene_regions_exons.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a <(tail -n +2 {input.original_gene_bed_file}) -b <(tail -n +2 {input.ascat_bed_file}) -wa -wb > {output.intersected_bed_file}"""

# combine coverage and visualize
rule combine_ascat_cna_genes_exons:
    input:
        expand("{res_dir}/analysis_sarek/consensus_cnas/{sample}/{sample}-{tumor}-ascat_coverage_modified_gene_regions_exons.tsv", res_dir = RES_DIR, sample = sample_codes_list, tumor = tumor_types),

        script = BIN_DIR + "/sarek_cnas/003c-combine_ascat_cna_genes_exons.py"

    output:
        cna_coverage_file = RES_DIR + "/analysis_sarek/_figure_data/combine_ascat_cna_genes_exons.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --cna_coverage_file {output.cna_coverage_file}"

rule visualize_coverage_at_cna_genes_ascat_exons:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/_figure_data/combine_ascat_cna_genes_exons.tsv",
        figure_stats_file = RES_DIR + "/analysis_sarek/gene_impacts/spec_gene_exons.bed",

        r_script = BIN_DIR + "/sarek_cnas/003d-visualize_coverage_at_cna_genes_exons.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/visualize_coverage_at_cna_genes_ascat_exons.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"







###################################
# compare expression of different CNAs
###################################

rule differential_expression_by_cna:
    input:
        combined_pcg_cna_file = RES_DIR + "/analysis_sarek/gene_impacts/combined_pcg_cnas.bed",
        tpm_file = RES_DIR + "/analysis_rna/star_salmon/salmon.merged.gene_tpm.tsv",

        script = BIN_DIR + "/sarek_cnas/007a-differential_expression_by_cna.py"

    output:
        figure_data_file = RES_DIR + "/analysis_sarek/gene_impacts/gene_cnas_expression_data.tsv",
        figure_stats_file = RES_DIR + "/analysis_sarek/gene_impacts/gene_cnas_expression_data_stats.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --combined_pcg_cna_file {input.combined_pcg_cna_file} --tpm_file {input.tpm_file} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file}"


rule visualize_differential_expression_by_cna:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/gene_impacts/gene_cnas_expression_data.tsv",
        figure_stats_file = RES_DIR + "/analysis_sarek/gene_impacts/gene_cnas_expression_data_stats.tsv",
        
        r_script = BIN_DIR + "/sarek_cnas/007b-visualize_differential_expression_by_cna.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/visualize_differential_expression_by_cna.pdf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"








#####################
# circos plot the CNAs
#####################

rule process_wgs_coverage:
    input:
        expand("{res_dir}/analysis_sarek/consensus_cnas/{sample}-{tumor}_cnas_merged_unique.tsv", res_dir = RES_DIR, sample = sample_codes_list, tumor = tumor_types),

        script = BIN_DIR + "/sarek_cnas/008a-process_wgs_coverage.py"

    output:
        processed_coverage_file = RES_DIR + "/analysis_sarek/consensus_cnas/008-rolling_coverage_processed.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --processed_coverage_file {output.processed_coverage_file}"

rule wgs_cnas_circos:
    input:
        expand("{res_dir}/analysis_sarek/consensus_cnas/{sample}-{tumor}_cnas_merged_unique.tsv", res_dir = RES_DIR, sample = sample_codes_list, tumor = tumor_types),

        script = BIN_DIR + "/sarek_cnas/008b-wgs_cnas_circos.py"

    output:
        figure_stats_file = RES_DIR + "/analysis_sarek/consensus_cnas/008-cnas_circos.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --figure_stats_file {output.figure_stats_file}"


rule extract_gbm_genes_from_bed_file:
    input:
        all_gene_bed_file = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.protein_coding_genes.tsv",

        script = BIN_DIR + "/sarek_cnas/008c-extract_gbm_genes_from_bed_file.py"

    output:
        gbm_genes_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/gbm_genes_bed_file.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --all_gene_bed_file {input.all_gene_bed_file} --gbm_genes_bed_file {output.gbm_genes_bed_file}"


rule visualize_wgs_cnas_circos:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/consensus_cnas/008-rolling_coverage_processed.tsv",
        figure_stats_file = RES_DIR + "/analysis_sarek/consensus_cnas/008-cnas_circos.tsv",
        gbm_genes_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/gbm_genes_bed_file.tsv",

        script = BIN_DIR + "/sarek_cnas/008d-wgs_cnas_circos.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/008-circos_cnas.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --gbm_genes_bed_file {input.gbm_genes_bed_file} --figure_file {output.figure_file}"









