# Alec <PERSON>cheli - <EMAIL>


#####################
# compare primary to recurrent SNVs
#####################

rule classify_mutations_primary_recurrent:
    input:
        rlgs_mutation_snv_file = RES_DIR + "/analysis_sarek/_figure_data/001-combined_vcfs_VEP.tsv",

        script = BIN_DIR + "/sarek_wgs/010a-classify_mutations_primary_recurrent.py"

    output:
        figure_data_file = RES_DIR + "/analysis_sarek/_figure_data/010-classify_mutations_primary_recurrent.tsv",
        figure_stats_file = RES_DIR + "/analysis_sarek/_figure_data/010-classify_mutations_primary_recurrent_stats.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --rlgs_mutation_snv_file {input.rlgs_mutation_snv_file} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file}"

rule classify_mutations_primary_recurrent_visualize:
    input:
        figure_stats_file = RES_DIR + "/analysis_sarek/_figure_data/010-classify_mutations_primary_recurrent_stats.tsv",

        r_script = BIN_DIR + "/sarek_wgs/010b-classify_mutations_primary_recurrent_visualize.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/010-classify_mutations_primary_recurrent_visualize.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"


#####################
# compare CNAs between primary-recurrent pairs
#####################

rule intersect_primary_recurrent_bed:
    input:
        sorted_primary_bed_file = RES_DIR + "/analysis_sarek/consensus_cnas/{patient}/{patient}-primary_cnas_merged_unique_{cna_type}.tsv",
        sorted_recurrent_bed_file = RES_DIR + "/analysis_sarek/consensus_cnas/{patient}/{patient}-recurrent_cnas_merged_unique_{cna_type}.tsv"

    output:
        intersected_bed_file = RES_DIR + "/analysis_sarek/consensus_cnas/{patient}/{patient}-intersected_primary_recurrent_{cna_type}.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a {input.sorted_primary_bed_file} -b {input.sorted_recurrent_bed_file} > {output.intersected_bed_file}"""


rule calculate_cna_overlaps_from_intersects:
    input:
        expand("{results_dir}/analysis_sarek/consensus_cnas/{patient}/{patient}-intersected_primary_recurrent_{cna_type}.tsv", results_dir = RES_DIR , patient = sample_codes_list, cna_type = ['gain', 'loss']),

        script = BIN_DIR + "/sarek_cnas/011a-calculate_cna_overlaps_from_intersects.py"

    output:
        cna_overlap_fractions_file = RES_DIR + "/analysis_sarek/consensus_cnas/cna_overlap_fractions.tsv",
        cna_overlap_fractions_stats_file = RES_DIR + "/analysis_sarek/consensus_cnas/cna_overlap_fractions_stats.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --cna_overlap_fractions_file {output.cna_overlap_fractions_file} --cna_overlap_fractions_stats_file {output.cna_overlap_fractions_stats_file}"

rule visualize_cna_fractions_primary_recurrent:
    input:
        figure_stats_file = RES_DIR + "/analysis_sarek/consensus_cnas/cna_overlap_fractions_stats.tsv",

        script = BIN_DIR + "/sarek_cnas/011b-visualize_cna_fractions_primary_recurrent.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/cna_fractions_primary_recurrent.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"




#####################
# compare SV/CNA and SNV rates
#####################

rule compare_snv_vs_sv_and_cna:
    input:
        snv_counts_file = RES_DIR + "/analysis_sarek/glass_mutations_comparison/003-compare_mutation_counts_glass.tsv",
        cna_file = RES_DIR + "/analysis_sarek/consensus_cnas/cna_overlap_fractions.tsv",
        sv_file = RES_DIR + "/analysis_nanopore/svs/processed_nanopore_svs.tsv",

        script = BIN_DIR + "/overview_analysis/004a-compare_snv_vs_sv_cna.py"

    output:
        compare_snv_vs_sv_and_cna_file = RES_DIR + "/analysis_overview/_figure_data/compare_snv_vs_sv_and_cna.tsv",
        compare_snv_vs_sv_and_cna_stats_file = RES_DIR + "/analysis_overview/_figure_data/compare_snv_vs_sv_and_cna_stats.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --snv_counts_file {input.snv_counts_file} --cna_file {input.cna_file} --sv_file {input.sv_file} --compare_snv_vs_sv_and_cna_file {output.compare_snv_vs_sv_and_cna_file} --compare_snv_vs_sv_and_cna_stats_file {output.compare_snv_vs_sv_and_cna_stats_file}"




###################################
# clustering mutations
###################################

# visualize the histograms of mutations
rule combine_vaf_mutations:
    input:
        expand("{res_dir}/analysis_sarek/canopy/subset_mutations_input/canopy-{patient}_total_depth_X.tsv", res_dir = RES_DIR, patient = sample_codes_list),
        expand("{res_dir}/analysis_sarek/canopy/subset_mutations_input/canopy-{patient}_mutant_allele_depth_R.tsv", res_dir = RES_DIR, patient = sample_codes_list),

        script = BIN_DIR + "/sarek_evolution/004a-combine_vaf_mutations.py"

    output:
        combined_mutation_vaf_file = RES_DIR + "/analysis_sarek/consensus_vcfs/combined_vafs.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --combined_mutation_vaf_file {output.combined_mutation_vaf_file}"

rule vaf_mutations_histogram:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/consensus_vcfs/combined_vafs.tsv",

        script = BIN_DIR + "/sarek_evolution/004b-vaf_mutations_histogram.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/vaf_mutations_histogram.pdf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"


# test GMMs to cluster
rule vaf_gmm_testing:
    input:
        combined_mutation_vaf_file = RES_DIR + "/analysis_sarek/consensus_vcfs/combined_vafs.tsv",

        script = BIN_DIR + "/sarek_evolution/004c-vaf_gmm_testing.py"

    output:
        gmm_cluster_assignments_file = RES_DIR + "/analysis_sarek/vaf_clustering/gmm_cluster_assignments.tsv",
        gmm_stats_file = RES_DIR + "/analysis_sarek/vaf_clustering/gmm_stats.tsv"
        
    resources:
        threads = 12,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '3G'
        
    shell:
        "{PYTHON} {input.script} --combined_mutation_vaf_file {input.combined_mutation_vaf_file} --gmm_cluster_assignments_file {output.gmm_cluster_assignments_file} --gmm_stats_file {output.gmm_stats_file} --threads {resources.threads}"

rule visualize_gmm_clusters:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/vaf_clustering/gmm_cluster_assignments.tsv",

        script = BIN_DIR + "/sarek_evolution/004d-visualize_gmm_clusters.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/visualize_gmm_clusters.pdf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"

rule visualize_gmm_stats:
    input:
        figure_stats_file = RES_DIR + "/analysis_sarek/vaf_clustering/gmm_stats.tsv",

        script = BIN_DIR + "/sarek_evolution/004e-visualize_gmm_stats.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/visualize_gmm_stats.pdf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"





#####################
# sigprofiler
#####################

# rule setup_vcfs_for_sigprofiler:
#     input:
#         rlgs_mutation_snv_file = RES_DIR + "/analysis_sarek/_figure_data/001-combined_vcfs_VEP.tsv",

#         script = BIN_DIR + "/sarek_evolution/002a-setup_vcfs_for_sigprofiler.py"

#     output:
#         expand("{res_dir}/analysis_sarek/sigprofiler/input/{sample}-consensus.tsv", res_dir = RES_DIR, sample = sample_list)

#     params:
#         output_dir = RES_DIR + "/analysis_sarek/sigprofiler/input"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --rlgs_mutation_snv_file {input.rlgs_mutation_snv_file} --output_dir {params.output_dir}"

rule copy_vcfs_for_sigprofiler:
    input:
        combined_vcf_file = RES_DIR + "/analysis_sarek/consensus_vcfs/{sample}_VEP_combined.vcf.gz"

    output:
        sigprofiler_vcf_file = RES_DIR + "/analysis_sarek/sigprofiler/input/{sample}_VEP_combined.vcf.gz"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        "cp {input.combined_vcf_file} {output.sigprofiler_vcf_file}"


rule sigprofiler_signature_assignment:
    input:
        expand("{res_dir}/analysis_sarek/sigprofiler/input/{sample}_VEP_combined.vcf.gz", res_dir = RES_DIR, sample = sample_list),

        script = BIN_DIR + "/sarek_evolution/002b-sigprofiler_signature_assignment.py"

    output:
        signature_finished_file = RES_DIR + "/analysis_sarek/sigprofiler/JOB_METADATA_SPA.txt"

    params:
        sigprofiler_python = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/sigprofiler/bin/python",

        sigprofiler_input_dir = RES_DIR + "/analysis_sarek/sigprofiler/input",
        sigprofiler_res_dir = RES_DIR + "/analysis_sarek/sigprofiler"

    resources:
        threads = 1,
        queue = "u20build",
        jobtime = '0:1:0:0',
        individual_core_memory = '10G'
        
    shell:
        "{params.sigprofiler_python} {input.script} --input_dir {params.sigprofiler_input_dir} --sigprofiler_res_dir {params.sigprofiler_res_dir}"


# visualize signature assignments
rule process_sigprofiler_assignments:
    input:
        signature_finished_file = RES_DIR + "/analysis_sarek/sigprofiler/JOB_METADATA_SPA.txt",

        script = BIN_DIR + "/sarek_evolution/002c-process_sigprofiler_assignments.py"

    output:
        figure_data_file = RES_DIR + "/analysis_sarek/sigprofiler/002-processed_sigprofiler_assignments.tsv",
        figure_stats_file = RES_DIR + "/analysis_sarek/sigprofiler/002-processed_sigprofiler_assignments_stats.tsv"

    params:
        sigprofiler_assignment_file = RES_DIR + "/analysis_sarek/sigprofiler/Assignment_Solution/Activities/Assignment_Solution_Activities.txt"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --sigprofiler_assignment_file {params.sigprofiler_assignment_file} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file}"

rule visualize_sigprofiler_assignments:
    input:
        figure_data_file = RES_DIR + "/analysis_sarek/sigprofiler/002-processed_sigprofiler_assignments.tsv",
        figure_stats_file = RES_DIR + "/analysis_sarek/sigprofiler/002-processed_sigprofiler_assignments_stats.tsv",

        r_script = BIN_DIR + "/sarek_evolution/002d-visualize_sigprofiler_assignments.R"

    output:
        figure_file = RES_DIR + "/analysis_sarek/_figures/002-sigprofiler_by_sample.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"

# run stats
rule sigprofiler_stats:
    input:
        signature_proportions_file = RES_DIR + "/analysis_sarek/sigprofiler/002-processed_sigprofiler_assignments_stats.tsv",

        script = BIN_DIR + "/sarek_evolution/002e-sigprofiler_stats.py"

    output:
        stats_file = RES_DIR + "/analysis_sarek/sigprofiler/similarity_stats.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --signature_proportions_file {input.signature_proportions_file} --stats_file {output.stats_file}"












