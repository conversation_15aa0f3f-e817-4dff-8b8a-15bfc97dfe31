# Alec Ba<PERSON>cheli



#####################
# test differential methylation of MGMT
#####################

rule subset_promoter_test_mgmt:
    input:
        protein_coding_genes_gff_file = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.protein_coding_genes.tsv",

        script = BIN_DIR + "/mgmt_promoter_analysis/001a-subset_promoter_test_mgmt.py"

    output:
        mgmt_protein_bed_file = RES_DIR + "/analysis_nanopore/mgmt_promoter_analysis/mgmt_protein_promoter_region.bed"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --protein_coding_genes_gff_file {input.protein_coding_genes_gff_file} --mgmt_protein_bed_file {output.mgmt_protein_bed_file}"

rule intersect_mgmt_methylation:
    input:
        mgmt_protein_bed_file = RES_DIR + "/analysis_nanopore/mgmt_promoter_analysis/mgmt_protein_promoter_region.bed",
        methyl_outfile = RES_DIR + "/analysis_nanopore/methyl_overview/{sample}-{tumor}_methylation_details.bed"

    output:
        intersected_bed_file = RES_DIR + "/analysis_nanopore/mgmt_promoter_analysis/{sample}-{tumor}_mgmt_methylation.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a <(tail -n +2 {input.mgmt_protein_bed_file}) -b <(tail -n +2 {input.methyl_outfile}) -wa -wb > {output.intersected_bed_file}"""


# merge methylation data
rule test_promoter_cpg_ranges_mgmt:
    input:
        expand("{res_dir}/analysis_nanopore/mgmt_promoter_analysis/{samples}_mgmt_methylation.bed", res_dir = RES_DIR, samples = ['RLGS2-normal', 'RLGS5-normal', 'RLGS5-tumor', 'RLGS7-tumor', 'RLGS8-normal', 'RLGS9-normal', 'RLGS11-normal', 'RLGS12-tumor']),

        script = BIN_DIR + "/mgmt_promoter_analysis/002a-test_promoter_cpg_ranges_mgmt.py"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/mgmt_promoter_analysis/mgmt_promoter_methylation_data.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/mgmt_promoter_analysis/mgmt_promoter_methylation_data_stats.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file}"

rule visualize_test_promoter_cpg_ranges_mgmt:
    input:
        figure_stats_file = RES_DIR + "/analysis_nanopore/mgmt_promoter_analysis/mgmt_promoter_methylation_data_stats.tsv",
        
        script = BIN_DIR + "/mgmt_promoter_analysis/002b-visualize_test_promoter_cpg_ranges_mgmt.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures_diff_meth/visualize_merge_test_methylation_data.pdf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"


#####################
# visualize the mgmt promoter and relative methylation rates at each position
#####################

# merge methylation data
rule merge_test_methylation_data_for_cpg_methylation:
    input:
        expand("{res_dir}/analysis_nanopore/mgmt_promoter_analysis/{samples}_mgmt_methylation.bed", res_dir = RES_DIR, samples = ['RLGS2-normal', 'RLGS5-normal', 'RLGS5-tumor', 'RLGS7-tumor', 'RLGS8-normal', 'RLGS9-normal', 'RLGS11-normal', 'RLGS12-tumor']),
        gene_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions.bed",

        script = BIN_DIR + "/mgmt_promoter_analysis/003a-merge_test_methylation_data_for_cpg_methylation.py"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/mgmt_promoter_analysis/cpg_methylation-mgmt_promoter_methylation_data.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/mgmt_promoter_analysis/cpg_methylation-mgmt_promoter_methylation_data_stats.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --gene_bed_file {input.gene_bed_file} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file}"

rule visualize_merge_test_methylation_data_for_cpg_methylation:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/mgmt_promoter_analysis/cpg_methylation-mgmt_promoter_methylation_data.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/mgmt_promoter_analysis/cpg_methylation-mgmt_promoter_methylation_data_stats.tsv",
        
        script = BIN_DIR + "/mgmt_promoter_analysis/003b-visualize_merge_test_methylation_data.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures_diff_meth/visualize_merge_test_methylation_data_for_cpg_methylation.pdf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"




















