# Alec Ba<PERSON>cheli


###################################
# methylation gene expression correlations
###################################

rule correlate_methylation_expression_per_gene:
    input:
        mean_promoter_methylation_file = RES_DIR + "/analysis_nanopore/mgmt_promoter_analysis/mean_methylation_by_gene_and_sample.tsv",
        rnaseq_counts_file = RES_DIR + "/analysis_rna/star_salmon/salmon.merged.gene_counts.tsv",

        script = BIN_DIR + "/rnaseq_methlyation/001a-correlate_methylation_expression_per_gene.py"

    output:
        figure_stats_file = RES_DIR + "/analysis_rna/rna_methylation/correlate_methylation_expression_per_gene_stats.tsv"

    resources:
        threads = 10,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '3G'
        
    shell:
        "{PYTHON} {input.script} --mean_promoter_methylation_file {input.mean_promoter_methylation_file} --rnaseq_counts_file {input.rnaseq_counts_file} --figure_stats_file {output.figure_stats_file} --threads {resources.threads}"


rule visualize_correlate_methylation_expression_per_gene:
    input:
        figure_stats_file = RES_DIR + "/analysis_rna/rna_methylation/correlate_methylation_expression_per_gene_stats.tsv",

        script = BIN_DIR + "/rnaseq_methlyation/001b-visualize_correlate_methylation_expression_per_gene.R"

    output:
        figure_file = RES_DIR + "/analysis_rna/_figures/correlate_methylation_expression_per_gene_stats.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"








