# Alec Bahcheli
# Nanopore ecDNA analysis pipeline

###################################
# Preparation for Decoil: long-read ecDNA identification
###################################

rule convert_cram_to_bam_for_ecdna:
    input:
        cram_file = RES_DIR + "/nanopore_somatic/{patient}-{tumor}/{patient}-{tumor}_tumor.ht.cram",
        fasta = RES_DIR + "/Homo_sapiens_assembly38.fasta"

    output:
        bam_file = temp(RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam")

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'

    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools view -b -T {input.fasta} -o {output.bam_file} {input.cram_file}"""

rule index_bam_file:
    input:
        bam_file = RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam"

    output:
        bam_index_file = temp(RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam.bai")

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'

    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools index {input.bam_file}"""

# Prepare CoRAL input file
rule prepare_coral_input:
    input:
        cna_merged_file = RES_DIR + "/analysis_sarek/consensus_cnas/{patient}/002-{patient}-{tumor}_combine_intersected_results_gain.tsv",
        cnvkit_cns_file = RES_DIR + "/sarek_wgs/{patient}-{tumor}/variant_calling/cnvkit/{patient}-{tumor}-{tumor}_vs_{patient}-{tumor}-blood/{patient}-{tumor}-{tumor}.call.cns",

        script = BIN_DIR + "/nanopore_episomes/010-coral_gain_regions_preparation.py"

    output:
        coral_input_bed_file = RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}/{patient}-{tumor}_coral_input.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'

    shell:
        """{PYTHON} {input.script} --cna_merged_file {input.cna_merged_file} --cnvkit_cns_file {input.cnvkit_cns_file} --coral_input_bed_file {output.coral_input_bed_file}"""


###################################
# Decoil pipeline
###################################

# rule run_decoil:
#     input:
#         bam_file = RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam",
#         bam_index = RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam.bai",
#         fasta = RES_DIR + "/Homo_sapiens_assembly38.fasta",
#         gtf = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.gtf"

#     output:
#         RES_DIR + "/analysis_nanopore/ecdna/decoil/{patient}-{tumor}/summary.txt",
#         RES_DIR + "/analysis_nanopore/ecdna/decoil/{patient}-{tumor}/reconstruct.ecDNA.filtered.fasta"

#     params:
#         name = "{patient}-{tumor}",
#         logs_dir = RES_DIR + "/analysis_nanopore/ecdna/decoil/{patient}-{tumor}/logs",
#         tmp_dir = RES_DIR + "/analysis_nanopore/ecdna/decoil/{patient}-{tumor}/tmp",
#         decoil_sif = "/.mounts/labs/reimandlab/private/users/abahcheli/software/decoil/decoil.sif",
#         output_dir = directory(RES_DIR + "/analysis_nanopore/ecdna/decoil/{patient}-{tumor}/")

#     conda:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/nextflow"

#     resources:
#         threads = 5,
#         queue = "all.q",
#         jobtime = '10:0:0:0',
#         individual_core_memory = '20G'

#     shell:
#         """
#         mkdir -p {params.output_dir}
#         mkdir -p {params.logs_dir}
#         mkdir -p {params.tmp_dir}
        
#         singularity run \
#             --bind {params.logs_dir}:/mnt/logs \
#             --bind {params.tmp_dir}:/tmp \
#             --bind {input.bam_file}:/data/input.bam \
#             --bind {input.bam_index}:/data/input.bam.bai \
#             --bind {input.fasta}:/annotation/reference.fa \
#             --bind {input.gtf}:/annotation/anno.gtf \
#             --bind {params.output_dir}:/mnt \
#             {params.decoil_sif} \
#             decoil-pipeline sv-reconstruct \
#                 -b /data/input.bam \
#                 -r /annotation/reference.fa \
#                 -g /annotation/anno.gtf \
#                 -o /mnt --name {params.name}
#         """


###################################
# Filter homologous ecDNAs
###################################

# Create bedtools intersection for homologous ecDNA detection
rule intersect_ecdnas_for_homology:
    input:
        ecdna_bed = RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/reconstruct.ecDNA.filtered.bed"

    output:
        intersect_file = RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/ecdna_intersections.bed"

    params:
        bedtools_path = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{params.bedtools_path} intersect -a {input.ecdna_bed} -b {input.ecdna_bed} -wo > {output.intersect_file}"


# Filter homologous ecDNAs based on bedtools intersection results
rule filter_homologous_ecdnas:
    input:
        ecdna_bed = RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/reconstruct.ecDNA.filtered.bed",
        intersect_file = RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/ecdna_intersections.bed",
        script = BIN_DIR + "/circ_dna_scripts/002c-filter_homologous_ecdnas.py"

    output:
        filtered_bed = RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/reconstruct.ecDNA.filtered.unique.bed"

    params:
        overlap_threshold = 0.9

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{PYTHON} {input.script} --input_bed {input.ecdna_bed} --intersect_file {input.intersect_file} --output_bed {output.filtered_bed} --overlap_threshold {params.overlap_threshold}"


# Filter summary.txt based on unique circ_ids from homologous filtering
rule filter_summary_by_unique_ecdnas:
    input:
        filtered_bed = RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/reconstruct.ecDNA.filtered.unique.bed",
        original_summary = RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/summary.txt",
        script = BIN_DIR + "/circ_dna_scripts/002d-filter_summary_by_unique_ecdnas.py"

    output:
        filtered_summary = RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/summary_unique.txt"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{PYTHON} {input.script} --filtered_bed {input.filtered_bed} --original_summary {input.original_summary} --output_summary {output.filtered_summary}"


# # Combine filtered decoil results from all samples
# rule combine_filtered_decoil_results:
#     input:
#         filtered_beds = expand(RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/reconstruct.ecDNA.filtered.unique.bed", sample=SAMPLES),
#         filtered_summaries = expand(RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/summary_unique.txt", sample=SAMPLES),
#         script = BIN_DIR + "/circ_dna_scripts/002a-combine_decoil_results.py"

#     output:
#         combined_results = RES_DIR + "/analysis_nanopore/ecdna/decoil/combined_ecdna_results_filtered.tsv",
#         summary_results = RES_DIR + "/analysis_nanopore/ecdna/decoil/summary_ecdna_results_filtered.tsv",
#         ecdna_counts = RES_DIR + "/analysis_nanopore/ecdna/decoil/ecdna_counts_filtered.tsv"

#     params:
#         sample_list = ",".join(SAMPLES),
#         results_dir_filtered = RES_DIR + "/analysis_nanopore/ecdna/decoil_filtered"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '1:0:0:0',
#         individual_core_memory = '10G'

#     shell:
#         """
#         # Create temporary directory structure for filtered results
#         mkdir -p {params.results_dir_filtered}
#         for sample in {params.sample_list//,/ }; do
#             mkdir -p {params.results_dir_filtered}/$sample
#             # Copy filtered bed file to expected location
#             cp $(dirname {input.filtered_beds[0]})/../$sample/reconstruct.ecDNA.filtered.unique.bed {params.results_dir_filtered}/$sample/reconstruct.ecDNA.filtered.bed
#             # Copy filtered summary file
#             cp $(dirname {input.filtered_summaries[0]})/../$sample/summary_unique.txt {params.results_dir_filtered}/$sample/summary.txt
#         done

#         # Run combine script on filtered data
#         {PYTHON} {input.script} --results_dir {params.results_dir_filtered}/.. --sample_list {params.sample_list} --combined_results {output.combined_results} --summary_results {output.summary_results} --ecdna_counts {output.ecdna_counts}
#         """


# # Test rule for filtering summary by unique ecDNAs using local test data
# rule test_filter_summary_by_unique_ecdnas_local:
#     input:
#         script = BIN_DIR + "/circ_dna_scripts/002d-filter_summary_by_unique_ecdnas.py"

#     output:
#         test_filtered_summary = "/Users/<USER>/Desktop/tmp/test_summary_unique.txt"

#     params:
#         test_filtered_bed = "/Users/<USER>/Desktop/tmp/test_filtered_detailed.bed",
#         test_original_summary = "/Users/<USER>/Desktop/RLGS5-primary/summary.txt"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '1:0:0:0',
#         individual_core_memory = '10G'

#     shell:
#         """
#         mkdir -p /Users/<USER>/Desktop/tmp
#         {PYTHON} {input.script} --filtered_bed {params.test_filtered_bed} --original_summary {params.test_original_summary} --output_summary {output.test_filtered_summary}
#         """

