# Alec <PERSON>i - <EMAIL>


###################################
# gbm molecular classification from "Tumor evolution of Glioma-Intrinsic Gene Expression Subtypes Associates with immunological changes in the microenvironment"
###################################

rule convert_tpm_to_gct:
    input:
        tpm_file = RES_DIR + "/analysis_rna/star_salmon/salmon.merged.gene_tpm.tsv",

        r_script = BIN_DIR + "/rnaseq_interpretation/010a-convert_tpm_to_gct.R"

    output:
        gct_file = RES_DIR + "/analysis_rna/molecular_classification/salmon_merged_tpm.gct"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --tpm_file {input.tpm_file} --gct_file {output.gct_file}"

rule ssgsea_GBM_classification:
    input:
        gct_file = RES_DIR + "/analysis_rna/molecular_classification/salmon_merged_tpm.gct",

        r_script = BIN_DIR + "/rnaseq_interpretation/010b-ssgsea_GBM_classification.R"

    output:
        classification_file = RES_DIR + "/analysis_rna/molecular_classification/p_result_salmon_merged_tpm.gct.txt"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r_dev/bin/Rscript {input.r_script} --gct_file {input.gct_file}"

rule process_molecular_subtypes:
    input:
        classification_file = RES_DIR + "/analysis_rna/molecular_classification/p_result_salmon_merged_tpm.gct.txt",

        script = BIN_DIR + "/rnaseq_interpretation/010c-process_molecular_subtypes.py",
        r_script = BIN_DIR + "/rnaseq_interpretation/010d-process_molecular_subtypes.R"

    output:
        subtype_file = RES_DIR + "/analysis_rna/molecular_classification/010-molecular_subtypes.tsv",
        figure_stats_file = RES_DIR + "/analysis_rna/_figure_data/010-process_molecular_subtypes_stats.tsv",

        figure_file = RES_DIR + "/analysis_rna/_figures/010-clinical_sequencing_summary_figure.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --classification_file {input.classification_file} --r_script {input.r_script} --subtype_file {output.subtype_file} --figure_stats_file {output.figure_stats_file} --figure_file {output.figure_file}"



###################################
# Sturgeon prediction of CNS methylation subtype
###################################

# map to genome single-step
rule map_ubam_to_genome_bam:
    input:
        unaligned_bam_file = RES_DIR + "/nanopore/{sample}/{sample}.pass.bam",
        genome_fasta = t2t_genome_fasta

    output:
        bam_file = temp(RES_DIR + "/analysis_nanopore/sturgeon/alignments/{sample}-t2t_aligned.bam"),
        bam_file_index = temp(RES_DIR + "/analysis_nanopore/sturgeon/alignments/{sample}-t2t_aligned.bam.bai")

    resources:
        threads = 22,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '6G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools bam2fq -@ 1 -T 1 {input.unaligned_bam_file} | /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/nanopore_tools/bin/minimap2 -y -t 20 -ax map-ont {input.genome_fasta} - | /.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools sort -@ 2 --write-index -o {output.bam_file}##idx##{output.bam_file_index} -O BAM --reference {input.genome_fasta} -"

# sturgeon preparation
rule adjust_5hmc_to_5mc:
    input:
        bam_file = RES_DIR + "/analysis_nanopore/sturgeon/alignments/{sample}-t2t_aligned.bam"

    output:
        modified_bam_file = temp(RES_DIR + "/analysis_nanopore/sturgeon/alignments/{sample}-t2t_aligned_5hmc_adjusted.bam")

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/nanopore_tools/bin/modkit adjust-mods --convert h m {input.bam_file} {output.modified_bam_file}"

rule extract_5mc:
    input:
        modified_bam_file = RES_DIR + "/analysis_nanopore/sturgeon/alignments/{sample}-t2t_aligned_5hmc_adjusted.bam"

    output:
        extracted_5mc_file = temp(RES_DIR + "/analysis_nanopore/sturgeon/sturgeon_input/{sample}-t2t_5mc_scores_full.txt")

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/nanopore_tools/bin/modkit extract {input.modified_bam_file} {output.extracted_5mc_file}"

rule downsample_extracted_reads:
    input:
        extracted_5mc_file = RES_DIR + "/analysis_nanopore/sturgeon/sturgeon_input/{sample}-t2t_5mc_scores_full.txt"

    output:
        final_5mc_file = temp(RES_DIR + "/analysis_nanopore/sturgeon/sturgeon_input/{sample}-t2t_5mc_scores.txt")

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "awk 'NR == 1 || NR % 9 == 0' {input.extracted_5mc_file} > {output.final_5mc_file}"


# sturgeon
rule sturgeon_input_to_bed:
    input:
        sturgeon_input_file = RES_DIR + "/analysis_nanopore/sturgeon/sturgeon_input/{sample}-t2t_5mc_scores.txt"

    output:
        sturgeon_output_file = RES_DIR + "/analysis_nanopore/sturgeon/sturgeon_bed/{sample}/merged_probes_methyl_calls.bed"

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/sturgeon"

    params:
        output_dir = RES_DIR + "/analysis_nanopore/sturgeon/sturgeon_bed/{sample}"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '200G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/bin/sturgeon inputtobed --input-path {input.sturgeon_input_file} --output-path {params.output_dir} --source modkit --reference-genome chm13v2"

rule move_sturgeon_bed:
    input:
        sturgeon_output_file = RES_DIR + "/analysis_nanopore/sturgeon/sturgeon_bed/{sample}/merged_probes_methyl_calls.bed"

    output:
        sturgeon_bed_file = RES_DIR + "/analysis_nanopore/sturgeon/sturgeon_bed/{sample}-merged_probes_methyl_calls.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        "cp {input.sturgeon_output_file} {output.sturgeon_bed_file}"

rule sturgeon_predict:
    input:
        expand("{res_dir}/analysis_nanopore/sturgeon/sturgeon_bed/{sample}-merged_probes_methyl_calls.bed", res_dir = RES_DIR, sample = sample_list),

        model_file = RES_DIR + "/analysis_nanopore/sturgeon/general.zip"

    output:
        expand("{res_dir}/analysis_nanopore/sturgeon/results/{sample}-merged_probes_methyl_calls_general.csv", res_dir = RES_DIR, sample = sample_list),
        expand("{res_dir}/analysis_nanopore/sturgeon/results/{sample}-merged_probes_methyl_calls_general.pdf", res_dir = RES_DIR, sample = sample_list)

        # csv_predictions_file = RES_DIR + "/analysis_nanopore/sturgeon/results/merged_probes_methyl_calls_general.csv",
        # pdf_predictions_file = RES_DIR + "/analysis_nanopore/sturgeon/results/merged_probes_methyl_calls_general.pdf"

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/sturgeon"

    params:
        sturgeon_input_dir = RES_DIR + "/analysis_nanopore/sturgeon/sturgeon_bed",
        sturgeon_output_dir = RES_DIR + "/analysis_nanopore/sturgeon/results"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '60G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/bin/sturgeon predict -i {params.sturgeon_input_dir} -o {params.sturgeon_output_dir} --model-files {input.model_file} --plot-results"














# ###################################
# # visualizing methylation correlation
# ###################################

# rule combine_methylation_correlation:
#     input:
#         primary_methylation_file = RES_DIR + "/nanopore_evolution/{sample}/{sample}-primary/mod/5mC/bedMethyl/5mC.{sample}-primary_normal.bed.gz",
#         recurrent_methylation_file = RES_DIR + "/nanopore_evolution/{sample}/{sample}-primary/mod/5mC/bedMethyl/5mC.{sample}-primary_tumor.bed.gz",

#         script = BIN_DIR + "/nanopore_methylation/008a-visualize_methylation_correlation.py"

#     output:
#         figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/008-visualize_methylation_correlation_{sample}.tsv"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '60G'
        
#     shell:
#         "{PYTHON} {input.script} --primary_methylation_file {input.primary_methylation_file} --recurrent_methylation_file {input.recurrent_methylation_file} --figure_data_file {output.figure_data_file}"

# rule visualize_methylation_correlation:
#     input:
#         figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/008-visualize_methylation_correlation_{sample}.tsv",

#         r_script = BIN_DIR + "/nanopore_methylation/008b-visualize_methylation_correlation.R"

#     output:
#         figure_file = RES_DIR + "/analysis_nanopore/_figures/008-visualize_methylation_correlation_{sample}.pdf"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '60G'
        
#     shell:
#         "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"

# # only the protein-coding gene promoters
# rule methylation_correlation_protein_coding_genes:
#     input:
#         methylation_by_gene_promoter_file = RES_DIR + "/analysis_nanopore/methylation/individual_cpg_methylation_by_gene.tsv",

#         script = BIN_DIR + "/nanopore_methylation/008c-visualize_methylation_correlation_protein_coding_genes.py"

#     output:
#         figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/008-visualize_methylation_correlation_protein_coding_genes.tsv"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --methylation_by_gene_promoter_file {input.methylation_by_gene_promoter_file} --figure_data_file {output.figure_data_file}"

# rule visualize_methylation_correlation_protein_coding_genes:
#     input:
#         figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/008-visualize_methylation_correlation_protein_coding_genes.tsv",

#         r_script = BIN_DIR + "/nanopore_methylation/008d-visualize_methylation_correlation_protein_coding_genes.R"

#     output:
#         figure_file = RES_DIR + "/analysis_nanopore/_figures/008-visualize_methylation_correlation_protein_coding_genes.pdf"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '60G'
        
#     shell:
#         "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"



# ###################################
# # faceted whole genome CpG overview
# ###################################

# # faceted methylation correlation plots
# rule combine_methylation_correlation_faceted:
#     input:
#         expand("{res_dir}/analysis_nanopore/_figure_data/008-visualize_methylation_correlation_{sample}.tsv", res_dir = RES_DIR, sample = sample_codes_list),

#         script = BIN_DIR + "/nanopore_methylation/008e-combine_methylation_correlation_faceted.py"

#     output:
#         figure_stats_file = RES_DIR + "/analysis_nanopore/_figure_data/008-combined_methylation_correlation_faceted_stats.tsv",
#         figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/008-combined_methylation_correlation_faceted.tsv"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "{PYTHON} {input.script} --res_dir {RES_DIR} --figure_stats_file {output.figure_stats_file} --figure_data_file {output.figure_data_file}"

# rule visualize_methylation_correlation_faceted:
#     input:
#         figure_stats_file = RES_DIR + "/analysis_nanopore/_figure_data/008-combined_methylation_correlation_faceted_stats.tsv",
#         figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/008-combined_methylation_correlation_faceted.tsv",

#         r_script = BIN_DIR + "/nanopore_methylation/008f-visualize_methylation_correlation.R"

#     output:
#         figure_file = RES_DIR + "/analysis_nanopore/_figures/008-combined_methylation_correlation_faceted.pdf"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '60G'
        
#     shell:
#         "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"

# rule visualize_methylation_correlation_faceted_hexbin:
#     input:
#         figure_stats_file = RES_DIR + "/analysis_nanopore/_figure_data/008-combined_methylation_correlation_faceted_stats.tsv",
#         figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/008-combined_methylation_correlation_faceted.tsv",

#         r_script = BIN_DIR + "/nanopore_methylation/008g-visualize_methylation_correlation_hexbin.R"

#     output:
#         figure_file = RES_DIR + "/analysis_nanopore/_figures/008-combined_methylation_correlation_faceted_hexbin.pdf"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '60G'
        
#     shell:
#         "{RSCRIPT} {input.r_script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"



###################################
# clustering methlyation sites using GMMs
###################################

rule gmm_methylation_sites_individual_samples:
    input:
        methylation_file = RES_DIR + "/analysis_nanopore/_figure_data/008-visualize_methylation_correlation_{sample}.tsv",

        script = BIN_DIR + "/nanopore_methylation/010a-gmm_methylation_sites_individual_samples.py"

    output:
        cpg_clusters_file = RES_DIR + "/analysis_nanopore/methylation/010-gmm_methylation_labels_{sample}.tsv",
        figure_data_file = RES_DIR + "/analysis_nanopore/methylation/010-gmm_methylation_sites_individual_samples_{sample}.tsv"

    resources:
        threads = 20,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '3G'
        
    shell:
        "{PYTHON} {input.script} --methylation_file {input.methylation_file} --figure_data_file {output.figure_data_file} --cpg_clusters_file {output.cpg_clusters_file} --threads {resources.threads}"

rule gmm_methylation_promoters_individual_samples:
    input:
        methylation_file = RES_DIR + "/analysis_nanopore/methylation/009-promoter_methylation_individual_cpgs_{sample}.tsv",

        script = BIN_DIR + "/nanopore_methylation/010b-gmm_methylation_promoters_individual_samples.py"

    output:
        cpg_clusters_file = RES_DIR + "/analysis_nanopore/methylation/010-gmm_methylation_promoters_labels_{sample}.tsv",
        figure_data_file = RES_DIR + "/analysis_nanopore/methylation/010-gmm_methylation_promoters_individual_samples_{sample}.tsv"

    resources:
        threads = 20,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '3G'
        
    shell:
        "{PYTHON} {input.script} --methylation_file {input.methylation_file} --figure_data_file {output.figure_data_file} --cpg_clusters_file {output.cpg_clusters_file} --threads {resources.threads}"



###################################
# clustering sample by methylation
###################################

rule umap_mean_promoter_methylation:
    input:
        mean_methylation_file = RES_DIR + "/analysis_nanopore/_figure_data/003-differentially_methylated_promoters_by_mean.tsv",

        script = BIN_DIR + "/nanopore_methylation/007a-umap_mean_promoter_methylation.py",
        r_script = BIN_DIR + "/nanopore_methylation/007b-umap_mean_promoter_methylation.R"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/007-umap_mean_promoter_methylation.tsv",

        figure_file = RES_DIR + "/analysis_nanopore/_figures/007-umap_mean_promoter_methylation.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --mean_methylation_file {input.mean_methylation_file} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_file {output.figure_file}"


rule umap_methylation_genome_segments:
    input:
        mean_methylation_file = RES_DIR + "/analysis_nanopore/_figure_data/002-binned_methylation_heatmap.tsv",

        script = BIN_DIR + "/nanopore_methylation/007c-umap_methylation_genome_segments.py",
        r_script = BIN_DIR + "/nanopore_methylation/007b-umap_mean_promoter_methylation.R"

    output:
        figure_data_file = RES_DIR + "/analysis_nanopore/_figure_data/007-umap_methylation_genome_segments.tsv",

        figure_file = RES_DIR + "/analysis_nanopore/_figures/007-umap_methylation_genome_segments.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --mean_methylation_file {input.mean_methylation_file} --r_script {input.r_script} --figure_data_file {output.figure_data_file} --figure_file {output.figure_file}"















