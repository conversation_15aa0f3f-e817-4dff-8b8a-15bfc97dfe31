# Alec Ba<PERSON>cheli - <EMAIL>

##########################################
# SNVs
##########################################

#####################
# Mutect2
#####################

rule Mutect2:
    input:
        RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/mutect2/null.txt",
        RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq-sorted.bam.bai",
        RES_DIR + "/" + GENOME_VERSION + ".fa.fai",
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        
        tumor_bam = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq-sorted.bam",
        normal_bam = RES_DIR + "/wgs_processing/{sample_code}-blood_wgs_seq-sorted.bam"
        
    output:
        output_vcf_gz = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-mutect2_{chromosome}.vcf.gz"
        
    singularity:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-4.4.0.0/gatk.sif"
        
    params:
        java_memory = '200g',
        normal_sample = "{sample_code}-blood_wgs_seq",
        chromosome = "{chromosome}",
        sample_tmp_dir = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/mutect2/"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '205G'
        
    shell:
        "gatk --java-options '-Xmx{params.java_memory}' Mutect2 -R {input.genome_fasta} --input {input.tumor_bam} --input {input.normal_bam} --output {output.output_vcf_gz} --normal-sample {params.normal_sample} --tmp-dir {params.sample_tmp_dir} --intervals {params.chromosome}"



#####################
# Strelka
#####################

rule init_Strelka:
    input:
        RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq-sorted.bam.bai",
        RES_DIR + "/wgs_processing/{sample_code}-blood_wgs_seq-sorted.bam.bai",
        RES_DIR + "/" + GENOME_VERSION + ".fa.fai",
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",

        tumor_bam = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq-sorted.bam",
        normal_bam = RES_DIR + "/wgs_processing/{sample_code}-blood_wgs_seq-sorted.bam"

    output:
        RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/runWorkflow.py"
        
    params:
        working_directory = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq"

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/snv_callers"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'

    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/snv_callers/bin/configureStrelkaSomaticWorkflow.py --normalBam {input.normal_bam} --tumorBam {input.tumor_bam} --referenceFasta {input.genome_fasta} --runDir {params.working_directory}"

rule Strelka:
    input:
        run_file = RES_DIR + "/wgs_processing/{sample}/runWorkflow.py"
        
    output:
        RES_DIR + "/wgs_processing/{sample}/results/variants/somatic.snvs.vcf.gz",
        RES_DIR + "/wgs_processing/{sample}/results/variants/somatic.indels.vcf.gz"
        
    resources:
        threads = WGS_THREADS,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'

    shell:
        "{input.run_file} -m local -j {resources.threads}"


#####################
# MuSe
#####################

rule Muse2_call:
    input:
        RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq-sorted.bam.bai",
        RES_DIR + "/wgs_processing/{sample_code}-blood_wgs_seq-sorted.bam.bai",
        RES_DIR + "/" + GENOME_VERSION + ".fa.fai",
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        
        tumor_bam = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq-sorted.bam",
        normal_bam = RES_DIR + "/wgs_processing/{sample_code}-blood_wgs_seq-sorted.bam"
        
    output:
        RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-muse2.MuSE.txt"
        
    params:
        output_call_prefix = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-muse2"

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/snv_callers"
        
    resources:
        threads = WGS_THREADS,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/MuSE2/MuSE call -f {input.genome_fasta} -O {params.output_call_prefix} -n {resources.threads} {input.tumor_bam} {input.normal_bam}"


rule Muse2_sump:
    input:
        dbSNP_VCF,
        muse2_sump = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-muse2.MuSE.txt"
        
    output:
        muse2_vcf = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-muse2.vcf"
        
    params:
        output_call_prefix = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-muse2"

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/snv_callers"
        
    resources:
        threads = WGS_THREADS,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/MuSE2.0.4/MuSE sump -I {input.muse2_sump} -O {output.muse2_vcf} -G -n {resources.threads} -D {dbSNP_VCF} "



#####################
# Varscan2
#####################

rule mpileup_Varscan2:
    input:
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        
        input_bam = RES_DIR + "/wgs_processing/{sample}-sorted.bam"
        
    output:
        output_pileup = temp(RES_DIR + "/wgs_processing/{sample}.pileup")
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '14:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools mpileup -f {input.genome_fasta} {input.input_bam} > {output.output_pileup}"

rule Varscan2_snv_indel:
    input:
        RES_DIR + "/" + GENOME_VERSION + ".fa.fai",
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        
        tumor_pileup = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq.pileup",
        normal_pileup = RES_DIR + "/wgs_processing/{sample_code}-blood_wgs_seq.pileup"
        
    output:
        varscan2_snv_vcf = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-varscan2_snv.vcf",
        varscan2_indel_vcf = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-varscan2_indel.vcf"

    singularity:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/java_amazoncorretto/java_amazoncorretto.sif"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '14:0:0:0',
        individual_core_memory = '120G'
        
    shell:
        "java -jar /.mounts/labs/reimandlab/private/users/abahcheli/software/VarScan.v2.3.9.jar somatic {input.normal_pileup} {input.tumor_pileup} --output-snp {output.varscan2_snv_vcf} --output-indel {output.varscan2_indel_vcf} --output-vcf 1"




#####################
# Octopus
#####################

rule octopus:
    input:
        RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq-sorted.bam.bai",
        RES_DIR + "/wgs_processing/{sample_code}-blood_wgs_seq-sorted.bam.bai",
        RES_DIR + "/" + GENOME_VERSION + ".fa.fai",
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        
        tumor_bam = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq-sorted.bam",
        normal_bam = RES_DIR + "/wgs_processing/{sample_code}-blood_wgs_seq-sorted.bam"
        
    output:
        output_vcf_gz = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-octopus_{chromosome}.vcf.gz"
        
    params:
        normal_sample = "{sample_code}-blood_wgs_seq",
        working_directory = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq",
        octopus_working_directory = "octopus_{chromosome}"

    singularity:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/octopus/octopus.sif"
        
    resources:
        threads = 10,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        '''cd {params.working_directory}
octopus -R {input.genome_fasta} -I {input.normal_bam} {input.tumor_bam} --normal-sample {params.normal_sample} --temp-directory-prefix {params.octopus_working_directory} --regions {wildcards.chromosome} --output {output.output_vcf_gz} --threads {resources.threads}
'''




