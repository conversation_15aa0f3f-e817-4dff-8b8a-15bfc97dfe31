# Alec Ba<PERSON>cheli - <EMAIL>


###################################
# filter Mutect2
###################################
rule filter_Mutect2:
    input:
        RES_DIR + "/" + GENOME_VERSION + ".fa.fai",
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",

        input_vcf_gz = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-mutect2_{chromosome}.vcf.gz"

    output:
        output_vcf_gz = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-filtered_mutect2_{chromosome}.vcf.gz"
        
    singularity:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-4.4.0.0/gatk.sif"
        
    params:
        java_memory = '200g',
        normal_sample = "{sample_code}-blood_wgs_seq",
        chromosome = "{chromosome}",
        sample_tmp_dir = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/mutect2/"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '205G'
        
    shell:
        "gatk --java-options '-Xmx{params.java_memory}' FilterMutectCalls --reference {input.genome_fasta} --variant {input.input_vcf_gz} --output {output.output_vcf_gz} --tmp-dir {params.sample_tmp_dir}"



###################################
# merge VCFs from each chromosome into a single VCF
###################################
rule merge_mutect2_vcfs:
    input:
        expanded_files = expand("{res_dir}/wgs_processing/{{sample}}/{{sample}}-filtered_mutect2_{chromosome}.vcf.gz", res_dir = RES_DIR, chromosome = chromosome_list)
        
    output:
        output_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-filtered_combined_mutect2.vcf.gz"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "vcf-merge {input.expanded_files} | gzip > {output.output_vcf}"

rule merge_octopus_vcfs:
    input:
        expanded_files = expand("{res_dir}/wgs_processing/{{sample}}/{{sample}}-octopus_{chromosome}.vcf.gz", res_dir = RES_DIR, chromosome = chromosome_list)
        
    output:
        output_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-combined_octopus.vcf.gz"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "vcf-merge {input.expanded_files} | gzip > {output.output_vcf}"

rule merge_strelka_vcfs:
    input:
        snvs_file = RES_DIR + "/wgs_processing/{sample}/results/variants/somatic.snvs.vcf.gz",
        indels_file = RES_DIR + "/wgs_processing/{sample}/results/variants/somatic.indels.vcf.gz"

    output:
        output_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-strelka_combined.vcf.gz"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "vcf-merge {input.snvs_file} {input.indels_file} | gzip > {output.output_vcf}"




rule gzip_varscan2_vcfs:
    input:
        varscan2_file = RES_DIR + "/wgs_processing/{sample}/{sample}-varscan2_{file}.vcf",

    output:
        varscan2_file_zipped = RES_DIR + "/wgs_processing/{sample}/{sample}-varscan2_{file}.vcf.gz"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "bgzip {input.varscan2_file}"

rule index_vcfs:
    input:
        varscan2_file_zipped = RES_DIR + "/wgs_processing/{sample}/{sample}-{file}.vcf.gz",

    output:
        varscan2_file_zipped_index = RES_DIR + "/wgs_processing/{sample}/{sample}-{file}.vcf.gz.tbi"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "tabix -p vcf {input.varscan2_file_zipped}"


rule merge_varscan2_vcfs:
    input:
        snvs_file = RES_DIR + "/wgs_processing/{sample}/{sample}-varscan2_indel.vcf.gz",
        indels_file = RES_DIR + "/wgs_processing/{sample}/{sample}-varscan2_snv.vcf.gz",

        snvs_file_index = RES_DIR + "/wgs_processing/{sample}/{sample}-varscan2_indel.vcf.gz.tbi",
        indels_file_index = RES_DIR + "/wgs_processing/{sample}/{sample}-varscan2_snv.vcf.gz.tbi"

    output:
        output_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-varscan2_combined.vcf.gz"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "vcf-merge {input.snvs_file} {input.indels_file} | gzip > {output.output_vcf}"




###################################
# filter for variants that pass
###################################
rule filter_final_mutect2:
    input:
        input_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-filtered_combined_mutect2.vcf.gz"
        
    output:
        output_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-final_mutect2.vcf.gz"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "zcat {input.input_vcf} | bcftools view -f PASS --output-type z -o {output.output_vcf}"

rule filter_final_octopus:
    input:
        input_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-combined_octopus.vcf.gz"
        
    output:
        output_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-filtered_octopus.vcf.gz"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "zcat {input.input_vcf} | bcftools view -f PASS --output-type z -o {output.output_vcf}"

rule filter_final_strelka:
    input:
        input_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-strelka_combined.vcf.gz"
        
    output:
        output_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-strelka_filtered.vcf.gz"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "zcat {input.input_vcf} | bcftools view -f PASS --output-type z -o {output.output_vcf}"

rule filter_final_muse2:
    input:
        input_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-muse2.vcf"
        
    output:
        output_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-muse2_filtered.vcf.gz"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "cat {input.input_vcf} | bcftools view -f PASS --output-type z -o {output.output_vcf}"


rule filter_final_varscan2:
    input:
        input_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-varscan2_combined.vcf.gz"

    output:
        output_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-varscan2_filtered.vcf.gz"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "zcat {input.input_vcf} | bcftools view -f PASS --output-type z -o {output.output_vcf}"





###################################
# common variants in strelka, muse2, and octopus
###################################

rule combine_vcfs_not_octopus:
    input:
        RES_DIR + "/wgs_processing/{sample}/{sample}-strelka_filtered.vcf.gz.tbi",
        RES_DIR + "/wgs_processing/{sample}/{sample}-muse2_filtered.vcf.gz.tbi",
        RES_DIR + "/wgs_processing/{sample}/{sample}-varscan2_filtered.vcf.gz.tbi",
        
        strelka_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-strelka_filtered.vcf.gz",
        muse2_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-muse2_filtered.vcf.gz",
        varscan2_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-varscan2_filtered.vcf.gz"

    output:
        output_vcf = RES_DIR + "/wgs_processing/{sample}/strelka_muse2_varscan2_combined.vcf.gz"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '40G'
        
    shell:
        "vcf-merge {input.strelka_vcf} {input.muse2_vcf} {input.varscan2_vcf} | gzip > {output.output_vcf}"


rule process_combined_vcfs_not_octopus:
    input:
        # RES_DIR + "/wgs_processing/{sample}/strelka_muse2_varscan2_combined.vcf.gz.tbi",
        input_vcf = RES_DIR + "/wgs_processing/{sample}/strelka_muse2_varscan2_combined.vcf.gz",

        script = BIN_DIR + "/wgs_processing/007a-common_snvs_from_vcfs.py"

    output:
        combined_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-final_strelka_muse2_varscan2_common_variants.vcf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '40G'
        
    shell:
        "{PYTHON} {input.script} --input_vcf {input.input_vcf} --output_file {output.combined_vcf}"



# rule combine_vcfs_not_octopus:
#     input:
#         RES_DIR + "/wgs_processing/{sample}/{sample}-strelka_filtered.vcf.gz.tbi",
#         RES_DIR + "/wgs_processing/{sample}/{sample}-muse2_filtered.vcf.gz.tbi",
#         RES_DIR + "/wgs_processing/{sample}/{sample}-varscan2_filtered.vcf.gz.tbi",
        
#         strelka_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-strelka_filtered.vcf.gz",
#         muse2_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-muse2_filtered.vcf.gz",
#         varscan2_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-varscan2_filtered.vcf.gz",

#         script = BIN_DIR + "/wgs_processing/007a-common_snvs_from_vcfs.py"

#     output:
#         combined_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-final_strelka_muse2_varscan2_common_variants.vcf"
        
#     resources:
#         threads = 4,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '20G'
        
#     shell:
#         "{PYTHON} {input.script} --strelka_file {input.strelka_vcf} --muse2_file {input.muse2_vcf} --varscan2_file {input.varscan2_vcf} --output_file {output.combined_vcf} --threads {resources.threads}"




###################################
# annotate variants
###################################
# rule gunzip_vcf:
#     input:
#         input_vcf = RES_DIR + "/wgs_processing/{sample}/{vcf_file}.vcf.gz"

#     output:
#         output_vcf = temp(RES_DIR + "/wgs_processing/{sample}/{vcf_file}.vcf")

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '20G'
        
#     shell:
#         "zcat {input.input_vcf} > {output.output_vcf}"



rule ensembl_annotate_variants:
    input:
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",

        input_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-final_{software}.vcf"

    output:
        output_maf = RES_DIR + "/wgs_processing/{sample}/{sample}-final_{software}.maf"

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/vep_annotations"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '40G'
        
    shell:
        "vcf2maf.pl --input-vcf {input.input_vcf} --output-maf {output.output_maf} --vep-path /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/vep_annotations/bin --vep-data /.mounts/labs/reimandlab/private/users/abahcheli/software/vep_ensembl/GRCh38/ --ref-fasta {input.genome_fasta} --ncbi-build GRCh38"





# rule annovar_initial_annotate:
#     input:
#         combined_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-strelka_octopus_common_variants_final.vcf"

#     output:
#         RES_DIR + "/wgs_processing/{sample}/{sample}-common_snvs.hg38_multianno.txt",
#         RES_DIR + "/wgs_processing/{sample}/{sample}-common_snvs.hg38_multianno.vcf"

#     params:
#         sample_prefix = RES_DIR + "/wgs_processing/{sample}/{sample}-common_snvs"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/software/annovar/table_annovar.pl {input.combined_vcf} /.mounts/labs/reimandlab/private/users/abahcheli/software/annovar/humandb_v3/ -buildver hg38 -out {params.sample_prefix} -remove -protocol refgene -operation g -nastring . -vcfinput"


# rule annovar_functional_annotate:
#     input:
#         annovar_annotated_file = RES_DIR + "/wgs_processing/{sample}/{sample}-common_snvs.hg38_multianno.txt",

#     output:
#         RES_DIR + "/wgs_processing/{sample}/{sample}-common_snvs.hg38_multianno.txt.variant_function",
#         RES_DIR + "/wgs_processing/{sample}/{sample}-common_snvs.hg38_multianno.txt.exonic_variant_function"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/software/annovar/annotate_variation.pl {input.combined_vcf} /.mounts/labs/reimandlab/private/users/abahcheli/software/annovar/humandb_v3/ -geneanno -dbtype refGene -buildver hg38"





# rule combine_vcfs:
#     input:
#         octopus_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-filtered_octopus.vcf.gz",
#         strelka_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-strelka_filtered.vcf.gz",
#         muse2_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-muse2_filtered.vcf.gz",

#         script = BIN_DIR + "/wgs_processing/007a-common_snvs_from_vcfs.py"

#     output:
#         combined_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-final_strelka_octopus_common_variants.vcf"
        
#     resources:
#         threads = 4,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '20G'
        
#     shell:
#         "{PYTHON} {input.script} --octopus_file {input.octopus_vcf} --strelka_file {input.strelka_vcf} --muse2_file {input.muse2_vcf} --output_file {output.combined_vcf} --threads {resources.threads}"






