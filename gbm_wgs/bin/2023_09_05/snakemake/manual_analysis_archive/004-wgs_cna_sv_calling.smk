# Alec Bahcheli - <EMAIL>
# compilation of different CNV callers benchmarked in https://www.ncbi.nlm.nih.gov/pmc/articles/PMC8699073/


##########################################
# SVs and coverage depth
##########################################


#####################
# mosdepth
#####################
rule mosdepth:
    input:
        RES_DIR + "/wgs_processing/{sample}-sorted.bam.bai",
        
        RES_DIR + "/" + GENOME_VERSION + ".fa.fai",
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        
        sorted_bam = RES_DIR + "/wgs_processing/{sample}-sorted.bam"
        
    output:
        RES_DIR + "/wgs_processing/{sample}/coverage.per-base.bed.gz",
        RES_DIR + "/wgs_processing/{sample}/coverage.regions.bed.gz"

        
    # conda:
    #     "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    params:
        output_prefix = RES_DIR + "/wgs_processing/{sample}/coverage",
        window_size = 50000
        
    resources:
        threads = WGS_THREADS,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '7G'
        
    shell:
        '/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/mosdepth {params.output_prefix} {input.sorted_bam} --fasta {input.genome_fasta} --by {params.window_size} --threads {resources.threads}'




#####################
# VarScan2
#####################

rule Varscan2:
    input:
        RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq-sorted.bam.bai",
        RES_DIR + "/wgs_processing/{sample_code}-blood_wgs_seq-sorted.bam.bai",
        CENTROMERE_BED,
        EXOME_BED,
        RES_DIR + "/" + GENOME_VERSION + ".fa.fai",
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        
        tumor_bam = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq-sorted.bam",
        normal_bam = RES_DIR + "/wgs_processing/{sample_code}-blood_wgs_seq-sorted.bam"
        
    output:
        output_cnv = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq.cnv"
        
    singularity:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/varscan2/varscan2.sif"
        
    params:
        sample_id = "{sample_code}-{tumor}",
        sample_tmp_dir = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '120G'
        
    shell:
        "/usr/local/bin/run_varscan -c {input.normal_bam} -t {input.tumor_bam} -q {params.sample_id} -i {input.genome_fasta} -b {CENTROMERE_BED} -w {EXOME_BED} -s {params.sample_tmp_dir} -d > {output.output_cnv}"
# docker run  -v /path/to/input/files:/data jeltje/varscan2 -c normal.bam -t  tumor.bam -q sampleid -i genome.fa -b centromeres.bed -w targets.bed -s tmpdir > varscan.cnv




#####################
# HMM copy
#####################

############
# genome pre-processing
# https://github.com/shahcompbio/hmmcopy_utils
############

rule bowtie_index:
    input:        
        genome = RES_DIR + "/" + GENOME_VERSION + ".fa"
        
    output:
        bowtie_index = RES_DIR + "/" + GENOME_VERSION + ".fa.4.ebwt"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/hmmcopy"

    params:
        read_length = read_length
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '200G'
        
    shell:
        '/.mounts/labs/reimandlab/private/users/abahcheli/software/hmmcopy_utils/util/mappability/generateMap.pl -b {input.genome} -o {output.bowtie_index} --window {params.read_length}'

rule bigwig_bowtie_mappability:
    input:
        RES_DIR + "/" + GENOME_VERSION + ".fa.4.ebwt",

        genome = RES_DIR + "/" + GENOME_VERSION + ".fa"
        
    output:
        mappability_file = RES_DIR + "/" + GENOME_VERSION + ".fa.map.bw"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/hmmcopy"

    params:
        read_length = read_length

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '200G'
        
    shell:
        '/.mounts/labs/reimandlab/private/users/abahcheli/software/hmmcopy_utils/util/mappability/generateMap.pl {input.genome} -o {output.mappability_file} --window {params.read_length}'

rule genome_mappability_bins:
    input:        
        mappability_file = RES_DIR + "/" + GENOME_VERSION + ".fa.map.bw"
        
    output:
        bigwig_mappability_file = RES_DIR + "/" + GENOME_VERSION + ".fa.map.ws_7500.wig"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/hmmcopy"

    params:
        window_size = hmm_window_size

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        '/.mounts/labs/reimandlab/private/users/abahcheli/software/hmmcopy_utils/bin/mapCounter -w {params.window_size} {input.mappability_file} > {output.bigwig_mappability_file}'

rule genome_gc_bigwig:
    input:        
        genome = RES_DIR + "/" + GENOME_VERSION + ".fa"
        
    output:
        bigwig_gc_file = RES_DIR + "/" + GENOME_VERSION + ".gc.wig"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/hmmcopy"

    params:
        window_size = hmm_window_size

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '40G'
        
    shell:
        '/.mounts/labs/reimandlab/private/users/abahcheli/software/hmmcopy_utils/bin/gcCounter -w {params.window_size} {input.genome} > {output.bigwig_gc_file}'



rule sample_bin_counts:
    input:        
        bowtie_index = RES_DIR + "/wgs_processing/{sample}-sorted.bam.bai",
        merged_alignment_bam = RES_DIR + "/wgs_processing/{sample}-sorted.bam"
        
    output:
        binned_read_counts = RES_DIR + "/wgs_processing/{sample}/hmm_binned_counts.seg"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/hmmcopy"

    params:
        window_size = hmm_window_size

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        '/.mounts/labs/reimandlab/private/users/abahcheli/software/hmmcopy_utils/bin/readCounter -w {params.window_size} -s {input.merged_alignment_bam} > {output.binned_read_counts}'

rule sample_counts_to_wig:
    input:
        binned_read_counts = RES_DIR + "/wgs_processing/{sample}/hmm_binned_counts.seg",

        script = BIN_DIR + "/wgs_processing/002a-hmmcopy_counts_to_wig.py"
        
    output:
        wig_file = RES_DIR + "/wgs_processing/{sample}/hmm_binned_counts.wig"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        '{PYTHON} {input.script} --segment_file {input.binned_read_counts} --wig_file {output.wig_file}'


rule hmmcopy:
    input:
        bigwig_gc_file = RES_DIR + "/" + GENOME_VERSION + ".gc.wig",
        bigwig_mappability_file = RES_DIR + "/" + GENOME_VERSION + ".fa.map.ws_7500.wig",

        tumor_wigfile = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/hmm_binned_counts.wig",
        normal_wigfile = RES_DIR + "/wgs_processing/{sample_code}-blood_wgs_seq/hmm_binned_counts.wig",

        script = BIN_DIR + "/wgs_processing/002b-hmmcopy.py",
        hmmcopy_rscript = BIN_DIR + "/wgs_processing/002c-hmmcopy.R"
        
    output:
        results_file = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/hmmcopy_results.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        '{PYTHON} {input.script} --bigwig_gc_file {input.bigwig_gc_file} --bigwig_mappability_file {input.bigwig_mappability_file} --tumor_wigfile {input.tumor_wigfile} --normal_wigfile {input.normal_wigfile} --hmmcopy_rscript {input.hmmcopy_rscript} --results_file {output.results_file}'




#####################
# matched NORMAL - control-freec
#####################
rule freec_config_matched:
    input:
        RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/null.txt",
        tumor_bam = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq-sorted.bam",
        normal_bam = RES_DIR + "/wgs_processing/{sample_code}-blood_wgs_seq-sorted.bam",

        sample_fasta_index = RES_DIR + "/" + GENOME_VERSION + ".fa.fai",

        script = BIN_DIR + "/wgs_processing/001a-config_freec_matched.py"
        
    output:
        config_file = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/config_freec-matched.txt"
        
    params:
        chr_directory = RES_DIR + "/chromosomes/",
        output_dir = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        '{PYTHON} {input.script} --tumor_bam {input.tumor_bam} --normal_bam {input.normal_bam} --sample_fasta_index {input.sample_fasta_index} --chr_directory {params.chr_directory} --output_dir {params.output_dir} --config_file {output.config_file}'



rule freec_matched:
    input:        
        RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq-sorted.bam.bai",
        RES_DIR + "/wgs_processing/{sample_code}-blood_wgs_seq-sorted.bam.bai",

        RES_DIR + "/chromosomes/chr1.fa",
        RES_DIR + "/" + GENOME_VERSION + ".fa.fai",
        
        config_file = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/config_freec-matched.txt"
        
    output:
        RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-sorted.bam_CNVs"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/cnv_callers"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        'freec -conf {input.config_file}'






#####################
# CNVkit
# https://github.com/etal/cnvkit
#####################

# run cnv_kit
rule cnv_kit:
    input:
        RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq-sorted.bam.bai",
        
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        cnv_kit_annotations = REF_DATA_DIR + "/ensembl_hg38_27_10_2022.bed",
        
        tumor_bam = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq-sorted.bam",
        normal_bam = RES_DIR + "/wgs_processing/{sample_code}-blood_wgs_seq-sorted.bam"
        
    output:
        RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-sorted.call.cns",
        cnv_kit_results = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/cnv_kit.cnn"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/cnv_callers"
    
    params:
        output_directory = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq"
    
    resources:
        threads = 10,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '15G'
        
    shell:
        "cnvkit.py batch {input.tumor_bam} --normal {input.normal_bam} --method wgs --fasta {input.genome_fasta} --annotate {input.cnv_kit_annotations} --output-reference {output.cnv_kit_results} --output-dir {params.output_directory} --processes {resources.threads}"



#####################
# CNVnator
# https://github.com/abyzovlab/CNVnator
#####################

rule cnvnator:
    input:
        RES_DIR + "/wgs_processing/{sample}-sorted.bam.bai",
        RES_DIR + "/chromosomes/chr1.fa",
        
        sorted_bam = RES_DIR + "/wgs_processing/{sample}-sorted.bam"
        
    output:
        root_file = RES_DIR + "/wgs_processing/{sample}/cnvnator.root",
        cnvnator_results = RES_DIR + "/wgs_processing/{sample}/cnvnator.tsv"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/cnvnator"
    
    params:
        chromosome_dir = RES_DIR + "/chromosomes_succinct/",
        his_size = 1000
    
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "cnvnator -root {output.root_file} -tree {input.sorted_bam} && \
        cnvnator -root {output.root_file} -his {params.his_size} -d {params.chromosome_dir} && \
        cnvnator -root {output.root_file} -stat {params.his_size} && \
        cnvnator -root {output.root_file} -partition {params.his_size} -ngc && \
        cnvnator -root {output.root_file} -call {params.his_size} -ngc > {output.cnvnator_results}"




#####################
# Delly2
# https://hub.docker.com/r/dellytools/delly/
#####################

rule delly2_call:
    input:
        RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq-sorted.bam.bai",
        RES_DIR + "/wgs_processing/{sample_code}-blood_wgs_seq-sorted.bam.bai",
        
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        genome_exclusion_regions = REF_DATA_DIR + "/hg38_exclusion_regions.tsv",

        tumor_bam = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq-sorted.bam",
        normal_bam = RES_DIR + "/wgs_processing/{sample_code}-blood_wgs_seq-sorted.bam"

    output:
        calls_file = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/delly2_calls.bcf"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/delly2"
    
    resources:
        threads = 10,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """export OMP_NUM_THREADS={resources.threads}
delly call -x {input.genome_exclusion_regions} -o {output.calls_file} -g {input.genome_fasta} {input.tumor_bam} {input.normal_bam}"""


rule delly2_filter_prep:
    input:
        RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/null.txt",

        script = BIN_DIR + "/wgs_processing/004a-delly2_filter_config.py"

    output:
        sample_description_file = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/delly2_samples.tsv"

    params:
        tumor_sample = "{sample_code}-{tumor}_wgs_seq",
        normal_sample = "{sample_code}-blood_wgs_seq"
    
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        "{PYTHON} {input.script} --tumor_sample {params.tumor_sample} --normal_sample {params.normal_sample} --sample_description_file {output.sample_description_file}"


rule delly2_filter:
    input:
        calls_file = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/delly2_calls.bcf",
        sample_description_file = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/delly2_samples.tsv"

    output:
        filtered_variants_file = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/delly2_calls-filtered.bcf"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/delly2"
    
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '80G'
        
    shell:
        "delly filter -f somatic -s {input.sample_description_file} -o {output.filtered_variants_file} {input.calls_file}"


rule delly2_translate_to_vcf:
    input:
        bcf_file = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/delly2_calls-filtered.bcf"

    output:
        vcf_file = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/delly2_calls-filtered.vcf"
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
    
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '40G'
        
    shell:
        "bcftools view {input.bcf_file} > {output.vcf_file}"




#####################
# Manta
# https://github.com/Illumina/manta/blob/master/docs/userGuide/README.md#input-requirements
#####################

rule manta_configure_workflow:
    input:
        RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq-sorted.bam.bai",
        RES_DIR + "/wgs_processing/{sample_code}-blood_wgs_seq-sorted.bam.bai",
        
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",

        tumor_bam = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq-sorted.bam",
        normal_bam = RES_DIR + "/wgs_processing/{sample_code}-blood_wgs_seq-sorted.bam"

    output:
        workflow_file = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/manta/runWorkflow.py"
        
    singularity:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/manta/manta.sif"

    params:
        working_dir = RES_DIR + "/wgs_processing/{sample_code}-{tumor}_wgs_seq/manta"
    
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        "configManta.py --referenceFasta {input.genome_fasta} --tumorBam {input.tumor_bam} --normalBam {input.normal_bam} --runDir {params.working_dir}"


rule manta_run_workflow:
    input:
        workflow_file = RES_DIR + "/wgs_processing/{sample}/manta/runWorkflow.py"

    output:
        RES_DIR + "/wgs_processing/{sample}/manta/results/variants/candidateSV.vcf.gz",
        RES_DIR + "/wgs_processing/{sample}/manta/results/variants/candidateSmallIndels.vcf.gz"
        
    singularity:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/manta/manta.sif"
    
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '40G'
        
    shell:
        "{input.workflow_file}"











# rule cnvnator_process_results:
#     input:
#         cnvnator_results = RES_DIR + "/wgs_processing/{sample}/cnvnator.tsv"
#         cnv_file = RES_DIR + "/wgs_processing/{sample}/cnvnator.tsv",
#         gene_bed_file = REF_DATA_DIR + "/gencode.vM32.annotation.bed",

#         script = BIN_DIR + "/cnvnator_results_per_sample.py"
        
#     output:
#         cnvnator_results_file = FIGURE_DATA_DIR + "wgs_processing/{sample}/cnvnator_results.tsv"
        
#     params:
#         control_files_csv = ",".join(expand("{res_dir}/wgs_processing/{sample}/cnvnator.tsv", res_dir = RES_DIR, sample = control_sample_codes)),
#         working_directory = RES_DIR + "/wgs_processing/{sample}/"
    
#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '20G'
        
#     shell:
#         "{PYTHON} {input.script} --cnv_file {input.cnv_file} --control_files_csv {params.control_files_csv} --gene_bed_file {input.gene_bed_file} --working_directory {params.working_directory} --cnvnator_results_file {output.cnvnator_results_file}"











