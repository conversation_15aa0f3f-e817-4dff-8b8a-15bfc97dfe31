# Alec Ba<PERSON>cheli - <EMAIL>


###################################
# mutation descriptions
###################################

rule maf_summary:
    input:
        expand("{res_dir}/wgs_processing/{sample}/{sample}-final_{{software}}.maf", res_dir = RES_DIR, sample = wgs_sample_list),

        script = BIN_DIR + "/wgs_interpretations/001a-maf_file_summary.py",
        r_script = BIN_DIR + "/wgs_interpretations/001b-maf_file_summary.R"

    output:
        figure_data_file = RES_DIR + "/analysis_wgs/_figure_data/001-{software}-combined.maf",
        figure_stats_file = RES_DIR + "/analysis_wgs/_figure_data/001-{software}-summary_stats.tsv",

        figure_file = RES_DIR + "/analysis_wgs/_figures/001-{software}-maf_summary.pdf"

    params:
        wgs_results_directory = RES_DIR + "/wgs_processing"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '40G'
        
    shell:
        "{PYTHON} {input.script} --wgs_results_directory {params.wgs_results_directory} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file} --r_script {input.r_script} --figure_file {output.figure_file}"


rule maf_cna_summary:
    input:
        snv_file = RES_DIR + "/analysis_wgs/_figure_data/001-{software}-combined.maf",
        cna_combined_file = RES_DIR + "/analysis_wgs/_figure_data/002-gene_alterations_by_cna.tsv",

        script = BIN_DIR + "/wgs_interpretations/001c-maf_cna_summary.py",
        r_script = BIN_DIR + "/wgs_interpretations/001d-maf_cna_summary.R"

    output:
        figure_data_file = RES_DIR + "/analysis_wgs/{software}-maf_cna.tsv",

        figure_file = RES_DIR + "/analysis_wgs/_figures/001-{software}-maf_cna.pdf"

    params:
        wgs_results_directory = RES_DIR + "/wgs_processing"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '40G'
        
    shell:
        "{PYTHON} {input.script} --snv_file {input.snv_file} --cna_combined_file {input.cna_combined_file} --figure_data_file {output.figure_data_file} --r_script {input.r_script} --figure_file {output.figure_file}"


###################################
# sigprofiler
###################################
rule link_vcf_for_sigprofiler:
    input:
        input_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-final_{software}.vcf.gz"

    output:
        output_vcf = RES_DIR + "/analysis_wgs/sigprofiler_input/{sample}-final_{software}.vcf",

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '5G'
        
    shell:
        "zcat {input.input_vcf} > {output.output_vcf}"

rule sigprofiler_signature_assignment:
    input:
        expand("{res_dir}/analysis_wgs/sigprofiler_input/{sample}-final_{software}.vcf", res_dir = RES_DIR, sample = wgs_sample_list, software = ['mutect2']),

        script = BIN_DIR + "/wgs_interpretations/003a-sigprofiler_assignment.py"

    output:
        signature_assignment_file = RES_DIR + "/analysis_wgs/sigprofiler/JOB_METADATA_SPA.txt"

    params:
        sigprofiler_python = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/sigprofiler/bin/python",

        sigprofiler_input_dir = RES_DIR + "/analysis_wgs/sigprofiler_input",
        sigprofiler_res_dir = RES_DIR + "/analysis_wgs/sigprofiler"

    resources:
        threads = 1,
        queue = "u20build",
        jobtime = '0:4:0:0',
        individual_core_memory = '10G'
        
    shell:
        "{params.sigprofiler_python} {input.script} --input_dir {params.sigprofiler_input_dir} --sigprofiler_res_dir {params.sigprofiler_res_dir}"


rule visualize_sigprofiler_assignments:
    input:
        signature_assignment_file = RES_DIR + "/analysis_wgs/sigprofiler/JOB_METADATA_SPA.txt",

        script = BIN_DIR + "/wgs_interpretations/003b-sigprofiler_by_sample.py",
        r_script = BIN_DIR + "/wgs_interpretations/003c-sigprofiler_by_sample.R"

    output:
        # figure_data_file = RES_DIR + "/analysis_wgs/_figure_data/001-sigprofiler_by_sample.tsv",
        figure_stats_file = RES_DIR + "/analysis_wgs/_figure_data/003-sigprofiler_by_sample.tsv",

        figure_file = RES_DIR + "/analysis_wgs/_figures/003-sigprofiler_by_sample.pdf"

    params:
        sigprofiler_assignment_file = RES_DIR + "/analysis_wgs/sigprofiler/Assignment_Solution/Activities/Assignment_Solution_Activities.txt"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --sigprofiler_assignment_file {params.sigprofiler_assignment_file} --figure_stats_file {output.figure_stats_file} --r_script {input.r_script} --figure_file {output.figure_file}"




###################################
# pyclone
###################################
rule vcf_pyclone_prep:
    input:
        input_vcf = RES_DIR + "/wgs_processing/{sample}/{sample}-final_{software}.vcf.gz",

        script = BIN_DIR + "/wgs_interpretations/004a-vcf_preparation.py"

    output:
        output_vcf = RES_DIR + "/analysis_wgs/pyclone_vi/{sample}-final_modified_{software}.vcf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --input_vcf {input.input_vcf} --output_vcf {output.output_vcf}"


rule pyclone_vi_input_prep:
    input:
        input_vcf = RES_DIR + "/analysis_wgs/pyclone_vi/{sample}-final_modified_{software}.vcf",
        cna_file = RES_DIR + "/analysis_wgs/_figure_data/002-cnas_primary_recurrent.tsv",

        script = BIN_DIR + "/wgs_interpretations/004b-pyclone_vi_preparation.py"

    output:
        output_tsv = RES_DIR + "/analysis_wgs/pyclone_vi/{sample}_{software}-pyclone_vi_input.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --input_vcf {input.input_vcf} --cna_file {input.cna_file} --output_tsv {output.output_tsv} --threads {resources.threads}"


rule pyclone_vi_analysis_part1:
    input:
        input_tsv = RES_DIR + "/analysis_wgs/pyclone_vi/{sample}_{software}-pyclone_vi_input.tsv"

    output:
        pyclone_results = RES_DIR + "/analysis_wgs/pyclone_vi/{sample}_{software}-pyclone_vi.h5"

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/pyclone-vi"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'
        
    shell:
        "pyclone-vi fit -i {input.input_tsv} -o {output.pyclone_results}"
        # "pyclone-vi fit -i {input.input_tsv} -o {output.pyclone_results} -c 40 -d beta-binomial -r 10"


rule pyclone_vi_analysis_part2:
    input:
        pyclone_results = RES_DIR + "/analysis_wgs/pyclone_vi/{sample}_{software}-pyclone_vi.h5"

    output:
        pyclone_results_final = RES_DIR + "/analysis_wgs/pyclone_vi/{sample}_{software}-pyclone_vi.tsv"

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/pyclone-vi"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '100G'
        
    shell:
        "pyclone-vi write-results-file -i {input.pyclone_results} -o {output.pyclone_results_final}"


rule pyclone_vi_visualize_results:
    input:
        expand("{res_dir}/analysis_wgs/pyclone_vi/{sample}_mutect2-pyclone_vi.tsv", res_dir = RES_DIR, sample = wgs_sample_list),

        script = BIN_DIR + "/wgs_interpretations/004c-pyclone_visualization.py",
        r_script = BIN_DIR + "/wgs_interpretations/004d-pyclone_visualization.R"

    output:
        figure_data_file = RES_DIR + "/analysis_wgs/_figure_data/004-pyclone_visualization.tsv",
        figure_stats_file = RES_DIR + "/analysis_wgs/_figure_data/004-pyclone_visualization_stats.tsv",

        figure_file = RES_DIR + "/analysis_wgs/_figures/004-pyclone_visualization.pdf"

    params:
        pyclone_dir = RES_DIR + "/analysis_wgs/pyclone_vi"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --pyclone_dir {params.pyclone_dir} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file} --r_script {input.r_script} --figure_file {output.figure_file}"



###################################
# CNA summaries
###################################

rule cna_combined_and_summarized:
    input:
        # CNAs
        expand("{res_dir}/wgs_processing/{sample}/{sample}.cnv", res_dir = RES_DIR, sample = wgs_sample_list, chromosome = chromosome_list),
        expand("{res_dir}/wgs_processing/{sample}/hmmcopy_results.tsv", res_dir = RES_DIR, sample = wgs_sample_list),
        expand("{res_dir}/wgs_processing/{sample}/{sample}-sorted.bam_CNVs", res_dir = RES_DIR, sample = wgs_sample_list),
        expand("{res_dir}/wgs_processing/{sample}/cnv_kit.cnn", res_dir = RES_DIR, sample = wgs_sample_list),
        expand("{res_dir}/wgs_processing/{sample}/cnvnator.tsv", res_dir = RES_DIR, sample = wgs_sample_list),

        script = BIN_DIR + "/wgs_interpretations/002a-combine_cnas_and_summarize.py",
        r_script = BIN_DIR + "/wgs_interpretations/002b-combine_cnas_and_summarize.R"

    output:
        figure_data_file = RES_DIR + "/analysis_wgs/_figure_data/002-combined_cnas.tsv",
        figure_stats_file = RES_DIR + "/analysis_wgs/_figure_data/002-combined_cnas_stats.tsv",

        figure_file = RES_DIR + "/analysis_wgs/_figures/002-combined_cnas_stats.pdf"

    params:
        wgs_results_directory = RES_DIR + "/wgs_processing"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --wgs_results_directory {params.wgs_results_directory} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file} --r_script {input.r_script} --figure_file {output.figure_file}"


rule cnas_primary_recurrent_comparison:
    input:
        cna_file = RES_DIR + "/analysis_wgs/_figure_data/002-combined_cnas.tsv",

        script = BIN_DIR + "/wgs_interpretations/002c-cnas_primary_recurrent_comparison.py",
        r_script = BIN_DIR + "/wgs_interpretations/002d-cnas_primary_recurrent_comparison.R"

    output:
        figure_data_file = RES_DIR + "/analysis_wgs/_figure_data/002-cnas_primary_recurrent.tsv",
        figure_stats_file = RES_DIR + "/analysis_wgs/_figure_data/002-cnas_primary_recurrent_stats.tsv",

        figure_file = RES_DIR + "/analysis_wgs/_figures/002-cnas_primary_recurrent.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --cna_file {input.cna_file} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file} --r_script {input.r_script} --figure_file {output.figure_file}"


rule cnas_primary_recurrent_sorted_heatmap:
    input:
        input_stats_file = RES_DIR + "/analysis_wgs/_figure_data/002-cnas_primary_recurrent_stats.tsv",

        script = BIN_DIR + "/wgs_interpretations/002g-cnas_primary_recurrent_sorted_heatmap.py",
        r_script = BIN_DIR + "/wgs_interpretations/002h-cnas_primary_recurrent_sorted_heatmap.R"

    output:
        figure_stats_file = RES_DIR + "/analysis_wgs/_figure_data/002-cnas_primary_recurrent_sorted_heatmap.tsv",

        figure_file = RES_DIR + "/analysis_wgs/_figures/002-cnas_primary_recurrent_sorted_heatmap.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --input_stats_file {input.input_stats_file} --figure_stats_file {output.figure_stats_file} --r_script {input.r_script} --figure_file {output.figure_file}"



rule cna_regions_to_genes:
    input:
        cna_combined_file = RES_DIR + "/analysis_wgs/_figure_data/002-combined_cnas.tsv",
        ensembl_gene_bed_file = REF_DATA_DIR + "/ensembl_hg38_27_10_2022.bed",

        script = BIN_DIR + "/wgs_interpretations/002i-cnas_to_genes.py"

    output:
        gene_alterations_by_cna_file = RES_DIR + "/analysis_wgs/_figure_data/002-gene_alterations_by_cna.tsv"
    
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --cna_combined_file {input.cna_combined_file} --ensembl_gene_bed_file {input.ensembl_gene_bed_file} --gene_alterations_by_cna_file {output.gene_alterations_by_cna_file}"





###################################
# circos plots
###################################

rule circos_cnas:
    input:
        expand("{res_dir}/wgs_processing/{sample}/coverage.regions.bed.gz", res_dir = RES_DIR, sample = wgs_sample_list_with_blood),
        consensus_cna_file = RES_DIR + "/analysis_wgs/_figure_data/002-cnas_primary_recurrent.tsv",

        script = BIN_DIR + "/wgs_interpretations/002e-cna_circos.py",
        r_script = BIN_DIR + "/wgs_interpretations/002f-cna_circos.R"

    output:
        figure_data_file = RES_DIR + "/analysis_wgs/_figure_data/002-cna_circos.tsv",
        figure_stats_file = RES_DIR + "/analysis_wgs/_figure_data/002-cna_circos_stats.tsv",

        figure_file = RES_DIR + "/analysis_wgs/_figures/002-cna_circos.pdf"

    params:
        process_dir = RES_DIR + "/wgs_processing"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --consensus_cna_file {input.consensus_cna_file} --process_dir {params.process_dir} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file} --r_script {input.r_script} --figure_file {output.figure_file}"





delly2_wgs_sample_list = ['RLGS1-primary_wgs_seq', 'RLGS1-recurrent_wgs_seq', 'RLGS2-recurrent_wgs_seq', 'RLGS3-primary_wgs_seq', 'RLGS3-recurrent_wgs_seq', 'RLGS4-primary_wgs_seq', 'RLGS4-recurrent_wgs_seq', 'RLGS5-primary_wgs_seq', 'RLGS5-recurrent_wgs_seq', 'RLGS6-primary_wgs_seq', 'RLGS6-recurrent_wgs_seq', 'RLGS7-primary_wgs_seq', 'RLGS7-recurrent_wgs_seq', 'RLGS8-primary_wgs_seq', 'RLGS8-recurrent_wgs_seq', 'RLGS9-primary_wgs_seq', 'RLGS9-recurrent_wgs_seq', 'RLGS10-primary_wgs_seq', 'RLGS10-recurrent_wgs_seq', 'RLGS11-primary_wgs_seq', 'RLGS11-recurrent_wgs_seq', 'RLGS12-primary_wgs_seq', 'RLGS12-recurrent_wgs_seq']

rule circos_svs:
    input:
        expand("{res_dir}/wgs_processing/{sample}/delly2_calls-filtered.vcf", res_dir = RES_DIR, sample = delly2_wgs_sample_list),
        # delly2_file = RES_DIR + "/wgs_processing/{sample}/delly2_calls-filtered.vcf",

        script = BIN_DIR + "/wgs_interpretations/005a-interpret_svs_delly2.py",
        r_script = BIN_DIR + "/wgs_interpretations/005c-sv_circos.R"

    output:
        figure_data_file = RES_DIR + "/analysis_wgs/_figure_data/002-sv_circos.tsv",

        figure_file = RES_DIR + "/analysis_wgs/_figures/002-sv_circos.pdf"

    params:
        results_dir = RES_DIR + "/wgs_processing"

    resources:
        threads = 20,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '3G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --figure_data_file {output.figure_data_file} --r_script {input.r_script} --figure_file {output.figure_file} --threads {resources.threads}"



rule delly2_svs_summary:
    input:
        sv_summary_file = RES_DIR + "/analysis_wgs/_figure_data/002-sv_circos.tsv",

        script = BIN_DIR + "/wgs_interpretations/005d-summarize_sv_sizes.py",
        r_script = BIN_DIR + "/wgs_interpretations/005e-summarize_sv_sizes.R"

    output:
        figure_data_file = RES_DIR + "/analysis_wgs/_figure_data/002-sv_circos_sizes.tsv",

        figure_file = RES_DIR + "/analysis_wgs/_figures/002-sv_circos_sizes.pdf"

    resources:
        threads = 20,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '3G'
        
    shell:
        "{PYTHON} {input.script} --sv_summary_file {input.sv_summary_file} --figure_data_file {output.figure_data_file} --r_script {input.r_script} --figure_file {output.figure_file}"











