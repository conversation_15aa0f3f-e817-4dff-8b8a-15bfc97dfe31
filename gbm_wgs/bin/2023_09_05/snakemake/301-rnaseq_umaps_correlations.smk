# Alec Ba<PERSON>chel<PERSON>


###################################
# clustering by gene expression
###################################

rule umap_tpm:
    input:
        tpm_file = RES_DIR + "/analysis_rna/star_salmon/salmon.merged.gene_tpm.tsv",

        script = BIN_DIR + "/rnaseq_interpretation/001a-umap_tpm.py"

    output:
        figure_data_file = RES_DIR + "/analysis_rna/_figure_data/001-umap_tpm.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --tpm_file {input.tpm_file} --figure_data_file {output.figure_data_file}"

rule visualize_umap_tpm:
    input:
        figure_data_file = RES_DIR + "/analysis_rna/_figure_data/001-umap_tpm.tsv",

        script = BIN_DIR + "/rnaseq_interpretation/001b-visualize_umap_tpm.R"

    output:
        figure_file = RES_DIR + "/analysis_rna/_figures/001-visualize_umap_tpm.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"


###################################
# gene expression correlations between primary-recurrent tumors
###################################

rule rna_correlation:
    input:
        tpm_file = RES_DIR + "/analysis_rna/star_salmon/salmon.merged.gene_tpm.tsv",

        script = BIN_DIR + "/rnaseq_interpretation/001c-rna_correlation.py"

    output:
        figure_stats_file = RES_DIR + "/analysis_rna/_figure_data/001-rna_correlation_stats.tsv",
        figure_data_file = RES_DIR + "/analysis_rna/_figure_data/001-rna_correlation.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --tpm_file {input.tpm_file} --figure_data_file {output.figure_data_file} --figure_stats_file {output.figure_stats_file}"

rule rna_correlation_visualize_faceted:
    input:
        figure_stats_file = RES_DIR + "/analysis_rna/_figure_data/001-rna_correlation_stats.tsv",
        figure_data_file = RES_DIR + "/analysis_rna/_figure_data/001-rna_correlation.tsv",

        r_script = BIN_DIR + "/rnaseq_interpretation/001d-rna_correlation_visualize_faceted.R"

    output:
        figure_file = RES_DIR + "/analysis_rna/_figures/001-rna_correlation_visualize_faceted.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.r_script} --figure_stats_file {input.figure_stats_file} --figure_data_file {input.figure_data_file} --figure_file {output.figure_file}"



