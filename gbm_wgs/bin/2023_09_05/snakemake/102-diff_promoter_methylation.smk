# Alec Bahcheli



###################################
# process the gff file
###################################

# subset to protein-coding genes and add tss
rule preprocess_gff_file:
    input:
        gff_file = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.gff",

        script = BIN_DIR + "/nanopore_diff_methylation/001a-preprocess_gff_file.py"

    output:
        protein_coding_genes_gff_file = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.protein_coding_genes.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --gff_file {input.gff_file} --protein_coding_genes_gff_file {output.protein_coding_genes_gff_file}"



###################################
# prepare bed and methylation files
###################################

# define the promoters of each gene as 300bp surrounding
rule define_protein_gene_promoters:
    input:
        protein_coding_genes_gff_file = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.protein_coding_genes.tsv",

        script = BIN_DIR + "/nanopore_diff_methylation/002a-protein_gene_promoters.py"

    output:
        protein_coding_promoter_bed_file = RES_DIR + "/analysis_nanopore/methyl_overview/protein_promoter_regions.bed"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --protein_coding_genes_gff_file {input.protein_coding_genes_gff_file} --protein_coding_promoter_bed_file {output.protein_coding_promoter_bed_file}"


# prepare methylation data
rule prepare_methylation_data:
    input:
        methyl_file = RES_DIR + "/nanopore_evolution/{sample}/{sample}-primary/mod/5mC/bedMethyl/5mC.{sample}-primary_{tumor}.bed.gz",

        script = BIN_DIR + "/nanopore_diff_methylation/002b-prepare_methylation_data.py"

    output:
        methyl_outfile = RES_DIR + "/analysis_nanopore/methyl_overview/{sample}-{tumor}_methylation_details.bed"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --methyl_file {input.methyl_file} --methyl_outfile {output.methyl_outfile}"

rule intersect_promoters_methylation:
    input:
        mgmt_protein_bed_file = RES_DIR + "/analysis_nanopore/methyl_overview/protein_promoter_regions.bed",
        methyl_outfile = RES_DIR + "/analysis_nanopore/methyl_overview/{sample}-{tumor}_methylation_details.bed"

    output:
        intersected_bed_file = RES_DIR + "/analysis_nanopore/methyl_overview/{sample}-{tumor}_promoters_methylation.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore/bin/bedtools intersect -a <(tail -n +2 {input.mgmt_protein_bed_file}) -b <(tail -n +2 {input.methyl_outfile}) -wa -wb > {output.intersected_bed_file}"""

# merge methylation data
rule merge_methylation_data:
    input:
        expand("{res_dir}/analysis_nanopore/methyl_overview/{sample_code}-{tumor}_promoters_methylation.bed", res_dir = RES_DIR, sample_code = sample_codes_list, tumor = ['normal', 'tumor']),
        gene_bed_file = RES_DIR + "/analysis_sarek/gene_impacts/protein_gene_regions.bed",

        script = BIN_DIR + "/nanopore_diff_methylation/002c-merge_methylation_data.py"

    output:
        combined_methylation_file = RES_DIR + "/analysis_nanopore/methyl_overview/promoter_methylation_data.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --gene_bed_file {input.gene_bed_file} --combined_methylation_file {output.combined_methylation_file}"



#####################
# differential methylation of promoters
#####################

# differential methylation of protein-coding gene promoters
rule primary_recurrent_differential_promoter_methylation_beta_value_test:
    input:
        promoter_methylation_combined_file = RES_DIR + "/analysis_nanopore/methyl_overview/promoter_methylation_data.tsv",

        script = BIN_DIR + "/nanopore_diff_methylation/003a-primary_recurrent_differential_promoter_methylation_beta_value.py"

    output:
        promoter_methlyation_results_file = RES_DIR + "/analysis_nanopore/diff_promoter_methylation/primary_recurrent_differential_promoter_methylation_beta_value.tsv"
        
    resources:
        threads = 30,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '3G'
        
    shell:
        "{PYTHON} {input.script} --promoter_methylation_combined_file {input.promoter_methylation_combined_file} --promoter_methlyation_results_file {output.promoter_methlyation_results_file} --threads {resources.threads}"


# extracting and visualizing using DPM
rule setup_df_for_dpm_test:
    input:
        promoter_methlyation_results_file = RES_DIR + "/analysis_nanopore/diff_promoter_methylation/primary_recurrent_differential_promoter_methylation_beta_value.tsv",

        script = BIN_DIR + "/nanopore_diff_methylation/003b-setup_df_for_dpm.py"

    output:
        pval_file = RES_DIR + "/analysis_nanopore/diff_promoter_methylation/pvals_for_dpm-{model}.tsv",
        fc_file = RES_DIR + "/analysis_nanopore/diff_promoter_methylation/fcs_for_dpm-{model}.tsv"

    params:
        model = '{model}'
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --promoter_methlyation_results_file {input.promoter_methlyation_results_file} --pval_file {output.pval_file} --fc_file {output.fc_file} --model {params.model}"

rule dpm_merge_methylation_pvalues_test:
    input:
        pval_file = RES_DIR + "/analysis_nanopore/diff_promoter_methylation/pvals_for_dpm-{model}.tsv",
        fc_file = RES_DIR + "/analysis_nanopore/diff_promoter_methylation/fcs_for_dpm-{model}.tsv",
        
        script = BIN_DIR + "/nanopore_diff_methylation/003c-dpm_merge_methylation_pvalues.R"

    output:
        merged_pval_file = RES_DIR + "/analysis_nanopore/diff_promoter_methylation/dpm_merged_pvalues-{model}.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{AP_RSCRIPT} {input.script} --pval_file {input.pval_file} --fc_file {input.fc_file} --merged_pval_file {output.merged_pval_file}"


rule finalize_methylation_pvalues:
    input:
        original_method_pval_file = RES_DIR + "/analysis_nanopore/diff_promoter_methylation/dpm_merged_pvalues-{model}.tsv",
        merged_pval_file = RES_DIR + "/analysis_nanopore/diff_promoter_methylation/dpm_merged_pvalues-{model}.tsv",
        
        script = BIN_DIR + "/nanopore_diff_methylation/003d-finalize_methylation_pvalues.py"

    output:
        final_pval_file = RES_DIR + "/analysis_nanopore/diff_promoter_methylation/finalized_pvalues_fcs-{model}.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --original_method_pval_file {input.original_method_pval_file} --merged_pval_file {input.merged_pval_file} --final_pval_file {output.final_pval_file}"



#####################
# visualizing p-value distributions
#####################

# here we explicitly selected the wilcoxon (paired) p-values
rule volcano_plot_methylation_pvalues_test:
    input:
        figure_stats_file = RES_DIR + "/analysis_nanopore/diff_promoter_methylation/finalized_pvalues_fcs-wilcoxon_p_value.tsv",
        
        script = BIN_DIR + "/nanopore_diff_methylation/004a-volcano_plot_methylation_pvalues.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures_diff_meth/volcano_plot_methylation_pvalues_defined_prom.pdf"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"


# visualize the mean promoter methylation differences in boxplots
rule mean_methylation_by_gene_and_sample:
    input:
        promoter_methylation_combined_file = RES_DIR + "/analysis_nanopore/methyl_overview/promoter_methylation_data.tsv",
        protein_coding_genes_gff_file = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.protein_coding_genes.tsv",

        script = BIN_DIR + "/nanopore_diff_methylation/004b-mean_methylation_by_gene_and_sample.py"

    output:
        mean_methylation_by_gene_sample_file = RES_DIR + "/analysis_nanopore/diff_promoter_methylation/mean_methylation_by_gene_and_sample.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{PYTHON} {input.script} --promoter_methylation_combined_file {input.promoter_methylation_combined_file} --protein_coding_genes_gff_file {input.protein_coding_genes_gff_file} --mean_methylation_by_gene_sample_file {output.mean_methylation_by_gene_sample_file}"

rule visualize_methylation_promoter_boxplots:
    input:
        figure_data_file = RES_DIR + "/analysis_nanopore/diff_promoter_methylation/mean_methylation_by_gene_and_sample.tsv",
        figure_stats_file = RES_DIR + "/analysis_nanopore/diff_promoter_methylation/finalized_pvalues_fcs-wilcoxon_p_value.tsv",

        script = BIN_DIR + "/nanopore_diff_methylation/004c-visualize_methylation_promoter_boxplots.R"

    output:
        figure_file = RES_DIR + "/analysis_nanopore/_figures_diff_meth/methylation_promoter_boxplots.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'
        
    shell:
        "{RSCRIPT} {input.script} --figure_data_file {input.figure_data_file} --figure_stats_file {input.figure_stats_file} --figure_file {output.figure_file}"



















