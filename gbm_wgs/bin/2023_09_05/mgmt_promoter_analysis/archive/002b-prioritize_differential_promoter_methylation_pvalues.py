# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from scipy.stats import combine_pvalues

help_message = '''
Failed
'''


def select_closest_rows(df, n_cpgs = 10):
    # Calculate the absolute distance from position to tss
    df['distance_to_tss'] = (df['position'] - df['tss']).abs()
    
    # Sort the DataFrame by gene and distance
    df_sorted = df.sort_values(by=['gene', 'distance_to_tss'])

    # Select up to n rows with the smallest distance for each gene
    closest_rows = df_sorted.groupby('gene').apply(lambda x: x.head(min(len(x), n_cpgs))).reset_index(drop=True)

    # also select the absolute closest rows and extract p-values from this
    single_closest_row = closest_rows.groupby('gene').apply(lambda x: x.head(1)).reset_index(drop=True)

    return single_closest_row, closest_rows


def calculate_fisher_pvalues(df, model):
    # Use the select_closest_rows function to filter the DataFrame
    single_closest_row_df, filtered_df = select_closest_rows(df)

    # subset to only the site and gene columns
    sub_df = filtered_df[['gene', 'position']]
    
    # Initialize a list to store Fisher's combined p-values
    fisher_pvalues = []

    # Group by gene and calculate Fisher's combined p-value for n selected rows
    for gene, group in filtered_df.groupby('gene'):
        # Ensure we only take the p-values for combination
        p_values = group[model].dropna().values
        
        if len(p_values) > 0:
            # Calculate Fisher's combined p-value using scipy's combine_pvalues
            combined_pvalue = combine_pvalues(p_values, method='fisher')[1]
            fisher_pvalues.append({'gene': gene, 'fisher_pvalue': combined_pvalue})

    # Create a DataFrame from the results
    fisher_pvalues_df = pd.DataFrame(fisher_pvalues)

    # subset single_closest_row to only the pvalue and gene columns
    single_closest_row_df = single_closest_row_df[['gene', model]]

    # rename the pvalue column to the pval_col
    single_closest_row_df.rename(columns={model: 'single_closest_pvalue'}, inplace=True)

    # Merge the single_closest_row with the fisher_pvalues_df
    fisher_pvalues_df = pd.merge(single_closest_row_df, fisher_pvalues_df, on='gene')

    return fisher_pvalues_df, sub_df


def prioritize_features(file, gene_sites_file, model, n_cpgs): #pval_col = 'wilcoxon_p_value'):
    # load df
    df = pd.read_csv(file, sep='\t')

    print(df)

    # Group by the gene column and find the index of the row with the smallest p-value
    smallest_pvalue_indices = df.groupby('gene')[model].idxmin()

    print(np.sum(np.isnan(smallest_pvalue_indices)))

    # Create a new DataFrame using these indices
    smallest_pvalue_df = df.loc[smallest_pvalue_indices].reset_index(drop=True)

    print(smallest_pvalue_df)

    # for pval in ['paired_ttest_p_value', 'unpaired_ttest_p_value', 'wilcoxon_p_value', 'u_test_p_value']:
    #     # calculate fisher p-values
    #     fisher_results_df = calculate_fisher_pvalues(df, pval)
    #     print(np.sum(fisher_results_df['fisher_pvalue'] < 0.05))

    # calculate fisher's merged p-value using wulcoxon p-values
    fisher_results_df, sub_df = calculate_fisher_pvalues(df, model)

    # Merge the smallest_pvalue_df with the fisher_results_df
    merged_df = pd.merge(smallest_pvalue_df, fisher_results_df, on='gene')

    # save sub_df
    sub_df.to_csv(gene_sites_file, sep='\t', index=False)

    return merged_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load df
    df = prioritize_features(promoter_methlyation_results_file, gene_sites_file, model, n_cpgs)

    # save to files
    df.to_csv(promoter_prioritized_stats_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # default threads
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["promoter_methlyation_results_file=", "model=", "n_cpgs=", "promoter_prioritized_stats_file=", "gene_sites_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--promoter_methlyation_results_file"):
            promoter_methlyation_results_file = str(arg)

        if opt in ("--model"):
            model = str(arg)

        if opt in ("--n_cpgs"):
            n_cpgs = int(arg)
        
        if opt in ("--promoter_prioritized_stats_file"):
            promoter_prioritized_stats_file = str(arg)
        if opt in ("--gene_sites_file"):
            gene_sites_file = str(arg)

    main()




