# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob, re
import itertools

help_message = '''
Failed
'''


def process_df(input_df, combinations):
    stats_df = []

    # iterate through sample pairs
    for samples in combinations:
        # subset to samples
        df = input_df[input_df['sample'].isin(samples)]

        for gene in df['gene'].unique():
            df = df[df['gene'] == gene]

            # calculate difference, log2 fold change, and p-value for each position
            methylated_df = df[df['methylation_classification'] == 'methylated']
            unmethylated_df = df[df['methylation_classification'] == 'unmethylated']

            # calculate percent methylation difference for each position between methylated and unmethylated
            for index, row in methylated_df.iterrows():
                gene = row['gene']
                start = row['start']
                percent_methylated = row['percent_methylated']

                try:
                    unmethyl_row = unmethylated_df[unmethylated_df['start'] == start].iloc[0].squeeze()
        
                    # get unmethylated percent for each position
                    unmethylated_percent_methylated = unmethyl_row['percent_methylated']
        
                    # calculate difference
                    difference = percent_methylated - unmethylated_percent_methylated

                    # create a unique name of the sample pair
                    sample = "_".join(samples)
        
                    stats_df.append([gene, start, difference, sample])
                
                except:
                    pass

    stats_df = pd.DataFrame(stats_df, columns=['gene', 'start', 'difference', 'sample_comparison'])

    # add log-transformed difference
    stats_df['log2_difference'] = np.log2(stats_df['difference'])

    return stats_df


def add_mgmt_loci(res, gene_bed_file):
    # load gene file and subset to MGMT
    gene_df = pd.read_csv(gene_bed_file, sep='\t', header=None)
    gene_df.columns = ['chr', 'start', 'end', 'gene', 'type', 'info']

    # subset to MGMT
    gene_df = gene_df[gene_df['gene'] == 'MGMT']

    # add methylation classification and sample name
    gene_df['methylation_classification'] = 'gene'

    # subset to columns of interest
    gene_df = gene_df[['start', 'gene', 'methylation_classification']]

    # set end position to be the max end position of res for each gene
    gene_df['end'] = max(res['end'])

    # add to res
    res = pd.concat([res, gene_df])

    return res

def merge_files(files, gene_bed_file, methylated_sampels, unmethylated_samples):
    res = []

    for file in files:
        df = pd.read_csv(file, sep='\t', header=None)

        # add column names
        column_names = ['chr', 'start2', 'end2', 'gene', 'tss', 'chr2', 'start', 'end', 'percent_methylated', 'n_methylated', 'n_bases']
        df.columns = column_names

        # add n_unmethylated
        df['n_unmethylated'] = df['n_bases'] - df['n_methylated']

        # subset to columns of interest
        df = df[['chr', 'start', 'end', 'gene', 'percent_methylated', 'n_methylated', 'n_unmethylated']]

        # add sample name
        sample = file.split('/')[-1].split('_')[0]

        # replace normal with primary and tumor with recurrent
        sample = re.sub(r'normal', 'primary', sample)
        sample = re.sub(r'tumor', 'recurrent', sample)
        df['sample'] = sample

        # classify as methylated or unmethylated
        if sample in methylated_sampels:
            df['methylation_classification'] = 'methylated'
        elif sample in unmethylated_samples:
            df['methylation_classification'] = 'unmethylated'

        # add tumor_type
        df['tumor_type'] = sample.split("-")[1]

        res.append(df)

    # concatenate
    res = pd.concat(res)


    # create combinations
    combinations = list(itertools.product(methylated_sampels, unmethylated_samples))

    # process df
    stats_df = process_df(res, combinations)

    # add mgmt loci
    res = add_mgmt_loci(res, gene_bed_file)

    return res, stats_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # define files
    file_regex = f'{results_dir}/analysis_nanopore/mgmt_promoter_analysis/*_mgmt_methylation.bed'
    files = glob.glob(file_regex)

    # load files
    df, stats_df = merge_files(files, gene_bed_file, methylated_sampels, unmethylated_samples)

    # save to file
    df.to_csv(figure_data_file, sep='\t', index=False)
    stats_df.to_csv(figure_stats_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # define methylated and unmethylated samples
    methylated_sampels = ['RLGS5-primary', 'RLGS5-recurrent', 'RLGS8-primary', 'RLGS11-primary']
    unmethylated_samples = ['RLGS2-primary', 'RLGS7-recurrent', 'RLGS9-primary', 'RLGS12-recurrent']

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "gene_bed_file=", "figure_data_file=", "figure_stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)
        if opt in ("--gene_bed_file"):
            gene_bed_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)

    main()




