# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob, re
from scipy import stats

help_message = '''
Failed
'''


def calculate_stats(df, mgmt_tss, minimum_n_sites = 3):
    # calculate distance to MGMT
    df['distance_to_mgmt'] = abs(df['start'] - mgmt_tss)

    # subset to only loci that exist in at least 3 methylated and 3 unmethylated samples
    df = df.groupby('start').filter(lambda x: len(x) >= minimum_n_sites and len(x[x['methylation_classification'] == 'methylated']) >= minimum_n_sites and len(x[x['methylation_classification'] == 'unmethylated']) >= minimum_n_sites)

    # sort by distance to MGMT
    df = df.sort_values(by='distance_to_mgmt')

    stats_df = []

    # iterate through n sites closest to MGMT for each sample
    for n in range(10, 51):
        # list the sites that are closest to MGMT
        closest_sites = df['distance_to_mgmt'].unique()[:n]

        # add a column to df that indicates if the site is one of the n closest to MGMT
        df[f'{n}'] = df['distance_to_mgmt'].isin(closest_sites)

        # subset to n closest loci to MGMT
        closest_df = df[df['distance_to_mgmt'].isin(closest_sites)]

        # calculate u-test between methylated and unmethylated samples
        methylated_arr = closest_df[closest_df['methylation_classification'] == 'methylated']['percent_methylated']
        unmethylated_arr = closest_df[closest_df['methylation_classification'] == 'unmethylated']['percent_methylated']

        # calculate p-value and means
        p_value = stats.mannwhitneyu(methylated_arr, unmethylated_arr)[1]
        mean_methylated = methylated_arr.mean()
        mean_unmethylated = unmethylated_arr.mean()

        # calculate size of promoter
        promoter_start = closest_df['start'].min() - mgmt_tss
        promoter_end = closest_df['start'].max() - mgmt_tss

        stats_df.append([n, mean_methylated, mean_unmethylated, p_value, np.sign(mean_methylated - mean_unmethylated) * p_value, promoter_start, promoter_end, promoter_end - promoter_start])

    stats_df = pd.DataFrame(stats_df, columns=['n_closest_to_mgmt', 'mean_methylated', 'mean_unmethylated', 'p_value', 'signed_p_value', 'promoter_start', 'promoter_end', 'promoter_size'])

    return df, stats_df


def load_and_concat(files, methylated_samples, unmethylated_samples):
    res = []

    for file in files:
        df = pd.read_csv(file, sep='\t', header=None)

        # add column names
        column_names = ['chr', 'promoter_start', 'promoter_end', 'gene', 'tss', 'chr2', 'start', 'end', 'percent_methylated', 'n_methylated', 'n_bases']
        df.columns = column_names

        # add n_unmethylated
        df['n_unmethylated'] = df['n_bases'] - df['n_methylated']

        # subset to columns of interest
        df = df[['chr', 'start', 'end', 'gene', 'percent_methylated', 'n_methylated', 'n_unmethylated']]

        # add sample name
        sample = file.split('/')[-1].split('_')[0]

        # replace normal with primary and tumor with recurrent
        sample = re.sub(r'normal', 'primary', sample)
        sample = re.sub(r'tumor', 'recurrent', sample)
        df['sample'] = sample

        # classify as methylated or unmethylated
        if sample in methylated_samples:
            df['methylation_classification'] = 'methylated'
        elif sample in unmethylated_samples:
            df['methylation_classification'] = 'unmethylated'

        # add tumor_type
        df['tumor_type'] = sample.split("-")[1]

        res.append(df)

    return pd.concat(res)


def merge_files(files, methylated_samples, unmethylated_samples, mgmt_tss = 129467241):
    # load files and concat
    df = load_and_concat(files, methylated_samples, unmethylated_samples)

    # calculate the mean fc and p-value for difference between methylated and unmethylated samples for n closest loci to MGMT
    df, stats_df = calculate_stats(df, mgmt_tss)

    return df, stats_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # define files
    file_regex = f'{results_dir}/analysis_nanopore/mgmt_promoter_analysis/*_mgmt_methylation.bed'
    files = glob.glob(file_regex)

    # load files
    df, stats_df = merge_files(files, methylated_samples, unmethylated_samples)

    # save to file
    df.to_csv(figure_data_file, sep='\t', index=False)
    stats_df.to_csv(figure_stats_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')



if __name__ == "__main__":
    # define methylated and unmethylated samples
    methylated_samples = ['RLGS5-primary', 'RLGS5-recurrent', 'RLGS8-primary', 'RLGS11-primary']
    unmethylated_samples = ['RLGS2-primary', 'RLGS7-recurrent', 'RLGS9-primary', 'RLGS12-recurrent']

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "figure_data_file=", "figure_stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)

    main()




