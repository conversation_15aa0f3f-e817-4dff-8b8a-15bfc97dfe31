# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def subset_to_mgmt_promoter(protein_coding_genes_gff_file):
    # load gene bed file
    df = pd.read_csv(protein_coding_genes_gff_file, sep='\t')

    # subset to mgmt
    df = df[df['gene'] == 'MGMT']

    # adjust the MGMT promoter to be 2000bp upstream and 500bp downstream of the TSS
    # NOTE MGMT is on the positive strand, this shortcut is OKAY for this analysis
    df['promoter_start'] = df['tss'] - 2000
    df['promoter_end'] = df['tss'] + 500

    # setup as bed file
    df = df[['chrom', 'promoter_start', 'promoter_end', 'gene', 'tss']]

    # promoter start and end should be integers
    df['promoter_start'] = df['promoter_start'].astype(int)
    df['promoter_end'] = df['promoter_end'].astype(int)

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process cnvkit file
    df = subset_to_mgmt_promoter(protein_coding_genes_gff_file)

    # save to files
    df.to_csv(mgmt_protein_bed_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["protein_coding_genes_gff_file=", "mgmt_protein_bed_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--protein_coding_genes_gff_file"):
            protein_coding_genes_gff_file = str(arg)

        if opt in ("--mgmt_protein_bed_file"):
            mgmt_protein_bed_file = str(arg)

    main()


