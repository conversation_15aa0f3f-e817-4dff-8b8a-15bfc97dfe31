# Alec <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




coverage_plot = function(samples, input_df, stats_df, gene) {

# extract the gene loci df
gene_df = input_df[input_df$methylation_classification == 'gene',]
input_df = input_df[input_df$methylation_classification != 'gene',]

# extract the samples of interest
samples_vector = strsplit(samples, '_')[[1]]

# subset dfs
input_df = input_df[input_df$sample %in% samples_vector,]
stats_df = stats_df[stats_df$sample_comparison == samples,]


# plot
p = ggplot(input_df) + plot_theme() +
geom_point(aes(x = start, y = percent_methylated, fill = methylation_classification, ), pch=21, color='black', size=3) +

# add gene loci
geom_segment(data = gene_df, aes(x = start, xend = end, y=0), y=0, linewidth = 1, color = 'purple') +

# define fill colors
scale_fill_manual(values = c('gene' = 'purple', 'methylated' = 'red', 'unmethylated' = 'blue')) +

ylim(0, 100) +  # Set the y-axis limits

ggtitle(paste0(gene, ', ', samples)) +
xlab('Chromosome position') + ylab('Percent methylated')

print(p)



# plot
p = ggplot(stats_df, aes(x = start, y = log2_difference)) + plot_theme() +
geom_point(pch=21, fill='red', size=3) +

# add gene loci
geom_segment(data = gene_df, aes(x = start, xend = end), y=0, linewidth = 1, color = 'purple') +

ggtitle(paste0(gene, ', ', samples)) +
xlab('Chromosome position') + ylab('Fold change (log2)')

print(p)



return()
}



plot_unique_sample = function(sample, df) {


# extract the gene loci df
gene_df = input_df[input_df$methylation_classification == 'gene',]
input_df = input_df[input_df$methylation_classification != 'gene',]

# subset dfs
input_df = input_df[input_df$sample == sample,]


# plot
p = ggplot(input_df) + plot_theme() +
geom_point(aes(x = start, y = percent_methylated, fill = methylation_classification), pch=21, color='black', size=3) +

# add gene loci
geom_segment(data = gene_df, aes(x = start, xend = end, y=0), y=0, linewidth = 1, color = 'purple') +

# define fill colors
scale_fill_manual(values = c('gene' = 'purple', 'methylated' = 'red', 'unmethylated' = 'blue')) +

ylim(0, 100) +  # Set the y-axis limits

ggtitle(paste0(sample)) +
xlab('Chromosome position') + ylab('Percent methylated')

print(p)



}




plot_gene = function(gene, input_df, stats_df) {

# subset dfs
input_df = input_df[input_df$gene == gene,]
stats_df = stats_df[stats_df$gene == gene,]

# plot all tumor pairs
lapply(unique(stats_df$sample_comparison), coverage_plot, input_df, stats_df, gene)

# plot each tumor individually
lapply(unique(input_df$sample), plot_unique_sample, input_df)
    
}


pdf(opt$figure_file, width=15)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')

# plot all unique genes
lapply(unique(input_df$gene), plot_gene, input_df, stats_df)

dev.off()


print(opt$figure_file)





