# <PERSON>cheli

import sys, getopt, time, os, glob, subprocess

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''

def combine_dfs(files):
    # combine all dfs
    combined_df = []

    for file in files:
        # load and subset to TPM
        df = pd.read_csv(file, sep='\t')

        # append
        file_name = "_".join(file.split("/")[-1].split("_")[:4])
        df['sample'] = file_name

        combined_df.append(df)

    # combine and remove "#" from column names
    combined_df = pd.concat(combined_df)
    combined_df.rename(columns=lambda x: x.lstrip('#'), inplace=True)

    return combined_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # combine dfs for all files
    files = glob.glob(os.path.join(aribba_fusions_directory, file_regex))
    combined_df = combine_dfs(files)

    # save to output
    combined_df.to_csv(figure_data_file, sep='\t')

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    # regex for files of interest
    file_regex = "*arriba_fusions.tsv"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["aribba_fusions_directory=", "r_script=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--aribba_fusions_directory"):
            aribba_fusions_directory = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()




