# <PERSON>i

import sys, getopt, time, os, glob

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''

def combine_dfs(files):
    # combine all dfs
    combined_df = []

    for file in files:
        # load and subset to TPM
        df = pd.read_csv(file, sep='\t')
        df.index = df.iloc[:,0].to_numpy()
        df = df.loc[:,['TPM']]

        # append
        file_name = file.split("/")[-1].split(".")[0]
        df.columns = [file_name]
        combined_df.append(df)

    return pd.concat(combined_df, axis=1)


# Define the transcript_to_gene function
def transcript_to_gene(transcript_id):
    # Your logic to map transcript IDs to gene names goes here
    # For example:
    gene_name = transcript_id_to_gene_dict.get(transcript_id, transcript_id)  # Assuming transcript_id_to_gene_dict is a dictionary mapping transcript IDs to gene names
    return gene_name
    
# Function to parse GFF3 file and create a dictionary mapping transcript_id to gene_name
def convert_from_gff3(file_path, df, gene_type='gene_id'):
    transcript_to_gene = {}  # Initialize an empty dictionary to store transcript_id to gene_name mapping

    with open(file_path, 'r') as f:  # Open the GFF3 file for reading
        for line in f:  # Iterate over each line in the file
            if line.startswith('#'):  # Skip comment lines
                continue
            fields = line.strip().split('\t')  # Split the line into fields using tab delimiter

            attributes = fields[8]  # Extract the attributes field
            
            attributes_dict = dict(item.strip().split(' ', 1) for item in attributes.split(';')[:-1]) # Convert attributes field to a dictionary
            
            if gene_type in attributes_dict and 'gene_name' in attributes_dict:  # Check if both transcript_id and gene_name are present
                transcript_id = attributes_dict[gene_type]  # Extract transcript_id

                gene_name = attributes_dict['gene_name'].strip('"')  # Extract gene_name

                transcript_to_gene[transcript_id] = gene_name  # Add transcript_id to gene_name mapping to the dictionary


    # print(df)
    # print(df.index.to_series.map(transcript_to_gene).fillna(df.index))
    # convert index to gene names
    df.index = df.index.to_series().map(transcript_to_gene).fillna(df.index.to_series())

    return df  # Return the transcript_to_gene dictionary



def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # combine dfs for all files
    files = glob.glob(os.path.join(tpm_counts_directory, file_regex))
    combined_df = combine_dfs(files)

    
    # translate from ensembl to gene IDs
    combined_df = convert_from_gff3(gtf_file, combined_df)

    # save to output
    combined_df.to_csv(tpm_outfile, sep='\t')


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # regex for files of interest
    file_regex = "*.out"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["tpm_counts_directory=", "gtf_file=", "gene_bed_file=", "tpm_outfile=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--tpm_counts_directory"):
            tpm_counts_directory = str(arg)
        if opt in ("--gtf_file"):
            gtf_file = str(arg)

        if opt in ("--tpm_outfile"):
            tpm_outfile = str(arg)
            
    main()




