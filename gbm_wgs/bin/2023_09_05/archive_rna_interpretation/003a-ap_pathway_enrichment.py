# Alec Ba<PERSON>cheli

import sys, getopt, time, os, subprocess

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''

def translate_gene_names(df, gene_translation_file):
    # load gene translation
    gene_translation = pd.read_csv(gene_translation_file, sep='\t')

    # create dictionary from gene translation
    gene_translation_dict = dict(zip(gene_translation['Gene stable ID'], gene_translation['Gene name']))

    # translate gene names
    df['gene'] = df['gene_name'].str.split(".").str[0].map(gene_translation_dict)

    # drop rows with no gene name
    df.index = df['gene'].to_numpy()
    df = df.dropna(subset=['gene'])
    df = df.loc[~df.index.duplicated(),:]

    # subset to just pvalues
    df = df[[pval_column]]

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load df
    df = pd.read_csv(dgea_results_file, sep='\t')

    # translate gene names
    df = translate_gene_names(df, gene_translation_file)

    # save pval file
    df.to_csv(pval_file, sep='\t')

    # define command
    cline = [rscript, r_script, '--pval_file', pval_file, '--gmt_file', gmt_infile, '--cytoscape_file_prefix', output_dir, '--output_file', enriched_pathways_file]

    # run active pathways
    print(" ".join(cline))
    subprocess.run(cline)



    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # r environment
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r_dev/bin/Rscript"

    # dgea pvalue column
    pval_column = 'pvalue'

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["dgea_results_file=", "gene_translation_file=", "gmt_infile=", "r_script=", "enriched_pathways_file=", "pval_file=", "output_dir="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--dgea_results_file"):
            dgea_results_file = str(arg)
        if opt in ("--gene_translation_file"):
            gene_translation_file = str(arg)
        if opt in ("--gmt_infile"):
            gmt_infile = str(arg)
            
        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--enriched_pathways_file"):
            enriched_pathways_file = str(arg)
        if opt in ("--pval_file"):
            pval_file = str(arg)
        if opt in ("--output_dir"):
            output_dir = str(arg)
            
    main()





