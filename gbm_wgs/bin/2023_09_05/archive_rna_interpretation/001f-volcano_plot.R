# <PERSON>
library(optparse)
library(ggplot2)
library(ggrepel)
library(ggrastr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}






# plot features
axis_size = 10
title_size = 12

pval_cutoff = 0.05
logfc_cutoff = 1



volcano_plot = function(condition, input_df){

input_df = input_df[input_df$condition == condition,]


# separate into significant and non-significant genes
nonsig_df = input_df[input_df$fdr >= pval_cutoff,]
sig_df = input_df[input_df$fdr < pval_cutoff,]


# separate into genes past log2FC logfc_cutoff and -logfc_cutoff
sigpval_df = sig_df[sig_df$log2FC <= logfc_cutoff & sig_df$log2FC >= -logfc_cutoff,]
sig_df = sig_df[sig_df$log2FC >= logfc_cutoff | sig_df$log2FC <= -logfc_cutoff,]

# separate by up and down
sig_df_up = sig_df[sig_df$log2FC >= logfc_cutoff,]
sig_df_down = sig_df[sig_df$log2FC <= -logfc_cutoff,]


# Calculate the counts
num_red_points = nrow(sig_df_up)
num_blue_points = nrow(sig_df_down)
num_orange_points = nrow(sigpval_df)



# create plot
p = ggplot(input_df, aes(x=log2FC, y=log10_padj)) + theme_bw() +

geom_vline(xintercept = -logfc_cutoff, linetype = "dashed", color = "grey") +
geom_vline(xintercept = logfc_cutoff, linetype = "dashed", color = "grey") +

geom_hline(yintercept = -log10(pval_cutoff), linetype = "dashed", color = "grey") +

geom_point(data = sig_df_up, fill = 'firebrick', size=4, pch=21, alpha=0.8) + 
geom_point(data = sig_df_down, fill = 'dodgerblue', size=4, pch=21, alpha=0.8) + 

# geom_point(data = sig_df, fill = '#f5b75c', size=4, pch=21, alpha=0.8) + 
geom_point(data = sigpval_df, fill = '#f5b75c', size=4, pch=21, alpha=0.8) + 
rasterize(geom_point(data = nonsig_df, size=4, pch=21, alpha=0.2, fill='grey'), dpi=350) + 


ggtitle(paste(condition, " - ", " Red:", num_red_points, "Blue:", num_blue_points, "Orange:", num_orange_points)) +
ylab('Padj (-log10)') + xlab('Fold change (log2)') +

theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), 
      axis.line = element_line(colour = "black"), 
      plot.title = element_text(size=title_size),
      axis.text=element_text(size=axis_size, color= 'black'), 
      axis.text.x = element_text(angle=0),
      axis.title=element_text(size=title_size*0.8),
     legend.position='none')


# genes requiring a label
p = p +
geom_label_repel(data=sig_df, aes(label=gene_name), show.legend=FALSE,
                seed              = 1234,
				size				= 3,
				force             = 0.5,
                max.overlaps      = 10,
				nudge_x           = 0.01,
				hjust             = 0,
				segment.size      = 0.2,
                color = 'black'
) # + geom_point(data = required_label, fill='darkcyan', size=4, pch=21, alpha=0.8)

print(p)

return()

}

pdf(opt$figure_file)

input_df = read.csv(opt$figure_data_file, sep='\t')

print(head(input_df))

# volcano_plot(unique(input_df$condition)[1], input_df)
lapply(unique(input_df$condition), volcano_plot, input_df)

dev.off()






