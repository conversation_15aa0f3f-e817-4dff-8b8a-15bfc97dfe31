# <PERSON>i

import sys, getopt, time, os, subprocess, re

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def combine_dfs(input_data_dir, analysis_types, data_type):
    # read in all dataframes
    dfs = []

    for analysis_type in analysis_types:
        tmp_df = pd.read_csv(f"{input_data_dir}/{data_type}-{analysis_type}.tsv", sep='\t', index_col=0)

        # add condition
        tmp_df['condition'] = analysis_type

        dfs.append(tmp_df)

    # combine all dataframes
    res_df = pd.concat(dfs)

    return res_df



# Function to parse GFF3 file and create a dictionary mapping transcript_id to gene_name
def parse_gff3(file_path, gene_type='ID'):
    transcript_to_gene = {}  # Initialize an empty dictionary to store transcript_id to gene_name mapping

    with open(file_path, 'r') as f:  # Open the GFF3 file for reading
        for line in f:  # Iterate over each line in the file
            if line.startswith('#'):  # Skip comment lines
                continue
            fields = line.strip().split('\t')  # Split the line into fields using tab delimiter

            attributes = fields[8]  # Extract the attributes field
            attributes_dict = dict(pair.split('=') for pair in attributes.split(';')) # Convert attributes field to a dictionary
            
            if gene_type in attributes_dict and 'gene_name' in attributes_dict:  # Check if both transcript_id and gene_name are present
                transcript_id = attributes_dict[gene_type]  # Extract transcript_id

                gene_name = attributes_dict['gene_name']  # Extract gene_name

                transcript_to_gene[transcript_id] = gene_name  # Add transcript_id to gene_name mapping to the dictionary

    return transcript_to_gene  # Return the transcript_to_gene dictionary



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # combine all dataframes
    res_df = combine_dfs(input_data_dir, analysis_types, data_type)
    res_df['gene'] = res_df.index.to_numpy()

    # add a column converting gene_id to gene_name
    gene_name_dict = parse_gff3(gff3_file, gene_type='ID')
    res_df['gene_name'] = res_df['gene'].map(gene_name_dict).fillna(res_df['gene'])

    # save combined dataframe
    res_df.to_csv(figure_data_file, sep='\t', index=False)

    # run R script
    cline = [rscript, r_script, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    analysis_types = ['deseq2_unpaired', 'deseq2_paired', 'edger_unpaired', 'edger_paired']


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["input_data_dir=", "gff3_file=", "data_types_csv=", "r_script=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)

    for opt, arg in opts:
        if opt in ("--input_data_dir"):
            input_data_dir = str(arg)
        if opt in ("--gff3_file"):
            gff3_file = str(arg)
        if opt in ("--data_types_csv"):
            data_type = str(arg)
            
        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()






