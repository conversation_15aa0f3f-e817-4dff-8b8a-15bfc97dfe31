# Alec <PERSON>i

import sys, getopt, time, os, subprocess

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load dfs
    data_df = pd.read_csv(input_data_file, sep="\t", index_col=0)
    stats_df = pd.read_csv(input_stats_file, sep="\t")

    # convert to counts per million
    total_counts_per_sample = data_df.sum()
    data_df = data_df / total_counts_per_sample * 1e6

    print(data_df.shape)
    # subset to significant genes
    sig_genes = stats_df['gene'].to_numpy()[stats_df['fdr'] < min_fdr]
    data_df = data_df.loc[data_df.index.isin(sig_genes),:]

    
    # add gene col
    data_df['gene'] = data_df.index.to_numpy()

    # melt data
    data_df = data_df.melt(id_vars='gene', var_name='sample', value_name='expression')

    # add details
    data_df['patient_id'] = [x.split("-")[0] for x in data_df['sample']]
    data_df['tumor'] = [x.split("-")[1].split("_")[0] for x in data_df['sample']]


    # save to files
    data_df.to_csv(figure_data_file, sep="\t", index=False)
    
    # run R script
    cline = [rscript, r_script, '--figure_stats_file', input_stats_file, '--figure_data_file', figure_data_file, '--figure_file', figure_file]

    print(" ".join(cline))
    subprocess.run(cline)


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    min_fdr = 0.05

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["input_stats_file=", "input_data_file=", "r_script=", "figure_data_file=", "figure_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)

    for opt, arg in opts:
        if opt in ("--input_stats_file"):
            input_stats_file = str(arg)
        if opt in ("--input_data_file"):
            input_data_file = str(arg)

        if opt in ("--r_script"):
            r_script = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        if opt in ("--figure_file"):
            figure_file = str(arg)
            
    main()






