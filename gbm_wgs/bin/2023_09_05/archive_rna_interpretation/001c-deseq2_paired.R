# <PERSON>
library(optparse)
library(DESeq2)


# options list for parser options
option_list <- list(
    make_option(c("-a","--counts_file"), type="character", default=NULL,
            help="",
            dest="counts_file"),
    make_option(c("-b","--dgea_file"), type="character", default=NULL,
            help="",
            dest="dgea_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



# minimum number of counts across all samples per gene
min_gene_count = 1
# set no log2fc cutoff
log2fc_cutoff = 1.5
log2fc_cutoff = log2(log2fc_cutoff)



# load df
rnaseq_counts = read.csv(opt$counts_file, sep='\t', row.names=1)


# remove duplicate rows according to gene name
rnaseq_counts = rnaseq_counts[!duplicated(rownames(rnaseq_counts)),]

# remove RLGS2 and RLGS7
rnaseq_counts = rnaseq_counts[,!grepl("RLGS2|RLGS7", colnames(rnaseq_counts))]


# create sample conditions df
colData <- data.frame(
  condition = factor(ifelse(grepl("primary", colnames(rnaseq_counts)), "Primary", "Recurrent")),
  pair = factor(rep(paste0('pair', 1:10), each = 2)),
  row.names = colnames(rnaseq_counts)
)


# filter by minimum count
rowMeansCounts <- rowMeans(rnaseq_counts)
rnaseq_counts = rnaseq_counts[rowMeansCounts > min_gene_count,]


# create DESeq object
dds <- DESeqDataSetFromMatrix(countData = rnaseq_counts, 
                              colData = colData, 
                              design = ~ pair + condition)


# run DESeq2
dds <- DESeq(dds)



# get results
resultsNames(dds)  # to view all the levels and choose the correct one
res <- results(dds)  # Adjust the name based on your levels


# rename columns
res$fdr = res$padj
res$log2FC = res$log2FoldChange
res$log10_padj = -log10(res$padj)
res$gene_name = rownames(res)


# save to file
write.table(as.data.frame(res), opt$dgea_file, sep='\t')










