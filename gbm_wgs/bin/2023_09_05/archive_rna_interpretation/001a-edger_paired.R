# <PERSON>
library(optparse)
library(edgeR)


# options list for parser options
option_list <- list(
    make_option(c("-a","--counts_file"), type="character", default=NULL,
            help="",
            dest="counts_file"),
    make_option(c("-b","--dgea_file"), type="character", default=NULL,
            help="",
            dest="dgea_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}



# minimum number of counts across all samples per gene
min_gene_count = 1
# set no log2fc cutoff
log2fc_cutoff = 1.5
log2fc_cutoff = log2(log2fc_cutoff)



# load df
rnaseq_counts = read.csv(opt$counts_file, sep='\t', row.names=1)

# remove duplicate rows according to gene name
rnaseq_counts = rnaseq_counts[!duplicated(rownames(rnaseq_counts)),]

# remove RLGS2 and RLGS7
rnaseq_counts = rnaseq_counts[,!grepl("RLGS2|RLGS7", colnames(rnaseq_counts))]


# group based on whether the sample has "primary" or not in the name
group = factor(ifelse(grepl("primary", colnames(rnaseq_counts)), "Primary", "Recurrent"))

# sample pairs repeat "pair1", "pair1", "pair2", "pair2", etc.
pairs = factor(rep(paste0('pair', 1:10), each = 2))


# filter by minimum count
rowMeansCounts <- rowMeans(rnaseq_counts)
rnaseq_counts = rnaseq_counts[rowMeansCounts > min_gene_count,]


# generate data object
y = DGEList(counts = rnaseq_counts, group = group, pairs = pairs)


# # filter for a minimum counts across all samples (6 reads minimum)
# keep = filterByExpr(y, min.total.count = min_gene_count * ncol(rnaseq_counts))
# y = y[keep,,keep.lib.sizes=FALSE]


# normalize, setup model
y = calcNormFactors(y)
design <- model.matrix(~pairs + group)

y = estimateDisp(y,design)


# fit quasi-likelihood model
fit = glmQLFit(y, design)
lrt = glmTreat(fit, coef=2, lfc = log2fc_cutoff)

# results table of DGEA observations
res = lrt$table


# fdr correction
res$fdr = p.adjust(res$PValue, method = "BH")
res$log10_padj = -log10(res$fdr)

res$log2FC = res$logFC
res$gene_name = rownames(res)

# save to file
write.table(as.data.frame(res), opt$dgea_file, sep='\t')








