# <PERSON>
library(optparse)
library(ggplot2)
library(dplyr)

library(optparse)
library(ActivePathways)

# options list for parser options
option_list <- list(
    make_option(c("-a","--pval_file"), type="character", default=NULL,
            help="",
            dest="pval_file"),
    make_option(c("-b","--gmt_file"), type="character", default=NULL,
            help="",
            dest="gmt_file"),
    make_option(c("-c","--cytoscape_file_prefix"), type="character", default=NULL,
            help="",
            dest="cytoscape_file_prefix"),
    make_option(c("-d","--output_file"), type="character", default=NULL,
            help="",
            dest="output_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)



# gmt file limitations
max_genes = 750
min_genes = 50



# load p-value df
scores = read.csv(opt$pval_file, sep='\t', row.names=1)
scores = as.matrix(scores)

# set NAs to 1
scores[is.na(scores)] = 1

# run ActivePathways
pos_enriched_pathways = ActivePathways(scores, opt$gmt_file, cytoscape_file_tag=opt$cytoscape_file_prefix, geneset_filter=c(min_genes, max_genes))


# export enriched pathways
export_as_CSV(pos_enriched_pathways, opt$output_file)

print("ActivePathways Complete")



