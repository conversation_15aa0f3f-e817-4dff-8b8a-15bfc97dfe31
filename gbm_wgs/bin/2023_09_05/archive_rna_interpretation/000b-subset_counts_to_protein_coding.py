# <PERSON>i

import sys, getopt, time, os, subprocess

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def load_counts_dfs(counts_dir, samples_csv, condition = 'counts'):
    # for each sample, load counts
    res_df = []

    for sample in samples_csv.split(","):
        # file path
        file = os.path.join(counts_dir, f"{sample}_{condition}.txt")

        # load df
        df = pd.read_csv(file, header=None, sep='\t')

        # assign columns
        df.columns = ["gene_id", sample]

        # set index
        df.index = df["gene_id"].to_numpy()
        df.drop("gene_id", axis=1, inplace=True)

        # remove last 5 rows which contains alignment stats
        df = df.iloc[:-5]

        res_df.append(df)
    
    return pd.concat(res_df, axis=1)


def subset_pc_genes(counts_df, hgnc_file, exon=False):
    # load hgnc file
    hgnc_df = pd.read_csv(hgnc_file, sep="\t")

    # subset to protein-coding genes
    pc_genes = hgnc_df.loc[hgnc_df["locus_type"].str.contains("protein"), 'ensembl_gene_id'].to_numpy()

    if exon:
        mask = np.isin(counts_df.index.str.split(".").str[0], pc_genes)
    else:
        mask = np.isin(counts_df.index.str.split(".").str[0], pc_genes)

    return counts_df[mask]


def main():
    print('XXX-XXX.py')
    t1 = time.time()


    # load dfs
    counts_df = load_counts_dfs(counts_dir, samples_csv)

    # subset to protein-coding genes only
    pc_counts_df = subset_pc_genes(counts_df, hgnc_file)



    # repeats for exons
    exons_df = load_counts_dfs(counts_dir, samples_csv, condition = 'exon_counts')


    # repeat for transcript
    transcripts_df = load_counts_dfs(counts_dir, samples_csv, condition = 'transcript_counts')
    

    # save
    counts_df.to_csv(all_counts_file, sep="\t")
    pc_counts_df.to_csv(protein_coding_counts_file, sep="\t")

    exons_df.to_csv(all_exons_file, sep="\t")
    transcripts_df.to_csv(all_transcript_file, sep="\t")


    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r_env/bin/Rscript"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["counts_dir=", "samples_csv=", "hgnc_file=", "all_counts_file=", "protein_coding_counts_file=", "all_exons_file=", "all_transcript_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)

    for opt, arg in opts:
        if opt in ("--counts_dir"):
            counts_dir = str(arg)
        if opt in ("--samples_csv"):
            samples_csv = str(arg)
        if opt in ("--hgnc_file"):
            hgnc_file = str(arg)

        if opt in ("--all_counts_file"):
            all_counts_file = str(arg)
        if opt in ("--protein_coding_counts_file"):
            protein_coding_counts_file = str(arg)
        if opt in ("--all_exons_file"):
            all_exons_file = str(arg)
        if opt in ("--all_transcript_file"):
            all_transcript_file = str(arg)

    main()






