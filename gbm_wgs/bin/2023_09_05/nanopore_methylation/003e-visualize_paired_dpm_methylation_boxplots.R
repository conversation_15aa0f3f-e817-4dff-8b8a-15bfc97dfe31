# <PERSON>
library(optparse)
library(forcats)
library(ggplot2)
library(ggrepel)
library(dplyr)
library(tidyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-b","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




create_boxplot = function(gene, input_df, stats_df){

# subset to gene
input_df = input_df[input_df$gene == gene,]
stats_df = stats_df[stats_df$gene == gene,]
    

# recurrent df
recurrent_df = input_df[input_df$tumor_type == 'recurrent',]


p = ggplot(input_df, aes(x = tumor_type, y = percent_methylated)) + plot_theme() +
geom_boxplot(outlier.shape = NA) +
geom_point(size = 3, alpha = 0.6) +
geom_line(aes(group = sample_code, color = methylation_difference), alpha = 0.25) +

scale_color_manual(values = c("Increase" = "blue", "Decrease" = "red")) +

# ggtitle(paste(gene, "p=", signif(stats_df$single_closest_pvalue, 3), "median difference=", signif(stats_df$median_difference, 3))) +
ggtitle(paste(gene, "p=", signif(stats_df$dpm_pval, 3))) +
xlab("") + ylab('Mean methylation (%)') +
ylim(0, 100) +

theme(plot.title = element_text(size = 16)) +

geom_label_repel(data=recurrent_df, aes(label=sample_code), show.legend=FALSE,
                seed              = 1234,
				size				= 2,
				force             = 1,
                max.overlaps      = 100,
				nudge_x           = 0.01,
				hjust             = 0,
				segment.size      = 0.2,
                color = 'black'
)


print(p)
return()
}





pdf(opt$figure_file, width=9)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')

# sort by p-value
stats_df$gene = fct_reorder(factor(stats_df$gene), stats_df$dpm_pval, .desc = FALSE)

# subset to significant genes
stats_df = stats_df[stats_df$dpm_pval < 0.05,]

# create boxplots for each sample
lapply(levels(stats_df$gene), create_boxplot, input_df, stats_df)


dev.off()


print(opt$figure_file)






