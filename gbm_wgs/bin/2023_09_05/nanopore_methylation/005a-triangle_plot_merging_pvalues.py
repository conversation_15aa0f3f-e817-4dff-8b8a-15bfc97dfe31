# Alec Bahcheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''


def load_and_combine_dfs(non_directional_merged_pval_file, directional_merged_pval_file, dgea_file, dgea_method='gene_counts-deseq2_paired', sig_cutoff = 0.05):
    # load each df, subset to p-value, and merge on gene
    non_directional_merged_pval_df = pd.read_csv(non_directional_merged_pval_file, sep='\t')
    non_directional_merged_pval_df = non_directional_merged_pval_df[['gene', 'brown_pval']]
    non_directional_merged_pval_df.columns = ['gene', 'non_directional_pval']

    directional_merged_pval_df = pd.read_csv(directional_merged_pval_file, sep='\t')
    directional_merged_pval_df = directional_merged_pval_df[['gene', 'dpm_pval']]
    directional_merged_pval_df.columns = ['gene', 'directional_pval']

    dgea_df = pd.read_csv(dgea_file, sep='\t')
    # subset to dgea method
    dgea_df = dgea_df[dgea_df['data_type_dgea'] == dgea_method]
    dgea_df = dgea_df[['gene', 'p_value']]
    dgea_df.columns = ['gene', 'dgea_pval']

    # merge all dfs
    df = non_directional_merged_pval_df.merge(directional_merged_pval_df, on='gene', how='outer')
    df = df.merge(dgea_df, on='gene', how='outer')

    # Create a single column based on how many p-values are significant for each gene
    pval_columns = ['non_directional_pval', 'directional_pval', 'dgea_pval']
    df['significant_pvals'] = (df[pval_columns] < sig_cutoff).sum(axis=1)

    # -log10 the p-values
    for col in pval_columns:
        df[col] = -np.log10(df[col])

    # create new columns that are the relative composition of log10 pvalues 
    for col in pval_columns:
        df[col + '_rel'] = df[col] / df[pval_columns].sum(axis=1)

    # convert from number to string based on dict
    sig_dict = {0: 'none', 1: 'one', 2: 'two', 3: 'three'}
    df['significant_pvals'] = df['significant_pvals'].map(sig_dict)
        
    
    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # process and combine all dfs
    df = load_and_combine_dfs(non_directional_merged_pval_file, directional_merged_pval_file, dgea_file)

    # save to files
    df.to_csv(figure_data_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["non_directional_merged_pval_file=", "directional_merged_pval_file=", "dgea_file=", "figure_data_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--non_directional_merged_pval_file"):
            non_directional_merged_pval_file = str(arg)
        if opt in ("--directional_merged_pval_file"):
            directional_merged_pval_file = str(arg)
        if opt in ("--dgea_file"):
            dgea_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        

    main()




