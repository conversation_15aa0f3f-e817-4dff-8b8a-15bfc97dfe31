# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)
library(grid)
library(png)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




qq_ggplot = function(raw_df, description) {

stats_sig = signif(sum(raw_df$pval < 0.05, na.rm = TRUE) / sum(!is.na(raw_df$pval)), 3) * 100

# # qqplot
# qqnorm(-log10(raw_df$pval), pch = 1, frame = FALSE)
# qqline(-log10(raw_df$pval), col = "steelblue", lwd = 2)


# Rasterize the QQ plot
png("/.mounts/labs/reimandlab/private/users/abahcheli/tmp/qq_plot.png", width = 800, height = 600)  # Open a graphics device for the QQ plot
qqnorm(-log10(raw_df$pval), pch = 1, frame = FALSE)
qqline(-log10(raw_df$pval), col = "steelblue", lwd = 2)
dev.off()  # Close the device

# Load the rasterized image
img <- readPNG("/.mounts/labs/reimandlab/private/users/abahcheli/tmp/qq_plot.png")

# Create a new page for the rasterized QQ plot
grid.newpage()
grid.raster(img)
    
# plot histogram
p = ggplot(raw_df, aes(x = pval)) + plot_theme() +
geom_histogram(color = "black") +

ggtitle(paste0(description, ' percent sig: ', stats_sig, '%')) +
xlab('P-value (raw)') + ylab('Counts')

print(p)


# plot histogram
p = ggplot(raw_df, aes(x = -log10(pval))) + plot_theme() +
geom_histogram(color = "black") +

ggtitle(paste0(description, ' percent sig: ', stats_sig, '%')) +
xlab('P-value (-log10)') + ylab('Counts')

print(p)


return()
}




pdf(opt$figure_file)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')

input_df$pval = input_df$paired_ttest_p_value
stats_df$pval = stats_df$paired_ttest_p_value

# set minimum value to 1e-300
input_df$pval[input_df$pval < 1e-300] = 1e-300
stats_df$pval[stats_df$pval < 1e-300] = 1e-300


# generate qqplots
qq_ggplot(input_df, 'All sites')
qq_ggplot(stats_df, 'Prioritized sites DPM')


stats_df$pval = stats_df$fisher_pvalue

# generate qqplots
qq_ggplot(stats_df, 'Prioritized sites Fisher')

dev.off()


print(opt$figure_file)





