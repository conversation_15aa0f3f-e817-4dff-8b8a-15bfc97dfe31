# Alec <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import glob

help_message = '''
Failed
'''


def load_and_process_dfs(file_list):
    # load each df and add to res_df
    res_df = []

    for file in file_list:
        df = pd.read_csv(file, sep='\t')

        # add data source
        data_source = file.split('/')[-1].split('-')[-1].split(".")[0]

        # add data source to df
        df['data_source'] = data_source

        res_df.append(df)

    # combine dfs
    res_df = pd.concat(res_df)

    # rename columns
    res_df = res_df.rename(columns={'dpm_pval': 'pvalue'})
    
    # add -log10(pvalue)
    res_df['log10_pvalue'] = -np.log10(res_df['pvalue'])

    return res_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # list of files
    regex = f'{results_dir}/analysis_nanopore/methylation/dpm_merged_pvalues-*tsv'
    files_list = glob.glob(regex)

    # process and combine all dfs
    df = load_and_process_dfs(files_list)

    # save to files
    df.to_csv(figure_data_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["results_dir=", "figure_data_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--results_dir"):
            results_dir = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)
        

    main()




