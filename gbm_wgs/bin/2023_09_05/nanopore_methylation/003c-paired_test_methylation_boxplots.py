# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from scipy import stats
from concurrent.futures import ProcessPoolExecutor


help_message = '''
Failed
'''



def mean_methylation(args):
    df, sites_df = args

    mean_methylation_per_gene_position = []

    # iterate through each gene position
    for gene in sites_df['gene'].unique():
        positions = sites_df[sites_df['gene'] == gene]['position'].unique()
        
        # Find matching rows in other_df
        matching_rows = df[(df['gene'] == gene) & (df['start'].isin(positions))]
        
        if not matching_rows.empty:
            # Calculate mean percent methylation per sample
            mean_methylation = matching_rows.groupby('sample')['percent_methylated'].mean().reset_index()
            mean_methylation['gene'] = gene
            mean_methylation['sample_code'] = mean_methylation['sample'].str.split("-").str[0]

            # add meathylation_difference
            mean_methylation['methylation_difference'] = 'Decrease'

            for sample_code in mean_methylation['sample_code'].unique():
                # mask is the sample code
                mask = mean_methylation['sample_code'] == sample_code

                # mask1 is the primary tumor
                mask1 = (mean_methylation['sample_code'] == sample_code) & (mean_methylation['sample'].str.contains('primary'))
                # mask2 is the recurrent tumor
                mask2 = (mean_methylation['sample_code'] == sample_code) & (mean_methylation['sample'].str.contains('recurrent'))

                # if primary is less than recurrent
                if mean_methylation[mask1]['percent_methylated'].mean() < mean_methylation[mask2]['percent_methylated'].mean():
                    mean_methylation.loc[mask, 'methylation_difference'] = 'Increase'
            
            mean_methylation_per_gene_position.append(mean_methylation)

    # Concatenate the results
    mean_methylation_per_gene_position = pd.concat(mean_methylation_per_gene_position, ignore_index=True)

    return mean_methylation_per_gene_position


def extract_mean_methylation(promoter_methylation_combined_file, gene_sites_file, threads):
    # load dfs
    input_df = pd.read_csv(promoter_methylation_combined_file, sep='\t')
    sites_df = pd.read_csv(gene_sites_file, sep='\t')

    # separate gene lists based on number of threads
    gene_lists = np.array_split(sites_df['gene'].unique(), threads)

    # process each gene list
    args = [(input_df[input_df['gene'].isin(gene_list)], sites_df[sites_df['gene'].isin(gene_list)]) for gene_list in gene_lists]

    # calculate extract mean between sites
    with ProcessPoolExecutor(max_workers=threads) as executor:
        df = list(executor.map(mean_methylation, args))

    # concatenate
    df = pd.concat(df, ignore_index=True)

    # add primary and recurrent columns
    df['tumor_type'] = df['sample'].str.split('-').str[1]
    df['sample_code'] = df['sample'].str.split('-').str[0]

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # process file for differential methylation
    df = extract_mean_methylation(promoter_methylation_combined_file, gene_sites_file, threads)

    # save to files
    df.to_csv(figure_data_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # default threads
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["promoter_methylation_combined_file=", "gene_sites_file=", "figure_data_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--promoter_methylation_combined_file"):
            promoter_methylation_combined_file = str(arg)
        if opt in ("--gene_sites_file"):
            gene_sites_file = str(arg)

        if opt in ("--figure_data_file"):
            figure_data_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()




