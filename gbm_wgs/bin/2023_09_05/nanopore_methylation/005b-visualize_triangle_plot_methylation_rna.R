# <PERSON>
library(optparse)
library(ggplot2)
library(dplyr)
library(ggtern)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




dotplot = function(input_df) {

fill_cols = c("firebrick", "darkorange", "dodgerblue", "grey")
names(fill_cols) = c("three", "two", "one", "none")

# Triangular scatter plot
p = ggtern(input_df, aes(x = non_directional_pval_rel, y = directional_pval_rel, z = dgea_pval_rel, fill=significant_pvals)) + plot_theme() +

geom_point(size = 1, pch = 21) +

# color by significant p-values
scale_fill_manual(values = fill_cols) +

labs(title = "Triangular Scatter Plot: RNA vs Methylation vs Overlap",
      x = "Non-Directional methylation",
      y = "Directional methylation",
      z = "RNA")

  # # Create the boxplot
  # p = ggplot(input_df, aes(x = data_source, y = log10_pvalue)) + plot_theme() +
  #   geom_boxplot() +
  #   ggtitle(dynamic_title) +
  #   xlab("") + ylab('Pvalue (-log10)') + 

  #   # reduce title size
  #   theme(plot.title = element_text(size = 8))

  print(p)

  return()
}



pdf(opt$figure_file, width = 10)

# load df
input_df = read.csv(opt$figure_data_file, sep='\t')

# repeat for only significant p-values
input_df = input_df[input_df$significant_pvals != 'none',]

dotplot(input_df)

dev.off()


print(opt$figure_file)





