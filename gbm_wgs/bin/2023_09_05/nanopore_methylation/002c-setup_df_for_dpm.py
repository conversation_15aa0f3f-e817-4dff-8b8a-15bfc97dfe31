# <PERSON>

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def prioritize_features(file, model, n_cpgs = 10):
    # load df
    df = pd.read_csv(file, sep='\t')

    # Calculate the absolute distance from position to tss
    df['distance_to_tss'] = (df['position'] - df['tss']).abs()
    
    # Sort the DataFrame by gene and distance
    df_sorted = df.sort_values(by=['gene', 'distance_to_tss'])

    # Select up to n rows with the smallest distance for each gene
    df = df_sorted.groupby('gene').apply(lambda x: x.head(min(len(x), n_cpgs))).reset_index(drop=True)

    # Add a new "sample" column that counts the occurrence of each row per gene
    df['gene_number'] = df.groupby('gene').cumcount() + 1

    # Pivot the data to get each gene as a row, with positions as columns
    wilcoxon_df = df.pivot(index='gene', columns='gene_number', values=model)
    log2_fc_df = df.pivot(index='gene', columns='gene_number', values='log2_fc')

    # replace nan with mean of row
    wilcoxon_df = wilcoxon_df.apply(lambda row: row.fillna(row.mean()), axis=1)
    log2_fc_df = log2_fc_df.apply(lambda row: row.fillna(row.mean()), axis=1)

    return wilcoxon_df, log2_fc_df



def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # load df
    pval_df, fc_df = prioritize_features(promoter_methlyation_results_file, model)

    # save to files
    pval_df.to_csv(pval_file, sep='\t')
    fc_df.to_csv(fc_file, sep='\t')
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # default threads
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["promoter_methlyation_results_file=", "model=", "pval_file=", "fc_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--promoter_methlyation_results_file"):
            promoter_methlyation_results_file = str(arg)

        if opt in ("--model"):
            model = str(arg)
        
        if opt in ("--pval_file"):
            pval_file = str(arg)
        if opt in ("--fc_file"):
            fc_file = str(arg)

    main()



