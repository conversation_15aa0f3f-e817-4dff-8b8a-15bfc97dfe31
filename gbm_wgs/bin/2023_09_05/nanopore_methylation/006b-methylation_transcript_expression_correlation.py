# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import scipy.stats as stats

help_message = '''
Failed
'''


def correlate_expression_with_methylation(mean_methylation_per_gene_sample, gene_expression_file):
    # load dfs
    expression_df = pd.read_csv(gene_expression_file, sep='\t')
    methylation_df = pd.read_csv(mean_methylation_per_gene_sample, sep='\t')

    # set indices
    expression_df.index = expression_df['gene_id']
    methylation_df.index = methylation_df['gene']

    # drop gene_name column from expression_df and log-transform
    expression_df = expression_df.drop(columns=['gene_name', 'gene_id'])
    expression_df = np.log10(expression_df + 1)

    # reshape methylation df
    methylation_df = methylation_df.pivot(index="gene", columns="sample", values="percent_methylated")

    # subset to common genes (row names)
    common_genes = np.intersect1d(expression_df.index.astype("<U64"), methylation_df.index.astype("<U64"))
    expression_df = expression_df.loc[common_genes,:]
    methylation_df = methylation_df.loc[common_genes,:]

    # rename the columns of rna to match methylation
    expression_df.columns = expression_df.columns.str.replace("_", "-")


    # create empty df to store results
    stats_df = []

    # get common samples
    samples = np.intersect1d(methylation_df.columns, expression_df.columns)

    # calculate correlation by column
    for col in samples:
        # remove missing values at gene level
        gene_expression_values = expression_df[col]
        methylation_values = methylation_df[col]

        # the samples need to match
        mask = np.logical_and(~np.isnan(gene_expression_values), ~np.isnan(methylation_values))
        gene_expression_values = gene_expression_values[mask]
        methylation_values = methylation_values[mask]

        
        # calculate spearman correlation
        r, p = stats.spearmanr(gene_expression_values, methylation_values)

        # add to df
        stats_df.append([col, r, p])

    # create df
    stats_df = pd.DataFrame(stats_df, columns=['sample', 'correlation', 'p_value'])

    # add a column of sample-correlation
    stats_df['sample_correlation'] = stats_df['sample'] + ": " + stats_df['correlation'].round(3).astype(str)

    # melt rna and methylation dfs and merge
    expression_df['gene'] = expression_df.index
    expression_df = expression_df.melt(value_name='expression', var_name='sample', id_vars='gene')

    methylation_df['gene'] = methylation_df.index
    methylation_df = methylation_df.melt(value_name='methylation', var_name='sample', id_vars='gene')

    # merge
    res_df = pd.merge(expression_df, methylation_df, on=['gene', 'sample'])

    # add sample_correlation according to the sample
    res_df = pd.merge(res_df, stats_df[['sample', 'sample_correlation']], on='sample')

    return res_df, stats_df



def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # load methylation by gene file and find correlation
    res_df, stats_df = correlate_expression_with_methylation(mean_methylation_per_gene_sample, gene_expression_file)

    # save to files
    res_df.to_csv(correlation_data_file, sep='\t', index=False)
    stats_df.to_csv(correlation_stats_file, sep='\t', index=False)
    
    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # rscript path
    rscript = "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript"

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["gene_expression_file=", "mean_methylation_per_gene_sample=", "correlation_data_file=", "correlation_stats_file="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--gene_expression_file"):
            gene_expression_file = str(arg)
        if opt in ("--mean_methylation_per_gene_sample"):
            mean_methylation_per_gene_sample = str(arg)

        if opt in ("--correlation_data_file"):
            correlation_data_file = str(arg)
        if opt in ("--correlation_stats_file"):
            correlation_stats_file = str(arg)

    main()


