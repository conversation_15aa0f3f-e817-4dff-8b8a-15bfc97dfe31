# Alec <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)

# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




dotplot = function(input_df){

# set max value for expression
max_expression = 4
input_df[input_df$expression > max_expression, 'expression'] = max_expression
    
# density polygons
p = ggplot(input_df, aes(x = methylation, y = expression)) + plot_theme() +
geom_point(pch=1, size=0.005, alpha = 0.25) + 
geom_smooth(method = "lm", color = "blue", se = FALSE, linetype = "dashed", linewidth = 0.75) +  # Add line of best fit
  
facet_wrap(~ sample_correlation, nrow = 4) +  # Facet by patient into 4 rows, 5-6 columns

ggtitle('') +
xlab('Promoter methylation (% methylated, mean)') + ylab('Gene expression (log10 TPM)') +
theme(axis.text.x = element_text(angle = 0))

print(p)


return()

}




pdf(opt$figure_file, width=10, height=10)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')


# sort patients by correlation coefficient
stats_df$sample_correlation = fct_reorder(stats_df$sample_correlation, stats_df$correlation, .desc = TRUE)
stats_df$sample = fct_reorder(stats_df$sample, stats_df$correlation, .desc = TRUE)

input_df$sample_correlation = factor(input_df$sample_correlation, levels = levels(stats_df$sample_correlation))


# plot
dotplot(input_df)


dev.off()


print(opt$figure_file)






