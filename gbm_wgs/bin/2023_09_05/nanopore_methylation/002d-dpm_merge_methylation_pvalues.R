# <PERSON>
library(optparse)
library(dplyr)
library(ActivePathways)


# options list for parser options
option_list <- list(
    make_option(c("-a","--pval_file"), type="character", default=NULL,
            help="",
            dest="pval_file"),
    make_option(c("-b","--fc_file"), type="character", default=NULL,
            help="",
            dest="fc_file"),
    make_option(c("-c","--merged_pval_file"), type="character", default=NULL,
            help="",
            dest="merged_pval_file")            
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}






# load df
pval_df = read.csv(opt$pval_file, sep='\t', row.names=1)
fc_df = read.csv(opt$fc_file, sep='\t', row.names=1)


# convert to matrices
pval_mat = as.matrix(pval_df)
fc_mat = as.matrix(fc_df)

# CV should be in agreement
constraints_vector = rep(1, 10)

# perform DPM on the gene p-values using the wilcoxon_pvalue and the mean_difference columns
res_df = merge_p_values(pval_mat, scores_direction = fc_mat, constraints_vector = constraints_vector, method = "DPM")

# create df
res_df = data.frame(pval = res_df)

# add the rownames back to the dataframe
colnames(res_df) = 'dpm_pval'
res_df$gene = rownames(res_df)

# add the fc values to the dataframe by averaging the fc values for each row (gene)
res_df$fc = rowMeans(fc_mat)

# write out the merged p-values
write.table(res_df, opt$merged_pval_file, sep='\t', row.names=FALSE, quote=FALSE)







