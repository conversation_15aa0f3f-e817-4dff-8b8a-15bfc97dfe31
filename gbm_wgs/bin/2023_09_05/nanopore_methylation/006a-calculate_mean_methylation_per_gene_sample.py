# Alec Ba<PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np


help_message = '''
Failed
'''


def subset_methylation_file(methylation_by_site_file, gene_sites_file):
    # load dfs
    methylation_df = pd.read_csv(methylation_by_site_file, sep='\t')
    gene_sites_df = pd.read_csv(gene_sites_file, sep='\t')

    # create id columns of gene and position
    methylation_df['id'] = methylation_df['gene'] + ':' + methylation_df['start'].astype(str)
    gene_sites_df['id'] = gene_sites_df['gene'] + ':' + gene_sites_df['position'].astype(str)

    # subset to common ids
    common_ids = np.intersect1d(methylation_df['id'], gene_sites_df['id'])
    methylation_df = methylation_df[methylation_df['id'].isin(common_ids)]

    # calculate the mean percent_methylated per sample and gene
    methylation_df = methylation_df.groupby(['gene', 'sample']).agg({'percent_methylated': 'mean'}).reset_index()

    print(methylation_df)

    return methylation_df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # subset methylation file to loci of interest
    df = subset_methylation_file(methylation_by_site_file, gene_sites_file)

    # write to file
    df.to_csv(mean_methylation_per_gene_sample, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["methylation_by_site_file=", "gene_sites_file=", "mean_methylation_per_gene_sample="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--methylation_by_site_file"):
            methylation_by_site_file = str(arg)
        if opt in ("--gene_sites_file"):
            gene_sites_file = str(arg)

        if opt in ("--mean_methylation_per_gene_sample"):
            mean_methylation_per_gene_sample = str(arg)

    main()



