{"cells": [{"cell_type": "code", "execution_count": 1, "id": "50ef7f26-53d2-49ec-89b6-55582702eb13", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "Attaching package: 'gplots'\n", "\n", "\n", "The following object is masked from 'package:stats':\n", "\n", "    lowess\n", "\n", "\n", "\n", "Attaching package: 'dplyr'\n", "\n", "\n", "The following objects are masked from 'package:stats':\n", "\n", "    filter, lag\n", "\n", "\n", "The following objects are masked from 'package:base':\n", "\n", "    intersect, setdiff, setequal, union\n", "\n", "\n", "\n", "Attaching package: 'data.table'\n", "\n", "\n", "The following objects are masked from 'package:dplyr':\n", "\n", "    between, first, last\n", "\n", "\n", "\n", "Attaching package: 'gridExtra'\n", "\n", "\n", "The following object is masked from 'package:dplyr':\n", "\n", "    combine\n", "\n", "\n", "Loading required package: ggpubr\n", "\n", "\n", "Attaching package: 'survminer'\n", "\n", "\n", "The following object is masked from 'package:survival':\n", "\n", "    myeloma\n", "\n", "\n"]}], "source": ["library(ggplot2)\n", "library(ggrepel)\n", "library(ggrastr)\n", "library(gplots)\n", "library(ggupset)\n", "\n", "\n", "library(dplyr)\n", "library(forcats)\n", "library(data.table)\n", "\n", "library(tidytext)\n", "\n", "library(grid)\n", "library(gridExtra)\n", "library(patchwork)\n", "\n", "\n", "library(survival)\n", "library(survminer)\n", "\n", "\n", "plot_theme = function(...) {\n", "     \n", "    theme_bw() +\n", "     \n", "    theme(    \n", "     \n", "        plot.title = element_text(size = 22),\n", "         \n", "        plot.caption = element_text(size = 12),\n", "         \n", "        plot.subtitle = element_text(size = 16),\n", "         \n", "        axis.title = element_text(size = 18),\n", "         \n", "        axis.text.x = element_text(size = 12,\n", "                angle = 90, hjust = 1, vjust=0.5, color = \"black\"),\n", "        axis.text.y = element_text(size = 12, color = \"black\"),\n", "        legend.title = element_text(size = 16),\n", "        legend.text = element_text(size = 14),\n", "        ...\n", "    )\n", "}\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "f2bfa72e-4d7f-46da-95a3-0f1372ee3633", "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A data.frame: 28 × 5</caption>\n", "<thead>\n", "\t<tr><th scope=col>Region</th><th scope=col>Flye_Contigs</th><th scope=col>Flye_Total_Length</th><th scope=col>Miniasm_Contigs</th><th scope=col>Miniasm_Total_Length</th></tr>\n", "\t<tr><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>3_125634993_125647896</td><td>1</td><td>36686</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>7_101192157_101203542</td><td>1</td><td>30978</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>7_124224771_124227807</td><td>1</td><td> 7772</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>7_129420885_129431511</td><td>1</td><td>27462</td><td>1</td><td>16501</td></tr>\n", "\t<tr><td>7_152407959_152417067</td><td>1</td><td>28189</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>7_74529246_74535318  </td><td>1</td><td>12600</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>7_75723912_75741369  </td><td>2</td><td>34933</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>7_85930185_85946883  </td><td>1</td><td>45537</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>7_118830558_118839666</td><td>1</td><td>18772</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>7_152401887_152407959</td><td>1</td><td>16954</td><td>1</td><td>10568</td></tr>\n", "\t<tr><td>1_125068779_125076369</td><td>1</td><td>17282</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>1_125080164_125085477</td><td>1</td><td> 5231</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>2_89795772_89826132  </td><td>1</td><td>80600</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>2_89826132_89841312  </td><td>1</td><td>23943</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>2_89841312_89851938  </td><td>1</td><td>23808</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>3_125634993_125647896</td><td>1</td><td>36686</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>7_74529246_74535318  </td><td>1</td><td>12600</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>7_75723912_75741369  </td><td>2</td><td>34933</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>7_85930185_85946883  </td><td>1</td><td>45537</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>7_101192157_101203542</td><td>1</td><td>30978</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>7_118830558_118839666</td><td>1</td><td>18772</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>7_124224771_124227807</td><td>1</td><td> 7772</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>7_129420885_129431511</td><td>1</td><td>27462</td><td>1</td><td>16501</td></tr>\n", "\t<tr><td>7_152401887_152407959</td><td>1</td><td>16954</td><td>1</td><td>10568</td></tr>\n", "\t<tr><td>7_152407959_152417067</td><td>1</td><td>28189</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>14_18658497_18662292 </td><td>1</td><td> 8160</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>15_20344236_20373837 </td><td>1</td><td>57622</td><td>0</td><td>    0</td></tr>\n", "\t<tr><td>15_20642076_20671365 </td><td>1</td><td>68055</td><td>0</td><td>    0</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A data.frame: 28 × 5\n", "\\begin{tabular}{lllll}\n", " Region & Flye\\_Contigs & Flye\\_Total\\_Length & Miniasm\\_Contigs & Miniasm\\_Total\\_Length\\\\\n", " <chr> & <int> & <int> & <int> & <int>\\\\\n", "\\hline\n", "\t 3\\_125634993\\_125647896 & 1 & 36686 & 0 &     0\\\\\n", "\t 7\\_101192157\\_101203542 & 1 & 30978 & 0 &     0\\\\\n", "\t 7\\_124224771\\_124227807 & 1 &  7772 & 0 &     0\\\\\n", "\t 7\\_129420885\\_129431511 & 1 & 27462 & 1 & 16501\\\\\n", "\t 7\\_152407959\\_152417067 & 1 & 28189 & 0 &     0\\\\\n", "\t 7\\_74529246\\_74535318   & 1 & 12600 & 0 &     0\\\\\n", "\t 7\\_75723912\\_75741369   & 2 & 34933 & 0 &     0\\\\\n", "\t 7\\_85930185\\_85946883   & 1 & 45537 & 0 &     0\\\\\n", "\t 7\\_118830558\\_118839666 & 1 & 18772 & 0 &     0\\\\\n", "\t 7\\_152401887\\_152407959 & 1 & 16954 & 1 & 10568\\\\\n", "\t 1\\_125068779\\_125076369 & 1 & 17282 & 0 &     0\\\\\n", "\t 1\\_125080164\\_125085477 & 1 &  5231 & 0 &     0\\\\\n", "\t 2\\_89795772\\_89826132   & 1 & 80600 & 0 &     0\\\\\n", "\t 2\\_89826132\\_89841312   & 1 & 23943 & 0 &     0\\\\\n", "\t 2\\_89841312\\_89851938   & 1 & 23808 & 0 &     0\\\\\n", "\t 3\\_125634993\\_125647896 & 1 & 36686 & 0 &     0\\\\\n", "\t 7\\_74529246\\_74535318   & 1 & 12600 & 0 &     0\\\\\n", "\t 7\\_75723912\\_75741369   & 2 & 34933 & 0 &     0\\\\\n", "\t 7\\_85930185\\_85946883   & 1 & 45537 & 0 &     0\\\\\n", "\t 7\\_101192157\\_101203542 & 1 & 30978 & 0 &     0\\\\\n", "\t 7\\_118830558\\_118839666 & 1 & 18772 & 0 &     0\\\\\n", "\t 7\\_124224771\\_124227807 & 1 &  7772 & 0 &     0\\\\\n", "\t 7\\_129420885\\_129431511 & 1 & 27462 & 1 & 16501\\\\\n", "\t 7\\_152401887\\_152407959 & 1 & 16954 & 1 & 10568\\\\\n", "\t 7\\_152407959\\_152417067 & 1 & 28189 & 0 &     0\\\\\n", "\t 14\\_18658497\\_18662292  & 1 &  8160 & 0 &     0\\\\\n", "\t 15\\_20344236\\_20373837  & 1 & 57622 & 0 &     0\\\\\n", "\t 15\\_20642076\\_20671365  & 1 & 68055 & 0 &     0\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A data.frame: 28 × 5\n", "\n", "| Region &lt;chr&gt; | Flye_Contigs &lt;int&gt; | Flye_Total_Length &lt;int&gt; | Miniasm_Contigs &lt;int&gt; | Miniasm_Total_Length &lt;int&gt; |\n", "|---|---|---|---|---|\n", "| 3_125634993_125647896 | 1 | 36686 | 0 |     0 |\n", "| 7_101192157_101203542 | 1 | 30978 | 0 |     0 |\n", "| 7_124224771_124227807 | 1 |  7772 | 0 |     0 |\n", "| 7_129420885_129431511 | 1 | 27462 | 1 | 16501 |\n", "| 7_152407959_152417067 | 1 | 28189 | 0 |     0 |\n", "| 7_74529246_74535318   | 1 | 12600 | 0 |     0 |\n", "| 7_75723912_75741369   | 2 | 34933 | 0 |     0 |\n", "| 7_85930185_85946883   | 1 | 45537 | 0 |     0 |\n", "| 7_118830558_118839666 | 1 | 18772 | 0 |     0 |\n", "| 7_152401887_152407959 | 1 | 16954 | 1 | 10568 |\n", "| 1_125068779_125076369 | 1 | 17282 | 0 |     0 |\n", "| 1_125080164_125085477 | 1 |  5231 | 0 |     0 |\n", "| 2_89795772_89826132   | 1 | 80600 | 0 |     0 |\n", "| 2_89826132_89841312   | 1 | 23943 | 0 |     0 |\n", "| 2_89841312_89851938   | 1 | 23808 | 0 |     0 |\n", "| 3_125634993_125647896 | 1 | 36686 | 0 |     0 |\n", "| 7_74529246_74535318   | 1 | 12600 | 0 |     0 |\n", "| 7_75723912_75741369   | 2 | 34933 | 0 |     0 |\n", "| 7_85930185_85946883   | 1 | 45537 | 0 |     0 |\n", "| 7_101192157_101203542 | 1 | 30978 | 0 |     0 |\n", "| 7_118830558_118839666 | 1 | 18772 | 0 |     0 |\n", "| 7_124224771_124227807 | 1 |  7772 | 0 |     0 |\n", "| 7_129420885_129431511 | 1 | 27462 | 1 | 16501 |\n", "| 7_152401887_152407959 | 1 | 16954 | 1 | 10568 |\n", "| 7_152407959_152417067 | 1 | 28189 | 0 |     0 |\n", "| 14_18658497_18662292  | 1 |  8160 | 0 |     0 |\n", "| 15_20344236_20373837  | 1 | 57622 | 0 |     0 |\n", "| 15_20642076_20671365  | 1 | 68055 | 0 |     0 |\n", "\n"], "text/plain": ["   Region                Flye_Contigs Flye_Total_Length Miniasm_Contigs\n", "1  3_125634993_125647896 1            36686             0              \n", "2  7_101192157_101203542 1            30978             0              \n", "3  7_124224771_124227807 1             7772             0              \n", "4  7_129420885_129431511 1            27462             1              \n", "5  7_152407959_152417067 1            28189             0              \n", "6  7_74529246_74535318   1            12600             0              \n", "7  7_75723912_75741369   2            34933             0              \n", "8  7_85930185_85946883   1            45537             0              \n", "9  7_118830558_118839666 1            18772             0              \n", "10 7_152401887_152407959 1            16954             1              \n", "11 1_125068779_125076369 1            17282             0              \n", "12 1_125080164_125085477 1             5231             0              \n", "13 2_89795772_89826132   1            80600             0              \n", "14 2_89826132_89841312   1            23943             0              \n", "15 2_89841312_89851938   1            23808             0              \n", "16 3_125634993_125647896 1            36686             0              \n", "17 7_74529246_74535318   1            12600             0              \n", "18 7_75723912_75741369   2            34933             0              \n", "19 7_85930185_85946883   1            45537             0              \n", "20 7_101192157_101203542 1            30978             0              \n", "21 7_118830558_118839666 1            18772             0              \n", "22 7_124224771_124227807 1             7772             0              \n", "23 7_129420885_129431511 1            27462             1              \n", "24 7_152401887_152407959 1            16954             1              \n", "25 7_152407959_152417067 1            28189             0              \n", "26 14_18658497_18662292  1             8160             0              \n", "27 15_20344236_20373837  1            57622             0              \n", "28 15_20642076_20671365  1            68055             0              \n", "   Miniasm_Total_Length\n", "1      0               \n", "2      0               \n", "3      0               \n", "4  16501               \n", "5      0               \n", "6      0               \n", "7      0               \n", "8      0               \n", "9      0               \n", "10 10568               \n", "11     0               \n", "12     0               \n", "13     0               \n", "14     0               \n", "15     0               \n", "16     0               \n", "17     0               \n", "18     0               \n", "19     0               \n", "20     0               \n", "21     0               \n", "22     0               \n", "23 16501               \n", "24 10568               \n", "25     0               \n", "26     0               \n", "27     0               \n", "28     0               "]}, "metadata": {}, "output_type": "display_data"}], "source": ["file = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/ecdna/manual_assembly/RLGS2-primary/assembly_summary.tsv'\n", "\n", "df = read.csv(file, sep='\\t')\n", "df"]}, {"cell_type": "code", "execution_count": 8, "id": "f5ccaf3b-2880-446f-8ecf-6f860b3f6978", "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A data.frame: 28 × 6</caption>\n", "<thead>\n", "\t<tr><th scope=col>Region</th><th scope=col>Flye_Contigs</th><th scope=col>Flye_Total_Length</th><th scope=col>Miniasm_Contigs</th><th scope=col>Miniasm_Total_Length</th><th scope=col>region_length</th></tr>\n", "\t<tr><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;dbl&gt;</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><td>3_125634993_125647896</td><td>1</td><td>36686</td><td>0</td><td>    0</td><td>12903</td></tr>\n", "\t<tr><td>7_101192157_101203542</td><td>1</td><td>30978</td><td>0</td><td>    0</td><td>11385</td></tr>\n", "\t<tr><td>7_124224771_124227807</td><td>1</td><td> 7772</td><td>0</td><td>    0</td><td> 3036</td></tr>\n", "\t<tr><td>7_129420885_129431511</td><td>1</td><td>27462</td><td>1</td><td>16501</td><td>10626</td></tr>\n", "\t<tr><td>7_152407959_152417067</td><td>1</td><td>28189</td><td>0</td><td>    0</td><td> 9108</td></tr>\n", "\t<tr><td>7_74529246_74535318  </td><td>1</td><td>12600</td><td>0</td><td>    0</td><td> 6072</td></tr>\n", "\t<tr><td>7_75723912_75741369  </td><td>2</td><td>34933</td><td>0</td><td>    0</td><td>17457</td></tr>\n", "\t<tr><td>7_85930185_85946883  </td><td>1</td><td>45537</td><td>0</td><td>    0</td><td>16698</td></tr>\n", "\t<tr><td>7_118830558_118839666</td><td>1</td><td>18772</td><td>0</td><td>    0</td><td> 9108</td></tr>\n", "\t<tr><td>7_152401887_152407959</td><td>1</td><td>16954</td><td>1</td><td>10568</td><td> 6072</td></tr>\n", "\t<tr><td>1_125068779_125076369</td><td>1</td><td>17282</td><td>0</td><td>    0</td><td> 7590</td></tr>\n", "\t<tr><td>1_125080164_125085477</td><td>1</td><td> 5231</td><td>0</td><td>    0</td><td> 5313</td></tr>\n", "\t<tr><td>2_89795772_89826132  </td><td>1</td><td>80600</td><td>0</td><td>    0</td><td>30360</td></tr>\n", "\t<tr><td>2_89826132_89841312  </td><td>1</td><td>23943</td><td>0</td><td>    0</td><td>15180</td></tr>\n", "\t<tr><td>2_89841312_89851938  </td><td>1</td><td>23808</td><td>0</td><td>    0</td><td>10626</td></tr>\n", "\t<tr><td>3_125634993_125647896</td><td>1</td><td>36686</td><td>0</td><td>    0</td><td>12903</td></tr>\n", "\t<tr><td>7_74529246_74535318  </td><td>1</td><td>12600</td><td>0</td><td>    0</td><td> 6072</td></tr>\n", "\t<tr><td>7_75723912_75741369  </td><td>2</td><td>34933</td><td>0</td><td>    0</td><td>17457</td></tr>\n", "\t<tr><td>7_85930185_85946883  </td><td>1</td><td>45537</td><td>0</td><td>    0</td><td>16698</td></tr>\n", "\t<tr><td>7_101192157_101203542</td><td>1</td><td>30978</td><td>0</td><td>    0</td><td>11385</td></tr>\n", "\t<tr><td>7_118830558_118839666</td><td>1</td><td>18772</td><td>0</td><td>    0</td><td> 9108</td></tr>\n", "\t<tr><td>7_124224771_124227807</td><td>1</td><td> 7772</td><td>0</td><td>    0</td><td> 3036</td></tr>\n", "\t<tr><td>7_129420885_129431511</td><td>1</td><td>27462</td><td>1</td><td>16501</td><td>10626</td></tr>\n", "\t<tr><td>7_152401887_152407959</td><td>1</td><td>16954</td><td>1</td><td>10568</td><td> 6072</td></tr>\n", "\t<tr><td>7_152407959_152417067</td><td>1</td><td>28189</td><td>0</td><td>    0</td><td> 9108</td></tr>\n", "\t<tr><td>14_18658497_18662292 </td><td>1</td><td> 8160</td><td>0</td><td>    0</td><td> 3795</td></tr>\n", "\t<tr><td>15_20344236_20373837 </td><td>1</td><td>57622</td><td>0</td><td>    0</td><td>29601</td></tr>\n", "\t<tr><td>15_20642076_20671365 </td><td>1</td><td>68055</td><td>0</td><td>    0</td><td>29289</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A data.frame: 28 × 6\n", "\\begin{tabular}{llllll}\n", " Region & Flye\\_Contigs & Flye\\_Total\\_Length & Miniasm\\_Contigs & Miniasm\\_Total\\_Length & region\\_length\\\\\n", " <chr> & <int> & <int> & <int> & <int> & <dbl>\\\\\n", "\\hline\n", "\t 3\\_125634993\\_125647896 & 1 & 36686 & 0 &     0 & 12903\\\\\n", "\t 7\\_101192157\\_101203542 & 1 & 30978 & 0 &     0 & 11385\\\\\n", "\t 7\\_124224771\\_124227807 & 1 &  7772 & 0 &     0 &  3036\\\\\n", "\t 7\\_129420885\\_129431511 & 1 & 27462 & 1 & 16501 & 10626\\\\\n", "\t 7\\_152407959\\_152417067 & 1 & 28189 & 0 &     0 &  9108\\\\\n", "\t 7\\_74529246\\_74535318   & 1 & 12600 & 0 &     0 &  6072\\\\\n", "\t 7\\_75723912\\_75741369   & 2 & 34933 & 0 &     0 & 17457\\\\\n", "\t 7\\_85930185\\_85946883   & 1 & 45537 & 0 &     0 & 16698\\\\\n", "\t 7\\_118830558\\_118839666 & 1 & 18772 & 0 &     0 &  9108\\\\\n", "\t 7\\_152401887\\_152407959 & 1 & 16954 & 1 & 10568 &  6072\\\\\n", "\t 1\\_125068779\\_125076369 & 1 & 17282 & 0 &     0 &  7590\\\\\n", "\t 1\\_125080164\\_125085477 & 1 &  5231 & 0 &     0 &  5313\\\\\n", "\t 2\\_89795772\\_89826132   & 1 & 80600 & 0 &     0 & 30360\\\\\n", "\t 2\\_89826132\\_89841312   & 1 & 23943 & 0 &     0 & 15180\\\\\n", "\t 2\\_89841312\\_89851938   & 1 & 23808 & 0 &     0 & 10626\\\\\n", "\t 3\\_125634993\\_125647896 & 1 & 36686 & 0 &     0 & 12903\\\\\n", "\t 7\\_74529246\\_74535318   & 1 & 12600 & 0 &     0 &  6072\\\\\n", "\t 7\\_75723912\\_75741369   & 2 & 34933 & 0 &     0 & 17457\\\\\n", "\t 7\\_85930185\\_85946883   & 1 & 45537 & 0 &     0 & 16698\\\\\n", "\t 7\\_101192157\\_101203542 & 1 & 30978 & 0 &     0 & 11385\\\\\n", "\t 7\\_118830558\\_118839666 & 1 & 18772 & 0 &     0 &  9108\\\\\n", "\t 7\\_124224771\\_124227807 & 1 &  7772 & 0 &     0 &  3036\\\\\n", "\t 7\\_129420885\\_129431511 & 1 & 27462 & 1 & 16501 & 10626\\\\\n", "\t 7\\_152401887\\_152407959 & 1 & 16954 & 1 & 10568 &  6072\\\\\n", "\t 7\\_152407959\\_152417067 & 1 & 28189 & 0 &     0 &  9108\\\\\n", "\t 14\\_18658497\\_18662292  & 1 &  8160 & 0 &     0 &  3795\\\\\n", "\t 15\\_20344236\\_20373837  & 1 & 57622 & 0 &     0 & 29601\\\\\n", "\t 15\\_20642076\\_20671365  & 1 & 68055 & 0 &     0 & 29289\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A data.frame: 28 × 6\n", "\n", "| Region &lt;chr&gt; | Flye_Contigs &lt;int&gt; | Flye_Total_Length &lt;int&gt; | Miniasm_Contigs &lt;int&gt; | Miniasm_Total_Length &lt;int&gt; | region_length &lt;dbl&gt; |\n", "|---|---|---|---|---|---|\n", "| 3_125634993_125647896 | 1 | 36686 | 0 |     0 | 12903 |\n", "| 7_101192157_101203542 | 1 | 30978 | 0 |     0 | 11385 |\n", "| 7_124224771_124227807 | 1 |  7772 | 0 |     0 |  3036 |\n", "| 7_129420885_129431511 | 1 | 27462 | 1 | 16501 | 10626 |\n", "| 7_152407959_152417067 | 1 | 28189 | 0 |     0 |  9108 |\n", "| 7_74529246_74535318   | 1 | 12600 | 0 |     0 |  6072 |\n", "| 7_75723912_75741369   | 2 | 34933 | 0 |     0 | 17457 |\n", "| 7_85930185_85946883   | 1 | 45537 | 0 |     0 | 16698 |\n", "| 7_118830558_118839666 | 1 | 18772 | 0 |     0 |  9108 |\n", "| 7_152401887_152407959 | 1 | 16954 | 1 | 10568 |  6072 |\n", "| 1_125068779_125076369 | 1 | 17282 | 0 |     0 |  7590 |\n", "| 1_125080164_125085477 | 1 |  5231 | 0 |     0 |  5313 |\n", "| 2_89795772_89826132   | 1 | 80600 | 0 |     0 | 30360 |\n", "| 2_89826132_89841312   | 1 | 23943 | 0 |     0 | 15180 |\n", "| 2_89841312_89851938   | 1 | 23808 | 0 |     0 | 10626 |\n", "| 3_125634993_125647896 | 1 | 36686 | 0 |     0 | 12903 |\n", "| 7_74529246_74535318   | 1 | 12600 | 0 |     0 |  6072 |\n", "| 7_75723912_75741369   | 2 | 34933 | 0 |     0 | 17457 |\n", "| 7_85930185_85946883   | 1 | 45537 | 0 |     0 | 16698 |\n", "| 7_101192157_101203542 | 1 | 30978 | 0 |     0 | 11385 |\n", "| 7_118830558_118839666 | 1 | 18772 | 0 |     0 |  9108 |\n", "| 7_124224771_124227807 | 1 |  7772 | 0 |     0 |  3036 |\n", "| 7_129420885_129431511 | 1 | 27462 | 1 | 16501 | 10626 |\n", "| 7_152401887_152407959 | 1 | 16954 | 1 | 10568 |  6072 |\n", "| 7_152407959_152417067 | 1 | 28189 | 0 |     0 |  9108 |\n", "| 14_18658497_18662292  | 1 |  8160 | 0 |     0 |  3795 |\n", "| 15_20344236_20373837  | 1 | 57622 | 0 |     0 | 29601 |\n", "| 15_20642076_20671365  | 1 | 68055 | 0 |     0 | 29289 |\n", "\n"], "text/plain": ["   Region                Flye_Contigs Flye_Total_Length Miniasm_Contigs\n", "1  3_125634993_125647896 1            36686             0              \n", "2  7_101192157_101203542 1            30978             0              \n", "3  7_124224771_124227807 1             7772             0              \n", "4  7_129420885_129431511 1            27462             1              \n", "5  7_152407959_152417067 1            28189             0              \n", "6  7_74529246_74535318   1            12600             0              \n", "7  7_75723912_75741369   2            34933             0              \n", "8  7_85930185_85946883   1            45537             0              \n", "9  7_118830558_118839666 1            18772             0              \n", "10 7_152401887_152407959 1            16954             1              \n", "11 1_125068779_125076369 1            17282             0              \n", "12 1_125080164_125085477 1             5231             0              \n", "13 2_89795772_89826132   1            80600             0              \n", "14 2_89826132_89841312   1            23943             0              \n", "15 2_89841312_89851938   1            23808             0              \n", "16 3_125634993_125647896 1            36686             0              \n", "17 7_74529246_74535318   1            12600             0              \n", "18 7_75723912_75741369   2            34933             0              \n", "19 7_85930185_85946883   1            45537             0              \n", "20 7_101192157_101203542 1            30978             0              \n", "21 7_118830558_118839666 1            18772             0              \n", "22 7_124224771_124227807 1             7772             0              \n", "23 7_129420885_129431511 1            27462             1              \n", "24 7_152401887_152407959 1            16954             1              \n", "25 7_152407959_152417067 1            28189             0              \n", "26 14_18658497_18662292  1             8160             0              \n", "27 15_20344236_20373837  1            57622             0              \n", "28 15_20642076_20671365  1            68055             0              \n", "   Miniasm_Total_Length region_length\n", "1      0                12903        \n", "2      0                11385        \n", "3      0                 3036        \n", "4  16501                10626        \n", "5      0                 9108        \n", "6      0                 6072        \n", "7      0                17457        \n", "8      0                16698        \n", "9      0                 9108        \n", "10 10568                 6072        \n", "11     0                 7590        \n", "12     0                 5313        \n", "13     0                30360        \n", "14     0                15180        \n", "15     0                10626        \n", "16     0                12903        \n", "17     0                 6072        \n", "18     0                17457        \n", "19     0                16698        \n", "20     0                11385        \n", "21     0                 9108        \n", "22     0                 3036        \n", "23 16501                10626        \n", "24 10568                 6072        \n", "25     0                 9108        \n", "26     0                 3795        \n", "27     0                29601        \n", "28     0                29289        "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Separate the string into parts\n", "split_parts <- strsplit(df$Region, \"_\")\n", "\n", "# Extract 2nd and 3rd elements, convert to numeric, and calculate the difference\n", "region_length <- sapply(split_parts, function(x) as.numeric(x[3]) - as.numeric(x[2]))\n", "\n", "# Add the result as a new column to your dataframe\n", "df$region_length <- region_length\n", "\n", "df"]}, {"cell_type": "code", "execution_count": 12, "id": "b19011a7-624b-41a1-97ff-69428710c013", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[1m\u001b[22m`stat_bin()` using `bins = 30`. Pick better value with `binwidth`.\n", "Warning message:\n", "\"\u001b[1m\u001b[22mRemoved 2 rows containing missing values (`geom_bar()`).\"\n", "\u001b[1m\u001b[22m`stat_bin()` using `bins = 30`. Pick better value with `binwidth`.\n", "Warning message:\n", "\"\u001b[1m\u001b[22mRemoved 2 rows containing missing values (`geom_bar()`).\"\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA0gAAANICAIAAAByhViMAAAABmJLR0QA/wD/AP+gvaeTAAAg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\n4QAAAABJRU5ErkJggg==", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA0gAAANICAIAAAByhViMAAAABmJLR0QA/wD/AP+gvaeTAAAg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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["\n", "    \n", "# plot\n", "p = ggplot(df, aes(x = Flye_Total_Length)) + plot_theme() +\n", "geom_histogram() +\n", "\n", "xlim(0,100000) +\n", "\n", "ggtitle('') +\n", "xlab('Contig length') + ylab('Count')\n", "\n", "print(p)\n", "\n", "\n", "# plot\n", "p = ggplot(df, aes(x = region_length)) + plot_theme() +\n", "geom_histogram() +\n", "\n", "xlim(0,100000) +\n", "\n", "ggtitle('') +\n", "xlab('Gain length') + ylab('Count')\n", "\n", "print(p)\n"]}, {"cell_type": "code", "execution_count": null, "id": "16d7fe28-8e40-4f64-b49c-3c0c7291195b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2f495b24-e133-4226-ae86-dc7e0a2bb6d6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2ba4d427-0eff-4004-bc73-24bf0b6a9d76", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7f560079-0e46-423c-b4c4-7ec2a0b038f1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.2.2"}}, "nbformat": 4, "nbformat_minor": 5}