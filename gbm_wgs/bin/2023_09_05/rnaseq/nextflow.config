process {
    executor = "uge"
    penv = "smp"
    beforeScript = '''
        source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh
        source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
        conda activate nextflow
    '''
    clusterOptions = {
        "-V -P reimandlab -V -l h_rt=21:0:0:0 -V -l h_vmem=${task.memory.toMega()}M"
    }
    withName: 'TRIMGALORE' {
        memory = '8192 MB'
    }
    withName: 'MAKE_TRANSCRIPTS_FASTA' {
        memory = '8192 MB'
    }
    withName: 'STAR_GENOMEGENERATE' {
        clusterOptions = "-V -P reimandlab -V -l h_rt=48:00:00 -V -l h_vmem=10G"  // 20 GB memory and extended runtime
    }
    withName: 'STAR_ALIGN' {
        memory = '8192 MB'
    }
}

executor {
    name = "uge"
    queueSize = 500
    queueStatInterval = "10s"
}

