import numpy as np
import pandas as pd

import glob, re, os

files = pd.Series(glob.glob("/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/data/raw_data/all_fastqs/*rna*"))

# list samples but remove blood samples
patient_tumors = np.unique(files.str.split("/").str[-1].str.split("_").str[0])

base_path = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/rnaseq'  # Adjust this path as per your requirement
outdir = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/rnaseq'


# final df
res_df = []

for patient in patient_tumors:
    df = []

    # specific files for that sample
    files_oi = [file for file in files if re.search(patient, file)]

    fastq1 = np.array(files_oi)[pd.Series(files_oi).str.contains('R1')][0]
    fastq2 = np.array(files_oi)[pd.Series(files_oi).str.contains('R2')][0]

    # add to df
    df.append([patient, fastq1, fastq2, "auto"])
    df = pd.DataFrame(df, columns = ['sample', 'fastq_1', 'fastq_2', 'strandedness'])

    # append to final df
    res_df.append(df)

# concat
res_df = pd.concat(res_df)

# save
res_df.to_csv(os.path.join(outdir, 'rnaseq_input.csv'), index = False)


# create processing file
sarek_file = os.path.join(outdir, 'rnaseq')
to_write = f"""#!/bin/bash
#$ -P reimandlab
#$ -N rnaseq
#$ -l h_vmem=10G,h_rt=7:0:0:0
#$ -q all.q
#$ -o /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/rnaseq/
#$ -e /.mounts/labs/reimandlab/private/users/abahcheli/gbm_tmp/rnaseq/


source /u/abahcheli/.profile
source /u/abahcheli/.bashrc
source /etc/profile.d/uge_settings.sh

source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/conda.sh
source /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/etc/profile.d/mamba.sh

mamba activate nextflow

cd /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/rnaseq

export NXF_SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/software/singularity_cache/
export NXF_OFFLINE='true'


nextflow run /.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/nextflow_pipelines_offline/nf-core-rnaseq_3.15.0/3_15_0 \
--input /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/rnaseq/rnaseq_input.csv \
--outdir /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/rnaseq \
--gtf /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/rnaseq/GCF_000001405.40_GRCh38.p14_genomic.gtf \
--fasta /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/rnaseq/GCF_000001405.40_GRCh38.p14_genomic.fna \
-profile singularity \
-c /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_09_05/rnaseq/nextflow.config \
-resume

"""
with open(sarek_file, "w") as outfile:
    outfile.write(to_write)

