# <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)

library(ggrastr)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}





boxplot = function(input_df){

# plot
p = ggplot(input_df, aes(y = log10(mean_expression), x = mean_methylation)) + plot_theme() +
rasterize(geom_point(pch=21), dpi=300) + 

# add line of best fit
geom_smooth(method = "lm", se = FALSE, color = "blue") +

ggtitle('') +
xlab('Mean promoter methlyation') + ylab('Mean expression (CPM)') +
theme(axis.text.x = element_text(angle = 0, hjust = 0.5))

    
print(p)


# plot
p = ggplot(input_df, aes(y = spearman_corr)) + plot_theme() +
geom_boxplot() +


ggtitle('') +
xlab('') + ylab('Spearman Rho')

print(p)


# repeat with violin plot
p = ggplot(input_df, aes(y = spearman_corr, x='all_genes')) + plot_theme() +
geom_violin() +

ggtitle('') +
xlab('') + ylab('Spearman Rho')

print(p)

return()
}




sort_samples = function(df){
# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# order samples
sorted_vector <- unique(df$patient_id)[order(sapply(unique(df$patient_id), extract_numeric))]
df$patient_id = factor(df$patient_id, levels = sorted_vector)

return(df)
}



pdf(opt$figure_file)

# load df
input_df = read.csv(opt$figure_stats_file, sep='\t')

# plot dotplots
boxplot(input_df)

dev.off()


print(opt$figure_file)




