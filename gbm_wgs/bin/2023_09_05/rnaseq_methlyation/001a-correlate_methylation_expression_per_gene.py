# Alec <PERSON>cheli

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

from concurrent.futures import ProcessPoolExecutor
import scipy.stats as stats

help_message = '''
Failed
'''

def run_correlation(args):
    methylation_df, rnaseq_df = args

    # stats df
    stats_df = []

    # set index to sample
    methylation_df.index = methylation_df['sample'].to_numpy()
    rnaseq_df.index = rnaseq_df['sample'].to_numpy()

    # for each gene, subset to the gene and calculate the correlation
    for gene in methylation_df['gene'].unique():
        methylation_gene = methylation_df[methylation_df['gene'] == gene]
        rnaseq_gene = rnaseq_df[rnaseq_df['gene_id'] == gene]

        # subset to common samples
        samples = np.intersect1d(methylation_gene['sample'], rnaseq_gene['sample'])

        # subset to samples
        methylation_gene = methylation_gene.loc[samples,'percent_methylated']
        rnaseq_gene = rnaseq_gene.loc[samples,'gene_counts']
        
        # calculate spearman correlation value and p-value
        spearman_corr, pvalue = stats.spearmanr(methylation_gene, rnaseq_gene)

        # calculate mean methylation and mean expression
        mean_methylation = np.mean(methylation_gene)
        mean_expression = np.mean(rnaseq_gene)

        # append to stats_df
        stats_df.append([gene, spearman_corr, pvalue, mean_methylation, mean_expression])
        
    return pd.DataFrame(stats_df, columns=['gene', 'spearman_corr', 'pvalue', 'mean_methylation', 'mean_expression'])


def run_correlation_stats(mean_promoter_methylation_file, rnaseq_counts_file, threads):
    # load dfs
    methylation_df = pd.read_csv(mean_promoter_methylation_file, sep='\t')
    rnaseq_df = pd.read_csv(rnaseq_counts_file, sep='\t')

    # melt rnaseq
    rnaseq_df.drop('gene_name', axis=1, inplace=True)
    rnaseq_df = pd.melt(rnaseq_df, id_vars=['gene_id'], var_name='sample', value_name='gene_counts')

    # replace "_" with "-"
    rnaseq_df['sample'] = rnaseq_df['sample'].str.replace('_', '-')

    # set index as gene_id
    rnaseq_df.index = rnaseq_df['gene_id'].to_numpy()
    methylation_df.index = methylation_df['gene'].to_numpy()

    # separate genes into n lists for parallel processing
    genes_oi = np.intersect1d(methylation_df['gene'].unique(), rnaseq_df['gene_id'].unique())
    gene_list = np.array_split(genes_oi, threads)

    # arguements for correlation
    args = [(methylation_df.loc[genes,:], rnaseq_df.loc[genes,:]) for genes in gene_list]

    # run correlation
    with ProcessPoolExecutor(max_workers=threads) as executor:
        results = executor.map(run_correlation, args)

    # combine results
    df = pd.concat(results)

    return df


def main():
    print('XXX-XXX.py')
    t1 = time.time()

    # generate maf format df
    df = run_correlation_stats(mean_promoter_methylation_file, rnaseq_counts_file, threads)

    # save to files
    df.to_csv(figure_stats_file, sep='\t', index=False)

    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    threads = 1

    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["mean_promoter_methylation_file=", "rnaseq_counts_file=", "figure_stats_file=", "threads="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--mean_promoter_methylation_file"):
            mean_promoter_methylation_file = str(arg)
        if opt in ("--rnaseq_counts_file"):
            rnaseq_counts_file = str(arg)

        if opt in ("--figure_stats_file"):
            figure_stats_file = str(arg)

        if opt in ("--threads"):
            threads = int(arg)

    main()




