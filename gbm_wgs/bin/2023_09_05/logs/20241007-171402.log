2024-10-07 17:14:11,255 - matplotlib   - DEBUG    - matplotlib data path: /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data
2024-10-07 17:14:11,372 - matplotlib   - DEBUG    - CONFIGDIR=/u/abahcheli/.config/matplotlib
2024-10-07 17:14:11,377 - matplotlib   - DEBUG    - interactive is False
2024-10-07 17:14:11,377 - matplotlib   - DEBUG    - platform is linux
2024-10-07 17:14:11,378 - matplotlib   - DEBUG    - loaded modules: ['sys', 'builtins', '_frozen_importlib', '_imp', '_warnings', '_io', 'marshal', 'posix', '_frozen_importlib_external', '_thread', '_weakref', 'time', 'zipimport', '_codecs', 'codecs', 'encodings.aliases', 'encodings', 'encodings.latin_1', '_signal', '__main__', 'encodings.utf_8', '_abc', 'abc', 'io', '_stat', 'stat', '_collections_abc', 'genericpath', 'posixpath', 'os.path', 'os', '_sitebuiltins', '_locale', '_bootlocale', 'types', 'importlib._bootstrap', 'importlib._bootstrap_external', 'warnings', 'importlib', 'importlib.machinery', 'importlib.abc', '_operator', 'operator', 'keyword', '_heapq', 'heapq', 'itertools', 'reprlib', '_collections', 'collections', '_functools', 'functools', 'contextlib', 'importlib.util', 'mpl_toolkits', 'site', 'enum', '_sre', 'sre_constants', 'sre_parse', 'sre_compile', 'copyreg', 're', 'fnmatch', 'ntpath', 'errno', 'urllib', 'urllib.parse', 'pathlib', '_weakrefset', 'weakref', 'copy', 'collections.abc', 'typing.io', 'typing.re', 'typing', 'token', 'tokenize', 'linecache', 'traceback', '_string', 'string', 'threading', 'atexit', 'logging', '__future__', 'numpy._globals', 'numpy.__config__', 'numpy._distributor_init', '_json', 'json.scanner', 'json.decoder', 'json.encoder', 'json', 'numpy._version', 'numpy.version', 'math', '_datetime', 'datetime', 'numpy.core._multiarray_umath', 'numpy.compat._inspect', '_struct', 'struct', '_compat_pickle', '_pickle', 'pickle', 'numpy.compat.py3k', 'numpy.compat', 'numpy.core.overrides', 'numpy.core.multiarray', 'numpy.core.umath', 'numbers', 'numpy.core._string_helpers', 'numpy.core._dtype', 'numpy.core._type_aliases', 'numpy.core.numerictypes', 'numpy.core._exceptions', '_contextvars', 'contextvars', 'numpy.core._ufunc_config', 'numpy.core._methods', 'numpy.core.fromnumeric', 'numpy.core.shape_base', 'numpy.core.arrayprint', 'numpy.core._asarray', 'numpy.core.numeric', 'numpy.core.defchararray', 'numpy.core.records', 'numpy.core.memmap', 'numpy.core.function_base', 'numpy.core._machar', 'numpy.core.getlimits', 'numpy.core.einsumfunc', 'numpy.core._multiarray_tests', 'numpy.core._add_newdocs', 'numpy.core._add_newdocs_scalars', 'numpy.core._dtype_ctypes', '_ast', 'ast', '_ctypes', 'ctypes._endian', 'ctypes', 'numpy.core._internal', 'numpy._pytesttester', 'numpy.core', 'numpy.lib.mixins', 'numpy.lib.ufunclike', 'numpy.lib.type_check', 'numpy.lib.scimath', 'numpy.lib.stride_tricks', 'numpy.lib.twodim_base', 'numpy.linalg._umath_linalg', 'numpy.linalg.linalg', 'numpy.linalg', 'numpy.matrixlib.defmatrix', 'numpy.matrixlib', 'numpy.lib.histograms', 'numpy.lib.function_base', 'numpy.lib.index_tricks', 'numpy.lib.nanfunctions', 'numpy.lib.shape_base', 'numpy.lib.polynomial', 'textwrap', 'numpy.lib.utils', 'numpy.lib.arraysetops', 'numpy.lib.format', 'numpy.lib._datasource', 'numpy.lib._iotools', 'numpy.lib.npyio', 'numpy.lib.arrayterator', 'numpy.lib.arraypad', 'numpy.lib._version', 'numpy.lib', 'numpy.fft._pocketfft_internal', 'numpy.fft._pocketfft', 'numpy.fft.helper', 'numpy.fft', 'numpy.polynomial.polyutils', 'numpy.polynomial._polybase', 'numpy.polynomial.polynomial', 'numpy.polynomial.chebyshev', 'numpy.polynomial.legendre', 'numpy.polynomial.hermite', 'numpy.polynomial.hermite_e', 'numpy.polynomial.laguerre', 'numpy.polynomial', 'cython_runtime', '_cython_0_29_35', 'numpy.random._common', 'binascii', 'base64', '_hashlib', '_blake2', '_sha3', 'hashlib', 'hmac', '_bisect', 'bisect', '_sha512', '_random', 'random', 'secrets', 'numpy.random.bit_generator', 'numpy.random._bounded_integers', 'numpy.random._mt19937', 'numpy.random.mtrand', 'numpy.random._philox', 'numpy.random._pcg64', 'numpy.random._sfc64', 'numpy.random._generator', 'numpy.random._pickle', 'numpy.random', 'numpy.ctypeslib', '_opcode', 'opcode', 'dis', 'inspect', 'numpy.ma.core', 'numpy.ma.extras', 'numpy.ma', 'numpy', 'pytz.exceptions', 'pytz.lazy', 'pytz.tzinfo', 'pytz.tzfile', 'pytz', 'dateutil._version', 'dateutil', 'platform', 'pandas._typing', 'pandas.compat._constants', '_compression', '_bz2', 'bz2', '_lzma', 'lzma', 'pandas.compat.compressors', 'pandas._libs.tslibs.np_datetime', 'pandas._libs.tslibs.dtypes', 'pandas._libs.tslibs.base', 'pandas._libs.tslibs.nattype', 'pandas.util._exceptions', 'pandas.util.version', 'pandas.compat._optional', 'six', 'six.moves', 'dateutil.tz._common', 'dateutil.tz._factories', 'dateutil.tz.tz', 'dateutil.tz', 'pandas._libs.tslibs.timezones', 'pandas._libs.tslibs.ccalendar', 'locale', 'calendar', '_strptime', 'pandas._config.config', 'pandas._config.dates', 'pandas._config.display', 'pandas._config', 'signal', '_posixsubprocess', 'select', 'selectors', 'subprocess', 'pandas._config.localization', 'pandas._libs.tslibs.fields', 'pandas._libs.tslibs.timedeltas', 'pandas._libs.tslibs.tzconversion', 'pandas._libs.tslibs.timestamps', 'dateutil.easter', 'dateutil._common', 'dateutil.relativedelta', 'pandas._libs.properties', 'pandas._libs.tslibs.offsets', '_decimal', 'decimal', 'dateutil.parser._parser', 'dateutil.parser.isoparser', 'dateutil.parser', 'pandas._libs.tslibs.strptime', 'pandas._libs.tslibs.parsing', 'pandas._libs.tslibs.conversion', 'pandas._libs.tslibs.period', 'pandas._libs.tslibs.vectorized', 'pandas._libs.tslibs', 'pandas._libs.ops_dispatch', 'pandas._libs.missing', 'pandas._libs.hashtable', 'pandas._libs.algos', 'pandas._libs.interval', 'pandas._libs', 'pandas.util._decorators', 'pandas.core', 'pandas.core.util', 'pandas._libs.lib', 'pandas._libs.hashing', 'pandas.core.dtypes', 'pandas.errors', 'pandas.core.dtypes.generic', 'pandas.core.dtypes.base', 'pandas.core.dtypes.inference', 'pandas.core.dtypes.dtypes', 'pandas.core.dtypes.common', 'pandas.core.util.hashing', 'pandas.util', 'pandas.compat.numpy', 'pandas.compat.pyarrow', 'pandas.compat', 'pandas._libs.tslib', 'pandas.core.config_init', 'pandas.core.dtypes.missing', 'pandas.io', 'pandas.io._util', 'pandas.core.dtypes.cast', 'pandas.core.dtypes.astype', 'pandas.core.dtypes.concat', 'pandas.core.array_algos', 'pandas.core.common', 'pandas.core.construction', 'pandas.core.array_algos.take', 'pandas.core.indexers.utils', 'pandas.core.indexers', 'pandas.core.algorithms', 'unicodedata', 'pandas.util._validators', 'pandas.core.roperator', 'pandas._libs.ops', 'pandas.core.computation', 'pandas.core.computation.check', 'pandas.core.computation.expressions', 'pandas.core.ops.missing', 'pandas.core.ops.dispatch', 'pandas.core.ops.invalid', 'pandas.core.ops.array_ops', 'pandas.core.ops.common', 'pandas.core.ops.docstrings', 'pandas.core.ops.mask_ops', 'pandas.core.ops.methods', 'pandas.core.ops', 'pandas.util._str_methods', 'pandas.core.arraylike', 'pandas.compat.numpy.function', 'pandas.core.missing', 'pandas.core.array_algos.quantile', 'pandas.core.sorting', 'pandas.core.arrays.base', 'pandas.core.strings', 'pandas.core.strings.base', 'pandas.tseries', 'pandas.tseries.frequencies', 'pandas.core.arrays.arrow.array', 'pandas.core.arrays.arrow.dtype', 'pandas.core.arrays.arrow', 'pandas.core.array_algos.masked_accumulations', 'pandas.core.nanops', 'pandas.core.array_algos.masked_reductions', 'pandas.core.arrays.masked', 'pandas.core.arrays.boolean', '_csv', 'csv', 'zlib', 'pwd', 'grp', 'shutil', 'pandas._libs.arrays', 'pandas.core.accessor', 'pandas.core.array_algos.transforms', 'pandas.core.arrays._mixins', 'pandas.core.base', 'pandas.core.strings.object_array', 'pandas.io.formats', 'pandas.io.formats.console', 'pandas.core.arrays.categorical', 'pandas.core.array_algos.datetimelike_accumulations', 'pandas.core.arrays.numeric', 'pandas.core.arrays.integer', 'pandas.core.arrays.datetimelike', 'pandas.core.arrays._ranges', 'pandas.tseries.offsets', 'pandas.core.arrays.datetimes', 'pandas.core.arrays.floating', 'pandas.core.arrays.timedeltas', 'pandas.core.arrays.interval', 'pandas.core.arrays.numpy_', 'pandas.core.arrays.period', 'pandas._libs.sparse', 'pandas.core.arrays.sparse.dtype', 'pandas.io.formats.printing', 'pandas.core.arrays.sparse.array', 'pandas.core.arrays.sparse.accessor', 'pandas.core.arrays.sparse', 'pandas.core.arrays.string_', 'pandas.core.arrays.string_arrow', 'pandas.core.arrays', 'pandas.core.flags', 'pandas._libs.reduction', 'pandas.core.apply', 'gc', 'pandas._libs.indexing', 'pandas.core.indexes', 'pandas._libs.index', 'pandas._libs.internals', 'pandas._libs.join', 'pandas.core.array_algos.putmask', 'pandas.core.indexes.frozen', 'pandas.core.strings.accessor', 'pandas.core.indexes.base', 'pandas.core.indexes.extension', 'pandas.core.indexes.category', 'pandas.core.indexes.range', 'pandas.core.tools', 'pandas.core.tools.timedeltas', 'pandas.core.indexes.datetimelike', 'pandas.core.tools.times', 'pandas.core.indexes.datetimes', 'pandas.core.indexes.multi', 'pandas.core.indexes.timedeltas', 'pandas.core.indexes.interval', 'pandas.core.indexes.period', 'pandas.core.indexes.api', 'pandas.core.indexing', 'pandas.core.sample', 'pandas.core.array_algos.replace', 'pandas._libs.writers', 'pandas.core.internals.blocks', 'pandas.core.internals.api', 'pandas.core.internals.base', 'pandas.core.internals.array_manager', 'pandas.core.internals.ops', 'pandas.core.internals.managers', 'pandas.core.internals.concat', 'pandas.core.internals', 'pandas.core.internals.construction', 'pandas.core.methods', 'pandas.core.reshape', 'pandas.core.reshape.concat', 'dataclasses', 'gzip', 'mmap', 'tarfile', 'zipfile', 'pandas.core.shared_docs', 'pandas.io.common', 'pandas.io.formats.format', 'pandas.core.methods.describe', 'pandas._libs.window', 'pandas._libs.window.aggregations', 'pandas._libs.window.indexers', 'pandas.core.indexers.objects', 'pandas.core.util.numba_', 'pandas.core.window.common', 'pandas.core.window.doc', 'pandas.core.window.numba_', 'pandas.core.window.online', 'pandas.core._numba', 'pandas.core._numba.executor', 'pandas.core.window.rolling', 'pandas.core.window.ewm', 'pandas.core.window.expanding', 'pandas.core.window', 'pandas.core.generic', 'pandas.core.methods.selectn', 'pandas.core.reshape.util', 'pandas.core.tools.numeric', 'pandas.core.reshape.melt', 'pandas._libs.reshape', 'pandas.core.indexes.accessors', 'pandas.arrays', 'pandas.core.tools.datetimes', 'pandas.io.formats.info', 'pandas.plotting._core', 'pandas.plotting._misc', 'pandas.plotting', 'pandas.core.series', 'pandas.core.frame', 'pandas.core.groupby.base', 'pandas._libs.groupby', 'pandas.core.groupby.numba_', 'pandas.core.groupby.categorical', 'pandas.core.groupby.grouper', 'pandas.core.groupby.ops', 'pandas.core.groupby.indexing', 'pandas.core.groupby.groupby', 'pandas.core.groupby.generic', 'pandas.core.groupby', 'pandas.core.api', 'pandas.tseries.api', 'pandas.core.computation.common', 'pandas.core.computation.align', 'pprint', 'pandas.core.computation.scope', 'pandas.core.computation.ops', 'pandas.core.computation.engines', 'pandas.core.computation.parsing', 'pandas.core.computation.expr', 'pandas.core.computation.eval', 'pandas.core.computation.api', 'pandas.core.reshape.encoding', '_uuid', 'uuid', 'pandas.core.reshape.merge', 'pandas.core.reshape.pivot', 'pandas.core.reshape.tile', 'pandas.core.reshape.api', 'pandas.api.extensions', 'pandas.api.indexers', 'pandas.core.interchange', 'pandas.core.interchange.dataframe_protocol', 'pandas.core.dtypes.api', 'pandas.api.types', 'pandas.core.interchange.utils', 'pandas.core.interchange.from_dataframe', 'pandas.api.interchange', 'pandas.api', '_socket', 'socket', 'pandas._testing._random', 'tempfile', 'pandas._testing.contexts', 'pandas._testing._io', 'pandas._testing._warnings', 'cmath', 'pandas._libs.testing', 'pandas._testing.asserters', 'pandas._testing.compat', 'pandas._testing', 'pandas.testing', 'pandas.util._print_versions', 'pandas.io.clipboards', 'pandas._libs.parsers', 'pandas.io.excel._util', 'pandas.io.parsers.base_parser', 'pandas.io.parsers.arrow_parser_wrapper', 'pandas.io.parsers.c_parser_wrapper', 'pandas.io.parsers.python_parser', 'pandas.io.parsers.readers', 'pandas.io.parsers', 'pandas.io.excel._odfreader', 'pandas.io.excel._openpyxl', 'pandas.io.excel._pyxlsb', 'pandas.io.excel._xlrd', 'pandas.io.excel._base', 'pandas._libs.json', 'pandas.io.excel._odswriter', 'pandas.io.excel._xlsxwriter', 'pandas.io.excel', 'pandas.io.feather_format', 'pandas.io.gbq', 'pandas.io.html', 'pandas.io.json._normalize', 'pandas.io.json._table_schema', 'pandas.io.json._json', 'pandas.io.json', 'pandas.io.orc', 'pandas.io.parquet', 'pandas.compat.pickle_compat', 'pandas.io.pickle', 'pandas.core.computation.pytables', 'pandas.io.pytables', 'pandas.io.sas.sasreader', 'pandas.io.sas', 'pandas.io.spss', 'pandas.io.sql', 'pandas.io.stata', 'pandas.io.xml', 'pandas.io.api', 'pandas.util._tester', 'pandas._version', 'pandas', 'gettext', 'argparse', '_cffi_backend', 'libmodbampy.lib', 'libmodbampy', 'modbampy', 'sturgeon.constants', 'sturgeon.utils', 'sturgeon.callmapping', 'sturgeon', 'sturgeon.logger', 'sturgeon.main', 'sturgeon.parsers', 'sturgeon.cli', 'sturgeon.cli.predict', 'onnxruntime.capi', 'onnxruntime.capi._ld_preload', 'onnxruntime.capi.onnxruntime_pybind11_state', 'onnxruntime.capi._pybind_state', 'onnxruntime.capi.onnxruntime_validation', 'onnxruntime.capi.onnxruntime_inference_collection', 'onnxruntime.capi.training', 'onnxruntime', 'sturgeon.prediction', 'sturgeon.plot', 'matplotlib', 'packaging', 'packaging._structures', 'packaging.version', 'matplotlib._api.deprecation', 'matplotlib._api', 'matplotlib._version', 'shlex', 'matplotlib._c_internal_utils', 'matplotlib.cbook', 'matplotlib.docstring', 'PIL._version', 'PIL', 'PIL.ExifTags', 'PIL._deprecate', 'PIL.ImageMode', 'PIL.TiffTags', 'PIL._binary', 'numpy._typing._nested_sequence', 'numpy._typing._nbit', 'numpy._typing._char_codes', 'numpy._typing._scalars', 'numpy._typing._shape', 'numpy._typing._generic_alias', 'numpy._typing._dtype_like', 'numpy._typing._array_like', 'numpy._typing', 'numpy._typing._add_docstring', 'numpy.typing', 'PIL._typing', 'PIL._util', 'PIL._imaging', 'cffi.lock', 'cffi.error', 'cffi.model', 'cffi.api', 'cffi', 'PIL.Image', 'PIL.ImageChops', 'PIL.ImageFile', 'array', 'PIL.GimpGradientFile', 'PIL.GimpPaletteFile', 'PIL.ImageColor', 'PIL.PaletteFile', 'PIL.ImagePalette', 'PIL.ImageSequence', 'PIL.PngImagePlugin', 'matplotlib._path', 'matplotlib.bezier', 'matplotlib.path', 'matplotlib.transforms', 'matplotlib.ticker', 'matplotlib.scale', 'matplotlib._color_data', 'matplotlib.colors', 'pyparsing.util', 'pyparsing.unicode', 'pyparsing.exceptions', 'pyparsing.actions', 'pyparsing.results', 'pyparsing.core', 'html.entities', 'html', 'pyparsing.helpers', 'pyparsing.testing', 'pyparsing.common', 'pyparsing', 'matplotlib.fontconfig_pattern', 'matplotlib._enums', 'cycler', 'matplotlib.rcsetup', 'matplotlib.ft2font', 'kiwisolver.exceptions', 'kiwisolver._cext', 'kiwisolver']
2024-10-07 17:14:12,563 - matplotlib   - DEBUG    - CACHEDIR=/u/abahcheli/.cache/matplotlib
2024-10-07 17:14:12,593 - matplotlib.font_manager - DEBUG    - Using fontManager instance from /u/abahcheli/.cache/matplotlib/fontlist-v330.json
2024-10-07 17:14:16,760 - matplotlib.pyplot - DEBUG    - Loaded backend agg version unknown.
2024-10-07 17:14:16,761 - matplotlib.pyplot - DEBUG    - Loaded backend agg version unknown.
2024-10-07 17:14:23,986 - root         - INFO     - Sturgeon start up
2024-10-07 17:14:23,987 - root         - INFO     - Prediction program
2024-10-07 17:14:23,992 - root         - WARNING  - 
            --output-path /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results contains files. Delete them or provide another path
            
2024-10-07 17:14:23,992 - root         - INFO     - Found a total of 24 bed files
2024-10-07 17:14:23,992 - root         - INFO     - Found a total of 1 model files
2024-10-07 17:14:23,992 - root         - INFO     - Results will be saved in: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results
2024-10-07 17:14:23,992 - root         - INFO     - Validating model: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/general.zip
2024-10-07 17:14:24,149 - root         - INFO     - Successful model validation
2024-10-07 17:14:24,149 - root         - INFO     - Starting prediction
2024-10-07 17:14:24,322 - root         - DEBUG    - Loading probes information
2024-10-07 17:14:26,256 - root         - DEBUG    - Loading the decoding dict
2024-10-07 17:14:26,265 - root         - DEBUG    - Loading calibration matrix
2024-10-07 17:14:26,271 - root         - INFO     - Starting inference session
2024-10-07 17:15:00,116 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS8-primary-merged_probes_methyl_calls.bed
2024-10-07 17:15:00,793 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:15:00,794 - root         - INFO     - Number of not measured probes:                 36646 (8.55%)
2024-10-07 17:15:00,795 - root         - INFO     - Number of measured probes:                    391997 (91.45%)
2024-10-07 17:15:00,795 - root         - INFO     - Number of measured methylated probes:         180644 (46.08%)
2024-10-07 17:15:00,796 - root         - INFO     - Number of measured non-methylated probes:     211353 (53.92%)
2024-10-07 17:15:01,048 - root         - INFO     - Top 1: Glioma IDH - A IDH - HG        (0.702)
2024-10-07 17:15:01,048 - root         - INFO     - Top 2: Glioblastoma - GBM - RTK I     (0.231)
2024-10-07 17:15:01,048 - root         - INFO     - Top 3: Other glioma - ANA PA - ANA PA (0.018)
2024-10-07 17:15:01,048 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS8-primary-merged_probes_methyl_calls_general.csv
2024-10-07 17:15:01,060 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS8-primary-merged_probes_methyl_calls_general.pdf
2024-10-07 17:15:01,061 - root         - DEBUG    - Loading colors dict
2024-10-07 17:15:01,067 - matplotlib.font_manager - DEBUG    - findfont: Matching sans\-serif:style=normal:variant=normal:weight=normal:stretch=normal:size=8.0.
2024-10-07 17:15:01,068 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Bold.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,068 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/cmr10.ttf', name='cmr10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,068 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/cmmi10.ttf', name='cmmi10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,068 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymBol.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,068 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Italic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2024-10-07 17:15:01,069 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizOneSymReg.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,069 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/cmb10.ttf', name='cmb10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,069 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,069 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerifDisplay.ttf', name='DejaVu Serif Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,069 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizOneSymBol.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,069 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUniBolIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2024-10-07 17:15:01,069 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneralBol.ttf', name='STIXGeneral', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,069 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizFourSymBol.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,069 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymReg.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,069 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansDisplay.ttf', name='DejaVu Sans Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,069 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/cmtt10.ttf', name='cmtt10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,069 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans-BoldOblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 1.335
2024-10-07 17:15:01,070 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymReg.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,070 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 0.05
2024-10-07 17:15:01,070 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneralBolIta.ttf', name='STIXGeneral', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2024-10-07 17:15:01,070 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneralItalic.ttf', name='STIXGeneral', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2024-10-07 17:15:01,070 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,070 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/cmex10.ttf', name='cmex10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,070 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Bold.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,070 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans-Bold.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 0.33499999999999996
2024-10-07 17:15:01,070 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/cmsy10.ttf', name='cmsy10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,070 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/cmss10.ttf', name='cmss10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,070 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUniIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2024-10-07 17:15:01,070 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneral.ttf', name='STIXGeneral', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,071 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymBol.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,071 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif-BoldItalic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2024-10-07 17:15:01,071 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUni.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,071 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-BoldOblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2024-10-07 17:15:01,071 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizFourSymReg.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,071 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizFiveSymReg.ttf', name='STIXSizeFiveSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,071 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUniBol.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,071 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Oblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2024-10-07 17:15:01,071 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans-Oblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 1.05
2024-10-07 17:15:01,071 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 0.33499999999999996
2024-10-07 17:15:01,071 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSerif-Bold.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,071 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 0.05
2024-10-07 17:15:01,071 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSerif.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,072 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSansMono-Bold.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,072 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSansMono.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,072 - matplotlib.font_manager - DEBUG    - findfont: Matching sans\-serif:style=normal:variant=normal:weight=normal:stretch=normal:size=8.0 to DejaVu Sans ('/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf') with score of 0.050000.
2024-10-07 17:15:01,609 - matplotlib.font_manager - DEBUG    - findfont: Matching sans\-serif:style=normal:variant=normal:weight=normal:stretch=normal:size=12.0.
2024-10-07 17:15:01,610 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Bold.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,610 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/cmr10.ttf', name='cmr10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,610 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/cmmi10.ttf', name='cmmi10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,610 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymBol.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,610 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Italic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2024-10-07 17:15:01,610 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizOneSymReg.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,610 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/cmb10.ttf', name='cmb10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,610 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,610 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerifDisplay.ttf', name='DejaVu Serif Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,610 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizOneSymBol.ttf', name='STIXSizeOneSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,610 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUniBolIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2024-10-07 17:15:01,610 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneralBol.ttf', name='STIXGeneral', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,610 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizFourSymBol.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,610 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymReg.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,611 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansDisplay.ttf', name='DejaVu Sans Display', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,611 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/cmtt10.ttf', name='cmtt10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,611 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans-BoldOblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 1.335
2024-10-07 17:15:01,611 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizTwoSymReg.ttf', name='STIXSizeTwoSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,611 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 0.05
2024-10-07 17:15:01,611 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneralBolIta.ttf', name='STIXGeneral', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2024-10-07 17:15:01,611 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneralItalic.ttf', name='STIXGeneral', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2024-10-07 17:15:01,611 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,611 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/cmex10.ttf', name='cmex10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,611 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif-Bold.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,611 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans-Bold.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 0.33499999999999996
2024-10-07 17:15:01,611 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/cmsy10.ttf', name='cmsy10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,611 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/cmss10.ttf', name='cmss10', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,611 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUniIta.ttf', name='STIXNonUnicode', style='italic', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2024-10-07 17:15:01,611 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXGeneral.ttf', name='STIXGeneral', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,611 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizThreeSymBol.ttf', name='STIXSizeThreeSym', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,612 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSerif-BoldItalic.ttf', name='DejaVu Serif', style='italic', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2024-10-07 17:15:01,612 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUni.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,612 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-BoldOblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=700, stretch='normal', size='scalable')) = 11.335
2024-10-07 17:15:01,612 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizFourSymReg.ttf', name='STIXSizeFourSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,612 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXSizFiveSymReg.ttf', name='STIXSizeFiveSym', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,612 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/STIXNonUniBol.ttf', name='STIXNonUnicode', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,612 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSansMono-Oblique.ttf', name='DejaVu Sans Mono', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 11.05
2024-10-07 17:15:01,612 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans-Oblique.ttf', name='DejaVu Sans', style='oblique', variant='normal', weight=400, stretch='normal', size='scalable')) = 1.05
2024-10-07 17:15:01,612 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 0.33499999999999996
2024-10-07 17:15:01,612 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSerif-Bold.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,612 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf', name='DejaVu Sans', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 0.05
2024-10-07 17:15:01,612 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSerif.ttf', name='DejaVu Serif', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,612 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSansMono-Bold.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=700, stretch='normal', size='scalable')) = 10.335
2024-10-07 17:15:01,612 - matplotlib.font_manager - DEBUG    - findfont: score(FontEntry(fname='/usr/share/fonts/truetype/dejavu/DejaVuSansMono.ttf', name='DejaVu Sans Mono', style='normal', variant='normal', weight=400, stretch='normal', size='scalable')) = 10.05
2024-10-07 17:15:01,612 - matplotlib.font_manager - DEBUG    - findfont: Matching sans\-serif:style=normal:variant=normal:weight=normal:stretch=normal:size=12.0 to DejaVu Sans ('/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf') with score of 0.050000.
2024-10-07 17:15:04,521 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:15:05,162 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:15:05,162 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:15:06,199 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS1-primary-merged_probes_methyl_calls.bed
2024-10-07 17:15:06,825 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:15:06,825 - root         - INFO     - Number of not measured probes:                 20327 (4.74%)
2024-10-07 17:15:06,826 - root         - INFO     - Number of measured probes:                    408316 (95.26%)
2024-10-07 17:15:06,827 - root         - INFO     - Number of measured methylated probes:         217390 (53.24%)
2024-10-07 17:15:06,827 - root         - INFO     - Number of measured non-methylated probes:     190926 (46.76%)
2024-10-07 17:15:07,035 - root         - INFO     - Top 1: Glioblastoma - GBM - MES       (0.981)
2024-10-07 17:15:07,035 - root         - INFO     - Top 2: Glioblastoma - GBM - RTK II    (0.012)
2024-10-07 17:15:07,035 - root         - INFO     - Top 3: Other glioma - PXA - PXA       (0.007)
2024-10-07 17:15:07,035 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS1-primary-merged_probes_methyl_calls_general.csv
2024-10-07 17:15:07,041 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS1-primary-merged_probes_methyl_calls_general.pdf
2024-10-07 17:15:07,042 - root         - DEBUG    - Loading colors dict
2024-10-07 17:15:10,073 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:15:10,725 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:15:10,725 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:15:11,730 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS6-recurrent-merged_probes_methyl_calls.bed
2024-10-07 17:15:12,415 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:15:12,416 - root         - INFO     - Number of not measured probes:                 38509 (8.98%)
2024-10-07 17:15:12,417 - root         - INFO     - Number of measured probes:                    390134 (91.02%)
2024-10-07 17:15:12,417 - root         - INFO     - Number of measured methylated probes:         180956 (46.38%)
2024-10-07 17:15:12,418 - root         - INFO     - Number of measured non-methylated probes:     209178 (53.62%)
2024-10-07 17:15:12,610 - root         - INFO     - Top 1: Glioblastoma - GBM - MID       (0.869)
2024-10-07 17:15:12,610 - root         - INFO     - Top 2: Glioblastoma - DMG - K27       (0.069)
2024-10-07 17:15:12,610 - root         - INFO     - Top 3: Glioma IDH - A IDH - HG        (0.046)
2024-10-07 17:15:12,610 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS6-recurrent-merged_probes_methyl_calls_general.csv
2024-10-07 17:15:12,615 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS6-recurrent-merged_probes_methyl_calls_general.pdf
2024-10-07 17:15:12,616 - root         - DEBUG    - Loading colors dict
2024-10-07 17:15:15,618 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:15:16,276 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:15:16,276 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:15:17,270 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS12-primary-merged_probes_methyl_calls.bed
2024-10-07 17:15:18,062 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:15:18,063 - root         - INFO     - Number of not measured probes:                 35910 (8.38%)
2024-10-07 17:15:18,063 - root         - INFO     - Number of measured probes:                    392733 (91.62%)
2024-10-07 17:15:18,064 - root         - INFO     - Number of measured methylated probes:         204243 (52.01%)
2024-10-07 17:15:18,065 - root         - INFO     - Number of measured non-methylated probes:     188490 (47.99%)
2024-10-07 17:15:18,255 - root         - INFO     - Top 1: Glioblastoma - GBM - RTK II    (0.967)
2024-10-07 17:15:18,255 - root         - INFO     - Top 2: Glioblastoma - GBM - MES       (0.031)
2024-10-07 17:15:18,255 - root         - INFO     - Top 3: Glioblastoma - GBM - RTK I     (0.002)
2024-10-07 17:15:18,255 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS12-primary-merged_probes_methyl_calls_general.csv
2024-10-07 17:15:18,260 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS12-primary-merged_probes_methyl_calls_general.pdf
2024-10-07 17:15:18,260 - root         - DEBUG    - Loading colors dict
2024-10-07 17:15:21,326 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:15:21,956 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:15:21,956 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:15:22,954 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS4-primary-merged_probes_methyl_calls.bed
2024-10-07 17:15:23,594 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:15:23,595 - root         - INFO     - Number of not measured probes:                 33117 (7.73%)
2024-10-07 17:15:23,595 - root         - INFO     - Number of measured probes:                    395526 (92.27%)
2024-10-07 17:15:23,596 - root         - INFO     - Number of measured methylated probes:         199384 (50.41%)
2024-10-07 17:15:23,596 - root         - INFO     - Number of measured non-methylated probes:     196142 (49.59%)
2024-10-07 17:15:23,776 - root         - INFO     - Top 1: Glioblastoma - GBM - RTK I     (0.979)
2024-10-07 17:15:23,776 - root         - INFO     - Top 2: Glioma IDH - A IDH - HG        (0.016)
2024-10-07 17:15:23,776 - root         - INFO     - Top 3: Glioblastoma - GBM - RTK II    (0.002)
2024-10-07 17:15:23,776 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS4-primary-merged_probes_methyl_calls_general.csv
2024-10-07 17:15:23,782 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS4-primary-merged_probes_methyl_calls_general.pdf
2024-10-07 17:15:23,782 - root         - DEBUG    - Loading colors dict
2024-10-07 17:15:26,661 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:15:27,289 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:15:27,289 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:15:28,252 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS2-primary-merged_probes_methyl_calls.bed
2024-10-07 17:15:28,937 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:15:28,937 - root         - INFO     - Number of not measured probes:                 30466 (7.11%)
2024-10-07 17:15:28,938 - root         - INFO     - Number of measured probes:                    398177 (92.89%)
2024-10-07 17:15:28,938 - root         - INFO     - Number of measured methylated probes:         203441 (51.09%)
2024-10-07 17:15:28,939 - root         - INFO     - Number of measured non-methylated probes:     194736 (48.91%)
2024-10-07 17:15:29,126 - root         - INFO     - Top 1: Glioblastoma - GBM - MES       (0.993)
2024-10-07 17:15:29,127 - root         - INFO     - Top 2: Glioblastoma - GBM - RTK II    (0.005)
2024-10-07 17:15:29,127 - root         - INFO     - Top 3: Other glioma - PXA - PXA       (0.001)
2024-10-07 17:15:29,127 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS2-primary-merged_probes_methyl_calls_general.csv
2024-10-07 17:15:29,132 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS2-primary-merged_probes_methyl_calls_general.pdf
2024-10-07 17:15:29,132 - root         - DEBUG    - Loading colors dict
2024-10-07 17:15:32,150 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:15:32,783 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:15:32,783 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:15:33,800 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS6-primary-merged_probes_methyl_calls.bed
2024-10-07 17:15:34,395 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:15:34,396 - root         - INFO     - Number of not measured probes:                 22515 (5.25%)
2024-10-07 17:15:34,396 - root         - INFO     - Number of measured probes:                    406128 (94.75%)
2024-10-07 17:15:34,397 - root         - INFO     - Number of measured methylated probes:         187751 (46.23%)
2024-10-07 17:15:34,397 - root         - INFO     - Number of measured non-methylated probes:     218377 (53.77%)
2024-10-07 17:15:34,585 - root         - INFO     - Top 1: Glioblastoma - GBM - MID       (0.856)
2024-10-07 17:15:34,585 - root         - INFO     - Top 2: Glioma IDH - A IDH - HG        (0.102)
2024-10-07 17:15:34,585 - root         - INFO     - Top 3: Glioblastoma - DMG - K27       (0.027)
2024-10-07 17:15:34,586 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS6-primary-merged_probes_methyl_calls_general.csv
2024-10-07 17:15:34,590 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS6-primary-merged_probes_methyl_calls_general.pdf
2024-10-07 17:15:34,591 - root         - DEBUG    - Loading colors dict
2024-10-07 17:15:37,573 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:15:38,192 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:15:38,192 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:15:39,151 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS8-recurrent-merged_probes_methyl_calls.bed
2024-10-07 17:15:39,773 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:15:39,774 - root         - INFO     - Number of not measured probes:                 19995 (4.66%)
2024-10-07 17:15:39,774 - root         - INFO     - Number of measured probes:                    408648 (95.34%)
2024-10-07 17:15:39,774 - root         - INFO     - Number of measured methylated probes:         212832 (52.08%)
2024-10-07 17:15:39,775 - root         - INFO     - Number of measured non-methylated probes:     195816 (47.92%)
2024-10-07 17:15:39,963 - root         - INFO     - Top 1: Glioma IDH - A IDH - A IDH     (0.632)
2024-10-07 17:15:39,963 - root         - INFO     - Top 2: Glioma IDH - A IDH - HG        (0.143)
2024-10-07 17:15:39,963 - root         - INFO     - Top 3: Other glioma - ANA PA - ANA PA (0.095)
2024-10-07 17:15:39,964 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS8-recurrent-merged_probes_methyl_calls_general.csv
2024-10-07 17:15:39,968 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS8-recurrent-merged_probes_methyl_calls_general.pdf
2024-10-07 17:15:39,969 - root         - DEBUG    - Loading colors dict
2024-10-07 17:15:42,973 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:15:43,641 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:15:43,641 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:15:44,636 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS3-recurrent-merged_probes_methyl_calls.bed
2024-10-07 17:15:45,134 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:15:45,135 - root         - INFO     - Number of not measured probes:                140961 (32.89%)
2024-10-07 17:15:45,135 - root         - INFO     - Number of measured probes:                    287682 (67.11%)
2024-10-07 17:15:45,136 - root         - INFO     - Number of measured methylated probes:         135961 (47.26%)
2024-10-07 17:15:45,136 - root         - INFO     - Number of measured non-methylated probes:     151721 (52.74%)
2024-10-07 17:15:45,379 - root         - INFO     - Top 1: Sella - PITUI SCO GCT - SCO GCT (0.627)
2024-10-07 17:15:45,379 - root         - INFO     - Top 2: Control - CONTR - REACT        (0.193)
2024-10-07 17:15:45,380 - root         - INFO     - Top 3: Melanocytic - MELCYT - MELCYT  (0.046)
2024-10-07 17:15:45,382 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS3-recurrent-merged_probes_methyl_calls_general.csv
2024-10-07 17:15:45,387 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS3-recurrent-merged_probes_methyl_calls_general.pdf
2024-10-07 17:15:45,388 - root         - DEBUG    - Loading colors dict
2024-10-07 17:15:48,352 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:15:48,986 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:15:48,986 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:15:49,962 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS5-primary-merged_probes_methyl_calls.bed
2024-10-07 17:15:50,629 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:15:50,629 - root         - INFO     - Number of not measured probes:                 30334 (7.08%)
2024-10-07 17:15:50,630 - root         - INFO     - Number of measured probes:                    398309 (92.92%)
2024-10-07 17:15:50,630 - root         - INFO     - Number of measured methylated probes:         217603 (54.63%)
2024-10-07 17:15:50,631 - root         - INFO     - Number of measured non-methylated probes:     180706 (45.37%)
2024-10-07 17:15:50,818 - root         - INFO     - Top 1: Glioblastoma - GBM - RTK II    (0.952)
2024-10-07 17:15:50,818 - root         - INFO     - Top 2: Glioblastoma - GBM - MES       (0.023)
2024-10-07 17:15:50,818 - root         - INFO     - Top 3: Glioblastoma - GBM - RTK I     (0.020)
2024-10-07 17:15:50,818 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS5-primary-merged_probes_methyl_calls_general.csv
2024-10-07 17:15:50,838 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS5-primary-merged_probes_methyl_calls_general.pdf
2024-10-07 17:15:50,839 - root         - DEBUG    - Loading colors dict
2024-10-07 17:15:53,838 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:15:54,481 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:15:54,481 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:15:55,475 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS12-recurrent-merged_probes_methyl_calls.bed
2024-10-07 17:15:56,136 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:15:56,136 - root         - INFO     - Number of not measured probes:                 34427 (8.03%)
2024-10-07 17:15:56,137 - root         - INFO     - Number of measured probes:                    394216 (91.97%)
2024-10-07 17:15:56,137 - root         - INFO     - Number of measured methylated probes:         205835 (52.21%)
2024-10-07 17:15:56,138 - root         - INFO     - Number of measured non-methylated probes:     188381 (47.79%)
2024-10-07 17:15:56,333 - root         - INFO     - Top 1: Glioblastoma - GBM - RTK II    (0.982)
2024-10-07 17:15:56,333 - root         - INFO     - Top 2: Glioblastoma - GBM - MES       (0.014)
2024-10-07 17:15:56,333 - root         - INFO     - Top 3: Glioblastoma - GBM - RTK I     (0.003)
2024-10-07 17:15:56,334 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS12-recurrent-merged_probes_methyl_calls_general.csv
2024-10-07 17:15:56,338 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS12-recurrent-merged_probes_methyl_calls_general.pdf
2024-10-07 17:15:56,339 - root         - DEBUG    - Loading colors dict
2024-10-07 17:15:59,442 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:16:00,129 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:16:00,129 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:16:01,151 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS4-recurrent-merged_probes_methyl_calls.bed
2024-10-07 17:16:01,849 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:16:01,850 - root         - INFO     - Number of not measured probes:                 30690 (7.16%)
2024-10-07 17:16:01,850 - root         - INFO     - Number of measured probes:                    397953 (92.84%)
2024-10-07 17:16:01,851 - root         - INFO     - Number of measured methylated probes:         203345 (51.10%)
2024-10-07 17:16:01,851 - root         - INFO     - Number of measured non-methylated probes:     194608 (48.90%)
2024-10-07 17:16:02,050 - root         - INFO     - Top 1: Control - CONTR - INFLAM       (0.999)
2024-10-07 17:16:02,050 - root         - INFO     - Top 2: Control - CONTR - REACT        (0.000)
2024-10-07 17:16:02,050 - root         - INFO     - Top 3: Other glioma - LGG SEGA - SEGA (0.000)
2024-10-07 17:16:02,050 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS4-recurrent-merged_probes_methyl_calls_general.csv
2024-10-07 17:16:02,054 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS4-recurrent-merged_probes_methyl_calls_general.pdf
2024-10-07 17:16:02,055 - root         - DEBUG    - Loading colors dict
2024-10-07 17:16:05,081 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:16:05,748 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:16:05,748 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:16:06,786 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS7-recurrent-merged_probes_methyl_calls.bed
2024-10-07 17:16:07,465 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:16:07,465 - root         - INFO     - Number of not measured probes:                 50920 (11.88%)
2024-10-07 17:16:07,466 - root         - INFO     - Number of measured probes:                    377723 (88.12%)
2024-10-07 17:16:07,467 - root         - INFO     - Number of measured methylated probes:         185928 (49.22%)
2024-10-07 17:16:07,467 - root         - INFO     - Number of measured non-methylated probes:     191795 (50.78%)
2024-10-07 17:16:07,697 - root         - INFO     - Top 1: Control - CONTR - HEMI         (0.747)
2024-10-07 17:16:07,697 - root         - INFO     - Top 2: Glio-neuronal - DLGNT - DLGNT  (0.084)
2024-10-07 17:16:07,697 - root         - INFO     - Top 3: Sella - PITUI SCO GCT - SCO GCT (0.039)
2024-10-07 17:16:07,698 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS7-recurrent-merged_probes_methyl_calls_general.csv
2024-10-07 17:16:07,702 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS7-recurrent-merged_probes_methyl_calls_general.pdf
2024-10-07 17:16:07,703 - root         - DEBUG    - Loading colors dict
2024-10-07 17:16:10,716 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:16:11,356 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:16:11,357 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:16:12,346 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS10-recurrent-merged_probes_methyl_calls.bed
2024-10-07 17:16:12,973 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:16:12,973 - root         - INFO     - Number of not measured probes:                 34431 (8.03%)
2024-10-07 17:16:12,974 - root         - INFO     - Number of measured probes:                    394212 (91.97%)
2024-10-07 17:16:12,974 - root         - INFO     - Number of measured methylated probes:         208696 (52.94%)
2024-10-07 17:16:12,975 - root         - INFO     - Number of measured non-methylated probes:     185516 (47.06%)
2024-10-07 17:16:13,251 - root         - INFO     - Top 1: Glioblastoma - GBM - MES       (0.841)
2024-10-07 17:16:13,251 - root         - INFO     - Top 2: Glioma IDH - A IDH - A IDH     (0.079)
2024-10-07 17:16:13,251 - root         - INFO     - Top 3: Glioblastoma - GBM - RTK II    (0.032)
2024-10-07 17:16:13,251 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS10-recurrent-merged_probes_methyl_calls_general.csv
2024-10-07 17:16:13,257 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS10-recurrent-merged_probes_methyl_calls_general.pdf
2024-10-07 17:16:13,257 - root         - DEBUG    - Loading colors dict
2024-10-07 17:16:16,308 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:16:16,946 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:16:16,946 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:16:17,935 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS5-recurrent-merged_probes_methyl_calls.bed
2024-10-07 17:16:18,586 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:16:18,587 - root         - INFO     - Number of not measured probes:                 35094 (8.19%)
2024-10-07 17:16:18,587 - root         - INFO     - Number of measured probes:                    393549 (91.81%)
2024-10-07 17:16:18,588 - root         - INFO     - Number of measured methylated probes:         203223 (51.64%)
2024-10-07 17:16:18,588 - root         - INFO     - Number of measured non-methylated probes:     190326 (48.36%)
2024-10-07 17:16:18,773 - root         - INFO     - Top 1: Control - CONTR - HEMI         (0.808)
2024-10-07 17:16:18,773 - root         - INFO     - Top 2: Sella - PITUI SCO GCT - SCO GCT (0.114)
2024-10-07 17:16:18,773 - root         - INFO     - Top 3: Control - CONTR - PONS         (0.015)
2024-10-07 17:16:18,774 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS5-recurrent-merged_probes_methyl_calls_general.csv
2024-10-07 17:16:18,778 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS5-recurrent-merged_probes_methyl_calls_general.pdf
2024-10-07 17:16:18,778 - root         - DEBUG    - Loading colors dict
2024-10-07 17:16:21,783 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:16:22,423 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:16:22,423 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:16:23,395 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS1-recurrent-merged_probes_methyl_calls.bed
2024-10-07 17:16:24,005 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:16:24,005 - root         - INFO     - Number of not measured probes:                 22929 (5.35%)
2024-10-07 17:16:24,006 - root         - INFO     - Number of measured probes:                    405714 (94.65%)
2024-10-07 17:16:24,006 - root         - INFO     - Number of measured methylated probes:         218160 (53.77%)
2024-10-07 17:16:24,007 - root         - INFO     - Number of measured non-methylated probes:     187554 (46.23%)
2024-10-07 17:16:24,190 - root         - INFO     - Top 1: Glioblastoma - GBM - MES       (0.847)
2024-10-07 17:16:24,191 - root         - INFO     - Top 2: Other glioma - PXA - PXA       (0.103)
2024-10-07 17:16:24,191 - root         - INFO     - Top 3: Other glioma - LGG PA - PA/GG ST (0.015)
2024-10-07 17:16:24,191 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS1-recurrent-merged_probes_methyl_calls_general.csv
2024-10-07 17:16:24,196 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS1-recurrent-merged_probes_methyl_calls_general.pdf
2024-10-07 17:16:24,196 - root         - DEBUG    - Loading colors dict
2024-10-07 17:16:26,997 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:16:27,665 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:16:27,665 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:16:28,659 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS9-recurrent-merged_probes_methyl_calls.bed
2024-10-07 17:16:29,338 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:16:29,339 - root         - INFO     - Number of not measured probes:                 39609 (9.24%)
2024-10-07 17:16:29,339 - root         - INFO     - Number of measured probes:                    389034 (90.76%)
2024-10-07 17:16:29,340 - root         - INFO     - Number of measured methylated probes:         198166 (50.94%)
2024-10-07 17:16:29,340 - root         - INFO     - Number of measured non-methylated probes:     190868 (49.06%)
2024-10-07 17:16:29,527 - root         - INFO     - Top 1: Control - CONTR - HEMI         (0.677)
2024-10-07 17:16:29,527 - root         - INFO     - Top 2: Control - CONTR - PONS         (0.172)
2024-10-07 17:16:29,527 - root         - INFO     - Top 3: Control - CONTR - WM           (0.032)
2024-10-07 17:16:29,527 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS9-recurrent-merged_probes_methyl_calls_general.csv
2024-10-07 17:16:29,532 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS9-recurrent-merged_probes_methyl_calls_general.pdf
2024-10-07 17:16:29,533 - root         - DEBUG    - Loading colors dict
2024-10-07 17:16:32,576 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:16:33,226 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:16:33,226 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:16:34,228 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS10-primary-merged_probes_methyl_calls.bed
2024-10-07 17:16:34,883 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:16:34,883 - root         - INFO     - Number of not measured probes:                 34957 (8.16%)
2024-10-07 17:16:34,884 - root         - INFO     - Number of measured probes:                    393686 (91.84%)
2024-10-07 17:16:34,885 - root         - INFO     - Number of measured methylated probes:         202487 (51.43%)
2024-10-07 17:16:34,885 - root         - INFO     - Number of measured non-methylated probes:     191199 (48.57%)
2024-10-07 17:16:35,068 - root         - INFO     - Top 1: Glioblastoma - GBM - MES       (0.928)
2024-10-07 17:16:35,068 - root         - INFO     - Top 2: Glioma IDH - A IDH - A IDH     (0.019)
2024-10-07 17:16:35,068 - root         - INFO     - Top 3: Other glioma - LGG PA - PA     (0.009)
2024-10-07 17:16:35,068 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS10-primary-merged_probes_methyl_calls_general.csv
2024-10-07 17:16:35,073 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS10-primary-merged_probes_methyl_calls_general.pdf
2024-10-07 17:16:35,073 - root         - DEBUG    - Loading colors dict
2024-10-07 17:16:37,886 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:16:38,482 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:16:38,482 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:16:39,396 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS7-primary-merged_probes_methyl_calls.bed
2024-10-07 17:16:40,021 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:16:40,022 - root         - INFO     - Number of not measured probes:                 36566 (8.53%)
2024-10-07 17:16:40,022 - root         - INFO     - Number of measured probes:                    392077 (91.47%)
2024-10-07 17:16:40,023 - root         - INFO     - Number of measured methylated probes:         214968 (54.83%)
2024-10-07 17:16:40,023 - root         - INFO     - Number of measured non-methylated probes:     177109 (45.17%)
2024-10-07 17:16:40,200 - root         - INFO     - Top 1: Glioblastoma - GBM - RTK II    (0.946)
2024-10-07 17:16:40,200 - root         - INFO     - Top 2: Glioblastoma - GBM - MES       (0.021)
2024-10-07 17:16:40,200 - root         - INFO     - Top 3: Glioma IDH - A IDH - HG        (0.017)
2024-10-07 17:16:40,200 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS7-primary-merged_probes_methyl_calls_general.csv
2024-10-07 17:16:40,205 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS7-primary-merged_probes_methyl_calls_general.pdf
2024-10-07 17:16:40,206 - root         - DEBUG    - Loading colors dict
2024-10-07 17:16:42,957 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:16:43,555 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:16:43,555 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:16:44,529 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS2-recurrent-merged_probes_methyl_calls.bed
2024-10-07 17:16:45,395 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:16:45,395 - root         - INFO     - Number of not measured probes:                 26259 (6.13%)
2024-10-07 17:16:45,396 - root         - INFO     - Number of measured probes:                    402384 (93.87%)
2024-10-07 17:16:45,397 - root         - INFO     - Number of measured methylated probes:         208615 (51.84%)
2024-10-07 17:16:45,397 - root         - INFO     - Number of measured non-methylated probes:     193769 (48.16%)
2024-10-07 17:16:45,645 - root         - INFO     - Top 1: Glioblastoma - GBM - RTK II    (0.963)
2024-10-07 17:16:45,645 - root         - INFO     - Top 2: Glioblastoma - GBM - MES       (0.031)
2024-10-07 17:16:45,645 - root         - INFO     - Top 3: Glioblastoma - GBM - RTK I     (0.005)
2024-10-07 17:16:45,647 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS2-recurrent-merged_probes_methyl_calls_general.csv
2024-10-07 17:16:45,652 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS2-recurrent-merged_probes_methyl_calls_general.pdf
2024-10-07 17:16:45,653 - root         - DEBUG    - Loading colors dict
2024-10-07 17:16:48,641 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:16:49,285 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:16:49,286 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:16:50,274 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS3-primary-merged_probes_methyl_calls.bed
2024-10-07 17:16:50,955 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:16:50,956 - root         - INFO     - Number of not measured probes:                 21740 (5.07%)
2024-10-07 17:16:50,956 - root         - INFO     - Number of measured probes:                    406903 (94.93%)
2024-10-07 17:16:50,957 - root         - INFO     - Number of measured methylated probes:         217368 (53.42%)
2024-10-07 17:16:50,957 - root         - INFO     - Number of measured non-methylated probes:     189535 (46.58%)
2024-10-07 17:16:51,236 - root         - INFO     - Top 1: Control - CONTR - INFLAM       (0.999)
2024-10-07 17:16:51,236 - root         - INFO     - Top 2: Control - CONTR - REACT        (0.001)
2024-10-07 17:16:51,236 - root         - INFO     - Top 3: Sella - PITUI SCO GCT - SCO GCT (0.000)
2024-10-07 17:16:51,237 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS3-primary-merged_probes_methyl_calls_general.csv
2024-10-07 17:16:51,241 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS3-primary-merged_probes_methyl_calls_general.pdf
2024-10-07 17:16:51,242 - root         - DEBUG    - Loading colors dict
2024-10-07 17:16:54,217 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:16:54,855 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:16:54,855 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:16:55,852 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS9-primary-merged_probes_methyl_calls.bed
2024-10-07 17:16:56,512 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:16:56,513 - root         - INFO     - Number of not measured probes:                 37761 (8.81%)
2024-10-07 17:16:56,514 - root         - INFO     - Number of measured probes:                    390882 (91.19%)
2024-10-07 17:16:56,514 - root         - INFO     - Number of measured methylated probes:         202220 (51.73%)
2024-10-07 17:16:56,515 - root         - INFO     - Number of measured non-methylated probes:     188662 (48.27%)
2024-10-07 17:16:56,699 - root         - INFO     - Top 1: Control - CONTR - REACT        (0.453)
2024-10-07 17:16:56,699 - root         - INFO     - Top 2: Other glioma - PXA - PXA       (0.264)
2024-10-07 17:16:56,699 - root         - INFO     - Top 3: Other glioma - LGG SEGA - SEGA (0.185)
2024-10-07 17:16:56,700 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS9-primary-merged_probes_methyl_calls_general.csv
2024-10-07 17:16:56,704 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS9-primary-merged_probes_methyl_calls_general.pdf
2024-10-07 17:16:56,705 - root         - DEBUG    - Loading colors dict
2024-10-07 17:16:59,700 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:17:00,346 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:17:00,346 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:17:01,335 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS11-primary-merged_probes_methyl_calls.bed
2024-10-07 17:17:02,099 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:17:02,099 - root         - INFO     - Number of not measured probes:                 35424 (8.26%)
2024-10-07 17:17:02,100 - root         - INFO     - Number of measured probes:                    393219 (91.74%)
2024-10-07 17:17:02,101 - root         - INFO     - Number of measured methylated probes:         215941 (54.92%)
2024-10-07 17:17:02,101 - root         - INFO     - Number of measured non-methylated probes:     177278 (45.08%)
2024-10-07 17:17:02,291 - root         - INFO     - Top 1: Other glioma - LGG SEGA - SEGA (0.924)
2024-10-07 17:17:02,292 - root         - INFO     - Top 2: Glioblastoma - GBM - MES       (0.025)
2024-10-07 17:17:02,292 - root         - INFO     - Top 3: Sella - PITUI SCO GCT - SCO GCT (0.016)
2024-10-07 17:17:02,292 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS11-primary-merged_probes_methyl_calls_general.csv
2024-10-07 17:17:02,297 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS11-primary-merged_probes_methyl_calls_general.pdf
2024-10-07 17:17:02,297 - root         - DEBUG    - Loading colors dict
2024-10-07 17:17:05,299 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:17:05,945 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:17:05,945 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
2024-10-07 17:17:06,917 - root         - INFO     - Loading bed file: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/sturgeon_bed/RLGS11-recurrent-merged_probes_methyl_calls.bed
2024-10-07 17:17:07,705 - root         - INFO     - Total amount of measurable probes:            428643
2024-10-07 17:17:07,705 - root         - INFO     - Number of not measured probes:                 39361 (9.18%)
2024-10-07 17:17:07,706 - root         - INFO     - Number of measured probes:                    389282 (90.82%)
2024-10-07 17:17:07,706 - root         - INFO     - Number of measured methylated probes:         196555 (50.49%)
2024-10-07 17:17:07,707 - root         - INFO     - Number of measured non-methylated probes:     192727 (49.51%)
2024-10-07 17:17:07,946 - root         - INFO     - Top 1: Glioblastoma - GBM - RTK II    (0.632)
2024-10-07 17:17:07,946 - root         - INFO     - Top 2: Glioblastoma - GBM - RTK I     (0.181)
2024-10-07 17:17:07,947 - root         - INFO     - Top 3: Glioblastoma - GBM - MES       (0.132)
2024-10-07 17:17:07,951 - root         - INFO     - Saving results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS11-recurrent-merged_probes_methyl_calls_general.csv
2024-10-07 17:17:07,956 - root         - INFO     - Plotting results to: /.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/sturgeon/results/RLGS11-recurrent-merged_probes_methyl_calls_general.pdf
2024-10-07 17:17:07,957 - root         - DEBUG    - Loading colors dict
2024-10-07 17:17:11,007 - matplotlib.backends.backend_pdf - DEBUG    - Assigning font /b'F1' = '/.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf'
2024-10-07 17:17:11,643 - matplotlib.backends.backend_pdf - DEBUG    - Embedding font /.mounts/labs/reimandlab/private/users/abahcheli/software/sturgeon/venv/lib/python3.8/site-packages/matplotlib/mpl-data/fonts/ttf/DejaVuSans.ttf.
2024-10-07 17:17:11,643 - matplotlib.backends.backend_pdf - DEBUG    - Writing TrueType font.
