# Alec Ba<PERSON>cheli - <EMAIL>

#####################
# linking germline control files for easy use
#####################


rule link_bam_primary:
    input:
        recalibrated_bam_germline = RES_DIR + "/wgs/{sample_code}-blood_wgs_seq-merged_alignment.bam",
        recalibrated_bam_index = RES_DIR + "/wgs/{sample_code}-blood_wgs_seq-merged_alignment.bai"
        
    output:
        bam_germline_link = RES_DIR + "/wgs/{sample_code}-primary_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bam",
        bam_index_link = RES_DIR + "/wgs/{sample_code}-primary_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bai"

    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '10G'
    
    run:
        shell("ln -s {input.recalibrated_bam_germline} {output.bam_germline_link}")
        shell("ln -s {input.recalibrated_bam_index} {output.bam_index_link}")

rule link_bam_recurrent:
    input:
        recalibrated_bam_germline = RES_DIR + "/wgs/{sample_code}-blood_wgs_seq-merged_alignment.bam",
        recalibrated_bam_index = RES_DIR + "/wgs/{sample_code}-blood_wgs_seq-merged_alignment.bai"
        
    output:
        bam_germline_link = RES_DIR + "/wgs/{sample_code}-recurrent_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bam",
        bam_index_link = RES_DIR + "/wgs/{sample_code}-recurrent_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bai"
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '10G'
    
    run:
        shell("ln -s {input.recalibrated_bam_germline} {output.bam_germline_link}")
        shell("ln -s {input.recalibrated_bam_index} {output.bam_index_link}")

#####################
# Mutect2
#####################

rule Mutect2:
    input:
        RES_DIR + "/" + GENOME_VERSION + ".fa.fai",
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        
        tumor_bam = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq-merged_alignment.bam",
        normal_bam = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bam"
        
    output:
        output_vcf_gz = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-mutect2_{chromosome}.vcf.gz"
        
    singularity:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-4.4.0.0/gatk.sif"
        
    params:
        java_memory = '200g',
        normal_sample = "{sample_code}-blood_wgs_seq",
        chromosome = "{chromosome}",
        sample_tmp_dir = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/"
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '205G'
        
    shell:
        "gatk --java-options '-Xmx{params.java_memory}' Mutect2 -R {input.genome_fasta} --input {input.tumor_bam} --input {input.normal_bam} --output {output.output_vcf_gz} -normal {params.normal_sample} --tmp-dir {params.sample_tmp_dir} --intervals {params.chromosome}"
# gatk  Mutect2 -R /path/to/hg38fasta -I tumor.bam -O tumour_id -I normal.bam -O normal_id -L chrN -L intervals.bed  -O sample.vcf



#####################
# Strelka
#####################


rule init_Strelka:
    input:
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        tumor_bam = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq-merged_alignment.bam",
        normal_bam = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bam"

    output:
        RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/runWorkflow.py"
        
    params:
        working_directory = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq"
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '10G'

    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/snv_callers/bin/configureStrelkaSomaticWorkflow.py --normalBam {input.normal_bam} --tumorBam {input.tumor_bam} --referenceFasta {input.genome_fasta} --runDir {params.working_directory}"

rule Strelka:
    input:
        run_file = RES_DIR + "/wgs/{sample}/runWorkflow.py"
        
    output:
        RES_DIR + "/wgs/{sample}/results/variants/somatic.snvs.vcf.gz",
        RES_DIR + "/wgs/{sample}/results/variants/somatic.indels.vcf.gz"
        
    resources:
        threads = WGS_THREADS,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '5G'

    shell:
        "{input.run_file} -m local -j {resources.threads}"



#####################
# VarScan2
#####################


rule Varscan2:
    input:
        CENTROMERE_BED,
        EXOME_BED,
        RES_DIR + "/" + GENOME_VERSION + ".fa.fai",
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        
        tumor_bam = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq-merged_alignment.bam",
        normal_bam = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bam"
        
    output:
        output_cnv = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq.cnv"
        
    singularity:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/varscan2/varscan2.sif"
        
    params:
        sample_id = "{sample_code}-{tumor}",
        sample_tmp_dir = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/"
        
    resources:
        threads = 1,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '120G'
        
    shell:
        "/usr/local/bin/run_varscan -c {input.normal_bam} -t {input.tumor_bam} -q {params.sample_id} -i {input.genome_fasta} -b {CENTROMERE_BED} -w {EXOME_BED} -n {params.sample_tmp_dir} > {output.output_cnv}"
# docker run  -v /path/to/input/files:/data jeltje/varscan2 -c normal.bam -t  tumor.bam -q sampleid -i genome.fa -b centromeres.bed -w targets.bed -s tmpdir > varscan.cnv




#####################
# MuSe
#####################


rule Muse2_call:
    input:
        RES_DIR + "/" + GENOME_VERSION + ".fa.fai",
        genome_fasta = RES_DIR + "/" + GENOME_VERSION + ".fa",
        
        tumor_bam = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq-merged_alignment.bam",
        normal_bam = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-blood_wgs_seq-merged_alignment.bam"
        
    output:
        RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-muse2.MuSE.txt"
        
    params:
        output_call_prefix = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-muse2"
        
    resources:
        threads = WGS_THREADS,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '7G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/MuSE2/MuSE call -f {input.genome_fasta} -O {params.output_call_prefix} -n {resources.threads} {input.tumor_bam} {input.normal_bam}"
# ./MuSE call -f Reference.Genome -O Output.Prefix -n 20 Tumor.bam Matched.Normal.bam


rule Muse2_sump:
    input:
        dbSNP_VCF,
        muse2_sump = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-muse2.MuSE.txt"
        
    output:
        muse2_vcf = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-muse2.vcf"
        
    params:
        output_call_prefix = RES_DIR + "/wgs/{sample_code}-{tumor}_wgs_seq/{sample_code}-{tumor}_wgs_seq-muse2"
        
    resources:
        threads = WGS_THREADS,
        queue = "all.q",
        runtime = '5d',
        jobtime = '0:112:0:0',
        individual_core_memory = '7G'
        
    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/MuSE2/MuSE sump -I {input.muse2_sump} -O {output.muse2_vcf} -G -n {resources.threads} -D {dbSNP_VCF}"
# ./MuSE sump -I Output.Prefix.MuSE.txt -O Output.Prefix.vcf -G -n 20 -D dbsnp.vcf.gz






