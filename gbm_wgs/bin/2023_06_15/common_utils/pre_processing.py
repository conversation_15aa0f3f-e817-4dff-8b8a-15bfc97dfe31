import sys, getopt, time, subprocess, os, pathlib, glob, re
import gzip, io

import pandas as pd 
import numpy as np
from lifelines import Cox<PERSON>HFitter, utils

import scipy.stats as stats
import statsmodels.stats.multitest as multitest



def subset_gzip_fa_to_chr(infile = "/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/hg38.fa.gz", outfile = "/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/hg38_chr.fa.gz"):
    # list of real chromosomes
    chrom = [f">chr{n}" for n in range(23)]
    chrom.extend([">chrX", ">chrY"])

    write = False

    # read file by line and write chromosomes of interest to new file
    with gzip.open(outfile, "wt") as outfile:
        with gzip.open(infile,'rt') as infile:
            for line in infile:
                if line.startswith(">"):
                    write = False
                    if line.strip("\n") in chrom:
                        print(line)
                        write = True
                if write:
                    outfile.write(line)



def read_vcf(path):
    if path.endswith(".gz"):
        with gzip.open(path, 'rt') as f:
            lines = [l for l in f if not l.startswith('##')]
    else:
        with open(path, 'r') as f:
            lines = [l for l in f if not l.startswith('##')]
    return pd.read_csv(
        io.StringIO(''.join(lines)),
        dtype={'#CHROM': str, 'POS': int, 'ID': str, 'REF': str, 'ALT': str,
               'QUAL': str, 'FILTER': str, 'INFO': str},
        sep='\t'
    ).rename(columns={'#CHROM': 'CHROM'})


def read_vcf_alt(path):
    header = []
    lines = []
    
    if path.endswith(".gz"):
        with gzip.open(path, 'rt') as f:
            for line in f:
                if line.startswith("##"):
                    header.append(line)
                else:
                    lines.append(line)
            # header = [l for l in f if l.startswith('##')]
            # lines = [l for l in f if not l.startswith('##')]
    else:
        with open(path, 'r') as f:
            for line in f:
                if line.startswith("##"):
                    header.append(line)
                else:
                    lines.append(line)
    
    df = pd.read_csv(
        io.StringIO(''.join(lines)),
        dtype={'#CHROM': str, 'POS': int, 'ID': str, 'REF': str, 'ALT': str,
               'QUAL': str, 'FILTER': str, 'INFO': str},
        sep='\t'
    ).rename(columns={'#CHROM': 'CHROM'})
    
    return header, df

def write_vcf(vcf, header, outfile):
    if outfile.endswith("gz"):
        with gzip.open(outfile, 'wt') as f:
            # write header
            for l in header:
                f.write(l)
            
            # write column names
            colnames = "#" + "\t".join(vcf.columns) + "\n"
            f.write(colnames)
            
            # write table
            for r in vcf.iterrows():
                f.write("\t".join(r[1].to_numpy(dtype='str')) + "\n")
            
    else:
        with open(outfile, "w") as f:
            # write header
            for l in header:
                f.write(l)
            
            # write column names
            colnames = "#" + "\t".join(vcf.columns) + "\n"
            colnames = "#" + colnames
            f.write(colnames)
            
            # write table
            for r in vcf.iterrows():
                f.write("\t".join(r[1].to_numpy(dtype='str')) + "\n")


