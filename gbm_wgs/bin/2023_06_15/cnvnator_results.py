# <PERSON>i

import sys, getopt, time, os

try:
    sys.path.insert(1, "/".join(os.path.realpath(__file__).split("/")[:-2]))
except:
    sys.path.insert(1, "/".join(os.getcwd().split("/")[:-1]))

import pandas as pd 
import numpy as np

import pybedtools


help_message = '''
Failed
'''

def combine_files(input_dir, sample_names):
    # list of files 
    files = [os.path.join(input_dir, sample, 'cnvnator.tsv') for sample in sample_names]

    # Initialize an empty list to store the DataFrames
    res_df = []

    # Iterate through each file in the directory
    for i, file in enumerate(files):
        # Load the file as a DataFrame
        df = pd.read_csv(file, sep='\t', header=None)
        df.columns = ['CNV_type', 'coordinates', 'CNV_size', 'normalized_read_depth', 'e-ttest', 'e-gaussian', 'e-ttest_middle', 'e-gaussian_middle', 'q0_fraction']
        
        # add sample-specific information
        df.loc[:,'sample_name'] = sample_names[i]

        # Append the DataFrame to the list
        res_df.append(df)

    # Combine the DataFrames into a single DataFrame
    return pd.concat(res_df, ignore_index=True)


def convert_df_to_bed(res_df):
    # record original columns
    original_columns = res_df.columns.to_numpy()

    # subset to amplifications on chromosomes
    res_df = res_df.loc[res_df['CNV_type'] == 'duplication',:]
    mask = res_df['coordinates'].str.startswith("chr")
    res_df = res_df.loc[mask,:]

    # convert into bed format
    chrom = res_df['coordinates'].str.split(":").str[0]
    start = res_df['coordinates'].str.split(":").str[1].str.split("-").str[0]
    stop = res_df['coordinates'].str.split(":").str[1].str.split("-").str[1]

    # add to df
    res_df.loc[:,new_cols] = np.column_stack([chrom, start, stop])

    # combine columns
    new_cols = ['chromosome', 'start', 'stop']
    new_cols.extend(original_columns)

    # reorder df
    return res_df.loc[:,new_cols]



def subtract_normal_cnvs(normal_df, cancer_df, normal_bed_file, cancer_bed_file, figure_data_file):
    # save to files for bedtools
    normal_df.to_csv(normal_bed_file, sep='\t', index=False, header=False)
    cancer_df.to_csv(cancer_bed_file, sep='\t', index=False, header=False)

    # subtract normal from cancer
    norm = pybedtools.BedTool(normal_bed_file)
    canc = pybedtools.BedTool(cancer_bed_file)

    # save and reload as df and add column names
    canc_specific_df = canc.subtract(norm)
    canc_specific_df.saveas(figure_data_file)

    canc_specific_df = pd.read_csv(figure_data_file, sep='\t', header=None)
    canc_specific_df.columns = normal_df.columns

    # # save for visualization
    # canc_specific_df.loc[:,'plot_t'] = -np.log10(canc_specific_df['e-ttest'])
    # canc_specific_df.loc[:,'plot_g'] = -np.log10(canc_specific_df['e-gaussian'])

    # # canc_specific_df.loc[np.where(canc_specific_df.loc[:,['plot_t', 'plot_g']] > 30), ['plot_t', 'plot_g']] = 30
    # canc_specific_df.loc[canc_specific_df['plot_g'] > 30, 'plot_g'] = 30
    # canc_specific_df.loc[canc_specific_df['plot_t'] > 30, 'plot_t'] = 30

    return canc_specific_df


def add_gene_bed_info(cancer_df, cancer_bed_file, gene_bed_file, cancer_genes_file):
    # save to bed file
    cancer_df.to_csv(cancer_bed_file, sep='\t', index=False, header=False)

    # record column names
    new_cols = cancer_df.columns.tolist()


    # add gene information to intersecting regions
    genes = pybedtools.BedTool(gene_bed_file)
    canc = pybedtools.BedTool(cancer_bed_file)

    canc = canc.intersect(genes, wa = True, wb = True)

    # reload as df
    canc.saveas(cancer_genes_file)
    cancer_df = pd.read_csv(cancer_genes_file, sep='\t', header=None)

    # add column names
    genes_columns = ['ens_chromosome', 'ens_start', 'ens_stop', 'ensemble_id', 'score', 'strand', 'source', 'gene_type', 'type_score', 'details']
    new_cols.extend(genes_columns)
    cancer_df.columns = new_cols

    # specify gene names
    mask = np.invert(cancer_df['details'] == ".")
    cancer_df.loc[mask,'gene'] = list(map(lambda x: x.split(";")[3].split("=")[1], cancer_df.loc[mask,'details']))

    return cancer_df


def combine_cnv_genes(cancer_df):
    cancer_df.index = cancer_df['coordinates'] + "-" + cancer_df['sample_name']

    spec_cancer_df = []

    # for each region in the duplication, combine all genes
    for coordinate in np.unique(cancer_df.index):
        # subset to this amplification
        sub_df = cancer_df.loc[[coordinate],:]
        
        # all genes in that region
        try:
            genes = cancer_df.loc[coordinate,'gene'].to_numpy()
        except:
            genes = cancer_df.loc[coordinate,'gene']
        
        # subset to a single line for this amplification
        sub_df = sub_df.iloc[[0],:13]
        
        # gene names
        if type(genes) == str:
            sub_df.loc[:,'genes'] = genes
        else:
            sub_df.loc[:,'genes'] = ",".join(genes)
        
        # add to final df
        spec_cancer_df.extend(sub_df.to_numpy())
        
    # create df
    new_cols = cancer_df.columns.to_list()[:13]
    new_cols.append('genes')

    # return df
    return pd.DataFrame(spec_cancer_df, columns = new_cols)



def main():
    print('XXX-XXX.py')
    t1 = time.time()
    
    # combine cnv files
    res_df = combine_files(input_dir, sample_names)

    # turn into bed file format
    res_df = convert_df_to_bed(res_df)



    # separate normal sample CNVs
    normal_mask = res_df['sample_name'].str.contains("S1|S2", regex=True)
    normal_df = res_df.loc[normal_mask,:]
    cancer_df = res_df.loc[np.invert(normal_mask),:]

    # subtract normal CNVs
    cancer_df = subtract_normal_cnvs(normal_df, cancer_df, normal_bed_file, cancer_bed_file, figure_data_file)



    # add gene information and save to file
    cancer_df = add_gene_bed_info(cancer_df, cancer_bed_file, gene_bed_file, cancer_genes_file)

    cancer_df.to_csv(cancer_genes_detailed_file, sep='\t', index=False)



    # combine region information and save to file
    cancer_df = combine_cnv_genes(cancer_df)
    cancer_df.to_csv(cancer_genes_file, sep='\t', index=False)


    # subset results by e-val and normalized_read_depth
    mask = np.logical_and(cancer_df['e-ttest'] < eval_sig, cancer_df['normalized_read_depth'] > min_coverage)
    sub_df = cancer_df.loc[mask,:]

    sub_df.to_csv(sig_res_file, sep='\t', index=False)



    print(round(time.time() - t1, 2))
    print('XXX-XXX.py COMPLETE')


if __name__ == "__main__":
    # significant SNV parameters
    eval_sig = 0.05
    min_coverage = 3

    # Directory path containing the files
    input_dir = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/schramek_brca_cna/wgs'
    sample_names = "KA_WGS1_S1,KA_WGS2_S2,KA_WGS3_S3,KA_WGS4_S4,KA_WGS5_S5,KA_WGS6_S6,KA_WGS7_S7".split(",")

    # files for finding intersects
    normal_bed_file = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/schramek_brca_cna/cnv_analysis/normal.bed"
    cancer_bed_file = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/schramek_brca_cna/cnv_analysis/cancer.bed"
    gene_bed_file = "/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/ensembl_hg38_27_10_2022.bed"

    # results files
    figure_data_file = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/schramek_brca_cna/cnv_analysis/cancer_specific.tsv"
    cancer_genes_detailed_file = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/schramek_brca_cna/cnv_analysis/cancer_specific_genes_detailed.tsv"
    
    cancer_genes_file = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/schramek_brca_cna/cnv_analysis/cancer_specific_genes.tsv"    
    sig_res_file = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/small_projects_reimand/results/schramek_brca_cna/cnv_analysis/sig_regions.tsv"


    try:
        opts, args = getopt.getopt(sys.argv[1:],"",["strelka_file=", "output_vcf="])
    except getopt.GetoptError:
        print (help_message)
        print(sys.argv)
        sys.exit(2)
    for opt, arg in opts:
        if opt in ("--strelka_file"):
            strelka_file = str(arg)
        if opt in ("--output_vcf"):
            output_vcf = str(arg)

    main()





