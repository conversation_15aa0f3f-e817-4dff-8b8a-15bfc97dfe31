# base image uses ubuntu; build with arm64
FROM ubuntu:latest

# Metadata
LABEL software.name="MuSE2"
LABEL software.version="2.0"

# load zip and nano
RUN apt-get update \
    && apt-get install -y \
    zip \
    nano \
    wget \
    gcc \
    g++ \
    git \
    make \
    bzip2 \
    libz-dev \
    liblzma-dev \
    libbz2-dev \
    libssl-dev \
    libcurl4-openssl-dev \
    libtcmalloc-minimal4 \
    && apt-get clean

# RUN cd /usr/bin \
#     && wget https://github.com/samtools/htslib/releases/download/1.17/htslib-1.17.tar.bz2 \
#     && tar -vxjf htslib-1.17.tar.bz2 \
#     && cd htslib-1.17 \
#     && make \
#     && export PATH="$PATH:/usr/bin/htslib-1.17"

# RUN git clone https://github.com/wwylab/MuSE.git \
#     && cd MuSE \
#     && ./install_muse.sh

# # copy the conda envs
# COPY plasmid_assembly.yml /plasmid_assembly.yml
# # create conda envs
# RUN conda env create -f /plasmid_assembly.yml


# # copy the common_utils directory to the image along with the execution script
# ADD common_utils /common_utils

# COPY plasmid_assembly.py /plasmid_assembly.py
# RUN chmod 777 /plasmid_assembly.py /common_utils/fastq_length_distribution.sh

