{"cells": [{"cell_type": "code", "execution_count": 38, "id": "d3419a12-ba6e-4c45-9014-8f3d07b85215", "metadata": {}, "outputs": [{"ename": "ImportError", "evalue": "cannot import name 'read_vcf_alt' from 'common_utils.pre_processing' (/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_01_30/common_utils/pre_processing.py)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mImportError\u001b[0m                               Traceback (most recent call last)", "Cell \u001b[0;32mIn[38], line 19\u001b[0m\n\u001b[1;32m     16\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m \u001b[38;5;21;01mscipy\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mstats\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mstats\u001b[39;00m\n\u001b[1;32m     17\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mstatsmodels\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mstats\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmultitest\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mmultitest\u001b[39;00m\n\u001b[0;32m---> 19\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mcommon_utils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpre_processing\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m read_vcf, read_vcf_alt, write_vcf\n", "\u001b[0;31mImportError\u001b[0m: cannot import name 'read_vcf_alt' from 'common_utils.pre_processing' (/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/2023_01_30/common_utils/pre_processing.py)"]}], "source": ["# load packages\n", "import sys, getopt, time, subprocess, os, pathlib, glob, re\n", "import gzip, io\n", "\n", "try:\n", "    sys.path.insert(1, \"/\".join(os.path.realpath(__file__).split(\"/\")[:-2]))\n", "except:\n", "    sys.path.insert(1, \"/\".join(os.getcwd().split(\"/\")[:-1]))\n", "\n", "\n", "import pandas as pd \n", "import numpy as np\n", "from lifelines import CoxPHFitter, utils\n", "\n", "# from common_utils.a_data_preprocessing import generate_cancer_abbreviation, generate_cancer_tissue_source_dict\n", "import scipy.stats as stats\n", "import statsmodels.stats.multitest as multitest\n", "\n", "from common_utils.pre_processing import read_vcf\n"]}, {"cell_type": "code", "execution_count": 31, "id": "a724016d-291c-4b09-8331-ce97dc3d13b2", "metadata": {}, "outputs": [], "source": ["infile = \"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_05_25/wgs/RLGS10-recurrent_wgs_seq/results/variants/test.vcf\"\n", "outfile = \"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_05_25/wgs/RLGS10-recurrent_wgs_seq/results/variants/test_mod.vcf\"\n", "\n", "header, vcf = read_vcf_alt(infile)\n", "\n", "vcf.loc[:,'FORMAT'] = vcf['FORMAT'] + \":GT\"\n", "vcf.loc[:,'INFO'] = vcf['INFO'] + \":0/1\"\n", "\n", "write_vcf(vcf, header, outfile)"]}, {"cell_type": "code", "execution_count": null, "id": "d49bd07d-d2aa-4f5f-99f0-644ed1f8d531", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 28, "id": "0d2c7aa7-283f-48b1-af70-4afd1a257b10", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CHROM</th>\n", "      <th>POS</th>\n", "      <th>ID</th>\n", "      <th>REF</th>\n", "      <th>ALT</th>\n", "      <th>QUAL</th>\n", "      <th>FILTER</th>\n", "      <th>INFO</th>\n", "      <th>FORMAT</th>\n", "      <th>NORMAL</th>\n", "      <th>TUMOR</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>chr1</td>\n", "      <td>47318</td>\n", "      <td>.</td>\n", "      <td>TA</td>\n", "      <td>T</td>\n", "      <td>.</td>\n", "      <td>LowEVS</td>\n", "      <td>SOMATIC;QSI=22;TQSI=2;NT=ref;QSI_NT=22;TQSI_NT...</td>\n", "      <td>DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT</td>\n", "      <td>100:100:83,99:0,1:15,13:97.01:3.65:0.00:0.03</td>\n", "      <td>220:220:183,228:9,10:28,30:215.49:6.77:0.00:0.03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>chr1</td>\n", "      <td>50481</td>\n", "      <td>.</td>\n", "      <td>G</td>\n", "      <td>GGT</td>\n", "      <td>.</td>\n", "      <td>LowEVS</td>\n", "      <td>SOMATIC;QSI=16;TQSI=1;NT=ref;QSI_NT=16;TQSI_NT...</td>\n", "      <td>DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT</td>\n", "      <td>40:40:26,38:1,5:14,35:36.12:3.30:0.00:0.08</td>\n", "      <td>75:75:54,59:7,22:16,67:70.96:2.32:0.00:0.03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>chr1</td>\n", "      <td>83937</td>\n", "      <td>.</td>\n", "      <td>A</td>\n", "      <td>AAG</td>\n", "      <td>.</td>\n", "      <td>LowEVS</td>\n", "      <td>SOMATIC;QSI=29;TQSI=2;NT=ref;QSI_NT=29;TQSI_NT...</td>\n", "      <td>DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT</td>\n", "      <td>99:99:75,85:1,1:23,19:95.20:18.41:0.00:0.19</td>\n", "      <td>232:232:182,211:5,5:49,35:215.42:34.83:0.00:0.16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>chr1</td>\n", "      <td>83944</td>\n", "      <td>.</td>\n", "      <td>GGAAA</td>\n", "      <td>G</td>\n", "      <td>.</td>\n", "      <td>LowEVS</td>\n", "      <td>SOMATIC;QSI=20;TQSI=1;NT=ref;QSI_NT=20;TQSI_NT...</td>\n", "      <td>DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT</td>\n", "      <td>96:96:63,72:1,1:38,35:94.59:18.46:0.00:0.19</td>\n", "      <td>218:218:150,170:6,9:73,67:215.87:34.78:0.00:0.16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>chr1</td>\n", "      <td>94996</td>\n", "      <td>.</td>\n", "      <td>A</td>\n", "      <td>AT</td>\n", "      <td>.</td>\n", "      <td>LowEVS</td>\n", "      <td>SOMATIC;QSI=3;TQSI=2;NT=ref;QSI_NT=3;TQSI_NT=2...</td>\n", "      <td>DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT</td>\n", "      <td>92:92:62,68:1,5:27,25:83.87:6.50:0.00:0.07</td>\n", "      <td>287:287:204,210:8,15:72,77:268.98:16.12:0.00:0.06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>181456</th>\n", "      <td>chrY</td>\n", "      <td>26573707</td>\n", "      <td>.</td>\n", "      <td>CT</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>LowEVS</td>\n", "      <td>SOMATIC;QSI=16;TQSI=2;NT=ref;QSI_NT=16;TQSI_NT...</td>\n", "      <td>DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT</td>\n", "      <td>26:26:22,22:0,0:5,5:30.72:1.92:0.00:0.03</td>\n", "      <td>78:78:58,64:4,4:16,11:75.17:5.24:0.00:0.07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>181457</th>\n", "      <td>chrY</td>\n", "      <td>26576289</td>\n", "      <td>.</td>\n", "      <td>TA</td>\n", "      <td>T</td>\n", "      <td>.</td>\n", "      <td>LowEVS</td>\n", "      <td>SOMATIC;QSI=7;TQSI=1;NT=ref;QSI_NT=7;TQSI_NT=1...</td>\n", "      <td>DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT</td>\n", "      <td>32:32:18,20:1,1:13,11:30.55:4.95:0.00:0.13</td>\n", "      <td>74:74:45,50:8,8:22,17:75.13:6.32:0.00:0.08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>181458</th>\n", "      <td>chrY</td>\n", "      <td>26598068</td>\n", "      <td>.</td>\n", "      <td>CA</td>\n", "      <td>C</td>\n", "      <td>.</td>\n", "      <td>LowEVS</td>\n", "      <td>SOMATIC;QSI=16;TQSI=1;NT=ref;QSI_NT=16;TQSI_NT...</td>\n", "      <td>DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT</td>\n", "      <td>46:46:33,33:0,0:12,12:44.14:2.72:0.00:0.05</td>\n", "      <td>75:75:53,58:4,6:17,10:74.41:5.14:0.00:0.07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>181459</th>\n", "      <td>chrY</td>\n", "      <td>26649707</td>\n", "      <td>.</td>\n", "      <td>T</td>\n", "      <td>TGGAAC</td>\n", "      <td>.</td>\n", "      <td>LowEVS</td>\n", "      <td>SOMATIC;QSI=26;TQSI=1;NT=ref;QSI_NT=26;TQSI_NT...</td>\n", "      <td>DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT</td>\n", "      <td>68:68:31,141:3,4:34,121:64.47:11.17:0.00:0.17</td>\n", "      <td>166:166:84,334:8,13:77,253:160.10:20.38:0.00:0.12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>181460</th>\n", "      <td>chrY</td>\n", "      <td>56884422</td>\n", "      <td>.</td>\n", "      <td>T</td>\n", "      <td>TTAA</td>\n", "      <td>.</td>\n", "      <td>HighDepth;LowEVS</td>\n", "      <td>SOMATIC;QSI=1;TQSI=1;NT=ref;QSI_NT=1;TQSI_NT=1...</td>\n", "      <td>DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT</td>\n", "      <td>259:259:166,240:0,4:93,76:271.13:73.09:0.00:0.27</td>\n", "      <td>524:524:354,498:5,9:167,133:539.20:127.24:0.00...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>181461 rows × 11 columns</p>\n", "</div>"], "text/plain": ["       CHROM       POS ID    REF     ALT QUAL            FILTER  \\\n", "0       chr1     47318  .     TA       T    .            LowEVS   \n", "1       chr1     50481  .      G     GGT    .            LowEVS   \n", "2       chr1     83937  .      A     AAG    .            LowEVS   \n", "3       chr1     83944  .  GGAAA       G    .            LowEVS   \n", "4       chr1     94996  .      A      AT    .            LowEVS   \n", "...      ...       ... ..    ...     ...  ...               ...   \n", "181456  chrY  26573707  .     CT       C    .            <PERSON>   \n", "181457  chrY  26576289  .     TA       T    .            <PERSON>   \n", "181458  chrY  26598068  .     CA       C    .            <PERSON>   \n", "181459  chrY  26649707  .      T  TGGAAC    .            LowEVS   \n", "181460  chrY  56884422  .      T    TTAA    .  HighDepth;LowEVS   \n", "\n", "                                                     INFO  \\\n", "0       SOMATIC;QSI=22;TQSI=2;NT=ref;QSI_NT=22;TQSI_NT...   \n", "1       SOMATIC;QSI=16;TQSI=1;NT=ref;QSI_NT=16;TQSI_NT...   \n", "2       SOMATIC;QSI=29;TQSI=2;NT=ref;QSI_NT=29;TQSI_NT...   \n", "3       SOMATIC;QSI=20;TQSI=1;NT=ref;QSI_NT=20;TQSI_NT...   \n", "4       SOMATIC;QSI=3;TQSI=2;NT=ref;QSI_NT=3;TQSI_NT=2...   \n", "...                                                   ...   \n", "181456  SOMATIC;QSI=16;TQSI=2;NT=ref;QSI_NT=16;TQSI_NT...   \n", "181457  SOMATIC;QSI=7;TQSI=1;NT=ref;QSI_NT=7;TQSI_NT=1...   \n", "181458  SOMATIC;QSI=16;TQSI=1;NT=ref;QSI_NT=16;TQSI_NT...   \n", "181459  SOMATIC;QSI=26;TQSI=1;NT=ref;QSI_NT=26;TQSI_NT...   \n", "181460  SOMATIC;QSI=1;TQSI=1;NT=ref;QSI_NT=1;TQSI_NT=1...   \n", "\n", "                                                FORMAT  \\\n", "0       DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT   \n", "1       DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT   \n", "2       DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT   \n", "3       DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT   \n", "4       DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT   \n", "...                                                ...   \n", "181456  DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT   \n", "181457  DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT   \n", "181458  DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT   \n", "181459  DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT   \n", "181460  DP:DP2:TAR:TIR:TOR:DP50:FDP50:SUBDP50:BCN50:GT   \n", "\n", "                                                  NORMAL  \\\n", "0           100:100:83,99:0,1:15,13:97.01:3.65:0.00:0.03   \n", "1             40:40:26,38:1,5:14,35:36.12:3.30:0.00:0.08   \n", "2            99:99:75,85:1,1:23,19:95.20:18.41:0.00:0.19   \n", "3            96:96:63,72:1,1:38,35:94.59:18.46:0.00:0.19   \n", "4             92:92:62,68:1,5:27,25:83.87:6.50:0.00:0.07   \n", "...                                                  ...   \n", "181456          26:26:22,22:0,0:5,5:30.72:1.92:0.00:0.03   \n", "181457        32:32:18,20:1,1:13,11:30.55:4.95:0.00:0.13   \n", "181458        46:46:33,33:0,0:12,12:44.14:2.72:0.00:0.05   \n", "181459     68:68:31,141:3,4:34,121:64.47:11.17:0.00:0.17   \n", "181460  259:259:166,240:0,4:93,76:271.13:73.09:0.00:0.27   \n", "\n", "                                                    TUMOR  \n", "0        220:220:183,228:9,10:28,30:215.49:6.77:0.00:0.03  \n", "1             75:75:54,59:7,22:16,67:70.96:2.32:0.00:0.03  \n", "2        232:232:182,211:5,5:49,35:215.42:34.83:0.00:0.16  \n", "3        218:218:150,170:6,9:73,67:215.87:34.78:0.00:0.16  \n", "4       287:287:204,210:8,15:72,77:268.98:16.12:0.00:0.06  \n", "...                                                   ...  \n", "181456         78:78:58,64:4,4:16,11:75.17:5.24:0.00:0.07  \n", "181457         74:74:45,50:8,8:22,17:75.13:6.32:0.00:0.08  \n", "181458         75:75:53,58:4,6:17,10:74.41:5.14:0.00:0.07  \n", "181459  166:166:84,334:8,13:77,253:160.10:20.38:0.00:0.12  \n", "181460  524:524:354,498:5,9:167,133:539.20:127.24:0.00...  \n", "\n", "[181461 rows x 11 columns]"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["vcf"]}, {"cell_type": "code", "execution_count": null, "id": "7505300b-ab62-4ff3-8eff-3efe1cd2585d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ed45d7ab-a47d-42da-8434-9fb08351b75e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b3d6bc70-6c45-4654-8730-581af86e2055", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a992b960-8dd4-4e53-a30e-e4c04b8c5079", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 5}