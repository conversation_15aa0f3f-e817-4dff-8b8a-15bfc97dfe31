{"cells": [{"cell_type": "code", "execution_count": 1, "id": "50ef7f26-53d2-49ec-89b6-55582702eb13", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "Attaching package: 'gplots'\n", "\n", "\n", "The following object is masked from 'package:stats':\n", "\n", "    lowess\n", "\n", "\n", "\n", "Attaching package: 'dplyr'\n", "\n", "\n", "The following objects are masked from 'package:stats':\n", "\n", "    filter, lag\n", "\n", "\n", "The following objects are masked from 'package:base':\n", "\n", "    intersect, setdiff, setequal, union\n", "\n", "\n", "\n", "Attaching package: 'data.table'\n", "\n", "\n", "The following objects are masked from 'package:dplyr':\n", "\n", "    between, first, last\n", "\n", "\n", "\n", "Attaching package: 'gridExtra'\n", "\n", "\n", "The following object is masked from 'package:dplyr':\n", "\n", "    combine\n", "\n", "\n", "Loading required package: ggpubr\n", "\n", "\n", "Attaching package: 'survminer'\n", "\n", "\n", "The following object is masked from 'package:survival':\n", "\n", "    myeloma\n", "\n", "\n"]}], "source": ["library(ggplot2)\n", "library(ggrepel)\n", "library(ggrastr)\n", "library(gplots)\n", "library(ggupset)\n", "\n", "\n", "library(dplyr)\n", "library(forcats)\n", "library(data.table)\n", "\n", "library(tidytext)\n", "\n", "library(grid)\n", "library(gridExtra)\n", "library(patchwork)\n", "\n", "\n", "library(survival)\n", "library(survminer)\n", "\n", "\n", "plot_theme = function(...) {\n", "     \n", "    theme_bw() +\n", "     \n", "    theme(    \n", "     \n", "        plot.title = element_text(size = 22),\n", "         \n", "        plot.caption = element_text(size = 12),\n", "         \n", "        plot.subtitle = element_text(size = 16),\n", "         \n", "        axis.title = element_text(size = 18),\n", "         \n", "        axis.text.x = element_text(size = 12,\n", "                angle = 90, hjust = 1, vjust=0.5, color = \"black\"),\n", "        axis.text.y = element_text(size = 12, color = \"black\"),\n", "        legend.title = element_text(size = 16),\n", "        legend.text = element_text(size = 14),\n", "        ...\n", "    )\n", "}\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "8f8dd2e3-2bfb-4f11-9e7c-0506b5563266", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "015819b3-5a97-4968-807f-08d4eab64481", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 4, "id": "8edb9721-4bf4-41e0-890c-d4884409d09f", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA0gAAANICAIAAAByhViMAAAABmJLR0QA/wD/AP+gvaeTAAAg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", "text/plain": ["plot without title"]}, "metadata": {"image/png": {"height": 420, "width": 420}}, "output_type": "display_data"}], "source": ["chromosomes <- data.frame(\n", "  chromosome = c(\"chr1\", \"chr2\", \"chr3\", \"chr4\", \"chr5\"),\n", "  size = c(249250621, 243199373, 198022430, 191154276, 180915260)\n", ")\n", "\n", "p = ggplot(chromosomes, aes(x = chromosome, y = size)) +\n", "  geom_bar(stat = \"identity\", fill = \"steelblue\") +\n", "  xlab(\"Chromosome\") +\n", "  ylab(\"Size\") +\n", "  ggtitle(\"Chromosome Sizes\")\n", "\n", "print(p)"]}, {"cell_type": "code", "execution_count": null, "id": "9edecf4f-0e21-4503-bc65-8a1600015363", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 11, "id": "111f116b-0ebf-47bc-be06-995ba6e6fdc6", "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A data.frame: 6 × 21</caption>\n", "<thead>\n", "\t<tr><th></th><th scope=col>chr</th><th scope=col>start</th><th scope=col>stop</th><th scope=col>type</th><th scope=col>length</th><th scope=col>support</th><th scope=col>coverage</th><th scope=col>t1</th><th scope=col>t2</th><th scope=col>ens_chromosome</th><th scope=col>...</th><th scope=col>ens_stop</th><th scope=col>ensemble_id</th><th scope=col>score</th><th scope=col>strand</th><th scope=col>source</th><th scope=col>gene_type</th><th scope=col>type_score</th><th scope=col>details</th><th scope=col>gene</th><th scope=col>index</th></tr>\n", "\t<tr><th></th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>...</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><th scope=row>1</th><td>chr1</td><td>1979665</td><td>1979878</td><td>DEL</td><td>213</td><td> 2</td><td> 2</td><td>True</td><td>False</td><td>chr1</td><td>...</td><td>2003837</td><td>ENSG00000142609.19</td><td>.</td><td>-</td><td>HAVANA</td><td>gene</td><td>.</td><td>ID=ENSG00000142609.19;gene_id=ENSG00000142609.19;gene_type=protein_coding;gene_name=CFAP74;level=1;hgnc_id=HGNC:29368;havana_gene=OTTHUMG00000000945.17                      </td><td>CFAP74   </td><td>29</td></tr>\n", "\t<tr><th scope=row>2</th><td>chr1</td><td>2124193</td><td>2124193</td><td>INS</td><td>112</td><td> 2</td><td> 2</td><td>True</td><td>False</td><td>chr1</td><td>...</td><td>2185395</td><td>ENSG00000067606.17</td><td>.</td><td>+</td><td>HAVANA</td><td>gene</td><td>.</td><td>ID=ENSG00000067606.17;gene_id=ENSG00000067606.17;gene_type=protein_coding;gene_name=PRKCZ;level=1;hgnc_id=HGNC:9412;havana_gene=OTTHUMG00000001238.14                        </td><td>PRKCZ    </td><td>33</td></tr>\n", "\t<tr><th scope=row>3</th><td>chr1</td><td>2281036</td><td>2281036</td><td>INS</td><td> 69</td><td> 2</td><td> 2</td><td>True</td><td>False</td><td>chr1</td><td>...</td><td>2310213</td><td>ENSG00000157933.10</td><td>.</td><td>+</td><td>HAVANA</td><td>gene</td><td>.</td><td>ID=ENSG00000157933.10;gene_id=ENSG00000157933.10;gene_type=protein_coding;gene_name=SKI;level=2;hgnc_id=HGNC:10896;havana_gene=OTTHUMG00000001407.4                          </td><td>SKI      </td><td>35</td></tr>\n", "\t<tr><th scope=row>4</th><td>chr1</td><td>3986108</td><td>3986143</td><td>DEL</td><td> 35</td><td> 2</td><td> 2</td><td>True</td><td>False</td><td>chr1</td><td>...</td><td>4012704</td><td>ENSG00000284668.2 </td><td>.</td><td>+</td><td>HAVANA</td><td>gene</td><td>.</td><td>ID=ENSG00000284668.2;gene_id=ENSG00000284668.2;gene_type=lncRNA;gene_name=LINC02780;level=2;hgnc_id=HGNC:54300;havana_gene=OTTHUMG00000192281.2                              </td><td>LINC02780</td><td>43</td></tr>\n", "\t<tr><th scope=row>5</th><td>chr1</td><td>6025935</td><td>6025935</td><td>INS</td><td> 45</td><td> 2</td><td> 2</td><td>True</td><td>False</td><td>chr1</td><td>...</td><td>6101193</td><td>ENSG00000069424.15</td><td>.</td><td>+</td><td>HAVANA</td><td>gene</td><td>.</td><td>ID=ENSG00000069424.15;gene_id=ENSG00000069424.15;gene_type=protein_coding;gene_name=KCNAB2;level=2;hgnc_id=HGNC:6229;havana_gene=OTTHUMG00000000795.17                       </td><td>KCNAB2   </td><td>44</td></tr>\n", "\t<tr><th scope=row>6</th><td>chr1</td><td>6488116</td><td>6488116</td><td>BND</td><td>  0</td><td>10</td><td>42</td><td>True</td><td>False</td><td>chr1</td><td>...</td><td>6520074</td><td>ENSG00000171680.23</td><td>.</td><td>-</td><td>HAVANA</td><td>gene</td><td>.</td><td>ID=ENSG00000171680.23;gene_id=ENSG00000171680.23;gene_type=protein_coding;gene_name=PLEKHG5;level=2;hgnc_id=HGNC:29105;tag=overlapping_locus;havana_gene=OTTHUMG00000000905.5</td><td>PLEKHG5  </td><td>46</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A data.frame: 6 × 21\n", "\\begin{tabular}{r|lllllllllllllllllllll}\n", "  & chr & start & stop & type & length & support & coverage & t1 & t2 & ens\\_chromosome & ... & ens\\_stop & ensemble\\_id & score & strand & source & gene\\_type & type\\_score & details & gene & index\\\\\n", "  & <chr> & <int> & <int> & <chr> & <int> & <int> & <int> & <chr> & <chr> & <chr> & ... & <int> & <chr> & <chr> & <chr> & <chr> & <chr> & <chr> & <chr> & <chr> & <int>\\\\\n", "\\hline\n", "\t1 & chr1 & 1979665 & 1979878 & DEL & 213 &  2 &  2 & True & False & chr1 & ... & 2003837 & ENSG00000142609.19 & . & - & HAVANA & gene & . & ID=ENSG00000142609.19;gene\\_id=ENSG00000142609.19;gene\\_type=protein\\_coding;gene\\_name=CFAP74;level=1;hgnc\\_id=HGNC:29368;havana\\_gene=OTTHUMG00000000945.17                       & CFAP74    & 29\\\\\n", "\t2 & chr1 & 2124193 & 2124193 & INS & 112 &  2 &  2 & True & False & chr1 & ... & 2185395 & ENSG00000067606.17 & . & + & HAVANA & gene & . & ID=ENSG00000067606.17;gene\\_id=ENSG00000067606.17;gene\\_type=protein\\_coding;gene\\_name=PRKCZ;level=1;hgnc\\_id=HGNC:9412;havana\\_gene=OTTHUMG00000001238.14                         & PRKCZ     & 33\\\\\n", "\t3 & chr1 & 2281036 & 2281036 & INS &  69 &  2 &  2 & True & False & chr1 & ... & 2310213 & ENSG00000157933.10 & . & + & HAVANA & gene & . & ID=ENSG00000157933.10;gene\\_id=ENSG00000157933.10;gene\\_type=protein\\_coding;gene\\_name=SKI;level=2;hgnc\\_id=HGNC:10896;havana\\_gene=OTTHUMG00000001407.4                           & SKI       & 35\\\\\n", "\t4 & chr1 & 3986108 & 3986143 & DEL &  35 &  2 &  2 & True & False & chr1 & ... & 4012704 & ENSG00000284668.2  & . & + & HAVANA & gene & . & ID=ENSG00000284668.2;gene\\_id=ENSG00000284668.2;gene\\_type=lncRNA;gene\\_name=LINC02780;level=2;hgnc\\_id=HGNC:54300;havana\\_gene=OTTHUMG00000192281.2                               & LINC02780 & 43\\\\\n", "\t5 & chr1 & 6025935 & 6025935 & INS &  45 &  2 &  2 & True & False & chr1 & ... & 6101193 & ENSG00000069424.15 & . & + & HAVANA & gene & . & ID=ENSG00000069424.15;gene\\_id=ENSG00000069424.15;gene\\_type=protein\\_coding;gene\\_name=KCNAB2;level=2;hgnc\\_id=HGNC:6229;havana\\_gene=OTTHUMG00000000795.17                        & KCNAB2    & 44\\\\\n", "\t6 & chr1 & 6488116 & 6488116 & BND &   0 & 10 & 42 & True & False & chr1 & ... & 6520074 & ENSG00000171680.23 & . & - & HAVANA & gene & . & ID=ENSG00000171680.23;gene\\_id=ENSG00000171680.23;gene\\_type=protein\\_coding;gene\\_name=PLEKHG5;level=2;hgnc\\_id=HGNC:29105;tag=overlapping\\_locus;havana\\_gene=OTTHUMG00000000905.5 & PLEKHG5   & 46\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A data.frame: 6 × 21\n", "\n", "| <!--/--> | chr &lt;chr&gt; | start &lt;int&gt; | stop &lt;int&gt; | type &lt;chr&gt; | length &lt;int&gt; | support &lt;int&gt; | coverage &lt;int&gt; | t1 &lt;chr&gt; | t2 &lt;chr&gt; | ens_chromosome &lt;chr&gt; | ... ... | ens_stop &lt;int&gt; | ensemble_id &lt;chr&gt; | score &lt;chr&gt; | strand &lt;chr&gt; | source &lt;chr&gt; | gene_type &lt;chr&gt; | type_score &lt;chr&gt; | details &lt;chr&gt; | gene &lt;chr&gt; | index &lt;int&gt; |\n", "|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|\n", "| 1 | chr1 | 1979665 | 1979878 | DEL | 213 |  2 |  2 | True | False | chr1 | ... | 2003837 | ENSG00000142609.19 | . | - | HAVANA | gene | . | ID=ENSG00000142609.19;gene_id=ENSG00000142609.19;gene_type=protein_coding;gene_name=CFAP74;level=1;hgnc_id=HGNC:29368;havana_gene=OTTHUMG00000000945.17                       | CFAP74    | 29 |\n", "| 2 | chr1 | 2124193 | 2124193 | INS | 112 |  2 |  2 | True | False | chr1 | ... | 2185395 | ENSG00000067606.17 | . | + | HAVANA | gene | . | ID=ENSG00000067606.17;gene_id=ENSG00000067606.17;gene_type=protein_coding;gene_name=PRKCZ;level=1;hgnc_id=HGNC:9412;havana_gene=OTTHUMG00000001238.14                         | PRKCZ     | 33 |\n", "| 3 | chr1 | 2281036 | 2281036 | INS |  69 |  2 |  2 | True | False | chr1 | ... | 2310213 | ENSG00000157933.10 | . | + | HAVANA | gene | . | ID=ENSG00000157933.10;gene_id=ENSG00000157933.10;gene_type=protein_coding;gene_name=SKI;level=2;hgnc_id=HGNC:10896;havana_gene=OTTHUMG00000001407.4                           | SKI       | 35 |\n", "| 4 | chr1 | 3986108 | 3986143 | DEL |  35 |  2 |  2 | True | False | chr1 | ... | 4012704 | ENSG00000284668.2  | . | + | HAVANA | gene | . | ID=ENSG00000284668.2;gene_id=ENSG00000284668.2;gene_type=lncRNA;gene_name=LINC02780;level=2;hgnc_id=HGNC:54300;havana_gene=OTTHUMG00000192281.2                               | LINC02780 | 43 |\n", "| 5 | chr1 | 6025935 | 6025935 | INS |  45 |  2 |  2 | True | False | chr1 | ... | 6101193 | ENSG00000069424.15 | . | + | HAVANA | gene | . | ID=ENSG00000069424.15;gene_id=ENSG00000069424.15;gene_type=protein_coding;gene_name=KCNAB2;level=2;hgnc_id=HGNC:6229;havana_gene=OTTHUMG00000000795.17                        | KCNAB2    | 44 |\n", "| 6 | chr1 | 6488116 | 6488116 | BND |   0 | 10 | 42 | True | False | chr1 | ... | 6520074 | ENSG00000171680.23 | . | - | HAVANA | gene | . | ID=ENSG00000171680.23;gene_id=ENSG00000171680.23;gene_type=protein_coding;gene_name=PLEKHG5;level=2;hgnc_id=HGNC:29105;tag=overlapping_locus;havana_gene=OTTHUMG00000000905.5 | PLEKHG5   | 46 |\n", "\n"], "text/plain": ["  chr  start   stop    type length support coverage t1   t2    ens_chromosome\n", "1 chr1 1979665 1979878 DEL  213     2       2       True False chr1          \n", "2 chr1 2124193 2124193 INS  112     2       2       True False chr1          \n", "3 chr1 2281036 2281036 INS   69     2       2       True False chr1          \n", "4 chr1 3986108 3986143 DEL   35     2       2       True False chr1          \n", "5 chr1 6025935 6025935 INS   45     2       2       True False chr1          \n", "6 chr1 6488116 6488116 BND    0    10      42       True False chr1          \n", "  ... ens_stop ensemble_id        score strand source gene_type type_score\n", "1 ... 2003837  ENSG00000142609.19 .     -      HAVANA gene      .         \n", "2 ... 2185395  ENSG00000067606.17 .     +      HAVANA gene      .         \n", "3 ... 2310213  ENSG00000157933.10 .     +      HAVANA gene      .         \n", "4 ... 4012704  ENSG00000284668.2  .     +      HAVANA gene      .         \n", "5 ... 6101193  ENSG00000069424.15 .     +      HAVANA gene      .         \n", "6 ... 6520074  ENSG00000171680.23 .     -      HAVANA gene      .         \n", "  details                                                                                                                                                                      \n", "1 ID=ENSG00000142609.19;gene_id=ENSG00000142609.19;gene_type=protein_coding;gene_name=CFAP74;level=1;hgnc_id=HGNC:29368;havana_gene=OTTHUMG00000000945.17                      \n", "2 ID=ENSG00000067606.17;gene_id=ENSG00000067606.17;gene_type=protein_coding;gene_name=PRKCZ;level=1;hgnc_id=HGNC:9412;havana_gene=OTTHUMG00000001238.14                        \n", "3 ID=ENSG00000157933.10;gene_id=ENSG00000157933.10;gene_type=protein_coding;gene_name=SKI;level=2;hgnc_id=HGNC:10896;havana_gene=OTTHUMG00000001407.4                          \n", "4 ID=ENSG00000284668.2;gene_id=ENSG00000284668.2;gene_type=lncRNA;gene_name=LINC02780;level=2;hgnc_id=HGNC:54300;havana_gene=OTTHUMG00000192281.2                              \n", "5 ID=ENSG00000069424.15;gene_id=ENSG00000069424.15;gene_type=protein_coding;gene_name=KCNAB2;level=2;hgnc_id=HGNC:6229;havana_gene=OTTHUMG00000000795.17                       \n", "6 ID=ENSG00000171680.23;gene_id=ENSG00000171680.23;gene_type=protein_coding;gene_name=PLEKHG5;level=2;hgnc_id=HGNC:29105;tag=overlapping_locus;havana_gene=OTTHUMG00000000905.5\n", "  gene      index\n", "1 CFAP74    29   \n", "2 PRKCZ     33   \n", "3 SKI       35   \n", "4 LINC02780 43   \n", "5 KCNAB2    44   \n", "6 PLEKHG5   46   "]}, "metadata": {}, "output_type": "display_data"}], "source": ["input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t1_only_spec.tsv\", sep='\\t')\n", "# input_df$index = fct_reorder(input_df$index, input_df$type, sum, .desc=TRUE)\n", "\n", "head(input_df)"]}, {"cell_type": "code", "execution_count": null, "id": "6cbccd0a-4909-474a-9d45-acbcd3419f80", "metadata": {}, "outputs": [], "source": ["library(qqman)"]}, {"cell_type": "code", "execution_count": 14, "id": "912db098-2604-4d08-9d2a-0610b57d964f", "metadata": {}, "outputs": [{"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "# pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/sv_counts.pdf\")\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t1_only.tsv\", sep='\\t')\n", "input_df$index = fct_reorder(input_df$index, input_df$type, sum, .desc=TRUE)\n", "# input_df = input_df[input_df$gene %in% levels(input_df$gene)[1:50],]\n", "\n", "p = ggplot(input_df, aes(x = index, y = type)) + plot_theme() +\n", "geom_bar(stat=\"identity\", width=1, color=\"black\") +\n", "\n", "ylim(0,6000) +\n", "\n", "ylab(\"Read coverage\") + xlab(\"\") + ggtitle('Primary tumor only') +\n", "theme(axis.text=element_text(size=10))\n", "\n", "print(p)\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t2_only.tsv\", sep='\\t')\n", "input_df$index = fct_reorder(input_df$index, input_df$type, sum, .desc=TRUE)\n", "# input_df = input_df[input_df$gene %in% levels(input_df$gene)[1:50],]\n", "\n", "p = ggplot(input_df, aes(x = index, y = type)) + plot_theme() +\n", "geom_bar(stat=\"identity\", width=1, color=\"black\") +\n", "# geom_histogram() +\n", "ylim(0,6000) +\n", "\n", "\n", "ylab(\"Read coverage\") + xlab(\"\") + ggtitle('Recurrent tumor only') +\n", "theme(axis.text=element_text(size=10))\n", "\n", "print(p)\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t1_t2.tsv\", sep='\\t')\n", "input_df$index = fct_reorder(input_df$index, input_df$type, sum, .desc=TRUE)\n", "# input_df = input_df[input_df$gene %in% levels(input_df$gene)[1:50],]\n", "\n", "p = ggplot(input_df, aes(x = index, y = type)) + plot_theme() +\n", "geom_bar(stat=\"identity\", width=1, color=\"black\") +\n", "# geom_histogram() +\n", "ylim(0,6000) +\n", "\n", "ylab(\"Read coverage\") + xlab(\"\") + ggtitle('Primary and recurrent')+\n", "theme(axis.text=element_text(size=10))\n", "\n", "print(p)\n", "\n", "dev.off()\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "f617c435-65ba-4f15-b761-f2d9ef9e96fe", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "eeefa794-4559-42c4-941c-dd72ba63e55e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "55869bf0-be7f-4a51-8740-6f8542426fd8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d879d609-8c2e-4a7a-a12e-767b9914876a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "id": "f925a8b4-fe6a-4ce7-b048-64b2de1c05e5", "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A data.frame: 6 × 20</caption>\n", "<thead>\n", "\t<tr><th></th><th scope=col>chr</th><th scope=col>start</th><th scope=col>stop</th><th scope=col>type</th><th scope=col>length</th><th scope=col>support</th><th scope=col>coverage</th><th scope=col>t1</th><th scope=col>t2</th><th scope=col>ens_chromosome</th><th scope=col>ens_start</th><th scope=col>ens_stop</th><th scope=col>ensemble_id</th><th scope=col>score</th><th scope=col>strand</th><th scope=col>source</th><th scope=col>gene_type</th><th scope=col>type_score</th><th scope=col>details</th><th scope=col>gene</th></tr>\n", "\t<tr><th></th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><th scope=row>1</th><td>chr1</td><td> 1979665</td><td> 1979878</td><td>DEL</td><td> 213</td><td>2</td><td>2</td><td>True</td><td>False</td><td>chr1</td><td> 1921950</td><td> 2003837</td><td>ENSG00000142609.19</td><td>.</td><td>-</td><td>HAVANA</td><td>gene</td><td>.</td><td>ID=ENSG00000142609.19;gene_id=ENSG00000142609.19;gene_type=protein_coding;gene_name=CFAP74;level=1;hgnc_id=HGNC:29368;havana_gene=OTTHUMG00000000945.17</td><td>CFAP74   </td></tr>\n", "\t<tr><th scope=row>2</th><td>chr1</td><td> 2124193</td><td> 2124193</td><td>INS</td><td> 112</td><td>2</td><td>2</td><td>True</td><td>False</td><td>chr1</td><td> 2050410</td><td> 2185395</td><td>ENSG00000067606.17</td><td>.</td><td>+</td><td>HAVANA</td><td>gene</td><td>.</td><td>ID=ENSG00000067606.17;gene_id=ENSG00000067606.17;gene_type=protein_coding;gene_name=PRKCZ;level=1;hgnc_id=HGNC:9412;havana_gene=OTTHUMG00000001238.14  </td><td>PRKCZ    </td></tr>\n", "\t<tr><th scope=row>3</th><td>chr1</td><td> 2281036</td><td> 2281036</td><td>INS</td><td>  69</td><td>2</td><td>2</td><td>True</td><td>False</td><td>chr1</td><td> 2228318</td><td> 2310213</td><td>ENSG00000157933.10</td><td>.</td><td>+</td><td>HAVANA</td><td>gene</td><td>.</td><td>ID=ENSG00000157933.10;gene_id=ENSG00000157933.10;gene_type=protein_coding;gene_name=SKI;level=2;hgnc_id=HGNC:10896;havana_gene=OTTHUMG00000001407.4    </td><td>SKI      </td></tr>\n", "\t<tr><th scope=row>4</th><td>chr1</td><td> 3986108</td><td> 3986143</td><td>DEL</td><td>  35</td><td>2</td><td>2</td><td>True</td><td>False</td><td>chr1</td><td> 3976132</td><td> 4012704</td><td>ENSG00000284668.2 </td><td>.</td><td>+</td><td>HAVANA</td><td>gene</td><td>.</td><td>ID=ENSG00000284668.2;gene_id=ENSG00000284668.2;gene_type=lncRNA;gene_name=LINC02780;level=2;hgnc_id=HGNC:54300;havana_gene=OTTHUMG00000192281.2        </td><td>LINC02780</td></tr>\n", "\t<tr><th scope=row>5</th><td>chr1</td><td> 6025935</td><td> 6025935</td><td>INS</td><td>  45</td><td>2</td><td>2</td><td>True</td><td>False</td><td>chr1</td><td> 5990926</td><td> 6101193</td><td>ENSG00000069424.15</td><td>.</td><td>+</td><td>HAVANA</td><td>gene</td><td>.</td><td>ID=ENSG00000069424.15;gene_id=ENSG00000069424.15;gene_type=protein_coding;gene_name=KCNAB2;level=2;hgnc_id=HGNC:6229;havana_gene=OTTHUMG00000000795.17 </td><td>KCNAB2   </td></tr>\n", "\t<tr><th scope=row>6</th><td>chr1</td><td>11522631</td><td>11522631</td><td>INS</td><td>1296</td><td>3</td><td>3</td><td>True</td><td>False</td><td>chr1</td><td>11479154</td><td>11537551</td><td>ENSG00000204624.8 </td><td>.</td><td>+</td><td>HAVANA</td><td>gene</td><td>.</td><td>ID=ENSG00000204624.8;gene_id=ENSG00000204624.8;gene_type=protein_coding;gene_name=DISP3;level=2;hgnc_id=HGNC:29251;havana_gene=OTTHUMG00000002074.3    </td><td>DISP3    </td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A data.frame: 6 × 20\n", "\\begin{tabular}{r|llllllllllllllllllll}\n", "  & chr & start & stop & type & length & support & coverage & t1 & t2 & ens\\_chromosome & ens\\_start & ens\\_stop & ensemble\\_id & score & strand & source & gene\\_type & type\\_score & details & gene\\\\\n", "  & <chr> & <int> & <int> & <chr> & <int> & <int> & <int> & <chr> & <chr> & <chr> & <int> & <int> & <chr> & <chr> & <chr> & <chr> & <chr> & <chr> & <chr> & <chr>\\\\\n", "\\hline\n", "\t1 & chr1 &  1979665 &  1979878 & DEL &  213 & 2 & 2 & True & False & chr1 &  1921950 &  2003837 & ENSG00000142609.19 & . & - & HAVANA & gene & . & ID=ENSG00000142609.19;gene\\_id=ENSG00000142609.19;gene\\_type=protein\\_coding;gene\\_name=CFAP74;level=1;hgnc\\_id=HGNC:29368;havana\\_gene=OTTHUMG00000000945.17 & CFAP74   \\\\\n", "\t2 & chr1 &  2124193 &  2124193 & INS &  112 & 2 & 2 & True & False & chr1 &  2050410 &  2185395 & ENSG00000067606.17 & . & + & HAVANA & gene & . & ID=ENSG00000067606.17;gene\\_id=ENSG00000067606.17;gene\\_type=protein\\_coding;gene\\_name=PRKCZ;level=1;hgnc\\_id=HGNC:9412;havana\\_gene=OTTHUMG00000001238.14   & PRKCZ    \\\\\n", "\t3 & chr1 &  2281036 &  2281036 & INS &   69 & 2 & 2 & True & False & chr1 &  2228318 &  2310213 & ENSG00000157933.10 & . & + & HAVANA & gene & . & ID=ENSG00000157933.10;gene\\_id=ENSG00000157933.10;gene\\_type=protein\\_coding;gene\\_name=SKI;level=2;hgnc\\_id=HGNC:10896;havana\\_gene=OTTHUMG00000001407.4     & SKI      \\\\\n", "\t4 & chr1 &  3986108 &  3986143 & DEL &   35 & 2 & 2 & True & False & chr1 &  3976132 &  4012704 & ENSG00000284668.2  & . & + & HAVANA & gene & . & ID=ENSG00000284668.2;gene\\_id=ENSG00000284668.2;gene\\_type=lncRNA;gene\\_name=LINC02780;level=2;hgnc\\_id=HGNC:54300;havana\\_gene=OTTHUMG00000192281.2         & LINC02780\\\\\n", "\t5 & chr1 &  6025935 &  6025935 & INS &   45 & 2 & 2 & True & False & chr1 &  5990926 &  6101193 & ENSG00000069424.15 & . & + & HAVANA & gene & . & ID=ENSG00000069424.15;gene\\_id=ENSG00000069424.15;gene\\_type=protein\\_coding;gene\\_name=KCNAB2;level=2;hgnc\\_id=HGNC:6229;havana\\_gene=OTTHUMG00000000795.17  & KCNAB2   \\\\\n", "\t6 & chr1 & 11522631 & 11522631 & INS & 1296 & 3 & 3 & True & False & chr1 & 11479154 & 11537551 & ENSG00000204624.8  & . & + & HAVANA & gene & . & ID=ENSG00000204624.8;gene\\_id=ENSG00000204624.8;gene\\_type=protein\\_coding;gene\\_name=DISP3;level=2;hgnc\\_id=HGNC:29251;havana\\_gene=OTTHUMG00000002074.3     & DISP3    \\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A data.frame: 6 × 20\n", "\n", "| <!--/--> | chr &lt;chr&gt; | start &lt;int&gt; | stop &lt;int&gt; | type &lt;chr&gt; | length &lt;int&gt; | support &lt;int&gt; | coverage &lt;int&gt; | t1 &lt;chr&gt; | t2 &lt;chr&gt; | ens_chromosome &lt;chr&gt; | ens_start &lt;int&gt; | ens_stop &lt;int&gt; | ensemble_id &lt;chr&gt; | score &lt;chr&gt; | strand &lt;chr&gt; | source &lt;chr&gt; | gene_type &lt;chr&gt; | type_score &lt;chr&gt; | details &lt;chr&gt; | gene &lt;chr&gt; |\n", "|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|\n", "| 1 | chr1 |  1979665 |  1979878 | DEL |  213 | 2 | 2 | True | False | chr1 |  1921950 |  2003837 | ENSG00000142609.19 | . | - | HAVANA | gene | . | ID=ENSG00000142609.19;gene_id=ENSG00000142609.19;gene_type=protein_coding;gene_name=CFAP74;level=1;hgnc_id=HGNC:29368;havana_gene=OTTHUMG00000000945.17 | CFAP74    |\n", "| 2 | chr1 |  2124193 |  2124193 | INS |  112 | 2 | 2 | True | False | chr1 |  2050410 |  2185395 | ENSG00000067606.17 | . | + | HAVANA | gene | . | ID=ENSG00000067606.17;gene_id=ENSG00000067606.17;gene_type=protein_coding;gene_name=PRKCZ;level=1;hgnc_id=HGNC:9412;havana_gene=OTTHUMG00000001238.14   | PRKCZ     |\n", "| 3 | chr1 |  2281036 |  2281036 | INS |   69 | 2 | 2 | True | False | chr1 |  2228318 |  2310213 | ENSG00000157933.10 | . | + | HAVANA | gene | . | ID=ENSG00000157933.10;gene_id=ENSG00000157933.10;gene_type=protein_coding;gene_name=SKI;level=2;hgnc_id=HGNC:10896;havana_gene=OTTHUMG00000001407.4     | SKI       |\n", "| 4 | chr1 |  3986108 |  3986143 | DEL |   35 | 2 | 2 | True | False | chr1 |  3976132 |  4012704 | ENSG00000284668.2  | . | + | HAVANA | gene | . | ID=ENSG00000284668.2;gene_id=ENSG00000284668.2;gene_type=lncRNA;gene_name=LINC02780;level=2;hgnc_id=HGNC:54300;havana_gene=OTTHUMG00000192281.2         | LINC02780 |\n", "| 5 | chr1 |  6025935 |  6025935 | INS |   45 | 2 | 2 | True | False | chr1 |  5990926 |  6101193 | ENSG00000069424.15 | . | + | HAVANA | gene | . | ID=ENSG00000069424.15;gene_id=ENSG00000069424.15;gene_type=protein_coding;gene_name=KCNAB2;level=2;hgnc_id=HGNC:6229;havana_gene=OTTHUMG00000000795.17  | KCNAB2    |\n", "| 6 | chr1 | 11522631 | 11522631 | INS | 1296 | 3 | 3 | True | False | chr1 | 11479154 | 11537551 | ENSG00000204624.8  | . | + | HAVANA | gene | . | ID=ENSG00000204624.8;gene_id=ENSG00000204624.8;gene_type=protein_coding;gene_name=DISP3;level=2;hgnc_id=HGNC:29251;havana_gene=OTTHUMG00000002074.3     | DISP3     |\n", "\n"], "text/plain": ["  chr  start    stop     type length support coverage t1   t2    ens_chromosome\n", "1 chr1  1979665  1979878 DEL   213   2       2        True False chr1          \n", "2 chr1  2124193  2124193 INS   112   2       2        True False chr1          \n", "3 chr1  2281036  2281036 INS    69   2       2        True False chr1          \n", "4 chr1  3986108  3986143 DEL    35   2       2        True False chr1          \n", "5 chr1  6025935  6025935 INS    45   2       2        True False chr1          \n", "6 chr1 11522631 11522631 INS  1296   3       3        True False chr1          \n", "  ens_start ens_stop ensemble_id        score strand source gene_type\n", "1  1921950   2003837 ENSG00000142609.19 .     -      HAVANA gene     \n", "2  2050410   2185395 ENSG00000067606.17 .     +      HAVANA gene     \n", "3  2228318   2310213 ENSG00000157933.10 .     +      HAVANA gene     \n", "4  3976132   4012704 ENSG00000284668.2  .     +      HAVANA gene     \n", "5  5990926   6101193 ENSG00000069424.15 .     +      HAVANA gene     \n", "6 11479154  11537551 ENSG00000204624.8  .     +      HAVANA gene     \n", "  type_score\n", "1 .         \n", "2 .         \n", "3 .         \n", "4 .         \n", "5 .         \n", "6 .         \n", "  details                                                                                                                                                \n", "1 ID=ENSG00000142609.19;gene_id=ENSG00000142609.19;gene_type=protein_coding;gene_name=CFAP74;level=1;hgnc_id=HGNC:29368;havana_gene=OTTHUMG00000000945.17\n", "2 ID=ENSG00000067606.17;gene_id=ENSG00000067606.17;gene_type=protein_coding;gene_name=PRKCZ;level=1;hgnc_id=HGNC:9412;havana_gene=OTTHUMG00000001238.14  \n", "3 ID=ENSG00000157933.10;gene_id=ENSG00000157933.10;gene_type=protein_coding;gene_name=SKI;level=2;hgnc_id=HGNC:10896;havana_gene=OTTHUMG00000001407.4    \n", "4 ID=ENSG00000284668.2;gene_id=ENSG00000284668.2;gene_type=lncRNA;gene_name=LINC02780;level=2;hgnc_id=HGNC:54300;havana_gene=OTTHUMG00000192281.2        \n", "5 ID=ENSG00000069424.15;gene_id=ENSG00000069424.15;gene_type=protein_coding;gene_name=KCNAB2;level=2;hgnc_id=HGNC:6229;havana_gene=OTTHUMG00000000795.17 \n", "6 ID=ENSG00000204624.8;gene_id=ENSG00000204624.8;gene_type=protein_coding;gene_name=DISP3;level=2;hgnc_id=HGNC:29251;havana_gene=OTTHUMG00000002074.3    \n", "  gene     \n", "1 CFAP74   \n", "2 PRKCZ    \n", "3 SKI      \n", "4 LINC02780\n", "5 KCNAB2   \n", "6 DISP3    "]}, "metadata": {}, "output_type": "display_data"}], "source": ["input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t1_only.tsv\", sep='\\t')\n", "head(input_df)"]}, {"cell_type": "code", "execution_count": 14, "id": "c003496b-0062-4288-b00c-4fa3b74861c2", "metadata": {}, "outputs": [{"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/sv_counts.pdf\")\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t1_only.tsv\", sep='\\t')\n", "input_df$index = fct_reorder(input_df$index, input_df$type, sum, .desc=TRUE)\n", "# input_df = input_df[input_df$gene %in% levels(input_df$gene)[1:50],]\n", "\n", "p = ggplot(input_df, aes(x = index, y = type)) + plot_theme() +\n", "geom_bar(stat=\"identity\", width=1, color=\"black\") +\n", "\n", "ylim(0,6000) +\n", "\n", "ylab(\"Read coverage\") + xlab(\"\") + ggtitle('Primary tumor only') +\n", "theme(axis.text=element_text(size=10))\n", "\n", "print(p)\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t2_only.tsv\", sep='\\t')\n", "input_df$index = fct_reorder(input_df$index, input_df$type, sum, .desc=TRUE)\n", "# input_df = input_df[input_df$gene %in% levels(input_df$gene)[1:50],]\n", "\n", "p = ggplot(input_df, aes(x = index, y = type)) + plot_theme() +\n", "geom_bar(stat=\"identity\", width=1, color=\"black\") +\n", "# geom_histogram() +\n", "ylim(0,6000) +\n", "\n", "\n", "ylab(\"Read coverage\") + xlab(\"\") + ggtitle('Recurrent tumor only') +\n", "theme(axis.text=element_text(size=10))\n", "\n", "print(p)\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t1_t2.tsv\", sep='\\t')\n", "input_df$index = fct_reorder(input_df$index, input_df$type, sum, .desc=TRUE)\n", "# input_df = input_df[input_df$gene %in% levels(input_df$gene)[1:50],]\n", "\n", "p = ggplot(input_df, aes(x = index, y = type)) + plot_theme() +\n", "geom_bar(stat=\"identity\", width=1, color=\"black\") +\n", "# geom_histogram() +\n", "ylim(0,6000) +\n", "\n", "ylab(\"Read coverage\") + xlab(\"\") + ggtitle('Primary and recurrent')+\n", "theme(axis.text=element_text(size=10))\n", "\n", "print(p)\n", "\n", "dev.off()\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 20, "id": "4130402e-f240-4e21-ad78-d52c7570000b", "metadata": {}, "outputs": [{"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/top_sv_support_goi.pdf\", width=4)\n", "\n", "genes_of_interest = c(\"EGFR\", \"VEGF\", \"PDGFRA\", \"OLIG2\", \"TP53\", \"PI3K\", \"IDH1\", \"PTEN\", \"CHKN2\", \"NFKB\", \"NF1\", \"TERT\", \"CDKN2A\", \"ATRX\", \"FGFR\", \"RB1\")\n", "\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t1_only_all.tsv\", sep='\\t')\n", "input_df$gene = fct_reorder(input_df$gene, input_df$support, sum, .desc=TRUE)\n", "input_df = input_df[input_df$gene %in% genes_of_interest,]\n", "\n", "p = ggplot(input_df, aes(x = gene, y = length, fill = type)) + plot_theme() +\n", "geom_bar(stat=\"identity\", width=1, color=\"black\") +\n", "\n", "ylab(\"Length\") + xlab(\"\") + ggtitle('Primary tumor only')\n", "print(p)\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t2_only_all.tsv\", sep='\\t')\n", "input_df$gene = fct_reorder(input_df$gene, input_df$support, sum, .desc=TRUE)\n", "input_df = input_df[input_df$gene %in% genes_of_interest,]\n", "\n", "p = ggplot(input_df, aes(x = gene, y = length, fill = type)) + plot_theme() +\n", "geom_bar(stat=\"identity\", width=1, color=\"black\") +\n", "\n", "ylab(\"Length\") + xlab(\"\") + ggtitle('Recurrent tumor only')\n", "print(p)\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/sv_t1_t2_all.tsv\", sep='\\t')\n", "\n", "input_df$gene = fct_reorder(input_df$gene, input_df$support, sum, .desc=TRUE)\n", "input_df = input_df[input_df$gene %in% genes_of_interest,]\n", "\n", "p = ggplot(input_df, aes(x = gene, y = length, fill = type)) + plot_theme() +\n", "geom_bar(stat=\"identity\", width=1, color=\"black\") +\n", "\n", "ylab(\"Length\") + xlab(\"\") + ggtitle('Primary and recurrent')\n", "print(p)\n", "\n", "dev.off()\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 33, "id": "c6e68c95-297e-4b68-8823-455e0221ebaa", "metadata": {}, "outputs": [], "source": ["\n", "# pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/vcf.pdf\")\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/wgs/RLGS10-recurrent_wgs_seq/pass_intersect.bed\", sep='\\t')\n", "input_df$gene = fct_reorder(input_df$gene, input_df$count, sum, .desc=TRUE)\n", "genes_of_interest = c(\"EGFR\", \"VEGF\", \"PDGFRA\", \"OLIG2\", \"TP53\", \"PI3K\", \"IDH1\", \"PTEN\", \"CHKN2\", \"NFKB\", \"NF1\", \"TERT\", \"CDKN2A\", \"ATRX\", \"FGFR\", \"RB1\")\n", "input_df = input_df[input_df$gene %in% genes_of_interest,]\n", "\n", "# input_df = input_df[input_df$gene %in% genes_of_interest,]\n", "\n", "# head( input_df)\n", "# p = ggplot(input_df, aes(x = gene)) + plot_theme() +\n", "# # geom_histogram() +\n", "\n", "# # geom_bar(position=\"stack\", stat=\"identity\") +\n", "\n", "# ylab(\"Number of mutations\") + xlab(\"Gene\") + ggtitle('')\n", "\n"]}, {"cell_type": "code", "execution_count": 37, "id": "3956a9d4-757d-4230-8790-b3c4819594b0", "metadata": {}, "outputs": [{"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/vcf.pdf\")\n", "\n", "p = ggplot(input_df, aes(x = gene, y = count)) + plot_theme() +\n", "# geom_bar(stat=\"identity\", width=1, color=\"black\", position = position_dodge(width=0.1)) +\n", "geom_bar(stat=\"identity\", width=1, color=\"black\") +\n", "\n", "# scale_y_continuous(trans='log10') +\n", "\n", "ylab(\"Tumor subclonal frequency\") + xlab(\"\") + ggtitle('') +\n", "theme(axis.text=element_text(size=10))\n", "\n", "\n", "print(p)\n", "\n", "dev.off()"]}, {"cell_type": "code", "execution_count": null, "id": "f5b41b33-f3b1-40a3-b885-a360a7a6b56c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c5ac0698-e895-4859-9c25-f719af3c2844", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ef3ac799-9bac-452a-b0ec-07460ff404cb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e338faac-9840-46e5-aa53-e263558cc770", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5d0d5fc5-8f4e-4bea-9dde-7d98501b13b3", "metadata": {}, "outputs": [], "source": ["gbm = read.maf(maf = \"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/wgs/RLGS10-recurrent_wgs_seq/somatic.snvs.maf\")\n", "\n", "\n", "plot_spec_gene = function(gene){\n", "try(\n", "lollipopPlot(\n", "  maf = gbm,\n", "  gene = gene,\n", "  AACol = 'AAChange.refGene',\n", "  showMutationRate = FALSE)\n", ")\n", "}\n", "\n", "\n", "genes_of_interest = c(\"EGFR\", \"VEGF\", \"PDGFRA\", \"OLIG2\", \"TP53\", \"PI3K\", \"IDH1\", \"PTEN\", \"CHKN2\", \"NFKB\", \"NF1\", \"TERT\", \"CDKN2A\", \"ATRX\", \"FGFR\", \"RB1\")\n", "\n", "# create a pdf\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/recurrent_snvs.pdf\")\n", "\n", "plotmafSummary(maf = gbm, rmOutlier = TRUE, addStat = 'median', dashboard = TRUE, titvRaw = FALSE)\n", "\n", "oncoplot(maf = gbm, top = 20)\n", "\n", "lapply(genes_of_interest, plot_spec_gene)\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": null, "id": "3916adea-c243-465c-b1bf-bcb59a20eb74", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d7d4abbd-228d-4f50-a416-b052e1f7c12d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 30, "id": "02a9f39d-8496-479d-a61d-529d9d0eb98d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[1m\u001b[22m`stat_bin()` using `bins = 30`. Pick better value with `binwidth`.\n", "Warning message in `[.data.frame`(input_df, !is.na(as.numeric(input_df$support)), :\n", "\"NAs introduced by coercion\"\n", "Warning message:\n", "\"\u001b[1m\u001b[22mRemoved 3 rows containing missing values (`geom_point()`).\"\n"]}, {"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/tmp/t2_genes.tsv\", sep='\\t')\n", "\n", "# pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/sig_sv_genes.pdf\")\n", "\n", "p = ggplot(input_df, aes(x = (length), fill = type)) + plot_theme() +\n", "geom_histogram() +\n", "\n", "# geom_bar(position=\"stack\", stat=\"identity\") +\n", "\n", "# scale_colour_manual(values=sig_colours) +\n", "\n", "# geom_hline(yintercept=1, linetype='solid', color='grey', size=0.5) +\n", "# geom_errorbar(aes(ymin=lower_ci, ymax=upper_ci),width=0)+\n", "# geom_point(size=2) +\n", "\n", "# scale_x_binned(n.breaks = 10000) +\n", "# scale_y_continuous(trans='log10') +\n", "scale_x_continuous(trans='log10') +\n", "\n", "\n", "ylab(\"Recurrent - Germline events\") + xlab(\"Length (bp)\") + ggtitle('')\n", "print(p)\n", "\n", "\n", "\n", "p = ggplot(input_df, aes(x = length, y = coverage, color = type)) + plot_theme() +\n", "geom_point() +\n", "\n", "scale_x_continuous(trans='log10') +\n", "\n", "ylab(\"Sequencing coverage\") + xlab(\"Length (bp)\") + ggtitle('')\n", "print(p)\n", "\n", "\n", "sub_df = input_df[!is.na(as.numeric(input_df$support)),]\n", "sub_df$support = as.numeric(sub_df$support)\n", "p = ggplot(sub_df, aes(x = support, y = coverage, color = type)) + plot_theme() +\n", "geom_point() +\n", "\n", "xlim(0,25)+\n", "ylab(\"Coverage\") + xlab(\"Read support\") + ggtitle('')\n", "print(p)\n", "\n", "# theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), legend.position='right')\n", "\n", "\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": null, "id": "237df693-e3d8-4404-ab7c-eaa4c7cce6dd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dea7c419-715c-4403-8c95-f0832cc18089", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6cee1caf-9cc9-4224-8b56-c784e8e59864", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cf1d4e22-c210-4f19-9a89-cfa25f26090c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0e75089b-4c0f-4bee-93c5-5926a67f1038", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3423e7d2-bf2e-4b57-8a2c-89ddaff9f1ef", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dc8058f0-358d-4e58-aaa6-a1c297e176a4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "6e3bbf32-96f8-4ae8-bfe5-4720de85fcd5", "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A data.frame: 6 × 7</caption>\n", "<thead>\n", "\t<tr><th></th><th scope=col>chr</th><th scope=col>start</th><th scope=col>stop</th><th scope=col>type</th><th scope=col>length</th><th scope=col>support</th><th scope=col>coverage</th></tr>\n", "\t<tr><th></th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><th scope=row>1</th><td>chr1</td><td>140033</td><td>140033</td><td>INS</td><td>3397</td><td>1</td><td>38</td></tr>\n", "\t<tr><th scope=row>2</th><td>chr1</td><td>492199</td><td>492199</td><td>INS</td><td>1121</td><td>2</td><td>13</td></tr>\n", "\t<tr><th scope=row>3</th><td>chr1</td><td>727291</td><td>727291</td><td>INS</td><td>  94</td><td>4</td><td>82</td></tr>\n", "\t<tr><th scope=row>4</th><td>chr1</td><td>740741</td><td>740741</td><td>INS</td><td>5668</td><td>2</td><td>91</td></tr>\n", "\t<tr><th scope=row>5</th><td>chr1</td><td>744868</td><td>744868</td><td>INS</td><td>  43</td><td>4</td><td>80</td></tr>\n", "\t<tr><th scope=row>6</th><td>chr1</td><td>748218</td><td>748218</td><td>INS</td><td> 331</td><td>3</td><td>73</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A data.frame: 6 × 7\n", "\\begin{tabular}{r|lllllll}\n", "  & chr & start & stop & type & length & support & coverage\\\\\n", "  & <chr> & <int> & <chr> & <chr> & <int> & <chr> & <int>\\\\\n", "\\hline\n", "\t1 & chr1 & 140033 & 140033 & INS & 3397 & 1 & 38\\\\\n", "\t2 & chr1 & 492199 & 492199 & INS & 1121 & 2 & 13\\\\\n", "\t3 & chr1 & 727291 & 727291 & INS &   94 & 4 & 82\\\\\n", "\t4 & chr1 & 740741 & 740741 & INS & 5668 & 2 & 91\\\\\n", "\t5 & chr1 & 744868 & 744868 & INS &   43 & 4 & 80\\\\\n", "\t6 & chr1 & 748218 & 748218 & INS &  331 & 3 & 73\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A data.frame: 6 × 7\n", "\n", "| <!--/--> | chr &lt;chr&gt; | start &lt;int&gt; | stop &lt;chr&gt; | type &lt;chr&gt; | length &lt;int&gt; | support &lt;chr&gt; | coverage &lt;int&gt; |\n", "|---|---|---|---|---|---|---|---|\n", "| 1 | chr1 | 140033 | 140033 | INS | 3397 | 1 | 38 |\n", "| 2 | chr1 | 492199 | 492199 | INS | 1121 | 2 | 13 |\n", "| 3 | chr1 | 727291 | 727291 | INS |   94 | 4 | 82 |\n", "| 4 | chr1 | 740741 | 740741 | INS | 5668 | 2 | 91 |\n", "| 5 | chr1 | 744868 | 744868 | INS |   43 | 4 | 80 |\n", "| 6 | chr1 | 748218 | 748218 | INS |  331 | 3 | 73 |\n", "\n"], "text/plain": ["  chr  start  stop   type length support coverage\n", "1 chr1 140033 140033 INS  3397   1       38      \n", "2 chr1 492199 492199 INS  1121   2       13      \n", "3 chr1 727291 727291 INS    94   4       82      \n", "4 chr1 740741 740741 INS  5668   2       91      \n", "5 chr1 744868 744868 INS    43   4       80      \n", "6 chr1 748218 748218 INS   331   3       73      "]}, "metadata": {}, "output_type": "display_data"}], "source": ["input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/tmp/sniffles.csv\")\n", "head(input_df)"]}, {"cell_type": "code", "execution_count": 30, "id": "85f5dedb-d6d9-4598-bfc5-16fd4d89ea91", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[1m\u001b[22m`stat_bin()` using `bins = 30`. Pick better value with `binwidth`.\n", "Warning message in `[.data.frame`(input_df, !is.na(as.numeric(input_df$support)), :\n", "\"NAs introduced by coercion\"\n", "Warning message:\n", "\"\u001b[1m\u001b[22mRemoved 3 rows containing missing values (`geom_point()`).\"\n"]}, {"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/sniffles.pdf\")\n", "\n", "p = ggplot(input_df, aes(x = (length), fill = type)) + plot_theme() +\n", "geom_histogram() +\n", "\n", "# geom_bar(position=\"stack\", stat=\"identity\") +\n", "\n", "# scale_colour_manual(values=sig_colours) +\n", "\n", "# geom_hline(yintercept=1, linetype='solid', color='grey', size=0.5) +\n", "# geom_errorbar(aes(ymin=lower_ci, ymax=upper_ci),width=0)+\n", "# geom_point(size=2) +\n", "\n", "# scale_x_binned(n.breaks = 10000) +\n", "# scale_y_continuous(trans='log10') +\n", "scale_x_continuous(trans='log10') +\n", "\n", "\n", "ylab(\"Recurrent - Germline events\") + xlab(\"Length (bp)\") + ggtitle('')\n", "print(p)\n", "\n", "\n", "\n", "p = ggplot(input_df, aes(x = length, y = coverage, color = type)) + plot_theme() +\n", "geom_point() +\n", "\n", "scale_x_continuous(trans='log10') +\n", "\n", "ylab(\"Sequencing coverage\") + xlab(\"Length (bp)\") + ggtitle('')\n", "print(p)\n", "\n", "\n", "sub_df = input_df[!is.na(as.numeric(input_df$support)),]\n", "sub_df$support = as.numeric(sub_df$support)\n", "p = ggplot(sub_df, aes(x = support, y = coverage, color = type)) + plot_theme() +\n", "geom_point() +\n", "\n", "xlim(0,25)+\n", "ylab(\"Coverage\") + xlab(\"Read support\") + ggtitle('')\n", "print(p)\n", "\n", "# theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), legend.position='right')\n", "\n", "\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": null, "id": "e93fb33a-9971-40a4-83f5-b7431387f81f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f89d4141-26cc-4b3b-a940-18abbb2357f8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "588b086e-a8af-4e0a-97dc-15522a06fe83", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 47, "id": "70d02a9c-bd21-46a3-8b9f-49466c90d6bd", "metadata": {}, "outputs": [{"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/glass_primary_recurrent.tsv\", sep='\\t')\n", "\n", "\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/glass_primary_recurrent.pdf\")\n", "\n", "\n", "p = ggplot(input_df, aes(x = tumor, y = signature_name, fill=signature_name)) + plot_theme() +\n", "geom_bar(position=\"stack\", stat=\"identity\") +\n", "\n", "# scale_colour_manual(values=sig_colours) +\n", "\n", "# geom_hline(yintercept=1, linetype='solid', color='grey', size=0.5) +\n", "# geom_errorbar(aes(ymin=lower_ci, ymax=upper_ci),width=0)+\n", "# geom_point(size=2) +\n", "\n", "# scale_x_binned(n.breaks = 10000) +\n", "# scale_y_continuous(trans='log10') +\n", "\n", "ylab(\"Expression subtype\") + xlab(\"\") + ggtitle('') +\n", "\n", "theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), legend.position='right')\n", "\n", "print(p)\n", "\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": 4, "id": "5186fa0b-78d1-40c3-bdc7-62ddd53bdf04", "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A data.frame: 6 × 3</caption>\n", "<thead>\n", "\t<tr><th></th><th scope=col>sample</th><th scope=col>class</th><th scope=col>score</th></tr>\n", "\t<tr><th></th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;dbl&gt;</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><th scope=row>1</th><td>primary_rna_seq  </td><td>mesenchymal</td><td> 0.14171423</td></tr>\n", "\t<tr><th scope=row>2</th><td>primary_rna_seq  </td><td>classical  </td><td> 0.23119252</td></tr>\n", "\t<tr><th scope=row>3</th><td>primary_rna_seq  </td><td>proneural  </td><td> 0.51256372</td></tr>\n", "\t<tr><th scope=row>4</th><td>recurrent_rna_seq</td><td>mesenchymal</td><td> 0.14601973</td></tr>\n", "\t<tr><th scope=row>5</th><td>recurrent_rna_seq</td><td>classical  </td><td>-0.01757637</td></tr>\n", "\t<tr><th scope=row>6</th><td>recurrent_rna_seq</td><td>proneural  </td><td> 0.56030892</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A data.frame: 6 × 3\n", "\\begin{tabular}{r|lll}\n", "  & sample & class & score\\\\\n", "  & <chr> & <chr> & <dbl>\\\\\n", "\\hline\n", "\t1 & primary\\_rna\\_seq   & mesenchymal &  0.14171423\\\\\n", "\t2 & primary\\_rna\\_seq   & classical   &  0.23119252\\\\\n", "\t3 & primary\\_rna\\_seq   & proneural   &  0.51256372\\\\\n", "\t4 & recurrent\\_rna\\_seq & mesenchymal &  0.14601973\\\\\n", "\t5 & recurrent\\_rna\\_seq & classical   & -0.01757637\\\\\n", "\t6 & recurrent\\_rna\\_seq & proneural   &  0.56030892\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A data.frame: 6 × 3\n", "\n", "| <!--/--> | sample &lt;chr&gt; | class &lt;chr&gt; | score &lt;dbl&gt; |\n", "|---|---|---|---|\n", "| 1 | primary_rna_seq   | mesenchymal |  0.14171423 |\n", "| 2 | primary_rna_seq   | classical   |  0.23119252 |\n", "| 3 | primary_rna_seq   | proneural   |  0.51256372 |\n", "| 4 | recurrent_rna_seq | mesenchymal |  0.14601973 |\n", "| 5 | recurrent_rna_seq | classical   | -0.01757637 |\n", "| 6 | recurrent_rna_seq | proneural   |  0.56030892 |\n", "\n"], "text/plain": ["  sample            class       score      \n", "1 primary_rna_seq   mesenchymal  0.14171423\n", "2 primary_rna_seq   classical    0.23119252\n", "3 primary_rna_seq   proneural    0.51256372\n", "4 recurrent_rna_seq mesenchymal  0.14601973\n", "5 recurrent_rna_seq classical   -0.01757637\n", "6 recurrent_rna_seq proneural    0.56030892"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/tmp/class_scores.tsv\", sep='\\t')\n", "head(input_df)\n", "\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/class_scores.pdf\")\n", "\n", "\n", "p = ggplot(input_df, aes(x = sample, y = score, fill=class)) + plot_theme() +\n", "geom_bar(position=\"dodge\", stat=\"identity\") +\n", "\n", "# scale_colour_manual(values=sig_colours) +\n", "\n", "# geom_hline(yintercept=1, linetype='solid', color='grey', size=0.5) +\n", "# geom_errorbar(aes(ymin=lower_ci, ymax=upper_ci),width=0)+\n", "# geom_point(size=2) +\n", "\n", "# scale_x_binned(n.breaks = 10000) +\n", "# scale_y_continuous(trans='log10') +\n", "\n", "ylab(\"Expression subtype score\") + xlab(\"\") + ggtitle('') +\n", "\n", "theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), legend.position='right')\n", "\n", "print(p)\n", "\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": null, "id": "e5b5fdf9-1abb-4b1a-8dd4-c2aa9cf20c23", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "78cf29fd-d19d-48e5-937d-11249864df40", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 31, "id": "bf938d50-16af-4951-a0cd-ed3d44433423", "metadata": {}, "outputs": [{"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/nanopore_coverage.tsv\", sep='\\t')\n", "\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/nanopore_coverage_t1.pdf\")\n", "\n", "# input_df$t1_gl[input_df$t1_gl > 20] = 20\n", "\n", "sig_colours = c(\"firebrick\", 'black')\n", "names(sig_colours) = c(\"True\", 'False')\n", "\n", "\n", "for (chr in unique(input_df$chr)){\n", "\n", "sub_df = input_df[input_df$chr == chr,]\n", "\n", "\n", "p = ggplot(sub_df, aes(x = start, y = t1_gl, color=t1_gl_high)) + plot_theme() +\n", "geom_point() +\n", "\n", "scale_colour_manual(values=sig_colours) +\n", "\n", "# geom_hline(yintercept=1, linetype='solid', color='grey', size=0.5) +\n", "# geom_errorbar(aes(ymin=lower_ci, ymax=upper_ci),width=0)+\n", "# geom_point(size=2) +\n", "\n", "# scale_x_binned(n.breaks = 10000) +\n", "# scale_y_continuous(trans='log10') +\n", "\n", "ylab(\"Mean difference (T1 - GL)\") + xlab(\"Position\") + ggtitle(chr) +\n", "\n", "theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), legend.position='none')\n", "\n", "print(p)\n", "\n", "}\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": 33, "id": "d1bbf807-89c5-4ed3-8c63-5e825869345a", "metadata": {}, "outputs": [{"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/nanopore_coverage.tsv\", sep='\\t')\n", "\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/nanopore_coverage_t2.pdf\")\n", "\n", "# input_df$t1_gl[input_df$t1_gl > 20] = 20\n", "\n", "sig_colours = c(\"firebrick\", 'black')\n", "names(sig_colours) = c(\"True\", 'False')\n", "\n", "\n", "for (chr in unique(input_df$chr)){\n", "\n", "sub_df = input_df[input_df$chr == chr,]\n", "\n", "\n", "p = ggplot(sub_df, aes(x = start, y = t2_gl, color=t2_gl_high)) + plot_theme() +\n", "geom_point() +\n", "\n", "scale_colour_manual(values=sig_colours) +\n", "\n", "# geom_hline(yintercept=1, linetype='solid', color='grey', size=0.5) +\n", "# geom_errorbar(aes(ymin=lower_ci, ymax=upper_ci),width=0)+\n", "# geom_point(size=2) +\n", "\n", "# scale_x_binned(n.breaks = 10000) +\n", "# scale_y_continuous(trans='log10') +\n", "\n", "ylab(\"Mean difference (T2 - GL)\") + xlab(\"Position\") + ggtitle(chr) +\n", "\n", "theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), legend.position='none')\n", "\n", "print(p)\n", "\n", "}\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": 24, "id": "2af08ad2-506a-4637-adc7-fe9bbd7ac09e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 27, "id": "860b88ca-7592-464d-9cd0-4d9a76b7a5d8", "metadata": {}, "outputs": [{"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/umap.tsv\", sep='\\t')\n", "\n", "\n", "cancer_colours = c('darkorange', 'green', 'beige', 'grey', 'violet', 'white', 'dodgerblue', 'firebrick')\n", "names(cancer_colours) = c('Classical', 'G-CIMP', 'Mesenchymal', 'Neural', 'Proneural', 'nan', 'primary', 'recurrent')\n", "\n", "# plot\n", "p1 = ggplot(input_df, aes(x=x_dim, y=y_dim, fill=subtype)) + plot_theme() +\n", "geom_point(pch=21, size = 4) +\n", "\n", "scale_fill_manual(values=cancer_colours) +\n", "# scale_colour_manual(values=cancer_text_colours) +\n", "\n", "# geom_label_repel(data=df_text, aes(label=cancer_type, fill=cancer_type, colour = cancer_type), show.legend=FALSE, segment.color = NA,label.size=NA,\n", "#         seed              = 1234,\n", "#         size\t\t\t\t= 7,\n", "#         force             = 1,\n", "#         nudge_x           = 1,\n", "#         hjust             = 0,\n", "#         segment.size      = 0.2,\n", "#         max.overlaps = 30\n", "#                ) +\n", "\n", "\n", "xlab(\"UMAP 1\") +\n", "ylab(\"UMAP 2\") +\n", "ggtitle('') +\n", "\n", "# guides(fill=guide_legend(title=\"Cancer type\", byrow = FALSE, override.aes = list(size = 3))) +\n", "\n", "theme(panel.grid = element_blank(), \n", "            axis.line = element_line(colour = 'black', size = 0.5))\n", "\n", "# legend = cowplot::get_legend(p1)\n", "\n", "# p1 = p1 + theme(legend.position='none')\n", "\n", "# print plots to file\n", "pdf('/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/umap_subset.pdf', width=10, height=7.5)\n", "\n", "print(p1)\n", "# print(legend)\n", "\n", "dev.off()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "2ba4d427-0eff-4004-bc73-24bf0b6a9d76", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7f560079-0e46-423c-b4c4-7ec2a0b038f1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.2.2"}}, "nbformat": 4, "nbformat_minor": 5}