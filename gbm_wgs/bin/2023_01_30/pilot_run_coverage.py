
import pandas as pd 
import numpy as np

# add list of chromosomes
chromosomes = []
for i in range(23):
    chromosomes.append(f"chr{i}")
    
chromosomes.append("chrX")

samples = ['RLGS_0010_gl', 'RLGS_0010_t1', 'RLGS_0010_t2']

cov_df = []

# combined dfs
for i, sample in enumerate(samples):
    df = pd.read_csv(f"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/nanopore/{sample}.regions.bed.gz", sep='\t', compression="gzip", header=None)
    df.columns = ['chr', 'start', 'stop', 'mean']
    
    # subset to chromosomes of interest and combine
    if i == 0:
        df.columns = ['chr', 'start', 'stop', sample]
        cov_df = df.loc[np.isin(df['chr'], chromosomes),:].copy()
    else:
        # subset to chromosomes of interest
        df = df.loc[np.isin(df['chr'], chromosomes),:]
        
        cov_df.loc[:,sample] = df['mean']

# calculate coverage differences
cov_df.loc[:,'t1_gl'] = cov_df['RLGS_0010_t1'] - cov_df['RLGS_0010_gl']
cov_df.loc[:,'t2_gl'] = cov_df['RLGS_0010_t2'] - cov_df['RLGS_0010_gl']

# determine if a region has higher than expected difference
for comparison in ['t1_gl', 't2_gl']:
    for chrom in chromosomes:
        mask = cov_df['chr'] == chrom

        # calculate the variance
        variance = np.std(cov_df.loc[mask,comparison])
        high_var_mask = np.abs(cov_df.loc[mask,comparison]) > 3*variance

        # indicate highly variant region
        cov_df.loc[mask,f'{comparison}_high'] = high_var_mask


# save to file
cov_df.to_csv("/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/nanopore_coverage.tsv", sep='\t', index=False)

# mask = np.abs(cov_df['t2_gl'] - cov_df['t1_gl']) > 30

mask = np.logical_and(cov_df['chr'] == 'chr8', mask)

print(cov_df.loc[mask,:])


