import sys, getopt, time, subprocess, os, pathlib, glob, re
import gzip, io

import pandas as pd 
import numpy as np
from lifelines import Cox<PERSON>HFitter, utils

import scipy.stats as stats
import statsmodels.stats.multitest as multitest



def subset_gzip_fa_to_chr(infile = "/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/hg38.fa.gz", outfile = "/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/hg38_chr.fa.gz"):
    # list of real chromosomes
    chrom = [f">chr{n}" for n in range(23)]
    chrom.extend([">chrX", ">chrY"])

    write = False

    # read file by line and write chromosomes of interest to new file
    with gzip.open(outfile, "wt") as outfile:
        with gzip.open(infile,'rt') as infile:
            for line in infile:
                if line.startswith(">"):
                    write = False
                    if line.strip("\n") in chrom:
                        print(line)
                        write = True
                if write:
                    outfile.write(line)



def read_vcf(path):
    if path.endswith(".gz"):
        with gzip.open(path, 'rt') as f:
            lines = [l for l in f if not l.startswith('##')]
    else:
        with open(path, 'r') as f:
            lines = [l for l in f if not l.startswith('##')]
    return pd.read_csv(
        io.StringIO(''.join(lines)),
        dtype={'#CHROM': str, 'POS': int, 'ID': str, 'REF': str, 'ALT': str,
               'QUAL': str, 'FILTER': str, 'INFO': str},
        sep='\t'
    ).rename(columns={'#CHROM': 'CHROM'})
