# Alec Bahcheli - <EMAIL>
# this workflow is designed for singularity containers and GPUs for massively paralleled basecalling 


###################################
# Create project directories
###################################
# create directory if it does not already exist
rule fastq_directories:
    output:
        working_file = RAW_DATA_DIR + "/{sample}/null.txt"
        
    params:
        working_directory = RAW_DATA_DIR + "/{sample}"
        
    resources:
        threads = 1,
        qeue = "all.q",
        runtime = '0:2:0:0',
        individual_core_memory = '10G'
        
    run:
        shell("mkdir -p {RAW_DATA_DIR} {params.working_directory}")
        shell("touch {output.working_file}")



#####################
# nanopore basecalling
#####################


# guppy basecalling parameters
rule guppy_gpu_basecaller:
    input:
        # input_fast5_dir = FAST5_DIRECTORY + "/{nanopore_sample}/{sub_dir}"
        input_fast5_dir = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/data/tmp/fast5_testing'
        
    output:
        # output_fastq_dir = directory(BASECALLED_DIRECTORY + "/{nanopore_sample}/{sub_dir}/pass")
        output_fastq_dir = directory('/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/data/tmp/tmp/pass')
        
    singularity:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/guppy/guppy_gpu_v3.sif"
    
    resources:
        threads = 1,
        qeue = "gpu.q",
        runtime = '0:119:0:0',
        individual_core_memory = '40G'
        
    shell:
        '/ont-guppy/guppy_basecaller --recursive --device "cuda:all" -q 0 --compress_fastq --config dna_r9.4.1_450bps_sup.cfg \
-i {input.input_fast5_dir} \
-s {output.output_fastq_dir}'






