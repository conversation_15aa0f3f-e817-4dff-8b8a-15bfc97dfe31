process {
    executor = "sge"
    penv = "smp"
    memory = '24G'
    cpus = '1'
    time = '12h'
    clusterOptions = { "-V -l h_vmem=24G -V -P reimandlab -l h_stack=32M -l h_rt=48:00:00" }

    withName: makeReport {
        memory = '48G'
        cpus = '1'
        clusterOptions = { "-V -l h_vmem=48G -V -P reimandlab -l h_stack=32M -l h_rt=48:00:00" }
    }

    withName: dorado {
        clusterOptions = { "-V -q gpu.q -l h_rt=96:00:00" }
    }
}

executor {
    name = "sge"
    queueSize = 500
    queueStatInterval = "10s"
}
