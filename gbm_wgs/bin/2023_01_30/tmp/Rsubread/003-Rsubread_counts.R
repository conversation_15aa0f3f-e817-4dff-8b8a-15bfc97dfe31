# Alec <PERSON>
library(optparse)
library(Rsubread)



# options list for parser options
option_list <- list(
    make_option(c("-b","--gtf_file"), type="character", default=NULL,
            help="",
            dest="gtf_file"),
    make_option(c("-c","--output_counts_file"), type="character", default=NULL,
            help="",
            dest="output_counts_file"),
    make_option(c("-d","--working_directory"), type="character", default=NULL,
            help="",
            dest="working_directory"),
    make_option(c("-e","--threads"), type="character", default=NULL,
            help="",
            dest="threads")
            )


parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)


# set directory
setwd(opt$working_directory)

# define bamfiles
bamfiles = list.files(path = opt$working_directory, pattern = ".BAM$", full.names = TRUE)

# gene counts
fc = featureCounts(bamfiles, annot.ext=opt$gtf_file, isGTFAnnotationFile = TRUE, isPairedEnd=TRUE, GTF.attrType = "gene_name", nthreads = opt$threads)

saveRDS(fc, file = paste(opt$working_directory, "counts.rds", sep=''))

write.table(fc$counts, file = opt$output_counts_file, sep='\t')




