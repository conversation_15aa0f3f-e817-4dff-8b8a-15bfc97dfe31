# Alec Bahcheli

# configfile: "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/snakemake_config.yaml"

# run version (typically date)
VERSION='2023_01_30'

# project directory
MAIN_DIR='/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs'

# directory of scripts / binaries
BIN_DIR = "/".join([MAIN_DIR, "bin", VERSION])



# results directories
DATA_DIR = "/".join([MAIN_DIR, "data", VERSION])
REF_DATA_DIR = "/".join([MAIN_DIR, "data", "ref_data"])
RAW_DATA_DIR= "/".join([MAIN_DIR, "data", "raw_data"])

RES_DIR = "/".join([MAIN_DIR, "results", VERSION])
FIGURE_DATA_DIR = "/".join([RES_DIR, "_figure_data"])
FIGURE_DIR = "/".join([RES_DIR, "_figures"])



# location of R environment for running R scripts 
RSCRIPT='/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript'
# location of python
PYTHON='/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev/bin/python'


# fastq directory
## NOTE assumes that reads are paired-end and end with either "...R1.fastq.gz" or "...R2.fastq.gz"
FASTQ_DIR = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/data/raw_data/all_fastqs"


# a fasta file of the genome that you are mapping to
## NOTE must be in the WGS_MAIN_DIR
GENOME_VERSION = "hg38_chr"

# a fasta file of the genome that you are mapping to
genome_fasta = RES_DIR + "/hg38_chr.fa"

# gtf file for calculating counts and tpm
GTF_FILE = REF_DATA_DIR + "/gencode.v43.gtf"

# list of chromosomes
chromosome_list = ['chr' + str(i) for i in range(23)][1:]
chromosome_list.extend(['chrX', 'chrY'])





###########################
# Nanopore sequence processing
# Hyperparameters that you may change the names of but need to be defined
###########################

# fast5 and fastq directories
FAST5_DIRECTORY = RAW_DATA_DIR + '/fast5s'
BASECALLED_DIRECTORY = RAW_DATA_DIR + '/basecalled_fastqs'

# number of smaller directories for basecalling
n_basecalling_directories = 20

# number of threads to use
NANOPORE_THREADS = '15'


# sample list for processing
# nanopore_sample_list = "RLGS10-blood_nanopore,RLGS10-primary_nanopore,RLGS10-recurrent_nanopore".split(",")
nanopore_sample_list = "RLGS_0010_gl,RLGS_0010_t1,RLGS_0010_t2".split(",")



###########################
# WGS sequence processing
# Hyperparameters that you may change the names of but need to be defined
###########################

# number of threads to use in each process for mapping
WGS_THREADS = '10'


# WGS sample list
wgs_sample_list = "RLGS10-blood_wgs_seq,RLGS10-primary_wgs_seq,RLGS10-recurrent_wgs_seq".split(",")
wgs_sample_list_cancer = "RLGS10-primary_wgs_seq,RLGS10-recurrent_wgs_seq".split(",")



###########################
# RNA sequence processing
# Hyperparameters that you may change the names of but need to be defined
###########################

# number of threads to use in each process for mapping
RNA_THREADS = '20'

# list of sample names you want to process
rna_sample_list = "RLGS10-primary_rna_seq,RLGS10-recurrent_rna_seq".split(",")






###################################
# Complete workflow
###################################

# define the objective (make the output files)
rule all:
    input:
        RES_DIR + "/nanopore/RLGS_0010-sniffles2.vcf.gz"
        # expand("{res_dir}/wgs/{sample}/results", res_dir = RES_DIR, sample = wgs_sample_list_cancer),
        
        # expand("{res_dir}/nanopore/{sample}/sniffles2.vcf.gz", res_dir = RES_DIR, sample = nanopore_sample_list)
        
        # # Nanopore seq
        # expand("{res_dir}/nanopore/{sample}.per-base.bed.gz", res_dir = RES_DIR, sample = nanopore_sample_list),
        # expand("{res_dir}/nanopore/{sample}/sniffles2.vcf.gz", res_dir = RES_DIR, sample = nanopore_sample_list),
        # expand("{res_dir}/nanopore/{sample}/pileup.vcf.gz", res_dir = RES_DIR, sample = nanopore_sample_list)

        # # WGS seq
        # expand("{res_dir}/wgs/{sample}/results", res_dir = RES_DIR, sample = wgs_sample_list_cancer)
        # expand("{res_dir}/wgs/{sample}-marked_duplicates.bam", res_dir = RES_DIR, sample = wgs_sample_list)
        # expand("{res_dir}/wgs/{sample}-merged_alignment.bam", res_dir = RES_DIR, sample = wgs_sample_list)
        # expand("{res_dir}/wgs/{sample}/{sample}-mutect2_{chromosome}.vcf.gz", res_dir = RES_DIR, sample = ['RLGS10-recurrent_wgs_seq'], chromosome = chromosome_list),
        # expand("{res_dir}/wgs/{sample}/results/", res_dir = RES_DIR, sample = ['RLGS10-recurrent_wgs_seq'])
        
        # # RNA seq
        # RES_DIR + "/counts.tsv",
        # RES_DIR + "/tpm.tsv"







#####################
# nanopore processing
#####################
# include: "snakemake/001-epi2me_nanopore_processing.smk"
include: "snakemake/001-nanopore_processing.smk"

#####################
# illumina wgs processing
#####################
include: "snakemake/002-wgs_processing.smk"

#####################
# illumina wgs variant identification
#####################
include: "snakemake/003-wgs_snv_calling.smk"

#####################
# illumina rna processing
#####################
include: "snakemake/004-rna_processing.smk"



