{"cells": [{"cell_type": "code", "execution_count": 1, "id": "50ef7f26-53d2-49ec-89b6-55582702eb13", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "Attaching package: 'gplots'\n", "\n", "\n", "The following object is masked from 'package:stats':\n", "\n", "    lowess\n", "\n", "\n", "\n", "Attaching package: 'dplyr'\n", "\n", "\n", "The following objects are masked from 'package:stats':\n", "\n", "    filter, lag\n", "\n", "\n", "The following objects are masked from 'package:base':\n", "\n", "    intersect, setdiff, setequal, union\n", "\n", "\n", "\n", "Attaching package: 'data.table'\n", "\n", "\n", "The following objects are masked from 'package:dplyr':\n", "\n", "    between, first, last\n", "\n", "\n", "\n", "Attaching package: 'gridExtra'\n", "\n", "\n", "The following object is masked from 'package:dplyr':\n", "\n", "    combine\n", "\n", "\n", "Loading required package: ggpubr\n", "\n", "\n", "Attaching package: 'survminer'\n", "\n", "\n", "The following object is masked from 'package:survival':\n", "\n", "    myeloma\n", "\n", "\n"]}], "source": ["library(ggplot2)\n", "library(ggrepel)\n", "library(ggrastr)\n", "library(gplots)\n", "library(ggupset)\n", "\n", "\n", "library(dplyr)\n", "library(forcats)\n", "library(data.table)\n", "\n", "library(tidytext)\n", "\n", "library(grid)\n", "library(gridExtra)\n", "library(patchwork)\n", "\n", "\n", "library(survival)\n", "library(survminer)\n", "\n", "\n", "plot_theme = function(...) {\n", "     \n", "    theme_bw() +\n", "     \n", "    theme(    \n", "     \n", "        plot.title = element_text(size = 22),\n", "         \n", "        plot.caption = element_text(size = 12),\n", "         \n", "        plot.subtitle = element_text(size = 16),\n", "         \n", "        axis.title = element_text(size = 18),\n", "         \n", "        axis.text.x = element_text(size = 12,\n", "                angle = 90, hjust = 1, vjust=0.5, color = \"black\"),\n", "        axis.text.y = element_text(size = 12, color = \"black\"),\n", "        legend.title = element_text(size = 16),\n", "        legend.text = element_text(size = 14),\n", "        ...\n", "    )\n", "}\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "d879d609-8c2e-4a7a-a12e-767b9914876a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5d0d5fc5-8f4e-4bea-9dde-7d98501b13b3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3916adea-c243-465c-b1bf-bcb59a20eb74", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d7d4abbd-228d-4f50-a416-b052e1f7c12d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 4, "id": "8fb9d116-2bad-4249-9b66-d75adc8a4995", "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A data.frame: 6 × 19</caption>\n", "<thead>\n", "\t<tr><th></th><th scope=col>X</th><th scope=col>X0</th><th scope=col>X1</th><th scope=col>X2</th><th scope=col>X3</th><th scope=col>X4</th><th scope=col>X5</th><th scope=col>X6</th><th scope=col>X7</th><th scope=col>X8</th><th scope=col>X9</th><th scope=col>X10</th><th scope=col>X11</th><th scope=col>X12</th><th scope=col>X13</th><th scope=col>X14</th><th scope=col>X15</th><th scope=col>X16</th><th scope=col>X17</th></tr>\n", "\t<tr><th></th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><th scope=row>1</th><td>ENSG00000239906</td><td>chr1</td><td>140033</td><td>140033</td><td>INS</td><td>3397</td><td>1</td><td>38</td><td>chr1</td><td>139789</td><td>140339</td><td>ENSG00000239906.1 </td><td>.</td><td>-</td><td>HAVANA</td><td>gene</td><td>.</td><td>ID=ENSG00000239906.1;gene_id=ENSG00000239906.1;gene_type=lncRNA;gene_name=RP11-34P13.14;level=2;havana_gene=OTTHUMG00000002481.1                                     </td><td>RP11-34P13.14 </td></tr>\n", "\t<tr><th scope=row>2</th><td>ENSG00000250575</td><td>chr1</td><td>492199</td><td>492199</td><td>INS</td><td>1121</td><td>2</td><td>13</td><td>chr1</td><td>491224</td><td>493241</td><td>ENSG00000250575.1 </td><td>.</td><td>-</td><td>HAVANA</td><td>gene</td><td>.</td><td>ID=ENSG00000250575.1;gene_id=ENSG00000250575.1;gene_type=unprocessed_pseudogene;gene_name=RP4-669L17.8;level=2;tag=overlapping_locus;havana_gene=OTTHUMG00000002861.2</td><td>RP4-669L17.8  </td></tr>\n", "\t<tr><th scope=row>3</th><td>ENSG00000237094</td><td>chr1</td><td>492199</td><td>492199</td><td>INS</td><td>1121</td><td>2</td><td>13</td><td>chr1</td><td>365388</td><td>522928</td><td>ENSG00000237094.12</td><td>.</td><td>-</td><td>HAVANA</td><td>gene</td><td>.</td><td>ID=ENSG00000237094.12;gene_id=ENSG00000237094.12;gene_type=transcribed_unprocessed_pseudogene;gene_name=RP4-669L17.4;level=2;havana_gene=OTTHUMG00000002857.7        </td><td>RP4-669L17.4  </td></tr>\n", "\t<tr><th scope=row>4</th><td>ENSG00000228327</td><td>chr1</td><td>727291</td><td>727291</td><td>INS</td><td>  94</td><td>4</td><td>82</td><td>chr1</td><td>725884</td><td>778626</td><td>ENSG00000228327.3 </td><td>.</td><td>-</td><td>HAVANA</td><td>gene</td><td>.</td><td>ID=ENSG00000228327.3;gene_id=ENSG00000228327.3;gene_type=transcribed_unprocessed_pseudogene;gene_name=RP11-206L10.2;level=2;havana_gene=OTTHUMG00000002406.2         </td><td>RP11-206L10.2 </td></tr>\n", "\t<tr><th scope=row>5</th><td>ENSG00000230021</td><td>chr1</td><td>727291</td><td>727291</td><td>INS</td><td>  94</td><td>4</td><td>82</td><td>chr1</td><td>586070</td><td>827796</td><td>ENSG00000230021.10</td><td>.</td><td>-</td><td>HAVANA</td><td>gene</td><td>.</td><td>ID=ENSG00000230021.10;gene_id=ENSG00000230021.10;gene_type=transcribed_processed_pseudogene;gene_name=RP11-206L10.17;level=2;havana_gene=OTTHUMG00000191652.4        </td><td>RP11-206L10.17</td></tr>\n", "\t<tr><th scope=row>6</th><td>ENSG00000228327</td><td>chr1</td><td>740741</td><td>740741</td><td>INS</td><td>5668</td><td>2</td><td>91</td><td>chr1</td><td>725884</td><td>778626</td><td>ENSG00000228327.3 </td><td>.</td><td>-</td><td>HAVANA</td><td>gene</td><td>.</td><td>ID=ENSG00000228327.3;gene_id=ENSG00000228327.3;gene_type=transcribed_unprocessed_pseudogene;gene_name=RP11-206L10.2;level=2;havana_gene=OTTHUMG00000002406.2         </td><td>RP11-206L10.2 </td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A data.frame: 6 × 19\n", "\\begin{tabular}{r|lllllllllllllllllll}\n", "  & X & X0 & X1 & X2 & X3 & X4 & X5 & X6 & X7 & X8 & X9 & X10 & X11 & X12 & X13 & X14 & X15 & X16 & X17\\\\\n", "  & <chr> & <chr> & <int> & <int> & <chr> & <int> & <chr> & <int> & <chr> & <int> & <int> & <chr> & <chr> & <chr> & <chr> & <chr> & <chr> & <chr> & <chr>\\\\\n", "\\hline\n", "\t1 & ENSG00000239906 & chr1 & 140033 & 140033 & INS & 3397 & 1 & 38 & chr1 & 139789 & 140339 & ENSG00000239906.1  & . & - & HAVANA & gene & . & ID=ENSG00000239906.1;gene\\_id=ENSG00000239906.1;gene\\_type=lncRNA;gene\\_name=RP11-34P13.14;level=2;havana\\_gene=OTTHUMG00000002481.1                                      & RP11-34P13.14 \\\\\n", "\t2 & ENSG00000250575 & chr1 & 492199 & 492199 & INS & 1121 & 2 & 13 & chr1 & 491224 & 493241 & ENSG00000250575.1  & . & - & HAVANA & gene & . & ID=ENSG00000250575.1;gene\\_id=ENSG00000250575.1;gene\\_type=unprocessed\\_pseudogene;gene\\_name=RP4-669L17.8;level=2;tag=overlapping\\_locus;havana\\_gene=OTTHUMG00000002861.2 & RP4-669L17.8  \\\\\n", "\t3 & ENSG00000237094 & chr1 & 492199 & 492199 & INS & 1121 & 2 & 13 & chr1 & 365388 & 522928 & ENSG00000237094.12 & . & - & HAVANA & gene & . & ID=ENSG00000237094.12;gene\\_id=ENSG00000237094.12;gene\\_type=transcribed\\_unprocessed\\_pseudogene;gene\\_name=RP4-669L17.4;level=2;havana\\_gene=OTTHUMG00000002857.7         & RP4-669L17.4  \\\\\n", "\t4 & ENSG00000228327 & chr1 & 727291 & 727291 & INS &   94 & 4 & 82 & chr1 & 725884 & 778626 & ENSG00000228327.3  & . & - & HAVANA & gene & . & ID=ENSG00000228327.3;gene\\_id=ENSG00000228327.3;gene\\_type=transcribed\\_unprocessed\\_pseudogene;gene\\_name=RP11-206L10.2;level=2;havana\\_gene=OTTHUMG00000002406.2          & RP11-206L10.2 \\\\\n", "\t5 & ENSG00000230021 & chr1 & 727291 & 727291 & INS &   94 & 4 & 82 & chr1 & 586070 & 827796 & ENSG00000230021.10 & . & - & HAVANA & gene & . & ID=ENSG00000230021.10;gene\\_id=ENSG00000230021.10;gene\\_type=transcribed\\_processed\\_pseudogene;gene\\_name=RP11-206L10.17;level=2;havana\\_gene=OTTHUMG00000191652.4         & RP11-206L10.17\\\\\n", "\t6 & ENSG00000228327 & chr1 & 740741 & 740741 & INS & 5668 & 2 & 91 & chr1 & 725884 & 778626 & ENSG00000228327.3  & . & - & HAVANA & gene & . & ID=ENSG00000228327.3;gene\\_id=ENSG00000228327.3;gene\\_type=transcribed\\_unprocessed\\_pseudogene;gene\\_name=RP11-206L10.2;level=2;havana\\_gene=OTTHUMG00000002406.2          & RP11-206L10.2 \\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A data.frame: 6 × 19\n", "\n", "| <!--/--> | X &lt;chr&gt; | X0 &lt;chr&gt; | X1 &lt;int&gt; | X2 &lt;int&gt; | X3 &lt;chr&gt; | X4 &lt;int&gt; | X5 &lt;chr&gt; | X6 &lt;int&gt; | X7 &lt;chr&gt; | X8 &lt;int&gt; | X9 &lt;int&gt; | X10 &lt;chr&gt; | X11 &lt;chr&gt; | X12 &lt;chr&gt; | X13 &lt;chr&gt; | X14 &lt;chr&gt; | X15 &lt;chr&gt; | X16 &lt;chr&gt; | X17 &lt;chr&gt; |\n", "|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|\n", "| 1 | ENSG00000239906 | chr1 | 140033 | 140033 | INS | 3397 | 1 | 38 | chr1 | 139789 | 140339 | ENSG00000239906.1  | . | - | HAVANA | gene | . | ID=ENSG00000239906.1;gene_id=ENSG00000239906.1;gene_type=lncRNA;gene_name=RP11-34P13.14;level=2;havana_gene=OTTHUMG00000002481.1                                      | RP11-34P13.14  |\n", "| 2 | ENSG00000250575 | chr1 | 492199 | 492199 | INS | 1121 | 2 | 13 | chr1 | 491224 | 493241 | ENSG00000250575.1  | . | - | HAVANA | gene | . | ID=ENSG00000250575.1;gene_id=ENSG00000250575.1;gene_type=unprocessed_pseudogene;gene_name=RP4-669L17.8;level=2;tag=overlapping_locus;havana_gene=OTTHUMG00000002861.2 | RP4-669L17.8   |\n", "| 3 | ENSG00000237094 | chr1 | 492199 | 492199 | INS | 1121 | 2 | 13 | chr1 | 365388 | 522928 | ENSG00000237094.12 | . | - | HAVANA | gene | . | ID=ENSG00000237094.12;gene_id=ENSG00000237094.12;gene_type=transcribed_unprocessed_pseudogene;gene_name=RP4-669L17.4;level=2;havana_gene=OTTHUMG00000002857.7         | RP4-669L17.4   |\n", "| 4 | ENSG00000228327 | chr1 | 727291 | 727291 | INS |   94 | 4 | 82 | chr1 | 725884 | 778626 | ENSG00000228327.3  | . | - | HAVANA | gene | . | ID=ENSG00000228327.3;gene_id=ENSG00000228327.3;gene_type=transcribed_unprocessed_pseudogene;gene_name=RP11-206L10.2;level=2;havana_gene=OTTHUMG00000002406.2          | RP11-206L10.2  |\n", "| 5 | ENSG00000230021 | chr1 | 727291 | 727291 | INS |   94 | 4 | 82 | chr1 | 586070 | 827796 | ENSG00000230021.10 | . | - | HAVANA | gene | . | ID=ENSG00000230021.10;gene_id=ENSG00000230021.10;gene_type=transcribed_processed_pseudogene;gene_name=RP11-206L10.17;level=2;havana_gene=OTTHUMG00000191652.4         | RP11-206L10.17 |\n", "| 6 | ENSG00000228327 | chr1 | 740741 | 740741 | INS | 5668 | 2 | 91 | chr1 | 725884 | 778626 | ENSG00000228327.3  | . | - | HAVANA | gene | . | ID=ENSG00000228327.3;gene_id=ENSG00000228327.3;gene_type=transcribed_unprocessed_pseudogene;gene_name=RP11-206L10.2;level=2;havana_gene=OTTHUMG00000002406.2          | RP11-206L10.2  |\n", "\n"], "text/plain": ["  X               X0   X1     X2     X3  X4   X5 X6 X7   X8     X9    \n", "1 ENSG00000239906 chr1 140033 140033 INS 3397 1  38 chr1 139789 140339\n", "2 ENSG00000250575 chr1 492199 492199 INS 1121 2  13 chr1 491224 493241\n", "3 ENSG00000237094 chr1 492199 492199 INS 1121 2  13 chr1 365388 522928\n", "4 ENSG00000228327 chr1 727291 727291 INS   94 4  82 chr1 725884 778626\n", "5 ENSG00000230021 chr1 727291 727291 INS   94 4  82 chr1 586070 827796\n", "6 ENSG00000228327 chr1 740741 740741 INS 5668 2  91 chr1 725884 778626\n", "  X10                X11 X12 X13    X14  X15\n", "1 ENSG00000239906.1  .   -   HAVANA gene .  \n", "2 ENSG00000250575.1  .   -   HAVANA gene .  \n", "3 ENSG00000237094.12 .   -   HAVANA gene .  \n", "4 ENSG00000228327.3  .   -   HAVANA gene .  \n", "5 ENSG00000230021.10 .   -   HAVANA gene .  \n", "6 ENSG00000228327.3  .   -   HAVANA gene .  \n", "  X16                                                                                                                                                                  \n", "1 ID=ENSG00000239906.1;gene_id=ENSG00000239906.1;gene_type=lncRNA;gene_name=RP11-34P13.14;level=2;havana_gene=OTTHUMG00000002481.1                                     \n", "2 ID=ENSG00000250575.1;gene_id=ENSG00000250575.1;gene_type=unprocessed_pseudogene;gene_name=RP4-669L17.8;level=2;tag=overlapping_locus;havana_gene=OTTHUMG00000002861.2\n", "3 ID=ENSG00000237094.12;gene_id=ENSG00000237094.12;gene_type=transcribed_unprocessed_pseudogene;gene_name=RP4-669L17.4;level=2;havana_gene=OTTHUMG00000002857.7        \n", "4 ID=ENSG00000228327.3;gene_id=ENSG00000228327.3;gene_type=transcribed_unprocessed_pseudogene;gene_name=RP11-206L10.2;level=2;havana_gene=OTTHUMG00000002406.2         \n", "5 ID=ENSG00000230021.10;gene_id=ENSG00000230021.10;gene_type=transcribed_processed_pseudogene;gene_name=RP11-206L10.17;level=2;havana_gene=OTTHUMG00000191652.4        \n", "6 ID=ENSG00000228327.3;gene_id=ENSG00000228327.3;gene_type=transcribed_unprocessed_pseudogene;gene_name=RP11-206L10.2;level=2;havana_gene=OTTHUMG00000002406.2         \n", "  X17           \n", "1 RP11-34P13.14 \n", "2 RP4-669L17.8  \n", "3 RP4-669L17.4  \n", "4 RP11-206L10.2 \n", "5 RP11-206L10.17\n", "6 RP11-206L10.2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/tmp/t2_genes.tsv\", sep='\\t')\n", "head(input_df)"]}, {"cell_type": "code", "execution_count": 30, "id": "02a9f39d-8496-479d-a61d-529d9d0eb98d", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[1m\u001b[22m`stat_bin()` using `bins = 30`. Pick better value with `binwidth`.\n", "Warning message in `[.data.frame`(input_df, !is.na(as.numeric(input_df$support)), :\n", "\"NAs introduced by coercion\"\n", "Warning message:\n", "\"\u001b[1m\u001b[22mRemoved 3 rows containing missing values (`geom_point()`).\"\n"]}, {"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/tmp/t2_genes.tsv\", sep='\\t')\n", "\n", "# pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/sig_sv_genes.pdf\")\n", "\n", "p = ggplot(input_df, aes(x = (length), fill = type)) + plot_theme() +\n", "geom_histogram() +\n", "\n", "# geom_bar(position=\"stack\", stat=\"identity\") +\n", "\n", "# scale_colour_manual(values=sig_colours) +\n", "\n", "# geom_hline(yintercept=1, linetype='solid', color='grey', size=0.5) +\n", "# geom_errorbar(aes(ymin=lower_ci, ymax=upper_ci),width=0)+\n", "# geom_point(size=2) +\n", "\n", "# scale_x_binned(n.breaks = 10000) +\n", "# scale_y_continuous(trans='log10') +\n", "scale_x_continuous(trans='log10') +\n", "\n", "\n", "ylab(\"Recurrent - Germline events\") + xlab(\"Length (bp)\") + ggtitle('')\n", "print(p)\n", "\n", "\n", "\n", "p = ggplot(input_df, aes(x = length, y = coverage, color = type)) + plot_theme() +\n", "geom_point() +\n", "\n", "scale_x_continuous(trans='log10') +\n", "\n", "ylab(\"Sequencing coverage\") + xlab(\"Length (bp)\") + ggtitle('')\n", "print(p)\n", "\n", "\n", "sub_df = input_df[!is.na(as.numeric(input_df$support)),]\n", "sub_df$support = as.numeric(sub_df$support)\n", "p = ggplot(sub_df, aes(x = support, y = coverage, color = type)) + plot_theme() +\n", "geom_point() +\n", "\n", "xlim(0,25)+\n", "ylab(\"Coverage\") + xlab(\"Read support\") + ggtitle('')\n", "print(p)\n", "\n", "# theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), legend.position='right')\n", "\n", "\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": null, "id": "237df693-e3d8-4404-ab7c-eaa4c7cce6dd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dea7c419-715c-4403-8c95-f0832cc18089", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6cee1caf-9cc9-4224-8b56-c784e8e59864", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cf1d4e22-c210-4f19-9a89-cfa25f26090c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "0e75089b-4c0f-4bee-93c5-5926a67f1038", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3423e7d2-bf2e-4b57-8a2c-89ddaff9f1ef", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "dc8058f0-358d-4e58-aaa6-a1c297e176a4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "6e3bbf32-96f8-4ae8-bfe5-4720de85fcd5", "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A data.frame: 6 × 7</caption>\n", "<thead>\n", "\t<tr><th></th><th scope=col>chr</th><th scope=col>start</th><th scope=col>stop</th><th scope=col>type</th><th scope=col>length</th><th scope=col>support</th><th scope=col>coverage</th></tr>\n", "\t<tr><th></th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;int&gt;</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><th scope=row>1</th><td>chr1</td><td>140033</td><td>140033</td><td>INS</td><td>3397</td><td>1</td><td>38</td></tr>\n", "\t<tr><th scope=row>2</th><td>chr1</td><td>492199</td><td>492199</td><td>INS</td><td>1121</td><td>2</td><td>13</td></tr>\n", "\t<tr><th scope=row>3</th><td>chr1</td><td>727291</td><td>727291</td><td>INS</td><td>  94</td><td>4</td><td>82</td></tr>\n", "\t<tr><th scope=row>4</th><td>chr1</td><td>740741</td><td>740741</td><td>INS</td><td>5668</td><td>2</td><td>91</td></tr>\n", "\t<tr><th scope=row>5</th><td>chr1</td><td>744868</td><td>744868</td><td>INS</td><td>  43</td><td>4</td><td>80</td></tr>\n", "\t<tr><th scope=row>6</th><td>chr1</td><td>748218</td><td>748218</td><td>INS</td><td> 331</td><td>3</td><td>73</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A data.frame: 6 × 7\n", "\\begin{tabular}{r|lllllll}\n", "  & chr & start & stop & type & length & support & coverage\\\\\n", "  & <chr> & <int> & <chr> & <chr> & <int> & <chr> & <int>\\\\\n", "\\hline\n", "\t1 & chr1 & 140033 & 140033 & INS & 3397 & 1 & 38\\\\\n", "\t2 & chr1 & 492199 & 492199 & INS & 1121 & 2 & 13\\\\\n", "\t3 & chr1 & 727291 & 727291 & INS &   94 & 4 & 82\\\\\n", "\t4 & chr1 & 740741 & 740741 & INS & 5668 & 2 & 91\\\\\n", "\t5 & chr1 & 744868 & 744868 & INS &   43 & 4 & 80\\\\\n", "\t6 & chr1 & 748218 & 748218 & INS &  331 & 3 & 73\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A data.frame: 6 × 7\n", "\n", "| <!--/--> | chr &lt;chr&gt; | start &lt;int&gt; | stop &lt;chr&gt; | type &lt;chr&gt; | length &lt;int&gt; | support &lt;chr&gt; | coverage &lt;int&gt; |\n", "|---|---|---|---|---|---|---|---|\n", "| 1 | chr1 | 140033 | 140033 | INS | 3397 | 1 | 38 |\n", "| 2 | chr1 | 492199 | 492199 | INS | 1121 | 2 | 13 |\n", "| 3 | chr1 | 727291 | 727291 | INS |   94 | 4 | 82 |\n", "| 4 | chr1 | 740741 | 740741 | INS | 5668 | 2 | 91 |\n", "| 5 | chr1 | 744868 | 744868 | INS |   43 | 4 | 80 |\n", "| 6 | chr1 | 748218 | 748218 | INS |  331 | 3 | 73 |\n", "\n"], "text/plain": ["  chr  start  stop   type length support coverage\n", "1 chr1 140033 140033 INS  3397   1       38      \n", "2 chr1 492199 492199 INS  1121   2       13      \n", "3 chr1 727291 727291 INS    94   4       82      \n", "4 chr1 740741 740741 INS  5668   2       91      \n", "5 chr1 744868 744868 INS    43   4       80      \n", "6 chr1 748218 748218 INS   331   3       73      "]}, "metadata": {}, "output_type": "display_data"}], "source": ["input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/tmp/sniffles.csv\")\n", "head(input_df)"]}, {"cell_type": "code", "execution_count": 30, "id": "85f5dedb-d6d9-4598-bfc5-16fd4d89ea91", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[1m\u001b[22m`stat_bin()` using `bins = 30`. Pick better value with `binwidth`.\n", "Warning message in `[.data.frame`(input_df, !is.na(as.numeric(input_df$support)), :\n", "\"NAs introduced by coercion\"\n", "Warning message:\n", "\"\u001b[1m\u001b[22mRemoved 3 rows containing missing values (`geom_point()`).\"\n"]}, {"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/sniffles.pdf\")\n", "\n", "p = ggplot(input_df, aes(x = (length), fill = type)) + plot_theme() +\n", "geom_histogram() +\n", "\n", "# geom_bar(position=\"stack\", stat=\"identity\") +\n", "\n", "# scale_colour_manual(values=sig_colours) +\n", "\n", "# geom_hline(yintercept=1, linetype='solid', color='grey', size=0.5) +\n", "# geom_errorbar(aes(ymin=lower_ci, ymax=upper_ci),width=0)+\n", "# geom_point(size=2) +\n", "\n", "# scale_x_binned(n.breaks = 10000) +\n", "# scale_y_continuous(trans='log10') +\n", "scale_x_continuous(trans='log10') +\n", "\n", "\n", "ylab(\"Recurrent - Germline events\") + xlab(\"Length (bp)\") + ggtitle('')\n", "print(p)\n", "\n", "\n", "\n", "p = ggplot(input_df, aes(x = length, y = coverage, color = type)) + plot_theme() +\n", "geom_point() +\n", "\n", "scale_x_continuous(trans='log10') +\n", "\n", "ylab(\"Sequencing coverage\") + xlab(\"Length (bp)\") + ggtitle('')\n", "print(p)\n", "\n", "\n", "sub_df = input_df[!is.na(as.numeric(input_df$support)),]\n", "sub_df$support = as.numeric(sub_df$support)\n", "p = ggplot(sub_df, aes(x = support, y = coverage, color = type)) + plot_theme() +\n", "geom_point() +\n", "\n", "xlim(0,25)+\n", "ylab(\"Coverage\") + xlab(\"Read support\") + ggtitle('')\n", "print(p)\n", "\n", "# theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), legend.position='right')\n", "\n", "\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": null, "id": "e93fb33a-9971-40a4-83f5-b7431387f81f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f89d4141-26cc-4b3b-a940-18abbb2357f8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "588b086e-a8af-4e0a-97dc-15522a06fe83", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 47, "id": "70d02a9c-bd21-46a3-8b9f-49466c90d6bd", "metadata": {}, "outputs": [{"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/glass_primary_recurrent.tsv\", sep='\\t')\n", "\n", "\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/glass_primary_recurrent.pdf\")\n", "\n", "\n", "p = ggplot(input_df, aes(x = tumor, y = signature_name, fill=signature_name)) + plot_theme() +\n", "geom_bar(position=\"stack\", stat=\"identity\") +\n", "\n", "# scale_colour_manual(values=sig_colours) +\n", "\n", "# geom_hline(yintercept=1, linetype='solid', color='grey', size=0.5) +\n", "# geom_errorbar(aes(ymin=lower_ci, ymax=upper_ci),width=0)+\n", "# geom_point(size=2) +\n", "\n", "# scale_x_binned(n.breaks = 10000) +\n", "# scale_y_continuous(trans='log10') +\n", "\n", "ylab(\"Expression subtype\") + xlab(\"\") + ggtitle('') +\n", "\n", "theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), legend.position='right')\n", "\n", "print(p)\n", "\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": 4, "id": "5186fa0b-78d1-40c3-bdc7-62ddd53bdf04", "metadata": {}, "outputs": [{"data": {"text/html": ["<table class=\"dataframe\">\n", "<caption>A data.frame: 6 × 3</caption>\n", "<thead>\n", "\t<tr><th></th><th scope=col>sample</th><th scope=col>class</th><th scope=col>score</th></tr>\n", "\t<tr><th></th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;chr&gt;</th><th scope=col>&lt;dbl&gt;</th></tr>\n", "</thead>\n", "<tbody>\n", "\t<tr><th scope=row>1</th><td>primary_rna_seq  </td><td>mesenchymal</td><td> 0.14171423</td></tr>\n", "\t<tr><th scope=row>2</th><td>primary_rna_seq  </td><td>classical  </td><td> 0.23119252</td></tr>\n", "\t<tr><th scope=row>3</th><td>primary_rna_seq  </td><td>proneural  </td><td> 0.51256372</td></tr>\n", "\t<tr><th scope=row>4</th><td>recurrent_rna_seq</td><td>mesenchymal</td><td> 0.14601973</td></tr>\n", "\t<tr><th scope=row>5</th><td>recurrent_rna_seq</td><td>classical  </td><td>-0.01757637</td></tr>\n", "\t<tr><th scope=row>6</th><td>recurrent_rna_seq</td><td>proneural  </td><td> 0.56030892</td></tr>\n", "</tbody>\n", "</table>\n"], "text/latex": ["A data.frame: 6 × 3\n", "\\begin{tabular}{r|lll}\n", "  & sample & class & score\\\\\n", "  & <chr> & <chr> & <dbl>\\\\\n", "\\hline\n", "\t1 & primary\\_rna\\_seq   & mesenchymal &  0.14171423\\\\\n", "\t2 & primary\\_rna\\_seq   & classical   &  0.23119252\\\\\n", "\t3 & primary\\_rna\\_seq   & proneural   &  0.51256372\\\\\n", "\t4 & recurrent\\_rna\\_seq & mesenchymal &  0.14601973\\\\\n", "\t5 & recurrent\\_rna\\_seq & classical   & -0.01757637\\\\\n", "\t6 & recurrent\\_rna\\_seq & proneural   &  0.56030892\\\\\n", "\\end{tabular}\n"], "text/markdown": ["\n", "A data.frame: 6 × 3\n", "\n", "| <!--/--> | sample &lt;chr&gt; | class &lt;chr&gt; | score &lt;dbl&gt; |\n", "|---|---|---|---|\n", "| 1 | primary_rna_seq   | mesenchymal |  0.14171423 |\n", "| 2 | primary_rna_seq   | classical   |  0.23119252 |\n", "| 3 | primary_rna_seq   | proneural   |  0.51256372 |\n", "| 4 | recurrent_rna_seq | mesenchymal |  0.14601973 |\n", "| 5 | recurrent_rna_seq | classical   | -0.01757637 |\n", "| 6 | recurrent_rna_seq | proneural   |  0.56030892 |\n", "\n"], "text/plain": ["  sample            class       score      \n", "1 primary_rna_seq   mesenchymal  0.14171423\n", "2 primary_rna_seq   classical    0.23119252\n", "3 primary_rna_seq   proneural    0.51256372\n", "4 recurrent_rna_seq mesenchymal  0.14601973\n", "5 recurrent_rna_seq classical   -0.01757637\n", "6 recurrent_rna_seq proneural    0.56030892"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/tmp/class_scores.tsv\", sep='\\t')\n", "head(input_df)\n", "\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/class_scores.pdf\")\n", "\n", "\n", "p = ggplot(input_df, aes(x = sample, y = score, fill=class)) + plot_theme() +\n", "geom_bar(position=\"dodge\", stat=\"identity\") +\n", "\n", "# scale_colour_manual(values=sig_colours) +\n", "\n", "# geom_hline(yintercept=1, linetype='solid', color='grey', size=0.5) +\n", "# geom_errorbar(aes(ymin=lower_ci, ymax=upper_ci),width=0)+\n", "# geom_point(size=2) +\n", "\n", "# scale_x_binned(n.breaks = 10000) +\n", "# scale_y_continuous(trans='log10') +\n", "\n", "ylab(\"Expression subtype score\") + xlab(\"\") + ggtitle('') +\n", "\n", "theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), legend.position='right')\n", "\n", "print(p)\n", "\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": null, "id": "e5b5fdf9-1abb-4b1a-8dd4-c2aa9cf20c23", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "78cf29fd-d19d-48e5-937d-11249864df40", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 31, "id": "bf938d50-16af-4951-a0cd-ed3d44433423", "metadata": {}, "outputs": [{"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/nanopore_coverage.tsv\", sep='\\t')\n", "\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/nanopore_coverage_t1.pdf\")\n", "\n", "# input_df$t1_gl[input_df$t1_gl > 20] = 20\n", "\n", "sig_colours = c(\"firebrick\", 'black')\n", "names(sig_colours) = c(\"True\", 'False')\n", "\n", "\n", "for (chr in unique(input_df$chr)){\n", "\n", "sub_df = input_df[input_df$chr == chr,]\n", "\n", "\n", "p = ggplot(sub_df, aes(x = start, y = t1_gl, color=t1_gl_high)) + plot_theme() +\n", "geom_point() +\n", "\n", "scale_colour_manual(values=sig_colours) +\n", "\n", "# geom_hline(yintercept=1, linetype='solid', color='grey', size=0.5) +\n", "# geom_errorbar(aes(ymin=lower_ci, ymax=upper_ci),width=0)+\n", "# geom_point(size=2) +\n", "\n", "# scale_x_binned(n.breaks = 10000) +\n", "# scale_y_continuous(trans='log10') +\n", "\n", "ylab(\"Mean difference (T1 - GL)\") + xlab(\"Position\") + ggtitle(chr) +\n", "\n", "theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), legend.position='none')\n", "\n", "print(p)\n", "\n", "}\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": 33, "id": "d1bbf807-89c5-4ed3-8c63-5e825869345a", "metadata": {}, "outputs": [{"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/nanopore_coverage.tsv\", sep='\\t')\n", "\n", "pdf(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/nanopore_coverage_t2.pdf\")\n", "\n", "# input_df$t1_gl[input_df$t1_gl > 20] = 20\n", "\n", "sig_colours = c(\"firebrick\", 'black')\n", "names(sig_colours) = c(\"True\", 'False')\n", "\n", "\n", "for (chr in unique(input_df$chr)){\n", "\n", "sub_df = input_df[input_df$chr == chr,]\n", "\n", "\n", "p = ggplot(sub_df, aes(x = start, y = t2_gl, color=t2_gl_high)) + plot_theme() +\n", "geom_point() +\n", "\n", "scale_colour_manual(values=sig_colours) +\n", "\n", "# geom_hline(yintercept=1, linetype='solid', color='grey', size=0.5) +\n", "# geom_errorbar(aes(ymin=lower_ci, ymax=upper_ci),width=0)+\n", "# geom_point(size=2) +\n", "\n", "# scale_x_binned(n.breaks = 10000) +\n", "# scale_y_continuous(trans='log10') +\n", "\n", "ylab(\"Mean difference (T2 - GL)\") + xlab(\"Position\") + ggtitle(chr) +\n", "\n", "theme(panel.grid.major = element_blank(), panel.grid.minor = element_blank(), panel.background = element_blank(), legend.position='none')\n", "\n", "print(p)\n", "\n", "}\n", "\n", "dev.off()\n"]}, {"cell_type": "code", "execution_count": 24, "id": "2af08ad2-506a-4637-adc7-fe9bbd7ac09e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 27, "id": "860b88ca-7592-464d-9cd0-4d9a76b7a5d8", "metadata": {}, "outputs": [{"data": {"text/html": ["<strong>png:</strong> 2"], "text/latex": ["\\textbf{png:} 2"], "text/markdown": ["**png:** 2"], "text/plain": ["png \n", "  2 "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# load df\n", "input_df = read.csv(\"/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/umap.tsv\", sep='\\t')\n", "\n", "\n", "cancer_colours = c('darkorange', 'green', 'beige', 'grey', 'violet', 'white', 'dodgerblue', 'firebrick')\n", "names(cancer_colours) = c('Classical', 'G-CIMP', 'Mesenchymal', 'Neural', 'Proneural', 'nan', 'primary', 'recurrent')\n", "\n", "# plot\n", "p1 = ggplot(input_df, aes(x=x_dim, y=y_dim, fill=subtype)) + plot_theme() +\n", "geom_point(pch=21, size = 4) +\n", "\n", "scale_fill_manual(values=cancer_colours) +\n", "# scale_colour_manual(values=cancer_text_colours) +\n", "\n", "# geom_label_repel(data=df_text, aes(label=cancer_type, fill=cancer_type, colour = cancer_type), show.legend=FALSE, segment.color = NA,label.size=NA,\n", "#         seed              = 1234,\n", "#         size\t\t\t\t= 7,\n", "#         force             = 1,\n", "#         nudge_x           = 1,\n", "#         hjust             = 0,\n", "#         segment.size      = 0.2,\n", "#         max.overlaps = 30\n", "#                ) +\n", "\n", "\n", "xlab(\"UMAP 1\") +\n", "ylab(\"UMAP 2\") +\n", "ggtitle('') +\n", "\n", "# guides(fill=guide_legend(title=\"Cancer type\", byrow = FALSE, override.aes = list(size = 3))) +\n", "\n", "theme(panel.grid = element_blank(), \n", "            axis.line = element_line(colour = 'black', size = 0.5))\n", "\n", "# legend = cowplot::get_legend(p1)\n", "\n", "# p1 = p1 + theme(legend.position='none')\n", "\n", "# print plots to file\n", "pdf('/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figures/umap_subset.pdf', width=10, height=7.5)\n", "\n", "print(p1)\n", "# print(legend)\n", "\n", "dev.off()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "2ba4d427-0eff-4004-bc73-24bf0b6a9d76", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7f560079-0e46-423c-b4c4-7ec2a0b038f1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "R", "language": "R", "name": "ir"}, "language_info": {"codemirror_mode": "r", "file_extension": ".r", "mimetype": "text/x-r-source", "name": "R", "pygments_lexer": "r", "version": "4.2.2"}}, "nbformat": 4, "nbformat_minor": 5}