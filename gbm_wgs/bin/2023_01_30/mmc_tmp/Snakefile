# Alec Bahcheli

# run version (typically date)
VERSION='2022_09_27'


# project directory
MAIN_DIR='/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ns_snvs_nina/data/mm10_wgs_analysis'

# processing temporary directory
TMP_DIR = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ns_snvs_nina/data/mm10_wgs_analysis/process_tmp"



GENOME_VERSION = 'hg19'




# number of threads to use
THREADS = '15'


# sample list for processing
# sample_csv = "1_HR_Notch_Tumor_1_S3,46775_HR_dgApobec3_Liver_S6,46775_HR_dgApobec3_Tumor_1_S7,46775_HR_dgApobec3_Tumor_2_S8,46775_HR_dgApobec3_Tumor_3_S9,46775_HR_dgApobec3_Tumor_4_S10,46775_HR_dgApobec3_Tumor_5_S11,943_HR_Adam10_Tumor_1_S4,944_HR_Adam10_Tumor_1_S5"
# sample_csv_not_normal = "1_HR_Notch_Tumor_1_S3,46775_HR_dgApobec3_Tumor_1_S7,46775_HR_dgApobec3_Tumor_2_S8,46775_HR_dgApobec3_Tumor_3_S9,46775_HR_dgApobec3_Tumor_4_S10,46775_HR_dgApobec3_Tumor_5_S11,943_HR_Adam10_Tumor_1_S4,944_HR_Adam10_Tumor_1_S5"

# sample_list = sample_csv.split(",")
# sample_list_not_normal = sample_csv_not_normal.split(",")

sample_list_not_normal = ['raw_fastq']



###################################
# Complete workflow
###################################

# define the objective (make the output files)
rule all:
    input:
        # do not process normal to vcf
        # expand("{main_dir}/annovar/{sample}.{genome_version}_multianno.txt", main_dir = MAIN_DIR, sample = sample_list_not_normal, genome_version = GENOME_VERSION)
        expand("{main_dir}/tmp/{sample}-merged_alignment.bam", main_dir = MAIN_DIR, sample = sample_list_not_normal)


#####################
# WGS data pre-processing 
#####################

# convert fastq files to bam files
rule FastqToSam:
    input:
        fastq_forward = MAIN_DIR + "/fastqs/{sample}_R1.fastq.gz",
        fastq_reverse = MAIN_DIR + "/fastqs/{sample}_R2.fastq.gz",
        
    output:
        output_bam = temp(MAIN_DIR + "/tmp/{sample}.bam")
        
    params:
        sample_name = "{sample}",
        java_memory = '25g'
        
    resources:
        threads = 1,
        runtime = '0:8:0:0',
        individual_core_memory = '30G'
        
    shell:
        "conda run -n gatk /.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-*******/gatk --java-options '-Xmx{params.java_memory}' FastqToSam --FASTQ {input.fastq_forward} --FASTQ2 {input.fastq_reverse} --SAMPLE_NAME {params.sample_name} --PLATFORM illumina --READ_GROUP_NAME rg{params.sample_name} --OUTPUT {output.output_bam} --TMP_DIR {TMP_DIR}"




rule MarkIlluminaAdapters:
    input:
        BAM_READS = MAIN_DIR + "/tmp/{sample}.bam"
        
    output:
        output_bam = temp(MAIN_DIR + "/tmp/{sample}-markilluminaadapters.bam"),
        output_adapters_file = temp(MAIN_DIR + "/tmp/{sample}-markilluminaadapters.txt")
        
    params:
        java_memory = '25g'
        
    resources:
        threads = 1,
        runtime = '0:8:0:0',
        individual_core_memory = '30G'
        
    shell:
        "conda run -n gatk /.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-*******/gatk --java-options '-Xmx{params.java_memory}' MarkIlluminaAdapters I={input.BAM_READS} O={output.output_bam} M={output.output_adapters_file} TMP_DIR={TMP_DIR}"



rule BWA_index:
    input:
        genome_fasta = MAIN_DIR + "/" + GENOME_VERSION + ".fa"
        
    output:
        MAIN_DIR + "/" + GENOME_VERSION + ".fa.amb"
        
    params:
        java_memory = '25g'
        
    resources:
        threads = 1,
        runtime = '0:8:0:0',
        individual_core_memory = '120G'

    shell:
        "conda run -n gatk bwa-mem2 index {input.genome_fasta}"



rule CreateSequenceDictionary:
    input:
        genome_fasta = MAIN_DIR + "/" + GENOME_VERSION + ".fa"
        
    output:
        genome_dict = MAIN_DIR + "/" + GENOME_VERSION + ".dict"
        
    params:
        java_memory = '25g'
        
    resources:
        threads = 1,
        runtime = '0:8:0:0',
        individual_core_memory = '30G'
        
    shell:
        "conda run -n gatk /.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-*******/gatk --java-options '-Xmx{params.java_memory}' CreateSequenceDictionary --REFERENCE {input.genome_fasta} --OUTPUT {output.genome_dict}"





rule SamToFastq:
    input:
        marked_adapters_bam = MAIN_DIR + "/tmp/{sample}-markilluminaadapters.bam"
        
    output:
        fastq_file = temp(MAIN_DIR + "/tmp/{sample}-trimmed_interleaved.fastq")
        
    params:
        java_memory = '25g'
        
    resources:
        threads = 1,
        runtime = '0:8:0:0',
        individual_core_memory = '30G'
        
    shell:
        "conda run -n gatk /.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-*******/gatk --java-options '-Xmx{params.java_memory}' SamToFastq --INPUT {input.marked_adapters_bam} --FASTQ {output.fastq_file} --CLIPPING_ATTRIBUTE XT --CLIPPING_ACTION 2 --INTERLEAVE true --INCLUDE_NON_PF_READS true --TMP_DIR {TMP_DIR}"






rule BWA_alignment:
    input:
        genome_fasta = MAIN_DIR + "/" + GENOME_VERSION + ".fa",
        genome_index = MAIN_DIR + "/" + GENOME_VERSION + ".fa.amb",
        fastq_file = MAIN_DIR + "/tmp/{sample}-trimmed_interleaved.fastq"
        
    output:
        aligned_sam_file = temp(MAIN_DIR + "/tmp/{sample}-bwa_mem.sam")

    resources:
        threads = THREADS,
        runtime = '0:8:0:0',
        individual_core_memory = '13G'

    shell:
        "conda run -n gatk bwa-mem2 mem -M -t {resources.threads} -p {input.genome_fasta} {input.fastq_file} > {output.aligned_sam_file}"


# this step if very important because, for whatever reason, an extra empty line is added at the end of the sam file that needs to be deleted before further analysis
rule sam_to_bam:
    input:
        aligned_sam_file = MAIN_DIR + "/tmp/{sample}-bwa_mem.sam"
        
    output:
        aligned_bam_file = MAIN_DIR + "/tmp/{sample}-bwa_mem.bam"
        
    resources:
        threads = THREADS,
        runtime = '0:8:0:0',
        individual_core_memory = '10G'

    shell:
        "sed '$d' {input.aligned_sam_file} | /.mounts/labs/reimandlab/private/users/abahcheli/software/bin/samtools view -@ {resources.threads} -b - > {output.aligned_bam_file}"




rule MergeBamAlignment:
    input:
        MAIN_DIR + "/" + GENOME_VERSION + ".dict",
        genome_fasta = MAIN_DIR + "/" + GENOME_VERSION + ".fa",
        
        original_bam_file = MAIN_DIR + "/tmp/{sample}.bam",
        aligned_bam_file = MAIN_DIR + "/tmp/{sample}-bwa_mem.bam"
        
    output:
        merged_alignment_bam = MAIN_DIR + "/tmp/{sample}-merged_alignment.bam"
        
    params:
        java_memory = '25g'
        
    resources:
        threads = 1,
        runtime = '0:8:0:0',
        individual_core_memory = '30G'

    shell:
        "conda run -n gatk /.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-*******/gatk --java-options '-Xmx{params.java_memory}' MergeBamAlignment --REFERENCE_SEQUENCE {input.genome_fasta} --UNMAPPED_BAM {input.original_bam_file} --ALIGNED_BAM {input.aligned_bam_file} --OUTPUT {output.merged_alignment_bam} --CREATE_INDEX true --ADD_MATE_CIGAR true --CLIP_ADAPTERS false --CLIP_OVERLAPPING_READS true --INCLUDE_SECONDARY_ALIGNMENTS true --MAX_INSERTIONS_OR_DELETIONS -1 --PRIMARY_ALIGNMENT_STRATEGY MostDistant --ATTRIBUTES_TO_RETAIN XS --TMP_DIR {TMP_DIR}"





# rule MarkDuplicatesSpark:
#     input:
#         merged_alignment_bam = MAIN_DIR + "/tmp/{sample}-merged_alignment.bam"
        
#     output:
#         marked_duplicates_bam = MAIN_DIR + "/tmp/{sample}-marked_duplicates.bam"
        
#     params:
#         java_memory = '25g'
        
#     resources:
#         threads = 1,
#         runtime = '0:8:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "conda run -n gatk /.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-*******/gatk --java-options '-Xmx{params.java_memory}' MarkDuplicatesSpark -I {input.merged_alignment_bam} -O {output.marked_duplicates_bam}"



# rule BaseRecalibrator:
#     input:
#         marked_duplicates_bam = MAIN_DIR + "/tmp/{sample}-marked_duplicates.bam"
        
#     output:
#         base_recalibrated_file = MAIN_DIR + "/tmp/{sample}-recal_data.table"
        
#     params:
#         java_memory = '25g'
        
#     resources:
#         threads = 1,
#         runtime = '0:8:0:0',
#         individual_core_memory = '30G'
        
#     shell:
#         "conda run -n gatk /.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-*******/gatk --java-options '-Xmx{params.java_memory}' BaseRecalibrator -I {input.merged_alignment_bam} -O {output.marked_duplicates_bam} --known-sites {}"







#####################
# Variant calling and VCF generation
#####################



rule index_fasta:
    input:
        fasta_file = MAIN_DIR + "/" + GENOME_VERSION + ".fa"
        
    output:
        fasta_index_file = MAIN_DIR + "/" + GENOME_VERSION + ".fa.fai"
        
    resources:
        threads = 1,
        runtime = '0:8:0:0',
        individual_core_memory = '40G'

    shell:
        "/.mounts/labs/reimandlab/private/users/abahcheli/software/bin/samtools faidx {input.fasta_file} > {output.fasta_index_file}"



rule Mutect2:
    input:
        # this time, include the normal sample in all runs
        MAIN_DIR + "/" + GENOME_VERSION + ".fa.fai",
        genome_fasta = MAIN_DIR + "/" + GENOME_VERSION + ".fa",

        tumor_bam = MAIN_DIR + "/tmp/{sample}-merged_alignment.bam",
        normal_bam = MAIN_DIR + "/tmp/46775_HR_dgApobec3_Liver_S6-merged_alignment.bam",
        
        intervals_bed_file = MAIN_DIR + "/S0276129_Covered.GRCm38_68.edited_manual.bed"
        
    output:
        output_vcf_gz = MAIN_DIR + "/vcfs/{sample}.vcf.gz"
        
    params:
        java_memory = '230g',
        sample_name = "{sample}",
        sample_tmp_dir = MAIN_DIR + "/vcfs/{sample}/"
        
    resources:
        threads = 1,
        runtime = '0:60:0:0',
        individual_core_memory = '235G'
        
    shell:
        "conda run -n gatk /.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-*******/gatk --java-options '-Xmx{params.java_memory}' Mutect2 -R {input.genome_fasta} --input {input.tumor_bam} --input {input.normal_bam} --output {output.output_vcf_gz} -normal 46775_HR_dgApobec3_Liver_S6 --native-pair-hmm-threads {resources.threads} --tmp-dir {params.sample_tmp_dir} --intervals {input.intervals_bed_file}"






rule FilterMutectCalls:
    input:
        # this time, include the normal sample in all runs
        MAIN_DIR + "/" + GENOME_VERSION + ".fa.fai",        
        genome_fasta = MAIN_DIR + "/" + GENOME_VERSION + ".fa",
        
        vcf_gz = MAIN_DIR + "/vcfs/{sample}.vcf.gz"
        
    output:
        # MAIN_DIR + "/vcfs/{sample}_filtered.vcf",
        filtered_vcf_gz = MAIN_DIR + "/vcfs/{sample}_filtered.vcf.gz"
        
    params:
        java_memory = '35g',
        sample_name = "{sample}",
        sample_tmp_dir = MAIN_DIR + "/vcfs/{sample}/"
        
    resources:
        threads = 1,
        runtime = '0:12:0:0',
        individual_core_memory = '40G'
        
    shell:
        "conda run -n gatk /.mounts/labs/reimandlab/private/users/abahcheli/software/gatk-*******/gatk --java-options '-Xmx{params.java_memory}' FilterMutectCalls --reference {input.genome_fasta} --variant {input.vcf_gz} --output {output.filtered_vcf_gz} --tmp-dir {params.sample_tmp_dir}"
        

# unzip
rule unzip_vcf:
    input:
        filtered_vcf_gz = MAIN_DIR + "/vcfs/{sample}_filtered.vcf.gz"
        
    output:
        MAIN_DIR + "/vcfs/{sample}_filtered.vcf"
        
    resources:
        threads = 1,
        runtime = '0:8:0:0',
        individual_core_memory = '10G'
        
    shell:
        "gunzip {input.filtered_vcf_gz}"




rule annovar_db_creation_internet:
    input:
        genome_fasta = MAIN_DIR + "/annovar/tmp.txt"
        
    output:
        MAIN_DIR + "/annovar/" + GENOME_VERSION + "_refGeneMrna.fa"
        
    params:
        annover_db_dir = MAIN_DIR + "/annovar"
        
    resources:
        threads = 1,
        runtime = '0:8:0:0',
        individual_core_memory = '20G'
        
    run:
        shell("/.mounts/labs/reimandlab/private/users/abahcheli/software/annovar/annotate_variation.pl -buildver {GENOME_VERSION} -downdb refGene {params.annover_db_dir}")
        shell("/.mounts/labs/reimandlab/private/users/abahcheli/software/annovar/annotate_variation.pl --buildver {GENOME_VERSION} --downdb seq {params.annover_db_dir}/{GENOME_VERSION}_seq")
        shell("/.mounts/labs/reimandlab/private/users/abahcheli/software/annovar/retrieve_seq_from_fasta.pl {params.annover_db_dir}/{GENOME_VERSION}_refGene.txt -seqdir {params.annover_db_dir}/{GENOME_VERSION}_seq -format refGene -outfile {params.annover_db_dir}/{GENOME_VERSION}_refGeneMrna.fa")




# annotate with ANNOVAR
rule Annovar:
    input:
        MAIN_DIR + "/annovar/{GENOME_VERSION}_refGeneMrna.fa",
        filtered_vcf = MAIN_DIR + "/vcfs/{sample}_filtered.vcf"
        
    output:
        MAIN_DIR + "/annovar/{sample}.{GENOME_VERSION}_multianno.txt"
        
    params:
        output_file_prefix = MAIN_DIR + "/annovar/{sample}",
        annover_db_dir = MAIN_DIR + "/annovar"
        
    resources:
        threads = 1,
        runtime = '0:8:0:0',
        individual_core_memory = '20G'
        
    run:
        shell("/.mounts/labs/reimandlab/private/users/abahcheli/software/annovar/table_annovar.pl --vcfinput --protocol refGene --operation g --buildver {GENOME_VERSION} --outfile {params.output_file_prefix} {input.filtered_vcf} {params.annover_db_dir}")

















