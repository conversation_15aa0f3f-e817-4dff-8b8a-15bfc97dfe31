import pandas as pd 
import numpy as np

from sklearn.metrics.pairwise import cosine_similarity

ref_data_dir = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/data/ref_data'
classes = ['mesenchymal', 'classical', 'proneural']

scores = []

for col in zscore.columns:
    for gbm in classes:
        sim_matrix = pd.read_csv(os.path.join(ref_data_dir, 'gbm_subtypes', f"{gbm}.csv"), header=None)
        sim_matrix.index = sim_matrix[0].to_numpy()
        sim_matrix = sim_matrix.loc[:,[1]]
        
        common_genes = np.intersect1d(sim_matrix.index, zscore.index)
        ninter = len(common_genes)
        
        # subset to genes of interest
        sub_df = zscore.loc[common_genes,col].to_numpy()
        cos_sim = cosine_similarity(sub_df.reshape(1,-1), sim_matrix.loc[common_genes,:].to_numpy().reshape(1,-1))[0][0]

        scores.append([col, gbm, cos_sim])

scores = pd.DataFrame(scores, columns = ['sample', 'class', 'score'])

scores.loc[:,'sample'] = list(map(lambda x: x.split("-")[1].split(".")[0], scores['sample']))

scores.to_csv("/.mounts/labs/reimandlab/private/users/abahcheli/tmp/class_scores.tsv", sep='\t', index=False)






########################################################
########################################################

# subset to classified samples
tcga_df = tcga_df.loc[np.unique(np.intersect1d(exp_df.index.to_numpy(dtype="<U64"), tcga_df.index.to_numpy(dtype='<U64'))), np.intersect1d(tcga_df.columns, subtypes_df.index)]
tcga_df = tcga_df.loc[np.invert(tcga_df.index.duplicated()),np.invert(tcga_df.columns.duplicated())]

subtypes_df = subtypes_df.loc[tcga_df.columns].astype('str')
subtypes_df = subtypes_df.loc[np.invert(subtypes_df.index.duplicated())]

# subset to common genes
exp_df = exp_df.loc[tcga_df.index,:]
exp_df = exp_df.loc[np.invert(exp_df.index.duplicated()),:]

exp_df = exp_df.loc[tcga_df.index,:]

# use genes of interest
genes_oi = open("/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/expression_subtype_genes.txt").read().splitlines()
genes_oi = np.intersect1d(genes_oi,exp_df.index)

# cluster
from sklearn.neighbors import KNeighborsClassifier
np.unique(subtypes_df.astype('str'))

knn = KNeighborsClassifier(n_neighbors=6)
knn.fit(tcga_df.transpose(), subtypes_df)

y_pred = knn.predict(exp_df.transpose())
y_pred

# repeat using only genes of interest
tcga_df = tcga_df.loc[genes_oi,:]
exp_df = exp_df.loc[genes_oi,:]

knn = KNeighborsClassifier(n_neighbors=6)
knn.fit(tcga_df.transpose(), subtypes_df)

y_pred = knn.predict(exp_df.transpose())
y_pred


########################################################
########################################################


subtypes_df = pd.read_csv("/.mounts/labs/reimandlab/private/users/abahcheli/additional_data/glass/expression_subtypes.csv")
subtypes_df.index = list(map(lambda x: "-".join(x.split("-")[:3]), subtypes_df['aliquot_barcode']))

subtypes_df

mask1 = list(map(lambda x: str(x.split("-")[3]) == "TP", subtypes_df['aliquot_barcode']))
subtypes_df.loc[mask1,'tumor'] = 'primary'

mask = list(map(lambda x: str(x.split("-")[3]) == "R1", subtypes_df['aliquot_barcode']))
subtypes_df.loc[mask,'tumor'] = 'recurrent'


subtypes_df = subtypes_df.loc[np.logical_or(mask1, mask),:]

subtypes_df.to_csv("/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/glass_primary_recurrent.tsv", sep='\t', index=False)


########################################################
########################################################



tmp_df = tcga_df.transpose().copy()
tmp_df.loc[:,'subtype'] = subtypes_df.to_numpy()

tmp_df = pd.concat([tmp_df, exp_df.transpose()])

tmp_df.loc['RLGS10-primary_rna_seq.subread.BAM', 'subtype'] = 'primary'
tmp_df.loc['RLGS10-recurrent_rna_seq.subread.BAM', 'subtype'] = 'recurrent'

tmp_df.to_csv("/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/combined.tsv", sep='\t', index=False)


import pandas as pd
import numpy as np
import umap
from sklearn.preprocessing import StandardScaler

tmp_df = pd.read_csv("/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/combined.tsv", sep='\t')
tmp_df2 = tmp_df.iloc[:,:-1]

reducer = umap.UMAP(transform_seed=1234, random_state=1234)
scaled_df = StandardScaler().fit_transform(np.log1p(tmp_df2.to_numpy()))
embedding = reducer.fit_transform(scaled_df)


# save dataframe and generate figure in R
embedded_df = pd.DataFrame(embedding, columns = ['x_dim', 'y_dim'])
embedded_df.loc[:,'subtype'] = tmp_df['subtype'].to_numpy()

embedded_df.to_csv('/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_01_30/_figure_data/umap.tsv', sep='\t', header=True, index=False)

