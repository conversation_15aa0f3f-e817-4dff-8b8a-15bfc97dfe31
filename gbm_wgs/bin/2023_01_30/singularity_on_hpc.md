# Introduction

Singularities replace dockers on the HPC because docker is not allowed on the HPC. Docker requires root access. Singularity does not require root access.

To use insgularity on the HPC, you can either load the singularity module or install singularity yourself.

__Load the module__
```
module load singularity
```

__Install singularity with conda__
```
conda create -y -n singularity-env

conda activate singularity-env

conda install -y -c conda-forge singularity
```
(https://anaconda.org/conda-forge/singularity)


# Building singularity images
Singularity can use the same images as dockers. However, you need to convert the docker image into a singularity image. To do this, you can either "pull" or "build" the image.

Remember, you will need an internet (u18build) node to access the docker cloud and download the docker images.


__*Important*__
Singularity uses a cache when building images. This cache very quickly fills up on the HPC and can return errors. Before building or pulling images, define a directory to use as a cache. 

```
export SINGULARITY_CACHEDIR=/.mounts/labs/reimandlab/private/users/abahcheli/singularity_cache/
```

__Build a singularity image__
To build an image, define the singularity output image (.sif file) and then the source.
```
singularity pull /.mounts/labs/reimandlab/private/users/abahcheli/software/guppy/guppy_gpu_v3.sif docker://abahcheli/guppy-gpu
```

# Running singularity containers
Once you have an image built (or a docker container in mind), you can run the singularity container. 

Remember that singularity is a virtual machine sandbox. This means that, by default, *singularity uses a read-only file system and does not automatically mount directories to the virtual machine.*

You need to specifically define mounts you want bound. Fortunately, anything mounted via the "--bind" flag is read-write. For example, to bind the "/.mounts" directory and run an interactive singularity container, you can do the following.

```
singularity run --bind /.mounts /.mounts/labs/reimandlab/private/users/abahcheli/software/guppy/guppy_gpu_v3.sif /bin/bash
```

__Singularity mounts do not merge with local mounts__

This means that, if your singularity / docker container has important data in a location that overlaps with your mount, it will be overwritten by the mounted directory. 

```
singularity run --bind /usr /.mounts/labs/reimandlab/private/users/abahcheli/software/guppy/guppy_gpu_v3.sif /bin/bash
```

The command above overrides the singularity image's "/usr" directory with the local directory.

For example, if you have software on a docker container in the "/usr" directory, and you bind the "/usr" directory on your local machine, the singularity directory will be lost.

As a workaround, try copying the singularity / docker directory to a new, unique directory before building the singularity / docker image. This can be done by adding a line to the docker file. This way, you can keep local and singularity software when you mount local directories.

*Custom Dockerfile for Guppy*
```
# base image uses ubuntu:16.04 (https://hub.docker.com/r/genomicpariscentre/guppy/dockerfile)
FROM genomicpariscentre/guppy-gpu:latest

# Metadata
LABEL software.name="Guppy GPU for HPC"

RUN cp -R /usr/bin /ont-guppy \
    && export PATH=/ont-guppy:PATH
```

*Running Guppy Singularity on the HPC*
```
singularity run --bind /usr /.mounts/labs/reimandlab/private/users/abahcheli/software/guppy/guppy_gpu_v3.sif /u/abahcheli/guppy_basecaller
```

# Other notes

- Singularity in linux specific. You cannot use it or even build images on MacOS or Windows. Basically, everything has to be done on the HPC.

- Singularity works really well with Snakemake. Ask Alec for an example script.

__Useful resources__
Singularity on the HPC: https://pawseysc.github.io/singularity-containers/12-singularity-intro/index.html

Singularity executions: https://docs.sylabs.io/guides/2.6/user-guide/singularity_and_docker.html

