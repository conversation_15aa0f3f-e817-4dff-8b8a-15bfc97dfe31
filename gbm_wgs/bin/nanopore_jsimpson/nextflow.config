// Default workflow parameters

profiles {
    cluster {
        process.executor = 'sge'
        process.penv = 'smp'
        process.memory = '2G'
        process.cpus = '1'
        process.time = '12h'
        process.clusterOptions = { "-l h_vmem=${task.memory.toMega()/task.cpus}M -V -P simpsonlab -l h_stack=32M " }
    }
}

process {
    conda = '/.mounts/labs/simpsonlab/sw/miniconda3/envs/cfdna'

    withLabel: GPU {
        clusterOptions = { "-l gpu=2 -l h_vmem=${task.memory.toMega()/task.cpus}M -V -P simpsonlab -l h_stack=32M " }
    }
}

params {
    guppy = "/.mounts/labs/ont/software/guppy-5.0.11/bin/guppy_basecaller"
    run_directory = null
    threads = 1
    reference = "/.mounts/labs/simpsonlab/data/references/GRCh38_no_alt_analysis_set.GCA_000001405.15.fna"
    rebasecall = false
    sample_name = null
    nanopolish = "/.mounts/labs/simpsonlab/users/jsimpson/code/nanopolish-master/nanopolish"
    mbtools = "/.mounts/labs/simpsonlab/users/jsimpson/code/mbtools/target/release/mbtools"
    fragmentation = "/.mounts/labs/simpsonlab/users/jbroadbent/code/cfdna/fragmentation_ratio.py"
}
