latency-wait: 75
rerun-incomplete: True
keep-going: True
# Cluster submission
conda-frontend: "conda"
jobs: 105
cluster: "/opt/uge-8.6.18/bin/lx-amd64/qsub -V -P reimandlab -l h_vmem={resources.individual_core_memory},h_rt={resources.runtime} -pe smp {resources.threads} -q {resources.qeue}"
# singularity
use-singularity: True
singularity-args: "--bind /.mounts,/usr --home /.mounts/labs/reimandlab/private/users/abahcheli/tmp/singularity_home"
# # Job resources
# set-resources:
#   - helloSingularity:mem_mb=1000
#   - helloSingularity:runtime=00:03:00
# # For some reasons time needs quotes to be read by snakemake
# default-resources:
#   - mem_mb=500
#   - runtime="00:01:00"
# # Define the number of threads used by rules
# set-threads:
#   - helloSingularity=1
