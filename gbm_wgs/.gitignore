
# that may be referenced to or modified
**/results/**
**/data/**

# snakemake, python and jupyter notebooks
**/__pycache__
**/.ipynb_checkpoints
**/.snakemake



# pyenv
.python-version

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Local computer
**/.vscode/
.DS_Store

# Distribution / packaging
Dockerfile
.dockerignore
.Python
build/
samfilt.egg-info
develop-eggs/
dist/
docker/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
*.egg*
conda/
MANIFEST

# Byte-compiled / optimized / DLL files
*.py[cod]
*$py.class


